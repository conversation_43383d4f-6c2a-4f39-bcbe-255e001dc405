<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sippar - Core Functionality Domains</title>
    <style>
        :root {
            /* Color System - Aligned with Sippar Application */
            /* Primary Colors - Emerald Theme */
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;

            /* Background Colors */
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;

            /* Card Colors */
            --card-background: #ffffff;
            --card-foreground: #0f172a;

            /* Border Colors */
            --border: #e2e8f0;
            --border-muted: #cbd5e1;

            /* Semantic Colors */
            --success: #059669;
            --success-light: #10b981;
            --success-foreground: #ffffff;

            --warning: #d97706;
            --warning-light: #f59e0b;
            --warning-foreground: #ffffff;

            --info: #0284c7;
            --info-light: #0ea5e9;
            --info-foreground: #ffffff;

            --destructive: #dc2626;
            --destructive-light: #ef4444;
            --destructive-foreground: #ffffff;

            /* Gradient Colors */
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --gradient-success: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
            --gradient-warning: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);

            /* Typography */
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

            /* Spacing & Layout */
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --radius-lg: 1rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Dark theme support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background: #0a0e1a;
                --background-muted: #1e293b;
                --background-secondary: #334155;
                --foreground: #f8fafc;
                --foreground-muted: #94a3b8;

                --card-background: #0f172a;
                --card-foreground: #f8fafc;

                --border: #334155;
                --border-muted: #475569;

                --primary: #10b981;
                --primary-light: #34d399;
                --primary-dark: #047857;
            }
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 30px;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
        }

        h1 {
            color: var(--foreground);
            text-align: center;
            border-bottom: 3px solid var(--primary);
            padding-bottom: 15px;
            margin-bottom: 30px;
        }

        h2 {
            color: var(--primary);
            border-left: 4px solid var(--primary);
            padding-left: 15px;
            margin-top: 30px;
        }

        h3 {
            color: var(--primary-dark);
            margin-top: 25px;
        }

        .domain {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
        }

        .domain-header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 15px;
            border-radius: var(--radius) var(--radius) 0 0;
            margin: -20px -20px 15px -20px;
            font-weight: bold;
            font-size: 1.2em;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .feature {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border-left: 4px solid var(--primary);
            box-shadow: var(--shadow-sm);
        }

        .feature h4 {
            margin: 0 0 10px 0;
            color: var(--primary-dark);
        }

        .feature ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .feature li {
            margin: 5px 0;
            color: var(--foreground-muted);
        }

        .tech-stack {
            background: var(--background-secondary);
            padding: 15px;
            border-radius: var(--radius-sm);
            margin: 15px 0;
            border: 1px solid var(--border);
        }

        .roles {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }

        .role {
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .overview {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 25px;
            border-radius: var(--radius-lg);
            margin-bottom: 30px;
            text-align: center;
        }

        .overview h2 {
            color: var(--primary-foreground);
            border: none;
            padding: 0;
            margin: 0 0 15px 0;
        }

        .architecture {
            background: var(--success-light);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.1) 100%);
            border: 1px solid var(--success-light);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }

        .integration {
            background: linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
            border: 1px solid var(--warning-light);
            padding: 15px;
            border-radius: var(--radius-sm);
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sippar - Petty Cash Management Solution</h1>

        <div class="overview">
            <h2>Application Overview</h2>
            <p><strong>Sippar</strong> is a comprehensive web-based petty cash management platform built with Laravel and React. It provides a streamlined system for tracking, approving, and reconciling petty cash transactions within organizations to improve transparency, reduce misuse, and streamline reimbursement processes.</p>
        </div>

        <div class="tech-stack">
            <h3>Technology Stack</h3>
            <p><strong>Backend:</strong> Laravel 11, PHP 8.2+, SQLite/MySQL</p>
            <p><strong>Frontend:</strong> React 18, TypeScript, Inertia.js, TailwindCSS</p>
            <p><strong>Architecture:</strong> Domain-Driven Design (DDD), Repository Pattern, Service Layer</p>
            <p><strong>Features:</strong> Role-based Access Control, Real-time Notifications, PDF Generation, File Uploads</p>
        </div>

        <div class="architecture">
            <h3>Domain-Driven Architecture</h3>
            <p>The application follows Domain-Driven Design principles with clear separation of concerns:</p>
            <ul>
                <li><strong>Domain Layer:</strong> Core business logic and entities</li>
                <li><strong>Application Layer:</strong> Use cases and application services</li>
                <li><strong>Infrastructure Layer:</strong> Data persistence and external integrations</li>
                <li><strong>Presentation Layer:</strong> React components and Inertia.js controllers</li>
            </ul>
        </div>

        <h2>Core Functionality Domains</h2>

        <div class="domain">
            <div class="domain-header">
                1. User Management & Authentication
            </div>
            <div class="features">
                <div class="feature">
                    <h4>User Authentication</h4>
                    <ul>
                        <li>Email/password authentication</li>
                        <li>Email verification with custom codes</li>
                        <li>Password reset functionality</li>
                        <li>Session management</li>
                        <li>Multi-factor authentication ready</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Role-Based Access Control</h4>
                    <ul>
                        <li>Platform Administrator</li>
                        <li>Organization Administrator</li>
                        <li>Finance Manager</li>
                        <li>Cashier</li>
                        <li>Head of Department (HOD)</li>
                        <li>Employee/Requestor</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>User Profile Management</h4>
                    <ul>
                        <li>Profile information management</li>
                        <li>Avatar upload and management</li>
                        <li>Password change functionality</li>
                        <li>Department and branch associations</li>
                    </ul>
                </div>
            </div>
            <div class="roles">
                <span class="role">Platform Admin</span>
                <span class="role">Organization Admin</span>
                <span class="role">Finance Manager</span>
                <span class="role">Cashier</span>
                <span class="role">HOD</span>
                <span class="role">Employee</span>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                2. Organization Structure Management
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Organization Management</h4>
                    <ul>
                        <li>Multi-tenant organization support</li>
                        <li>Organization setup wizard</li>
                        <li>Contact information management</li>
                        <li>M-Pesa account integration</li>
                        <li>Organization status management</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Branch Management</h4>
                    <ul>
                        <li>Multiple branch support per organization</li>
                        <li>Branch-specific configurations</li>
                        <li>Branch administrator assignment</li>
                        <li>Location and contact management</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Department Management</h4>
                    <ul>
                        <li>Hierarchical department structure</li>
                        <li>Department templates for quick setup</li>
                        <li>Head of Department (HOD) assignment</li>
                        <li>Department-specific workflows</li>
                        <li>Budget allocation per department</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                3. Chart of Accounts Management
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Account Structure</h4>
                    <ul>
                        <li>Hierarchical chart of accounts</li>
                        <li>Platform-level and organization-specific accounts</li>
                        <li>Account codes and descriptions</li>
                        <li>Account type categorization</li>
                        <li>Active/inactive account status</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Spending Controls</h4>
                    <ul>
                        <li>Spending limits per account</li>
                        <li>Time-based limit periods</li>
                        <li>Budget tracking and alerts</li>
                        <li>Account-specific permissions</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Role-Based Visibility</h4>
                    <ul>
                        <li>Employee view (descendants only, no codes)</li>
                        <li>Finance manager view (all accounts with codes)</li>
                        <li>Organization admin full access</li>
                        <li>Filtered account selection during requisitions</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                4. Requisition Management
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Requisition Creation</h4>
                    <ul>
                        <li>Multi-item requisition support</li>
                        <li>Chart of accounts integration</li>
                        <li>File attachment support</li>
                        <li>Purpose and description fields</li>
                        <li>Automatic requisition numbering</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Approval Workflow</h4>
                    <ul>
                        <li>Dynamic approval workflows</li>
                        <li>Department-specific workflows</li>
                        <li>Multi-step approval process</li>
                        <li>Approval, rejection, and revision requests</li>
                        <li>Fallback approval mechanisms</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Status Management</h4>
                    <ul>
                        <li>Draft, submitted, approved, rejected states</li>
                        <li>Revision request handling</li>
                        <li>Approval history tracking</li>
                        <li>Real-time status updates</li>
                        <li>Notification system integration</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                5. Approval Workflow Engine
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Workflow Templates</h4>
                    <ul>
                        <li>Pre-defined workflow templates</li>
                        <li>Template categories and descriptions</li>
                        <li>Quick workflow setup from templates</li>
                        <li>Template preview functionality</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Dynamic Workflows</h4>
                    <ul>
                        <li>Department-specific workflows</li>
                        <li>Branch-level workflow configuration</li>
                        <li>Multi-step approval chains</li>
                        <li>Role-based approver assignment</li>
                        <li>Fallback approval routing</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Workflow Management</h4>
                    <ul>
                        <li>Workflow creation and editing</li>
                        <li>Step-by-step configuration</li>
                        <li>Approver role assignment</li>
                        <li>Default workflow designation</li>
                        <li>Workflow activation/deactivation</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                6. Cash Float Management
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Float Operations</h4>
                    <ul>
                        <li>Cash float creation and management</li>
                        <li>Initial float issuance tracking</li>
                        <li>Real-time balance monitoring</li>
                        <li>Float reimbursement processing</li>
                        <li>Float return handling</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Transaction Logging</h4>
                    <ul>
                        <li>Comprehensive transaction history</li>
                        <li>Cash in/out tracking</li>
                        <li>Running balance calculations</li>
                        <li>Transaction cost recording</li>
                        <li>Audit trail maintenance</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Reconciliation & Reporting</h4>
                    <ul>
                        <li>Cash float log generation</li>
                        <li>PDF export functionality</li>
                        <li>Reconciliation section with verification</li>
                        <li>Low balance alerts</li>
                        <li>Daily/monthly reconciliation reports</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                7. Transaction & Disbursement Management
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Transaction Processing</h4>
                    <ul>
                        <li>Automatic transaction creation from approved requisitions</li>
                        <li>Multiple payment methods (M-Pesa, Bank Transfer)</li>
                        <li>Transaction status tracking</li>
                        <li>Payment detail management</li>
                        <li>Transaction cost calculation</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Disbursement Workflow</h4>
                    <ul>
                        <li>Ready for disbursement queue</li>
                        <li>Disbursement approval process</li>
                        <li>Payment method selection</li>
                        <li>Disbursement completion tracking</li>
                        <li>Failed disbursement handling</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Payment Integration</h4>
                    <ul>
                        <li>M-Pesa payment integration</li>
                        <li>Bank account details management</li>
                        <li>Mobile money payment options</li>
                        <li>Payment confirmation tracking</li>
                        <li>Transaction reference management</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                8. Attachment & Document Management
            </div>
            <div class="features">
                <div class="feature">
                    <h4>File Upload System</h4>
                    <ul>
                        <li>Multi-file upload support</li>
                        <li>Drag and drop interface</li>
                        <li>File type validation</li>
                        <li>File size restrictions</li>
                        <li>Secure file storage</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Document Association</h4>
                    <ul>
                        <li>Requisition receipt attachments</li>
                        <li>Transaction document linking</li>
                        <li>Polymorphic attachment relationships</li>
                        <li>Document categorization</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>File Operations</h4>
                    <ul>
                        <li>File download functionality</li>
                        <li>In-browser file viewing</li>
                        <li>File deletion with permissions</li>
                        <li>Attachment history tracking</li>
                        <li>File metadata management</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                9. Notification & Communication System
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Email Notifications</h4>
                    <ul>
                        <li>Requisition submission confirmations</li>
                        <li>Approval/rejection notifications</li>
                        <li>Disbursement completion alerts</li>
                        <li>Low cash float warnings</li>
                        <li>User onboarding notifications</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>In-App Notifications</h4>
                    <ul>
                        <li>Real-time notification system</li>
                        <li>Notification center with history</li>
                        <li>Mark as read functionality</li>
                        <li>Notification categorization</li>
                        <li>Toast notifications for immediate alerts</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Communication Workflow</h4>
                    <ul>
                        <li>Automated workflow notifications</li>
                        <li>Role-based notification routing</li>
                        <li>Escalation notifications</li>
                        <li>Reminder notifications</li>
                        <li>Bulk notification management</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="domain">
            <div class="domain-header">
                10. Dashboard & Reporting
            </div>
            <div class="features">
                <div class="feature">
                    <h4>Role-Based Dashboards</h4>
                    <ul>
                        <li>Platform Administrator dashboard</li>
                        <li>Organization Administrator dashboard</li>
                        <li>Finance Manager dashboard</li>
                        <li>Head of Department dashboard</li>
                        <li>Employee dashboard</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Analytics & Metrics</h4>
                    <ul>
                        <li>Requisition status counts</li>
                        <li>Pending approvals tracking</li>
                        <li>Cash float balance monitoring</li>
                        <li>Department spending analytics</li>
                        <li>Transaction volume metrics</li>
                    </ul>
                </div>
                <div class="feature">
                    <h4>Reporting Features</h4>
                    <ul>
                        <li>PDF report generation</li>
                        <li>Excel export functionality</li>
                        <li>Date range filtering</li>
                        <li>Department-wise reports</li>
                        <li>Audit trail reports</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="integration">
            <h3>Integration Capabilities</h3>
            <ul>
                <li><strong>M-Pesa Integration:</strong> Mobile money payment processing</li>
                <li><strong>Email Services:</strong> SMTP integration for notifications</li>
                <li><strong>File Storage:</strong> Local and cloud storage support</li>
                <li><strong>Accounting Systems:</strong> Chart of accounts integration ready</li>
                <li><strong>Employee Directory:</strong> User management integration</li>
                <li><strong>Banking APIs:</strong> Future bank integration capabilities</li>
            </ul>
        </div>

        <div class="architecture">
            <h3>Security & Compliance Features</h3>
            <ul>
                <li><strong>Authentication:</strong> Secure login with email verification</li>
                <li><strong>Authorization:</strong> Role-based access control (RBAC)</li>
                <li><strong>Audit Trail:</strong> Complete transaction and approval history</li>
                <li><strong>Data Validation:</strong> Input validation and sanitization</li>
                <li><strong>File Security:</strong> Secure file upload and storage</li>
                <li><strong>Session Management:</strong> Secure session handling</li>
                <li><strong>CSRF Protection:</strong> Cross-site request forgery protection</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: var(--background-muted); border-radius: 8px;">
            <p><strong>Sippar</strong> provides a comprehensive solution for petty cash management with robust workflow automation, real-time tracking, and detailed reporting capabilities. The application is designed to scale with organizational needs while maintaining security and compliance standards.</p>
            <p style="margin-top: 15px; color: #718096; font-size: 0.9em;">
                Version: 1.0 |
            </p>
        </div>
    </div>

    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString();
    </script>
</body>
</html>
