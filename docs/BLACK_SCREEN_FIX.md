# Black Screen Fix for Notification View Button

## Issue
When clicking the "View" button in notifications, the page would go black first before loading the destination page.

## Root Cause
The issue was caused by using `window.location.href` for navigation instead of Inertia.js router. This caused:
1. **Full page reload** instead of smooth SPA navigation
2. **Black screen flash** during the reload process
3. **Loss of application state** and context
4. **Slower navigation** compared to Inertia's smooth transitions

## Solution
Replaced `window.location.href` with `router.visit()` from Inertia.js for smooth, seamless navigation.

### Before (Problematic Code):
```typescript
onClick={() => {
    if (isUnread) {
        onMarkAsRead(notification.id);
    }
    // ❌ This causes full page reload and black screen
    window.location.href = data.action_url;
}}
```

### After (Fixed Code):
```typescript
onClick={() => {
    if (isUnread) {
        onMarkAsRead(notification.id);
    }
    // ✅ This uses Inertia for smooth navigation
    router.visit(data.action_url);
}}
```

## Files Updated
- **`resources/js/components/notifications/NotificationItem.tsx`**
  - Added `router` import from `@inertiajs/react`
  - Replaced `window.location.href` with `router.visit()`

## Benefits of the Fix
1. **Smooth Navigation**: No more black screen flash
2. **Faster Loading**: Inertia's SPA navigation is faster than full page reloads
3. **Better UX**: Seamless transitions between pages
4. **State Preservation**: Application state is maintained during navigation
5. **Consistent Behavior**: Matches the rest of the application's navigation pattern

## Testing the Fix
1. Go to the notifications page
2. Click the "View" button on any notification
3. ✅ Should navigate smoothly without any black screen
4. ✅ Page should load immediately with Inertia's smooth transition
5. ✅ Notification should be marked as read and badge count should update

## Technical Details

### Inertia.js Navigation Benefits:
- **Partial Page Updates**: Only updates the necessary parts of the page
- **Preserved Layout**: Header, sidebar, and other layout elements remain intact
- **Faster Rendering**: No need to re-download and re-parse CSS/JS assets
- **Smooth Transitions**: Built-in loading states and transitions
- **Browser History**: Proper back/forward button support

### Why `window.location.href` Caused Issues:
- Forces a complete page reload
- Browser shows blank/white screen during reload
- All JavaScript state is lost
- CSS and JavaScript files are re-downloaded
- Layout components are completely re-rendered

## Additional Notes
The ToastNotification component was already using `router.visit()` correctly, so no changes were needed there. This fix specifically addressed the NotificationItem component used in the notifications index page.

## Future Considerations
Always use Inertia.js router methods for navigation within the application:
- `router.visit(url)` - Standard navigation
- `router.get(url)` - GET request navigation
- `router.post(url, data)` - POST request navigation
- `router.put(url, data)` - PUT request navigation
- `router.delete(url)` - DELETE request navigation

Avoid using `window.location.href` unless you specifically need a full page reload (e.g., external links, logout, etc.).