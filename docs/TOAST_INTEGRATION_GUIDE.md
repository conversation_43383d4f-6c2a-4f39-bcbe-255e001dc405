# Toast Integration Guide

This guide explains how to integrate the custom toast notification system into form components across the application.

## Overview

The toast system consists of:
- `ToastContainer.tsx` - Main container that manages toast state
- `ToastNotification.tsx` - Individual toast component
- `useFormToasts.ts` - Custom hook for form-specific toast notifications
- `window.d.ts` - TypeScript declarations for global toast function

## Implementation

### 1. Basic Toast Usage

The toast system exposes a global `window.showToast` function:

```typescript
window.showToast({
    title: 'Success!',
    message: 'Operation completed successfully.',
    type: 'success', // 'success' | 'error' | 'warning' | 'info'
    duration: 5000, // Optional, defaults to 5000ms
    actionUrl: '/some-url', // Optional, makes toast clickable
    dismissible: true // Optional, defaults to true
});
```

### 2. Using the useFormToasts Hook

For form components, use the `useFormToasts` hook for automatic toast notifications:

```typescript
import { useFormToasts } from '@/hooks/useFormToasts';

export default function MyFormComponent() {
    const { data, setData, post, processing, errors, wasSuccessful, recentlySuccessful } = useForm({
        name: '',
        email: ''
    });

    // Automatic toasts for success and validation errors
    const { showErrorToast, showSuccessToast, showWarningToast, showInfoToast } = useFormToasts({
        wasSuccessful,
        recentlySuccessful,
        errors,
        entityName: `Item "${data.name}"`, // Customize the entity name
        action: 'create' // 'create' | 'update' | 'delete'
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/items', {
            onError: () => {
                showErrorToast('Error', 'Failed to create item. Please try again.');
            }
        });
    };

    return (
        // Your form JSX
    );
}
```

### 3. Manual Toast Notifications

For custom scenarios, use the manual toast functions:

```typescript
const { showErrorToast, showSuccessToast, showWarningToast, showInfoToast } = useFormToasts({});

// Show custom toasts
showSuccessToast('Custom Success', 'Your custom message here');
showErrorToast('Custom Error', 'Something went wrong');
showWarningToast('Warning', 'Please be careful');
showInfoToast('Info', 'Here is some information');
```

### 4. Delete Operations

For delete operations, use the router with toast notifications:

```typescript
import { router } from '@inertiajs/react';
import { useFormToasts } from '@/hooks/useFormToasts';

const { showSuccessToast, showErrorToast } = useFormToasts({});
const [isDeleting, setIsDeleting] = useState(false);

const handleDelete = () => {
    if (confirm('Are you sure you want to delete this item?')) {
        setIsDeleting(true);
        
        router.delete(`/items/${item.id}`, {
            onSuccess: () => {
                showSuccessToast('Item Deleted', 'Item has been deleted successfully.');
            },
            onError: () => {
                showErrorToast('Delete Failed', 'Failed to delete item. Please try again.');
            },
            onFinish: () => {
                setIsDeleting(false);
            }
        });
    }
};
```

## Toast Types

### Success Toast
- **Type**: `success`
- **Color**: Green
- **Icon**: CheckCircle
- **Use for**: Successful operations, confirmations

### Error Toast
- **Type**: `error`
- **Color**: Red
- **Icon**: XCircle
- **Use for**: Errors, failures, validation issues

### Warning Toast
- **Type**: `warning`
- **Color**: Yellow/Orange
- **Icon**: AlertTriangle
- **Use for**: Warnings, cautions, important notices

### Info Toast
- **Type**: `info`
- **Color**: Blue
- **Icon**: Info
- **Use for**: Information, tips, neutral messages

## Best Practices

1. **Consistent Messaging**: Use clear, actionable messages
2. **Appropriate Duration**: 
   - Success: 5 seconds
   - Error: 6 seconds (longer for user to read)
   - Warning: 5 seconds
   - Info: 5 seconds

3. **Entity Names**: Include specific entity names in messages for clarity
4. **Action Context**: Specify what action was performed (created, updated, deleted)

## Examples

### Create Form
```typescript
// branches-create.tsx example
const { showErrorToast } = useFormToasts({
    wasSuccessful,
    recentlySuccessful,
    errors,
    entityName: `Branch "${data.name}"`,
    action: 'create'
});
```

### Edit Form
```typescript
// branches-edit.tsx example
const { showErrorToast } = useFormToasts({
    wasSuccessful,
    recentlySuccessful,
    errors,
    entityName: `Branch "${data.name}"`,
    action: 'update'
});
```

### Delete Operation
```typescript
// branches-show.tsx example
const handleDelete = () => {
    if (confirm(`Are you sure you want to delete "${branch.name}"?`)) {
        router.delete(`/branches/${branch.id}`, {
            onSuccess: () => {
                showSuccessToast('Branch Deleted', `Branch "${branch.name}" has been deleted successfully.`);
            },
            onError: () => {
                showErrorToast('Delete Failed', `Failed to delete branch "${branch.name}".`);
            }
        });
    }
};
```

## Integration Status

### ✅ Completed
- Branch components (create, edit, show with delete)
- Toast container and notification components
- useFormToasts hook
- TypeScript declarations

### 🔄 Next Steps
Apply the same pattern to other form components:
- Departments
- Organizations
- Users
- Roles
- Approval Workflows
- Requisitions
- Cash Floats
- Chart of Accounts
- Disbursements
- Transactions

## File Structure

```
resources/js/
├── components/
│   └── notifications/
│       ├── ToastContainer.tsx
│       ├── ToastNotification.tsx
│       └── ...
├── hooks/
│   └── useFormToasts.ts
├── types/
│   └── window.d.ts
└── pages/
    └── branches/
        ├── branches-create.tsx ✅
        ├── branches-edit.tsx ✅
        └── branches-show.tsx ✅
```