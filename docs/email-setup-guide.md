# 📧 Email Notification System Setup Guide

## Overview
This guide covers setting up SMTP with <PERSON><PERSON><PERSON> and configuring cron jobs for the email notification system.

---

## 📮 SMTP Setup with Mailtrap

### What is Mailtrap?
Mailtrap is a safe email testing environment that captures emails instead of sending them to real recipients. Perfect for development and testing.

### Getting Mailtrap Credentials

1. **Create Account**
   - Go to [https://mailtrap.io](https://mailtrap.io)
   - Sign up for a free account
   - Verify your email address

2. **Create Inbox**
   - After login, click "Add Inbox" under Sandbox tab 
   - Choose "Email Testing" → "My First Inbox"
   - Give it a name (e.g., "SIPPAR Development")

3. **Get SMTP Credentials**
   - Click on your inbox
   - Go to "SMTP Settings" tab
   - Select "Laravel 9+" from the integrations dropdown
   - Copy the credentials shown:

   ```env
   MAIL_MAILER=smtp
   MAIL_HOST=sandbox.smtp.mailtrap.io
   MAIL_PORT=2525
   MAIL_USERNAME=1a2b3c4d5e6f7g  # Your unique username
   MAIL_PASSWORD=9h8i7j6k5l4m3n  # Your unique password
   MAIL_ENCRYPTION=tls
   ```

4. **Update Your .env File**
   - Replace the placeholder values in your `.env` file
   - Keep `MAIL_FROM_ADDRESS` and `MAIL_FROM_NAME` as your company details

   ```env
   # Queue Configuration
   QUEUE_CONNECTION=database

   # Mail Configuration
   MAIL_MAILER=smtp
   MAIL_HOST=sandbox.smtp.mailtrap.io
   MAIL_PORT=2525
   MAIL_USERNAME=your_mailtrap_username
   MAIL_PASSWORD=your_mailtrap_password
   MAIL_ENCRYPTION=tls
   MAIL_FROM_ADDRESS=<EMAIL>
   MAIL_FROM_NAME="${APP_NAME}"
   ```

---

## ⏰ Cron Job Setup

### What are Cron Jobs?
Cron jobs are scheduled tasks that run automatically at specified time intervals on your server.

### What They Do
- Process email queues automatically
- Run background tasks without manual intervention
- Keep your application running smoothly

### How to Set Up(separate terminal)

#### 1. Edit Cron Jobs
```bash
crontab -e
```

#### 2. Add This Line
```bash
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

**Replace `/path/to/your/project` with your actual project path. For example:**
```bash
* * * * * cd /home/<USER>/sippar && php artisan schedule:run >> /dev/null 2>&1
```

#### 3. Check Cron Service
```bash
# Check if cron is running
systemctl status cron

# Start cron if needed
sudo systemctl start cron
```

#### 4. Verify Setup
```bash
# View your cron jobs
crontab -l
```

---

## 🚀 Quick Start Commands

### Setup Queue Tables
```bash
php artisan queue:table
php artisan migrate
```

### Start Queue Worker (separate terminal)
```bash
php artisan queue:work
```

---

## 🔧 Troubleshooting

### Check Queue Status
```bash
php artisan queue:monitor
```

### View Failed Jobs
```bash
php artisan queue:failed
```

### Check Logs
```bash
tail -f storage/logs/laravel.log
```

---

