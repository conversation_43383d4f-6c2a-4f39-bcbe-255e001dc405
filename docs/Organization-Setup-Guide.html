<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sippar - Organization Setup Guide</title>
    <style>
        :root {
            /* Color System - Aligned with Sippar Application */
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;

            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;

            --card-background: #ffffff;
            --card-foreground: #0f172a;

            --border: #e2e8f0;
            --border-muted: #cbd5e1;

            --success: #059669;
            --success-light: #10b981;
            --warning: #d97706;
            --warning-light: #f59e0b;
            --info: #0284c7;
            --info-light: #0ea5e9;
            --destructive: #dc2626;

            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --gradient-success: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);

            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --radius-lg: 1rem;

            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
        }

        .header {
            text-align: center;
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius-lg);
            margin-bottom: 40px;
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: bold;
        }

        .header p {
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .intro {
            background: var(--background-secondary);
            padding: 25px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            border-left: 4px solid var(--primary);
        }

        .step {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }

        .step-header {
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 15px 20px;
            border-radius: var(--radius) var(--radius) 0 0;
            margin: -25px -25px 20px -25px;
            font-weight: bold;
            font-size: 1.3em;
        }

        .step-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .step-info {
            background: var(--card-background);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }

        .step-info h4 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
            font-size: 1.1em;
        }

        .step-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .step-info li {
            margin: 8px 0;
            color: var(--foreground-muted);
        }

        .diagram {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
        }

        .flow-step {
            background: var(--primary-light);
            color: var(--primary-foreground);
            padding: 15px 20px;
            border-radius: var(--radius);
            font-weight: bold;
            text-align: center;
            min-width: 120px;
            position: relative;
        }

        .flow-step:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary);
            font-size: 1.5em;
            font-weight: bold;
        }

        .term-box {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success-light);
            padding: 15px;
            border-radius: var(--radius-sm);
            margin: 15px 0;
        }

        .term-box h5 {
            margin: 0 0 10px 0;
            color: var(--primary-dark);
            font-weight: bold;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
            border: 1px solid var(--warning-light);
            padding: 15px;
            border-radius: var(--radius-sm);
            margin: 15px 0;
        }

        .warning-box h5 {
            margin: 0 0 10px 0;
            color: var(--warning);
            font-weight: bold;
        }

        .checklist {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
            margin: 20px 0;
        }

        .checklist h4 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }

        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            border-radius: var(--radius-sm);
            background: var(--background-muted);
        }

        .checklist-item input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            accent-color: var(--primary);
        }

        .role-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .role-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 15px;
            text-align: center;
        }

        .role-card h5 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-weight: bold;
        }

        .role-card p {
            margin: 0;
            font-size: 0.9em;
            color: var(--foreground-muted);
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--primary);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary);
        }

        .timeline-content {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 15px;
        }

        .timeline-content h5 {
            margin: 0 0 10px 0;
            color: var(--primary-dark);
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 25px;
            background: var(--background-secondary);
            border-radius: var(--radius);
            border: 1px solid var(--border);
        }

        .footer p {
            margin: 5px 0;
            color: var(--foreground-muted);
        }

        @media (max-width: 768px) {
            .step-content {
                grid-template-columns: 1fr;
            }

            .flow-diagram {
                flex-direction: column;
            }

            .flow-step:not(:last-child)::after {
                content: '↓';
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sippar Organization Setup Guide</h1>
            <p>Your Complete Guide to Setting Up Petty Cash Management</p>
        </div>

        <div class="intro">
            <h2>Welcome to Sippar</h2>
            <p>This comprehensive guide will walk you through setting up your organization in Sippar, our advanced petty cash management system. By following these steps, you'll create a fully functional expense management workflow that improves transparency, reduces misuse, and streamlines your reimbursement processes.</p>
        </div>

        <div class="diagram">
            <h3>Organization Setup Overview</h3>
            <div class="flow-diagram">
                <div class="flow-step">Organization Registration</div>
                <div class="flow-step">Structure Setup</div>
                <div class="flow-step">User Management</div>
                <div class="flow-step">Workflow Configuration</div>
                <div class="flow-step">Go Live</div>
            </div>
        </div>

        <!-- Step 1: Organization Registration -->
        <div class="step">
            <div class="step-header">
                Step 1: Organization Registration & Initial Setup
            </div>

            <div class="term-box">
                <h5>What is an Organization in Sippar?</h5>
                <p>An <strong>Organization</strong> is your company's main entity in Sippar. It serves as the top-level container for all your branches, departments, users, and financial activities. Think of it as your company's digital headquarters in the system.</p>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Required Information</h4>
                    <ul>
                        <li><strong>Organization Name:</strong> Your company's legal or trading name</li>
                        <li><strong>Contact Email:</strong> Primary email for system notifications</li>
                        <li><strong>Contact Phone:</strong> Main business phone number</li>
                        <li><strong>Business Address:</strong> Physical location of your organization</li>
                        <li><strong>M-Pesa Details:</strong> Mobile money account for payments (if applicable)</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>What Happens Automatically</h4>
                    <ul>
                        <li><strong>Chart of Accounts:</strong> Standard expense categories are created</li>
                        <li><strong>Default Roles:</strong> Basic user roles are set up</li>
                        <li><strong>Organization Admin:</strong> You become the first administrator</li>
                        <li><strong>System Configuration:</strong> Basic settings are initialized</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>Important Note</h5>
                <p>Once created, your organization name and core settings should not be changed frequently as they affect all users and historical data. Choose carefully during setup.</p>
            </div>
        </div>

        <!-- Step 2: Organizational Structure -->
        <div class="step">
            <div class="step-header">
                Step 2: Building Your Organizational Structure
            </div>

            <div class="term-box">
                <h5>Understanding the Hierarchy</h5>
                <p><strong>Organization → Branches → Departments</strong><br>
                This three-level structure mirrors most business organizations and helps organize users, budgets, and approval workflows effectively.</p>
            </div>

            <h3>2.1 Setting Up Branches</h3>
            <div class="step-content">
                <div class="step-info">
                    <h4>What is a Branch?</h4>
                    <ul>
                        <li>Physical or logical divisions of your organization</li>
                        <li>Examples: Head Office, Regional Office, Warehouse</li>
                        <li>Each branch can have its own departments</li>
                        <li>Useful for multi-location businesses</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Branch Setup Requirements</h4>
                    <ul>
                        <li><strong>Branch Name:</strong> Clear, descriptive name</li>
                        <li><strong>Location:</strong> Physical address or description</li>
                        <li><strong>Contact Details:</strong> Local contact information</li>
                        <li><strong>Branch Manager:</strong> Assigned after user creation</li>
                    </ul>
                </div>
            </div>

            <h3>2.2 Creating Departments</h3>
            <div class="step-content">
                <div class="step-info">
                    <h4>What is a Department?</h4>
                    <ul>
                        <li>Functional divisions within branches</li>
                        <li>Examples: Finance, IT, Human Resources, Marketing</li>
                        <li>Each department has its own approval workflow</li>
                        <li>Budget allocation and spending tracking per department</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Department Templates Available</h4>
                    <ul>
                        <li><strong>Finance:</strong> Accounting, budgeting, financial controls</li>
                        <li><strong>IT:</strong> Technology, software, hardware</li>
                        <li><strong>HR:</strong> Human resources, recruitment, training</li>
                        <li><strong>Operations:</strong> Day-to-day business operations</li>
                        <li><strong>Marketing:</strong> Advertising, promotions, events</li>
                    </ul>
                </div>
            </div>

            <div class="diagram">
                <h4>Organizational Structure Example</h4>
                <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <div style="background: var(--primary); color: white; padding: 10px; border-radius: var(--radius-sm); margin-bottom: 10px; text-align: center;">
                        <strong>ABC Company Ltd</strong> (Organization)
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                        <div style="background: var(--primary-light); color: white; padding: 8px; border-radius: var(--radius-sm); text-align: center;">
                            <strong>Head Office</strong> (Branch)
                        </div>
                        <div style="background: var(--primary-light); color: white; padding: 8px; border-radius: var(--radius-sm); text-align: center;">
                            <strong>Regional Office</strong> (Branch)
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 5px;">
                        <div style="background: var(--info); color: white; padding: 6px; border-radius: var(--radius-sm); text-align: center; font-size: 0.9em;">Finance</div>
                        <div style="background: var(--info); color: white; padding: 6px; border-radius: var(--radius-sm); text-align: center; font-size: 0.9em;">IT</div>
                        <div style="background: var(--info); color: white; padding: 6px; border-radius: var(--radius-sm); text-align: center; font-size: 0.9em;">HR</div>
                        <div style="background: var(--info); color: white; padding: 6px; border-radius: var(--radius-sm); text-align: center; font-size: 0.9em;">Operations</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: User Management -->
        <div class="step">
            <div class="step-header">
                Step 3: User Management & Role Assignment
            </div>

            <div class="term-box">
                <h5>Understanding User Roles</h5>
                <p>Sippar uses a <strong>Role-Based Access Control (RBAC)</strong> system. Each user is assigned specific roles that determine what they can see and do in the system. This ensures security and proper workflow management.</p>
            </div>

            <div class="role-grid">
                <div class="role-card">
                    <h5>Platform Administrator</h5>
                    <p>Manages multiple organizations, system-wide settings, and platform configurations. Usually Sippar support staff.</p>
                </div>
                <div class="role-card">
                    <h5>Organization Administrator</h5>
                    <p>Full control over their organization, manages branches, departments, and users. Typically the business owner or IT manager.</p>
                </div>
                <div class="role-card">
                    <h5>Finance Manager</h5>
                    <p>Oversees financial operations, manages cash floats, handles disbursements, and generates financial reports.</p>
                </div>
                <div class="role-card">
                    <h5>Cashier</h5>
                    <p>Processes payments, manages cash floats, handles day-to-day financial transactions and reconciliations.</p>
                </div>
                <div class="role-card">
                    <h5>Head of Department (HOD)</h5>
                    <p>Approves requisitions from their department, manages department budgets and spending.</p>
                </div>
                <div class="role-card">
                    <h5>Employee</h5>
                    <p>Creates expense requisitions, uploads receipts, tracks their submission status.</p>
                </div>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>User Creation Process</h4>
                    <ul>
                        <li><strong>Basic Information:</strong> Name, email, phone number</li>
                        <li><strong>Department Assignment:</strong> Link user to their department</li>
                        <li><strong>Role Assignment:</strong> Assign appropriate roles</li>
                        <li><strong>Email Verification:</strong> User receives setup email</li>
                        <li><strong>Password Setup:</strong> User sets their own password</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Best Practices</h4>
                    <ul>
                        <li>Start with key personnel (HODs, Finance team)</li>
                        <li>Use company email addresses for all users</li>
                        <li>Assign roles based on actual job responsibilities</li>
                        <li>Test the system with a small group first</li>
                        <li>Provide training before full rollout</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>Security Reminder</h5>
                <p>Each user should have unique login credentials. Never share accounts between multiple people as this breaks the audit trail and accountability features.</p>
            </div>
        </div>

        <!-- Step 4: Chart of Accounts -->
        <div class="step">
            <div class="step-header">
                Step 4: Chart of Accounts Configuration
            </div>

            <div class="term-box">
                <h5>What is a Chart of Accounts?</h5>
                <p>A <strong>Chart of Accounts</strong> is a categorized list of expense types used to organize and track spending. Think of it as your expense filing system - every expense must be assigned to a specific account category.</p>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Default Categories Created</h4>
                    <ul>
                        <li><strong>Office Supplies:</strong> Stationery, printing, basic office needs</li>
                        <li><strong>Travel & Transport:</strong> Business travel, fuel, transport costs</li>
                        <li><strong>Meals & Entertainment:</strong> Business meals, client entertainment</li>
                        <li><strong>Utilities:</strong> Phone bills, internet, basic utilities</li>
                        <li><strong>Maintenance:</strong> Equipment repairs, facility maintenance</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Customization Options</h4>
                    <ul>
                        <li><strong>Add Categories:</strong> Create industry-specific accounts</li>
                        <li><strong>Set Spending Limits:</strong> Control maximum amounts per category</li>
                        <li><strong>Time Periods:</strong> Daily, weekly, or monthly limits</li>
                        <li><strong>Role Visibility:</strong> Control who sees which accounts</li>
                        <li><strong>Account Codes:</strong> Add accounting codes for integration</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>Important for Finance Teams</h5>
                <p>Finance Managers see all account details including codes, while regular employees see simplified views without codes. This maintains professional accounting standards while keeping the interface user-friendly.</p>
            </div>
        </div>

        <!-- Step 5: Approval Workflows -->
        <div class="step">
            <div class="step-header">
                Step 5: Approval Workflow Configuration
            </div>

            <div class="term-box">
                <h5>What is an Approval Workflow?</h5>
                <p>An <strong>Approval Workflow</strong> defines the path an expense request takes from submission to final approval. It ensures proper authorization and maintains financial controls within your organization.</p>
            </div>

            <div class="diagram">
                <h4>Typical Approval Flow</h4>
                <div class="flow-diagram">
                    <div class="flow-step">Employee Submits</div>
                    <div class="flow-step">HOD Reviews</div>
                    <div class="flow-step">Finance Approves</div>
                    <div class="flow-step">Payment Processed</div>
                </div>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Workflow Templates Available</h4>
                    <ul>
                        <li><strong>Simple Approval:</strong> Direct to Finance Manager</li>
                        <li><strong>Department Approval:</strong> HOD → Finance Manager</li>
                        <li><strong>Multi-Level:</strong> HOD → Senior Manager → Finance</li>
                        <li><strong>Amount-Based:</strong> Different flows for different amounts</li>
                        <li><strong>Emergency:</strong> Fast-track for urgent expenses</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Workflow Features</h4>
                    <ul>
                        <li><strong>Fallback Approvers:</strong> Automatic routing when approvers are unavailable</li>
                        <li><strong>Parallel Approval:</strong> Multiple approvers at the same step</li>
                        <li><strong>Conditional Routing:</strong> Different paths based on amount or category</li>
                        <li><strong>Time Limits:</strong> Automatic escalation after set periods</li>
                        <li><strong>Revision Requests:</strong> Send back for corrections</li>
                    </ul>
                </div>
            </div>

            <div class="term-box">
                <h5>Fallback Approval System</h5>
                <p>Sippar includes intelligent fallback routing. If a department doesn't have a specific approver (like Finance without an HOD), requests automatically route to other departments' approvers (like IT HOD). This ensures no request gets stuck in the system.</p>
            </div>
        </div>

        <!-- Step 6: Cash Float Management -->
        <div class="step">
            <div class="step-header">
                Step 6: Cash Float Setup & Management
            </div>

            <div class="term-box">
                <h5>What is a Cash Float?</h5>
                <p>A <strong>Cash Float</strong> is a fixed amount of money allocated to a department or individual for handling small, immediate expenses. It's like a petty cash box that needs to be tracked and reconciled regularly.</p>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Setting Up Cash Floats</h4>
                    <ul>
                        <li><strong>Float Name:</strong> Descriptive name (e.g., "IT Department Float")</li>
                        <li><strong>Initial Amount:</strong> Starting cash amount</li>
                        <li><strong>Responsible Person:</strong> Who manages the float</li>
                        <li><strong>Alert Threshold:</strong> When to warn about low balance</li>
                        <li><strong>Department Assignment:</strong> Link to specific department</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Float Management Features</h4>
                    <ul>
                        <li><strong>Real-time Balance:</strong> Always know current float status</li>
                        <li><strong>Transaction Logging:</strong> Every expense is recorded</li>
                        <li><strong>Reconciliation Reports:</strong> PDF reports for accounting</li>
                        <li><strong>Low Balance Alerts:</strong> Automatic notifications</li>
                        <li><strong>Reimbursement Tracking:</strong> When float needs topping up</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>Reconciliation Requirement</h5>
                <p>Cash floats must be reconciled regularly (daily or weekly). The system generates detailed reconciliation reports showing all transactions, running balances, and physical cash verification.</p>
            </div>
        </div>

        <!-- Step 7: Testing & Training -->
        <div class="step">
            <div class="step-header">
                Step 7: System Testing & User Training
            </div>

            <div class="checklist">
                <h4>Pre-Launch Checklist</h4>
                <div class="checklist-item">
                    <input type="checkbox" id="test1">
                    <label for="test1">Test requisition creation with sample data</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test2">
                    <label for="test2">Verify approval workflows work correctly</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test3">
                    <label for="test3">Test file upload and attachment features</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test4">
                    <label for="test4">Confirm email notifications are working</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test5">
                    <label for="test5">Test cash float transactions and reconciliation</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test6">
                    <label for="test6">Verify user roles and permissions</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test7">
                    <label for="test7">Test payment processing (if using M-Pesa)</label>
                </div>
                <div class="checklist-item">
                    <input type="checkbox" id="test8">
                    <label for="test8">Generate and review sample reports</label>
                </div>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Training Recommendations</h4>
                    <ul>
                        <li><strong>Admin Training:</strong> 2-3 hours for organization administrators</li>
                        <li><strong>Finance Training:</strong> 1-2 hours for finance team</li>
                        <li><strong>HOD Training:</strong> 1 hour for department heads</li>
                        <li><strong>User Training:</strong> 30 minutes for regular employees</li>
                        <li><strong>Documentation:</strong> Provide user guides and quick reference</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Support Resources</h4>
                    <ul>
                        <li><strong>User Manuals:</strong> Role-specific documentation</li>
                        <li><strong>Video Tutorials:</strong> Step-by-step process guides</li>
                        <li><strong>Help Desk:</strong> Technical support contact</li>
                        <li><strong>FAQ Section:</strong> Common questions and answers</li>
                        <li><strong>Training Sessions:</strong> Live or recorded training</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Step 8: Go Live -->
        <div class="step">
            <div class="step-header">
                Step 8: Going Live & Ongoing Management
            </div>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h5>Week 1: Soft Launch</h5>
                        <p>Start with a small group of users (5-10 people) to test real-world usage and identify any issues.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h5>Week 2-3: Gradual Rollout</h5>
                        <p>Add more departments and users gradually, ensuring each group is properly trained and comfortable.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h5>Week 4: Full Deployment</h5>
                        <p>All users are active, old paper-based processes are discontinued, and Sippar becomes the primary system.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h5>Month 2+: Optimization</h5>
                        <p>Review usage patterns, adjust workflows, add new features, and optimize based on user feedback.</p>
                    </div>
                </div>
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Success Metrics to Monitor</h4>
                    <ul>
                        <li><strong>User Adoption:</strong> Percentage of employees actively using the system</li>
                        <li><strong>Processing Time:</strong> Average time from submission to approval</li>
                        <li><strong>Error Reduction:</strong> Fewer missing receipts and incorrect submissions</li>
                        <li><strong>Compliance:</strong> All expenses properly categorized and approved</li>
                        <li><strong>Transparency:</strong> Clear audit trails for all transactions</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Ongoing Maintenance Tasks</h4>
                    <ul>
                        <li><strong>User Management:</strong> Add/remove users as staff changes</li>
                        <li><strong>Workflow Updates:</strong> Adjust approval processes as needed</li>
                        <li><strong>Account Management:</strong> Add new expense categories</li>
                        <li><strong>Report Reviews:</strong> Regular financial reporting and analysis</li>
                        <li><strong>System Updates:</strong> Keep software updated with latest features</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Common Terms Glossary -->
        <div class="step">
            <div class="step-header">
                Quick Reference: Key Terms & Concepts
            </div>

            <div class="role-grid">
                <div class="term-box">
                    <h5>Requisition</h5>
                    <p>A formal request for expense reimbursement, including items, amounts, and supporting documents.</p>
                </div>
                <div class="term-box">
                    <h5>Approval Workflow</h5>
                    <p>The defined path a requisition follows from submission to final approval and payment.</p>
                </div>
                <div class="term-box">
                    <h5>Chart of Accounts</h5>
                    <p>Categorized list of expense types used to organize and track different kinds of spending.</p>
                </div>
                <div class="term-box">
                    <h5>Cash Float</h5>
                    <p>A fixed amount of money allocated for immediate small expenses, tracked and reconciled regularly.</p>
                </div>
                <div class="term-box">
                    <h5>Disbursement</h5>
                    <p>The actual payment process after a requisition has been approved and is ready for payment.</p>
                </div>
                <div class="term-box">
                    <h5>Reconciliation</h5>
                    <p>The process of matching recorded transactions with actual cash or bank statements to ensure accuracy.</p>
                </div>
            </div>
        </div>

        <!-- Support Information -->
        <div class="step">
            <div class="step-header">
                Support & Next Steps
            </div>

            <div class="step-content">
                <div class="step-info">
                    <h4>Getting Help</h4>
                    <ul>
                        <li><strong>Documentation:</strong> Comprehensive user guides available in the system</li>
                        <li><strong>In-App Help:</strong> Context-sensitive help throughout the interface</li>
                        <li><strong>Email Support:</strong> Technical support team available</li>
                        <li><strong>Training Sessions:</strong> Additional training can be arranged</li>
                        <li><strong>System Updates:</strong> Regular feature updates and improvements</li>
                    </ul>
                </div>

                <div class="step-info">
                    <h4>Advanced Features (Future)</h4>
                    <ul>
                        <li><strong>Mobile App:</strong> Submit expenses on the go</li>
                        <li><strong>Bank Integration:</strong> Direct bank account payments</li>
                        <li><strong>Advanced Analytics:</strong> Spending pattern analysis</li>
                        <li><strong>Budget Management:</strong> Department budget tracking</li>
                        <li><strong>API Integration:</strong> Connect with accounting software</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>Remember</h5>
                <p>Sippar is designed to grow with your organization. Start simple, get comfortable with basic features, then gradually adopt more advanced capabilities as your team becomes proficient.</p>
            </div>
        </div>

        <div class="footer">
            <h3>Congratulations!</h3>
            <p>You're now ready to implement Sippar in your organization.</p>
            <p>This guide provides the foundation for a successful petty cash management system.</p>
            <p style="margin-top: 20px; font-weight: bold; color: var(--primary);">
                For additional support or questions, contact your Sippar implementation team.
            </p>
            <p style="margin-top: 15px; color: var(--foreground-muted); font-size: 0.9em;">
                Document Version: 1.0 |
                Sippar Petty Cash Management System
            </p>
        </div>
    </div>

    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString();

        // Add interactivity to checkboxes
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    this.parentElement.style.background = 'var(--success-light)';
                    this.parentElement.style.background = 'linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%)';
                    this.parentElement.style.color = 'var(--foreground)';
                } else {
                    this.parentElement.style.background = 'var(--background-muted)';
                    this.parentElement.style.color = 'var(--foreground)';
                }
            });
        });
    </script>
</body>
</html>