# Store Requisition Workflow Analysis

## Overview
This document provides a comprehensive analysis of how the store requisition system works for different user types, focusing on the approval workflow and access control mechanisms.

## Employee Requisition Workflow

### Process Flow
1. **Creation**: Employee creates a store requisition through the create form
2. **Status**: Requisition automatically gets `pending_approval` status when submitted
3. **Approval**: Only Store Keepers can approve employee requisitions
4. **Oversight**: Overseers can view but cannot approve employee requisitions

### Employee Capabilities
- Create store requisitions
- Edit own draft/rejected requisitions
- View own requisition history
- Browse inventory catalog
- Cannot approve any requisitions
- Cannot issue items

### Overseer Role for Employee Requisitions
**Overseers (Finance Manager/Organization Admin) can:**
- View all employee/HOD requisitions (oversight function)
- View requisition details and history
- Access requisition management interface
- **Cannot approve employee/HOD requisitions**
- **Cannot issue items to employees**

**Key Restriction**: Overseers are explicitly prevented from approving employee requisitions through multiple security layers.

## Store Keeper Requisition Workflow

### Process Flow
1. **Creation**: Store Keeper creates a requisition for store operations
2. **Status**: Requisition gets `pending_approval` status
3. **Approval**: Only Overseers (Finance Manager/Organization Admin) can approve Store Keeper requisitions
4. **Self-Restriction**: Store Keepers cannot approve their own requisitions
5. **Peer Restriction**: Store Keepers cannot approve other Store Keepers' requisitions

### Store Keeper Capabilities
- Create own requisitions (require Overseer approval)
- Approve Employee/HOD requisitions
- Issue approved items to requesters
- Manage inventory (add/edit items, stock adjustments)
- View all requisitions in organization
- **Cannot approve other Store Keeper requisitions**
- **Cannot self-approve own requisitions**

### Overseer Role for Store Keeper Requisitions
**Overseers can:**
- Approve Store Keeper requisitions ONLY
- View Store Keeper requisition details
- Provide oversight for store management activities
- **Cannot issue items (Store Keeper responsibility)**

## Security Implementation

### Backend Security Layers

#### 1. Policy Level Protection
```php
// StoreRequisitionPolicy::approve()
if ($requesterIsStoreKeeper) {
    return $user->hasRole('Finance Manager') || $user->hasRole('Organization Admin');
} else {
    if ($user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
        return false; // Explicitly prevent overseer approval of employee requisitions
    }
    return $user->can('store-keep');
}
```

#### 2. Controller Level Filtering
```php
// StoreRequisitionController::approvals()
if ($user->can('store-keep')) {
    // Store keepers see only employee requisitions
    $query->whereHas('requester', function ($q) {
        $q->whereDoesntHave('permissions', function ($permQuery) {
            $permQuery->where('name', 'store-keep');
        });
    });
} elseif ($user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
    // Overseers see only store keeper requisitions
    $query->whereHas('requester', function ($q) {
        $q->whereHas('permissions', function ($permQuery) {
            $permQuery->where('name', 'store-keep');
        });
    });
}
```

#### 3. Route Middleware Protection
- Approval routes are rate-limited (10 requests per minute)
- Permission middleware: `approve-store-requisition|store-keep`
- Throttling prevents abuse of approval endpoints

### Frontend Security Layers

#### 1. Granular Permission Checks
```typescript
const canApproveRequisition = useCallback((requisition: StoreRequisition) => {
    const requesterIsStoreKeeper = requisition.requester?.permissions?.includes('store-keep');
    
    if (requesterIsStoreKeeper) {
        return isOverseer; // Only overseers can approve store keeper requisitions
    } else {
        return isStoreKeeper; // Only store keepers can approve employee requisitions
    }
}, [user.id, isOverseer, isStoreKeeper]);
```

#### 2. UI Component Protection
- Approval buttons only shown when user has actual approval rights
- Permission wrappers control component visibility
- Separate tabs for different requisition types based on user role

## Approval Matrix

| Requester Type | Can Be Approved By | Cannot Be Approved By |
|----------------|-------------------|----------------------|
| Employee | Store Keepers | Overseers, Self |
| HOD | Store Keepers | Overseers, Self |
| Store Keeper | Overseers (Finance Manager/Org Admin) | Store Keepers, Self |
| Overseer | Store Keepers | Other Overseers, Self |

## Business Rules

1. **Separation of Duties**: Store Keepers need oversight for their own requests
2. **Hierarchical Approval**: Higher-level approvals required for store management requests
3. **No Self-Approval**: Users cannot approve their own requisitions
4. **Single Approval**: Each requisition requires only one approval (no multi-stage approval)
5. **Role-Based Filtering**: Users only see requisitions they can act upon or have oversight responsibility for

## Key Files Involved

### Backend Files
- `app/Http/Controllers/StoreRequisitionController.php` - Main controller with approval logic
- `app/Policies/StoreRequisitionPolicy.php` - Authorization policies
- `app/Models/StoreRequisition.php` - Model with status constants and business logic
- `routes/web.php` - Route definitions with middleware protection

### Frontend Files
- `resources/js/pages/StoreRequisitions/StoreRequisitionManagement.tsx` - Main management interface
- `resources/js/pages/StoreRequisitions/ShowStoreRequisition.tsx` - Individual requisition view
- `resources/js/pages/StoreRequisitions/ApprovalDialog.tsx` - Approval/rejection dialog
- `resources/js/components/PermissionWrapper.tsx` - Permission-based UI rendering
- `resources/js/hooks/use-store-requisition-approval.ts` - Approval functionality hook

### Test Files
- `tests/Feature/StoreRequisitionControllerTest.php` - Comprehensive workflow testing

## Status Lifecycle

1. **Draft** - Editable by requester only
2. **Pending Approval** - Awaiting appropriate approver action
3. **Approved** - Ready for item issuance (Store Keeper action)
4. **Rejected** - Returned to requester with reason
5. **Issued** - Items distributed and inventory updated
6. **Partially Issued** - Some items distributed, remainder pending

## Integration Points

- **Inventory System**: Stock validation and deduction
- **User Management**: Role and permission verification
- **Audit Trail**: Complete history tracking via StoreRequisitionHistory
- **Dashboard Systems**: Role-specific dashboard integration
- **Notification System**: Status change notifications (if implemented)
