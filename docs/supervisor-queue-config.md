# Supervisor Configuration for Laravel Queue Worker

## Install Supervisor
```bash
sudo apt update
sudo apt install supervisor
```

## Create Configuration File
Create `/etc/supervisor/conf.d/laravel-worker.conf`:

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /home/<USER>/sippar/artisan queue:work --tries=3 --timeout=90
directory=/home/<USER>/sippar
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=fayaz
numprocs=2
redirect_stderr=true
stdout_logfile=/home/<USER>/sippar/storage/logs/worker.log
stopwaitsecs=3600
```

## Start Supervisor
```bash
# Reload supervisor configuration
sudo supervisorctl reread
sudo supervisorctl update

# Start the workers
sudo supervisorctl start laravel-worker:*

# Check status
sudo supervisorctl status
```

## Management Commands
```bash
# Restart workers
sudo supervisorctl restart laravel-worker:*

# Stop workers
sudo supervisorctl stop laravel-worker:*

# View logs
sudo supervisorctl tail laravel-worker:laravel-worker_00
```