<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Requisition Management Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Requisition Management Domain
        </div>

        <div class="header">
            <h1>Requisition Management Domain</h1>
            <p>Core Expense Request System & Multi-Item Management</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Requisition Management Domain is the core of the Sippar expense management system. It handles the creation, validation, and lifecycle management of expense requisitions, supporting multi-item requests with detailed categorization and comprehensive approval integration.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Requisition Creation:</strong> Handle expense request creation with validation and business rules</li>
                <li><strong>Multi-Item Support:</strong> Manage multiple expense items within a single requisition</li>
                <li><strong>Status Management:</strong> Track requisition lifecycle from creation to completion</li>
                <li><strong>Approval Integration:</strong> Seamless integration with approval workflow system</li>
                <li><strong>Budget Validation:</strong> Validate expenses against chart of accounts spending limits</li>
                <li><strong>History Tracking:</strong> Maintain comprehensive audit trail of all changes</li>
                <li><strong>Attachment Support:</strong> Handle supporting documents and evidence files</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                       REQUISITION MANAGEMENT DOMAIN ERD                            │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│      REQUISITIONS       │    │    REQUISITION_ITEMS    │    │   REQUISITION_HISTORIES │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │    │ id (PK)                 │
│ requisition_number      │    │ requisition_id (FK)     │    │ requisition_id (FK)     │
│ requester_id (FK)       │    │ chart_of_account_id(FK) │    │ user_id (FK)            │
│ organization_id (FK)    │    │ description             │    │ action                  │
│ department_id (FK)      │    │ quantity                │    │ comments                │
│ branch_id (FK)          │    │ unit_price              │    │ created_at              │
│ purpose                 │    │ total_price             │    └─────────────────────────┘
│ total_amount            │    │ created_at              │              │
│ status                  │    │ updated_at              │              │
│ current_step_id (FK)    │    └─────────────────────────┘              │
│ created_at              │              │                              │
│ updated_at              │              │                              │
└─────────────────────────┘              │                              │
           │                             │                              │
           │                             │                              │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              CHART_OF_ACCOUNTS                   │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ id (PK)                                         │    │
           │    │ name                                            │    │
           │    │ spending_limit                                  │    │
           │    │ ... (other account fields)                     │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           │    │                    USERS                             │
           │    ├──────────────────────────────────────────────────────┤
           │    │ id (PK)                                              │
           │    │ first_name, last_name                                │
           │    │ organization_id (FK)                                 │
           │    │ department_id (FK)                                   │
           │    │ ... (other user fields)                             │
           │    └──────────────────────────────────────────────────────┘
           │
           │    ┌──────────────────────────────────────────────────────┐
           └────┤              APPROVAL_WORKFLOW_STEPS                 │
                ├──────────────────────────────────────────────────────┤
                │ id (PK)                                              │
                │ workflow_id (FK)                                     │
                │ step_order                                           │
                │ approver_role                                        │
                │ ... (workflow step fields)                          │
                └──────────────────────────────────────────────────────┘

STATUS FLOW:
pending → under_review → approved → rejected → returned

RELATIONSHIPS:
• Requisitions belong to Users (requester)
• Requisitions belong to Organizations, Departments, Branches
• Requisitions have many Requisition Items (1:N)
• Requisitions have many Requisition Histories (1:N)
• Requisition Items belong to Chart of Accounts (N:1)
• Requisitions have current Approval Workflow Step (N:1)
• Requisitions can have Attachments (polymorphic)
• Approved Requisitions create Transactions (1:1)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Requisition Management Domain is implemented through the RequisitionService and supporting repositories.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>RequisitionService</h4>
                    <div class="signature">createRequisition($data, $items)</div>
                    <div class="description">Creates requisition with items, validates budgets, and initiates approval workflow</div>
                    
                    <div class="signature">updateRequisition($id, $data, $items)</div>
                    <div class="description">Updates requisition and items with validation and history tracking</div>
                    
                    <div class="signature">submitForApproval($requisitionId)</div>
                    <div class="description">Submits requisition to approval workflow system</div>
                    
                    <div class="signature">getRequisitionsForUser($userId, $filters)</div>
                    <div class="description">Retrieves user's requisitions with filtering and pagination</div>
                </div>

                <div class="method-card">
                    <h4>Validation & Business Logic</h4>
                    <div class="signature">validateBudgetLimits($items)</div>
                    <div class="description">Validates all items against chart of accounts spending limits</div>
                    
                    <div class="signature">calculateTotalAmount($items)</div>
                    <div class="description">Calculates total requisition amount from all items</div>
                    
                    <div class="signature">validateRequisitionData($data)</div>
                    <div class="description">Validates requisition data against business rules</div>
                    
                    <div class="signature">checkUserPermissions($user, $requisition)</div>
                    <div class="description">Validates user permissions for requisition operations</div>
                </div>

                <div class="method-card">
                    <h4>Status Management</h4>
                    <div class="signature">updateStatus($requisitionId, $status, $comments)</div>
                    <div class="description">Updates requisition status with history tracking</div>
                    
                    <div class="signature">approveRequisition($requisitionId, $approverId)</div>
                    <div class="description">Processes requisition approval and moves to next step</div>
                    
                    <div class="signature">rejectRequisition($requisitionId, $reason)</div>
                    <div class="description">Rejects requisition with reason and notifies requester</div>
                    
                    <div class="signature">returnForRevision($requisitionId, $comments)</div>
                    <div class="description">Returns requisition to requester for modifications</div>
                </div>

                <div class="method-card">
                    <h4>Repository Methods</h4>
                    <div class="signature">findById($id)</div>
                    <div class="description">Finds requisition with all relationships loaded</div>
                    
                    <div class="signature">getAllWithFilters($filters, $perPage)</div>
                    <div class="description">Retrieves requisitions with advanced filtering</div>
                    
                    <div class="signature">getByStatus($status, $organizationId)</div>
                    <div class="description">Gets requisitions by status for organization</div>
                    
                    <div class="signature">getPendingApprovals($userId)</div>
                    <div class="description">Gets requisitions pending approval by user</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>RequisitionService</li>
                        <li>Business Rule Validation</li>
                        <li>Status Management Logic</li>
                        <li>Budget Validation</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure</h4>
                    <ul>
                        <li>EloquentRequisitionRepository</li>
                        <li>EloquentRequisitionItemRepository</li>
                        <li>EloquentRequisitionHistoryRepository</li>
                        <li>Database Transactions</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Integration</h4>
                    <ul>
                        <li>Approval Workflow Service</li>
                        <li>Chart of Accounts Service</li>
                        <li>Notification Service</li>
                        <li>Attachment Service</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Data Management</h4>
                    <ul>
                        <li>Multi-item Transactions</li>
                        <li>Audit Trail Logging</li>
                        <li>Status State Machine</li>
                        <li>Soft Deletes</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Multi-Item Support</h3>
            <p>Each requisition can contain multiple expense items, each categorized under different chart of accounts. The system validates each item against its respective spending limits and calculates the total requisition amount.</p>
            
            <h3>Comprehensive Validation</h3>
            <p>The system performs multiple validation layers:</p>
            <ul>
                <li><strong>Budget Validation:</strong> Checks spending limits for each account</li>
                <li><strong>Business Rules:</strong> Validates requisition data against organizational policies</li>
                <li><strong>User Permissions:</strong> Ensures users can only access appropriate requisitions</li>
                <li><strong>Data Integrity:</strong> Validates relationships and data consistency</li>
            </ul>
            
            <h3>Status Lifecycle Management</h3>
            <p>Requisitions follow a defined status flow: pending → under_review → approved/rejected/returned. Each status change is tracked in the history with timestamps and user information.</p>
            
            <h3>Approval Workflow Integration</h3>
            <p>Seamlessly integrates with the Approval Workflow Domain to route requisitions through appropriate approval chains based on amount, department, and organizational rules.</p>
            
            <h3>Audit Trail</h3>
            <p>Complete history tracking of all requisition changes, including creation, modifications, status changes, and approvals. This provides full transparency and accountability.</p>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
