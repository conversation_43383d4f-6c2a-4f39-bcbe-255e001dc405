<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .schema-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .schema-diagram {
            font-family: monospace;
            font-size: 11px;
            line-height: 1.3;
            white-space: pre;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .table-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .table-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .table-card h4 {
            margin: 0 0 15px 0;
            color: var(--primary);
            font-size: 1.2em;
            border-bottom: 1px solid var(--border);
            padding-bottom: 8px;
        }
        
        .table-card table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
        }
        
        .table-card th {
            background: var(--background-secondary);
            padding: 8px;
            text-align: left;
            font-weight: 600;
            color: var(--primary-dark);
            border: 1px solid var(--border);
        }
        
        .table-card td {
            padding: 6px 8px;
            border: 1px solid var(--border);
            vertical-align: top;
        }
        
        .table-card .field-type {
            font-family: monospace;
            font-size: 0.85em;
            color: var(--info);
        }
        
        .table-card .constraint {
            font-size: 0.8em;
            color: var(--warning);
            font-style: italic;
        }
        
        .relationships {
            background: var(--background-secondary);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .relationships h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .relationship-item {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 15px;
            margin: 10px 0;
        }
        
        .relationship-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Database Schema
        </div>

        <div class="header">
            <h1>Database Schema</h1>
            <p>Complete Database Structure & Relationships</p>
        </div>

        <div class="section">
            <h2>Schema Overview</h2>
            <p>The Sippar database follows a domain-driven design with clear separation of concerns. Each domain has its own set of tables with well-defined relationships. The schema supports multi-tenancy, audit trails, and scalable data organization.</p>
            
            <h3>Key Design Principles</h3>
            <ul>
                <li><strong>Domain Separation:</strong> Tables are logically grouped by business domain</li>
                <li><strong>Multi-tenancy:</strong> Organization-level data isolation</li>
                <li><strong>Audit Trails:</strong> Created/updated timestamps and user tracking</li>
                <li><strong>Soft Deletes:</strong> Data preservation with logical deletion</li>
                <li><strong>Polymorphic Relations:</strong> Flexible associations (attachments, notifications)</li>
                <li><strong>Referential Integrity:</strong> Foreign key constraints and cascading rules</li>
            </ul>
        </div>

        <div class="section">
            <h2>Complete Database Schema</h2>
            <div class="schema-container">
                <div class="schema-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                        SIPPAR DATABASE SCHEMA                                                      │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘

USER MANAGEMENT DOMAIN:
┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│         USERS           │    │         ROLES           │    │      PERMISSIONS        │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │    │ id (PK)                 │    │ id (PK)                 │
│ first_name              │    │ name                    │    │ name                    │
│ last_name               │    │ guard_name              │    │ guard_name              │
│ email (UNIQUE)          │    │ created_at              │    │ created_at              │
│ email_verified_at       │    │ updated_at              │    │ updated_at              │
│ password                │    └─────────────────────────┘    └─────────────────────────┘
│ phone_number            │
│ avatar                  │    ┌─────────────────────────┐    ┌─────────────────────────┐
│ is_platform_admin       │    │    MODEL_HAS_ROLES      │    │ ROLE_HAS_PERMISSIONS    │
│ organization_id (FK)    │    ├─────────────────────────┤    ├─────────────────────────┤
│ department_id (FK)      │    │ role_id (FK)            │    │ permission_id (FK)      │
│ branch_id (FK)          │    │ model_type              │    │ role_id (FK)            │
│ email_verification_code │    │ model_id (FK)           │    └─────────────────────────┘
│ created_at              │    └─────────────────────────┘
│ updated_at              │
└─────────────────────────┘

ORGANIZATION MANAGEMENT DOMAIN:
┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│      ORGANIZATIONS      │    │        BRANCHES         │    │      DEPARTMENTS        │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │◄───┤ id (PK)                 │
│ name                    │    │ name                    │    │ name                    │
│ contact_email           │    │ organization_id (FK)    │    │ organization_id (FK)    │
│ contact_phone           │    │ location                │    │ branch_id (FK)          │
│ address                 │    │ contact_email           │    │ description             │
│ mpesa_account_name      │    │ contact_phone           │    │ head_of_department_id   │
│ mpesa_phone_number      │    │ address                 │    │ budget_limit            │
│ status                  │    │ status                  │    │ status                  │
│ created_at              │    │ created_at              │    │ created_at              │
│ updated_at              │    │ updated_at              │    │ updated_at              │
└─────────────────────────┘    └─────────────────────────┘    └─────────────────────────┘

                               ┌─────────────────────────┐
                               │  DEPARTMENT_TEMPLATES   │
                               ├─────────────────────────┤
                               │ id (PK)                 │
                               │ name                    │
                               │ description             │
                               │ category                │
                               │ default_budget_limit    │
                               │ is_active               │
                               │ created_at              │
                               │ updated_at              │
                               └─────────────────────────┘

CHART OF ACCOUNTS DOMAIN:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                            CHART_OF_ACCOUNTS                                       │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ id (PK)                                                                             │
│ organization_id (FK) [nullable]                                                    │
│ branch_id (FK) [nullable]                                                          │
│ parent_id (FK) [self-referencing]                                                  │
│ name                                                                                │
│ code [nullable]                                                                    │
│ description                                                                         │
│ account_type (enum: 'expense', 'income', 'asset', 'liability')                     │
│ spending_limit [nullable]                                                          │
│ limit_period (enum: 'daily', 'weekly', 'monthly', 'yearly') [nullable]            │
│ is_active                                                                           │
│ created_at                                                                          │
│ updated_at                                                                          │
└─────────────────────────────────────────────────────────────────────────────────────┘

REQUISITION MANAGEMENT DOMAIN:
┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│      REQUISITIONS       │    │    REQUISITION_ITEMS    │    │ REQUISITION_HISTORIES   │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │    │ id (PK)                 │
│ requisition_number      │    │ requisition_id (FK)     │    │ requisition_id (FK)     │
│ requester_id (FK)       │    │ chart_of_account_id(FK) │    │ user_id (FK)            │
│ organization_id (FK)    │    │ description             │    │ action                  │
│ department_id (FK)      │    │ quantity                │    │ comments                │
│ branch_id (FK)          │    │ unit_price              │    │ created_at              │
│ purpose                 │    │ total_price             │    └─────────────────────────┘
│ total_amount            │    │ created_at              │
│ status                  │    │ updated_at              │
│ current_step_id (FK)    │    └─────────────────────────┘
│ created_at              │
│ updated_at              │
└─────────────────────────┘

APPROVAL WORKFLOW DOMAIN:
┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│   APPROVAL_WORKFLOWS    │    │ APPROVAL_WORKFLOW_STEPS │    │   APPROVAL_HISTORIES    │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │    │ id (PK)                 │
│ organization_id (FK)    │    │ workflow_id (FK)        │    │ requisition_id (FK)     │
│ name                    │    │ step_order              │    │ workflow_step_id (FK)   │
│ description             │    │ step_name               │    │ approver_id (FK)        │
│ is_active               │    │ approver_role           │    │ action                  │
│ min_amount              │    │ fallback_role           │    │ comments                │
│ max_amount              │    │ requires_all_approvers  │    │ step_description        │
│ department_id (FK)      │    │ is_final_step           │    │ created_at              │
│ created_at              │    │ created_at              │    └─────────────────────────┘
│ updated_at              │    │ updated_at              │
└─────────────────────────┘    └─────────────────────────┘

                               ┌─────────────────────────┐
                               │   WORKFLOW_TEMPLATES    │
                               ├─────────────────────────┤
                               │ id (PK)                 │
                               │ name                    │
                               │ category                │
                               │ description             │
                               │ template_data (JSON)    │
                               │ is_active               │
                               │ organization_id (FK)    │
                               │ created_by (FK)         │
                               │ version                 │
                               │ usage_count             │
                               │ created_at              │
                               │ updated_at              │
                               └─────────────────────────┘
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Tables</h2>
            <div class="table-grid">
                <div class="table-card">
                    <h4>users</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Constraints</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">PK, AUTO_INCREMENT</td>
                            </tr>
                            <tr>
                                <td>first_name</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">NOT NULL</td>
                            </tr>
                            <tr>
                                <td>last_name</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">NOT NULL</td>
                            </tr>
                            <tr>
                                <td>email</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">UNIQUE, NOT NULL</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">NOT NULL</td>
                            </tr>
                            <tr>
                                <td>organization_id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">FK → organizations.id</td>
                            </tr>
                            <tr>
                                <td>department_id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">FK → departments.id</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="table-card">
                    <h4>organizations</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Constraints</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">PK, AUTO_INCREMENT</td>
                            </tr>
                            <tr>
                                <td>name</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">NOT NULL</td>
                            </tr>
                            <tr>
                                <td>contact_email</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">NULLABLE</td>
                            </tr>
                            <tr>
                                <td>mpesa_account_name</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">NULLABLE</td>
                            </tr>
                            <tr>
                                <td>status</td>
                                <td class="field-type">enum</td>
                                <td class="constraint">'active', 'inactive'</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="table-card">
                    <h4>requisitions</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Constraints</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">PK, AUTO_INCREMENT</td>
                            </tr>
                            <tr>
                                <td>requisition_number</td>
                                <td class="field-type">varchar(255)</td>
                                <td class="constraint">UNIQUE, NOT NULL</td>
                            </tr>
                            <tr>
                                <td>requester_id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">FK → users.id</td>
                            </tr>
                            <tr>
                                <td>total_amount</td>
                                <td class="field-type">decimal(10,2)</td>
                                <td class="constraint">NOT NULL</td>
                            </tr>
                            <tr>
                                <td>status</td>
                                <td class="field-type">enum</td>
                                <td class="constraint">'pending', 'approved', 'rejected'</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="table-card">
                    <h4>transactions</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Constraints</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">PK, AUTO_INCREMENT</td>
                            </tr>
                            <tr>
                                <td>requisition_id</td>
                                <td class="field-type">bigint unsigned</td>
                                <td class="constraint">FK → requisitions.id</td>
                            </tr>
                            <tr>
                                <td>payment_method</td>
                                <td class="field-type">enum</td>
                                <td class="constraint">'mpesa', 'bank', 'cash'</td>
                            </tr>
                            <tr>
                                <td>total_amount</td>
                                <td class="field-type">decimal(10,2)</td>
                                <td class="constraint">NOT NULL</td>
                            </tr>
                            <tr>
                                <td>status</td>
                                <td class="field-type">enum</td>
                                <td class="constraint">'opened', 'updated', 'completed'</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Database Technology Stack</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Database Engine</h4>
                    <ul>
                        <li>SQLite (Development)</li>
                        <li>MySQL (Production)</li>
                        <li>Laravel Migrations</li>
                        <li>Database Seeders</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>ORM & Queries</h4>
                    <ul>
                        <li>Eloquent ORM</li>
                        <li>Query Builder</li>
                        <li>Relationship Loading</li>
                        <li>Database Transactions</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Data Integrity</h4>
                    <ul>
                        <li>Foreign Key Constraints</li>
                        <li>Unique Constraints</li>
                        <li>Enum Validations</li>
                        <li>Soft Deletes</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Performance</h4>
                    <ul>
                        <li>Database Indexing</li>
                        <li>Query Optimization</li>
                        <li>Eager Loading</li>
                        <li>Connection Pooling</li>
                    </ul>
                </div>
            </div>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
