<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Reference - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .endpoint-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .endpoint-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .endpoint-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .method-badge {
            padding: 4px 12px;
            border-radius: var(--radius-sm);
            font-weight: 600;
            font-size: 0.85em;
            text-transform: uppercase;
        }
        
        .method-get {
            background: #e3f2fd;
            color: #1565c0;
        }
        
        .method-post {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .method-put {
            background: #fff3e0;
            color: #ef6c00;
        }
        
        .method-delete {
            background: #ffebee;
            color: #c62828;
        }
        
        .endpoint-url {
            font-family: monospace;
            font-size: 1.1em;
            color: var(--primary-dark);
            font-weight: 500;
        }
        
        .endpoint-description {
            color: var(--foreground-muted);
            margin-bottom: 15px;
        }
        
        .params-section {
            margin: 15px 0;
        }
        
        .params-section h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .params-table th {
            background: var(--background-secondary);
            padding: 8px 12px;
            text-align: left;
            font-weight: 600;
            color: var(--primary-dark);
            border: 1px solid var(--border);
        }
        
        .params-table td {
            padding: 8px 12px;
            border: 1px solid var(--border);
            vertical-align: top;
        }
        
        .param-name {
            font-family: monospace;
            font-weight: 600;
            color: var(--primary);
        }
        
        .param-type {
            font-family: monospace;
            font-size: 0.85em;
            color: var(--info);
        }
        
        .param-required {
            color: var(--warning);
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .response-section {
            background: var(--background-secondary);
            padding: 15px;
            border-radius: var(--radius-sm);
            margin: 15px 0;
        }
        
        .response-section h4 {
            margin: 0 0 10px 0;
            color: var(--primary-dark);
        }
        
        .response-code {
            font-family: monospace;
            background: var(--card-background);
            padding: 10px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
            font-size: 0.9em;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .auth-section {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .auth-section h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > API Reference
        </div>

        <div class="header">
            <h1>API Reference</h1>
            <p>Complete REST API Documentation for Sippar Platform</p>
        </div>

        <div class="section">
            <h2>API Overview</h2>
            <p>Sippar provides a comprehensive REST API built with Laravel and Inertia.js. The API follows RESTful conventions and uses Laravel Sanctum for authentication. All endpoints return JSON responses and support standard HTTP status codes.</p>
            
            <h3>Base URL</h3>
            <p><code>https://your-domain.com/api</code> (for API endpoints)</p>
            <p><code>https://your-domain.com</code> (for web routes with Inertia.js)</p>
            
            <h3>Content Type</h3>
            <p>All requests should include: <code>Content-Type: application/json</code></p>
            <p>File uploads should use: <code>Content-Type: multipart/form-data</code></p>
        </div>

        <div class="auth-section">
            <h3>Authentication</h3>
            <p>Sippar uses Laravel Sanctum for API authentication. Include the authentication token in the Authorization header:</p>
            <div class="response-code">Authorization: Bearer {your-api-token}</div>
            <p><strong>Login Endpoint:</strong> <code>POST /login</code></p>
            <p><strong>Logout Endpoint:</strong> <code>POST /logout</code></p>
        </div>

        <div class="section">
            <h2>Requisition Management API</h2>
            <div class="endpoint-grid">
                <div class="endpoint-card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/requisitions</span>
                    </div>
                    <div class="endpoint-description">Retrieve paginated list of requisitions with filtering options</div>
                    
                    <div class="params-section">
                        <h4>Query Parameters</h4>
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="param-name">page</td>
                                    <td class="param-type">integer</td>
                                    <td>No</td>
                                    <td>Page number for pagination</td>
                                </tr>
                                <tr>
                                    <td class="param-name">status</td>
                                    <td class="param-type">string</td>
                                    <td>No</td>
                                    <td>Filter by status: pending, approved, rejected</td>
                                </tr>
                                <tr>
                                    <td class="param-name">search</td>
                                    <td class="param-type">string</td>
                                    <td>No</td>
                                    <td>Search in requisition number or purpose</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="response-section">
                        <h4>Response (200 OK)</h4>
                        <div class="response-code">{
  "data": [
    {
      "id": 1,
      "requisition_number": "REQ-2024-001",
      "purpose": "Office supplies",
      "total_amount": "150.00",
      "status": "pending",
      "requester": {
        "id": 1,
        "first_name": "John",
        "last_name": "Doe"
      },
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "total": 25,
    "per_page": 15
  }
}</div>
                    </div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-header">
                        <span class="method-badge method-post">POST</span>
                        <span class="endpoint-url">/requisitions</span>
                    </div>
                    <div class="endpoint-description">Create a new requisition with multiple items</div>
                    
                    <div class="params-section">
                        <h4>Request Body</h4>
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="param-name">purpose</td>
                                    <td class="param-type">string</td>
                                    <td class="param-required">Required</td>
                                    <td>Purpose of the requisition</td>
                                </tr>
                                <tr>
                                    <td class="param-name">items</td>
                                    <td class="param-type">array</td>
                                    <td class="param-required">Required</td>
                                    <td>Array of requisition items</td>
                                </tr>
                                <tr>
                                    <td class="param-name">items.*.chart_of_account_id</td>
                                    <td class="param-type">integer</td>
                                    <td class="param-required">Required</td>
                                    <td>Chart of account ID for categorization</td>
                                </tr>
                                <tr>
                                    <td class="param-name">items.*.description</td>
                                    <td class="param-type">string</td>
                                    <td class="param-required">Required</td>
                                    <td>Item description</td>
                                </tr>
                                <tr>
                                    <td class="param-name">items.*.quantity</td>
                                    <td class="param-type">integer</td>
                                    <td class="param-required">Required</td>
                                    <td>Item quantity</td>
                                </tr>
                                <tr>
                                    <td class="param-name">items.*.unit_price</td>
                                    <td class="param-type">decimal</td>
                                    <td class="param-required">Required</td>
                                    <td>Price per unit</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="response-section">
                        <h4>Response (201 Created)</h4>
                        <div class="response-code">{
  "message": "Requisition created successfully",
  "data": {
    "id": 1,
    "requisition_number": "REQ-2024-001",
    "purpose": "Office supplies",
    "total_amount": "150.00",
    "status": "pending",
    "items": [
      {
        "id": 1,
        "description": "Printer paper",
        "quantity": 5,
        "unit_price": "30.00",
        "total_price": "150.00"
      }
    ]
  }
}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Transaction Management API</h2>
            <div class="endpoint-grid">
                <div class="endpoint-card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/transactions</span>
                    </div>
                    <div class="endpoint-description">Retrieve paginated list of transactions</div>
                    
                    <div class="response-section">
                        <h4>Response (200 OK)</h4>
                        <div class="response-code">{
  "data": [
    {
      "id": 1,
      "requisition_id": 1,
      "payment_method": "mpesa",
      "total_amount": "150.00",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}</div>
                    </div>
                </div>

                <div class="endpoint-card">
                    <div class="endpoint-header">
                        <span class="method-badge method-put">PUT</span>
                        <span class="endpoint-url">/transactions/{id}</span>
                    </div>
                    <div class="endpoint-description">Update transaction payment details</div>
                    
                    <div class="params-section">
                        <h4>Request Body</h4>
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="param-name">payment_method</td>
                                    <td class="param-type">string</td>
                                    <td class="param-required">Required</td>
                                    <td>Payment method: mpesa, bank, cash</td>
                                </tr>
                                <tr>
                                    <td class="param-name">account_details</td>
                                    <td class="param-type">string</td>
                                    <td>No</td>
                                    <td>Account details for bank/mpesa payments</td>
                                </tr>
                                <tr>
                                    <td class="param-name">description</td>
                                    <td class="param-type">string</td>
                                    <td>No</td>
                                    <td>Additional transaction description</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Chart of Accounts API</h2>
            <div class="endpoint-grid">
                <div class="endpoint-card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/chart-of-accounts</span>
                    </div>
                    <div class="endpoint-description">Retrieve hierarchical chart of accounts with role-based visibility</div>
                    
                    <div class="response-section">
                        <h4>Response (200 OK)</h4>
                        <div class="response-code">{
  "data": [
    {
      "id": 1,
      "name": "Office Supplies",
      "description": "General office supplies and stationery",
      "account_type": "expense",
      "spending_limit": "1000.00",
      "children": [
        {
          "id": 2,
          "name": "Stationery",
          "parent_id": 1
        }
      ]
    }
  ]
}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>File Upload API</h2>
            <div class="endpoint-grid">
                <div class="endpoint-card">
                    <div class="endpoint-header">
                        <span class="method-badge method-post">POST</span>
                        <span class="endpoint-url">/attachments</span>
                    </div>
                    <div class="endpoint-description">Upload file attachment to any entity</div>
                    
                    <div class="params-section">
                        <h4>Request Body (multipart/form-data)</h4>
                        <table class="params-table">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="param-name">file</td>
                                    <td class="param-type">file</td>
                                    <td class="param-required">Required</td>
                                    <td>File to upload (max 10MB)</td>
                                </tr>
                                <tr>
                                    <td class="param-name">attachable_type</td>
                                    <td class="param-type">string</td>
                                    <td class="param-required">Required</td>
                                    <td>Model type: requisition, transaction, etc.</td>
                                </tr>
                                <tr>
                                    <td class="param-name">attachable_id</td>
                                    <td class="param-type">integer</td>
                                    <td class="param-required">Required</td>
                                    <td>ID of the model to attach to</td>
                                </tr>
                                <tr>
                                    <td class="param-name">description</td>
                                    <td class="param-type">string</td>
                                    <td>No</td>
                                    <td>File description</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Error Responses</h2>
            <p>The API uses standard HTTP status codes and returns error details in JSON format:</p>
            
            <div class="response-section">
                <h4>Validation Error (422 Unprocessable Entity)</h4>
                <div class="response-code">{
  "message": "The given data was invalid.",
  "errors": {
    "purpose": ["The purpose field is required."],
    "items.0.quantity": ["The quantity must be at least 1."]
  }
}</div>
            </div>
            
            <div class="response-section">
                <h4>Authentication Error (401 Unauthorized)</h4>
                <div class="response-code">{
  "message": "Unauthenticated."
}</div>
            </div>
            
            <div class="response-section">
                <h4>Authorization Error (403 Forbidden)</h4>
                <div class="response-code">{
  "message": "This action is unauthorized."
}</div>
            </div>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
