<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow Template Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Workflow Template Domain
        </div>

        <div class="header">
            <h1>Workflow Template Domain</h1>
            <p>Pre-configured Workflow Templates & Quick Setup System</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Workflow Template Domain provides pre-configured workflow templates for quick setup of approval processes. It enables organizations to rapidly deploy standard approval workflows while maintaining the flexibility to customize them according to their specific needs.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Template Management:</strong> Create and manage pre-configured workflow templates</li>
                <li><strong>Category Organization:</strong> Organize templates by business categories and use cases</li>
                <li><strong>Quick Setup:</strong> Enable rapid deployment of standard approval workflows</li>
                <li><strong>Template Preview:</strong> Provide preview functionality for template evaluation</li>
                <li><strong>Customization Support:</strong> Allow template modification during deployment</li>
                <li><strong>Best Practices:</strong> Embed industry best practices in template designs</li>
                <li><strong>Template Versioning:</strong> Manage template versions and updates</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                        WORKFLOW TEMPLATE DOMAIN ERD                                │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                            WORKFLOW_TEMPLATES                                      │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ id (PK)                                                                             │
│ name                                                                                │
│ description                                                                         │
│ category (enum: 'basic', 'finance', 'procurement', 'hr', 'custom')                 │
│ template_data (JSON) [workflow structure and steps]                                │
│ is_active (boolean)                                                                 │
│ is_system_template (boolean) [platform vs organization templates]                  │
│ organization_id (FK) [nullable - for organization-specific templates]              │
│ created_by (FK) [user who created the template]                                    │
│ version                                                                             │
│ usage_count [how many times template has been used]                                │
│ created_at                                                                          │
│ updated_at                                                                          │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │ (creates workflows from templates)
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           TEMPLATE USAGE TRACKING                                  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ TEMPLATE CATEGORIES:                                                                │
│                                                                                     │
│ ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐     │
│ │       BASIC         │    │      FINANCE        │    │    PROCUREMENT      │     │
│ ├─────────────────────┤    ├─────────────────────┤    ├─────────────────────┤     │
│ │ • Simple Approval   │    │ • Two-tier Finance  │    │ • Vendor Approval   │     │
│ │ • Department Head   │    │ • Budget Approval   │    │ • Purchase Orders   │     │
│ │ • Manager Approval  │    │ • Expense Review    │    │ • Contract Review   │     │
│ └─────────────────────┘    └─────────────────────┘    └─────────────────────┘     │
│                                                                                     │
│ ┌─────────────────────┐    ┌─────────────────────┐                                │
│ │        HR           │    │       CUSTOM        │                                │
│ ├─────────────────────┤    ├─────────────────────┤                                │
│ │ • Leave Approval    │    │ • Organization      │                                │
│ │ • Training Requests │    │   Specific          │                                │
│ │ • Equipment Issue   │    │ • Industry Specific │                                │
│ └─────────────────────┘    └─────────────────────┘                                │
│                                                                                     │
│ TEMPLATE DATA STRUCTURE (JSON):                                                    │
│ {                                                                                   │
│   "name": "Two-tier Approval",                                                     │
│   "description": "Department Head → Finance Manager",                              │
│   "min_amount": 0,                                                                 │
│   "max_amount": 10000,                                                             │
│   "steps": [                                                                       │
│     {                                                                               │
│       "step_order": 1,                                                             │
│       "step_name": "Department Approval",                                          │
│       "approver_role": "Department Head",                                          │
│       "fallback_role": "IT Head",                                                  │
│       "is_final_step": false                                                       │
│     },                                                                              │
│     {                                                                               │
│       "step_order": 2,                                                             │
│       "step_name": "Finance Approval",                                             │
│       "approver_role": "Finance Manager",                                          │
│       "fallback_role": "Organization Admin",                                       │
│       "is_final_step": true                                                        │
│     }                                                                               │
│   ]                                                                                 │
│ }                                                                                   │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘

RELATIONSHIPS:
• Workflow Templates can be system-wide or organization-specific
• Templates are used to create Approval Workflows
• Templates track usage statistics
• Templates can be versioned and updated
• Organizations can create custom templates based on system templates
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Workflow Template Domain is implemented through the WorkflowTemplateService and EloquentWorkflowTemplateRepository.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>WorkflowTemplateService</h4>
                    <div class="signature">getTemplatesByCategory($category)</div>
                    <div class="description">Retrieves all active templates for a specific category</div>
                    
                    <div class="signature">getTemplate($templateId)</div>
                    <div class="description">Gets detailed template information including structure</div>
                    
                    <div class="signature">createWorkflowFromTemplate($templateId, $organizationId, $customizations)</div>
                    <div class="description">Creates actual workflow from template with optional customizations</div>
                    
                    <div class="signature">previewTemplate($templateId)</div>
                    <div class="description">Generates preview of workflow steps and structure</div>
                </div>

                <div class="method-card">
                    <h4>Template Management</h4>
                    <div class="signature">createTemplate($data)</div>
                    <div class="description">Creates new workflow template with validation</div>
                    
                    <div class="signature">updateTemplate($templateId, $data)</div>
                    <div class="description">Updates existing template and increments version</div>
                    
                    <div class="signature">cloneTemplate($templateId, $organizationId)</div>
                    <div class="description">Creates organization-specific copy of system template</div>
                    
                    <div class="signature">validateTemplateStructure($templateData)</div>
                    <div class="description">Validates template JSON structure and business rules</div>
                </div>

                <div class="method-card">
                    <h4>Repository Methods</h4>
                    <div class="signature">getAllActive()</div>
                    <div class="description">Returns all active workflow templates</div>
                    
                    <div class="signature">getByCategory($category)</div>
                    <div class="description">Gets templates filtered by category</div>
                    
                    <div class="signature">getSystemTemplates()</div>
                    <div class="description">Returns platform-level system templates</div>
                    
                    <div class="signature">getOrganizationTemplates($organizationId)</div>
                    <div class="description">Gets organization-specific templates</div>
                </div>

                <div class="method-card">
                    <h4>Template Analytics</h4>
                    <div class="signature">getUsageStatistics($templateId)</div>
                    <div class="description">Returns usage statistics for template</div>
                    
                    <div class="signature">getMostUsedTemplates($limit)</div>
                    <div class="description">Gets most popular templates by usage</div>
                    
                    <div class="signature">trackTemplateUsage($templateId, $organizationId)</div>
                    <div class="description">Records template usage for analytics</div>
                    
                    <div class="signature">generateUsageReport($dateRange)</div>
                    <div class="description">Generates comprehensive template usage report</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>WorkflowTemplateService</li>
                        <li>Template Validation Logic</li>
                        <li>Workflow Generation</li>
                        <li>Business Rule Enforcement</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure</h4>
                    <ul>
                        <li>EloquentWorkflowTemplateRepository</li>
                        <li>JSON Template Storage</li>
                        <li>Template Caching</li>
                        <li>Version Management</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Data Structure</h4>
                    <ul>
                        <li>JSON Template Schema</li>
                        <li>Category Enumeration</li>
                        <li>Version Tracking</li>
                        <li>Usage Analytics</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Integration</h4>
                    <ul>
                        <li>Approval Workflow Service</li>
                        <li>Organization Management</li>
                        <li>User Management</li>
                        <li>Template Preview System</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Pre-configured Templates</h3>
            <p>The system includes several pre-built templates covering common business scenarios:</p>
            <ul>
                <li><strong>Basic Approval:</strong> Simple single-step approval for small expenses</li>
                <li><strong>Two-tier Approval:</strong> Department Head → Finance Manager</li>
                <li><strong>Three-tier Approval:</strong> Department Head → Finance Manager → Organization Admin</li>
                <li><strong>Finance-specific:</strong> Specialized workflows for finance department expenses</li>
                <li><strong>High-value Approval:</strong> Multi-level approval for large expenses</li>
            </ul>
            
            <h3>Category Organization</h3>
            <p>Templates are organized into logical categories:</p>
            <ul>
                <li><strong>Basic:</strong> Simple, general-purpose workflows</li>
                <li><strong>Finance:</strong> Finance department specific workflows</li>
                <li><strong>Procurement:</strong> Vendor and purchase-related workflows</li>
                <li><strong>HR:</strong> Human resources specific workflows</li>
                <li><strong>Custom:</strong> Organization-specific custom workflows</li>
            </ul>
            
            <h3>Template Customization</h3>
            <p>Organizations can customize templates during deployment:</p>
            <ul>
                <li><strong>Amount Thresholds:</strong> Adjust minimum and maximum amounts</li>
                <li><strong>Role Mapping:</strong> Map template roles to organization roles</li>
                <li><strong>Step Modification:</strong> Add, remove, or modify approval steps</li>
                <li><strong>Fallback Rules:</strong> Customize fallback approval routing</li>
            </ul>
            
            <h3>Template Preview</h3>
            <p>Interactive preview system allows organizations to:</p>
            <ul>
                <li>Visualize workflow steps and flow</li>
                <li>Understand approval routing logic</li>
                <li>Preview with sample data</li>
                <li>Test different scenarios before deployment</li>
            </ul>
            
            <h3>Usage Analytics</h3>
            <p>Comprehensive analytics track template effectiveness:</p>
            <ul>
                <li><strong>Usage Statistics:</strong> Track how often templates are used</li>
                <li><strong>Performance Metrics:</strong> Measure approval times and success rates</li>
                <li><strong>Popular Templates:</strong> Identify most effective templates</li>
                <li><strong>Improvement Insights:</strong> Data-driven template optimization</li>
            </ul>
            
            <h3>System vs Organization Templates</h3>
            <p>Two-tier template system provides flexibility:</p>
            <ul>
                <li><strong>System Templates:</strong> Platform-level templates available to all organizations</li>
                <li><strong>Organization Templates:</strong> Custom templates created by specific organizations</li>
                <li><strong>Template Inheritance:</strong> Organizations can clone and modify system templates</li>
                <li><strong>Best Practices:</strong> System templates embed industry best practices</li>
            </ul>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
