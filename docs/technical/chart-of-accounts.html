<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart of Accounts Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Chart of Accounts Domain
        </div>

        <div class="header">
            <h1>Chart of Accounts Domain</h1>
            <p>Hierarchical Expense Categorization & Budget Control System</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Chart of Accounts Domain provides a comprehensive expense categorization system with hierarchical account structures, spending controls, and role-based visibility. It serves as the foundation for all financial tracking and budget management within the Sippar platform.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Account Hierarchy:</strong> Manage parent-child relationships in account structures</li>
                <li><strong>Spending Controls:</strong> Implement spending limits and budget tracking per account</li>
                <li><strong>Role-based Visibility:</strong> Control account visibility based on user roles</li>
                <li><strong>Multi-tenant Support:</strong> Platform-level and organization-specific accounts</li>
                <li><strong>Budget Management:</strong> Track spending against allocated budgets</li>
                <li><strong>Integration Support:</strong> Provide account codes for external accounting systems</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                         CHART OF ACCOUNTS DOMAIN ERD                               │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                            CHART_OF_ACCOUNTS                                       │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ id (PK)                                                                             │
│ organization_id (FK) [nullable - for platform-level accounts]                      │
│ branch_id (FK) [nullable - for branch-specific accounts]                           │
│ parent_id (FK) [self-referencing for hierarchy]                                    │
│ name                                                                                │
│ code [nullable - for accounting system integration]                                │
│ description                                                                         │
│ account_type (enum: 'expense', 'income', 'asset', 'liability')                     │
│ spending_limit [nullable - maximum spending allowed]                               │
│ limit_period (enum: 'daily', 'weekly', 'monthly', 'yearly') [nullable]            │
│ is_active (boolean)                                                                 │
│ created_at                                                                          │
│ updated_at                                                                          │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │ (self-referencing)
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              RELATIONSHIPS                                         │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ HIERARCHICAL STRUCTURE:                                                             │
│ • Platform Level Accounts (organization_id = NULL)                                 │
│   ├── Office Supplies                                                              │
│   ├── Travel & Transport                                                           │
│   ├── Meals & Entertainment                                                        │
│   ├── Utilities                                                                    │
│   └── Maintenance                                                                  │
│                                                                                     │
│ ORGANIZATION-SPECIFIC ACCOUNTS:                                                     │
│ • Custom accounts created by organizations                                         │
│ • Can inherit from platform accounts or be completely custom                       │
│                                                                                     │
│ RELATIONSHIPS WITH OTHER DOMAINS:                                                   │
│ • Organizations (1:N) - accounts belong to organizations                           │
│ • Branches (1:N) - accounts can be branch-specific                                 │
│ • Requisition Items (1:N) - items are categorized by accounts                      │
│ • Transaction Items (1:N) - transaction items reference accounts                   │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘

ACCOUNT HIERARCHY EXAMPLE:
┌─ Office Supplies (Platform Level)
│  ├─ Stationery
│  ├─ Printing Materials
│  └─ Office Equipment
├─ Travel & Transport (Platform Level)
│  ├─ Local Transport
│  ├─ Accommodation
│  └─ Meals During Travel
└─ Custom Marketing (Organization Specific)
   ├─ Digital Advertising
   ├─ Print Materials
   └─ Event Sponsorship

ROLE-BASED VISIBILITY:
• Employees: See only descendant accounts (no codes)
• Finance Managers: See all accounts with codes
• Organization Admins: Full access to organization accounts
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Chart of Accounts Domain is implemented through the ChartOfAccountService and EloquentChartOfAccountRepository.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>ChartOfAccountService</h4>
                    <div class="signature">getAllWithFilters($filters, $perPage)</div>
                    <div class="description">Retrieves accounts with filtering, pagination, and role-based visibility</div>
                    
                    <div class="signature">getByOrganizationId($orgId, $filters, $perPage)</div>
                    <div class="description">Gets accounts for a specific organization with inheritance</div>
                    
                    <div class="signature">getAccountsForUser($user)</div>
                    <div class="description">Returns accounts visible to user based on their role</div>
                    
                    <div class="signature">createAccount($data)</div>
                    <div class="description">Creates new account with validation and hierarchy checks</div>
                </div>

                <div class="method-card">
                    <h4>Repository Methods</h4>
                    <div class="signature">findById($id)</div>
                    <div class="description">Finds account by ID with relationships loaded</div>
                    
                    <div class="signature">getHierarchy($parentId = null)</div>
                    <div class="description">Returns hierarchical account structure</div>
                    
                    <div class="signature">getDescendants($accountId)</div>
                    <div class="description">Gets all child accounts recursively</div>
                    
                    <div class="signature">validateSpendingLimit($accountId, $amount)</div>
                    <div class="description">Checks if spending amount is within account limits</div>
                </div>

                <div class="method-card">
                    <h4>Budget & Spending Control</h4>
                    <div class="signature">checkSpendingLimit($accountId, $amount, $period)</div>
                    <div class="description">Validates spending against account limits</div>
                    
                    <div class="signature">getCurrentSpending($accountId, $period)</div>
                    <div class="description">Calculates current spending for the period</div>
                    
                    <div class="signature">getRemainingBudget($accountId)</div>
                    <div class="description">Returns remaining budget for the account</div>
                    
                    <div class="signature">generateSpendingReport($accountId, $dateRange)</div>
                    <div class="description">Generates spending analysis report</div>
                </div>

                <div class="method-card">
                    <h4>Role-based Access</h4>
                    <div class="signature">getAccountsForRole($role, $organizationId)</div>
                    <div class="description">Returns accounts visible to specific role</div>
                    
                    <div class="signature">filterAccountsByVisibility($accounts, $user)</div>
                    <div class="description">Filters accounts based on user permissions</div>
                    
                    <div class="signature">hideAccountCodes($accounts)</div>
                    <div class="description">Removes account codes for non-finance users</div>
                    
                    <div class="signature">canUserAccessAccount($user, $accountId)</div>
                    <div class="description">Checks if user can access specific account</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>ChartOfAccountService</li>
                        <li>Spending Validation Logic</li>
                        <li>Hierarchy Management</li>
                        <li>Budget Calculations</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure</h4>
                    <ul>
                        <li>EloquentChartOfAccountRepository</li>
                        <li>Database Relationships</li>
                        <li>Query Optimization</li>
                        <li>Caching Strategy</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Data Structure</h4>
                    <ul>
                        <li>Self-referencing Hierarchy</li>
                        <li>Multi-tenant Scoping</li>
                        <li>Enum Constraints</li>
                        <li>Soft Deletes</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Business Rules</h4>
                    <ul>
                        <li>Spending Limit Validation</li>
                        <li>Role-based Visibility</li>
                        <li>Hierarchy Constraints</li>
                        <li>Account Code Uniqueness</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Hierarchical Account Structure</h3>
            <p>Supports unlimited levels of account hierarchy with parent-child relationships. Platform-level accounts serve as templates, while organizations can create custom accounts or inherit from platform accounts.</p>
            
            <h3>Spending Controls</h3>
            <p>Each account can have spending limits with configurable time periods (daily, weekly, monthly, yearly). The system validates all expenses against these limits before approval.</p>
            
            <h3>Role-based Visibility</h3>
            <p>Different user roles see different views of the chart of accounts:</p>
            <ul>
                <li><strong>Employees:</strong> See only descendant accounts without codes</li>
                <li><strong>Finance Managers:</strong> See all accounts with full details including codes</li>
                <li><strong>Organization Admins:</strong> Full access to organization-specific accounts</li>
            </ul>
            
            <h3>Multi-tenant Support</h3>
            <p>Platform-level accounts are shared across all organizations, while organization-specific accounts provide customization. Branch-specific accounts allow for location-based categorization.</p>
            
            <h3>Integration Ready</h3>
            <p>Account codes support integration with external accounting systems. The flexible structure accommodates various accounting standards and practices.</p>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
