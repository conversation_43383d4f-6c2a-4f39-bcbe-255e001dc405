<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attachment Management Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Attachment Management Domain
        </div>

        <div class="header">
            <h1>Attachment Management Domain</h1>
            <p>Secure File Upload, Storage & Management System</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Attachment Management Domain provides a comprehensive file management system with secure upload, storage, and retrieval capabilities. It uses polymorphic relationships to attach files to any entity in the system and provides robust file operations with proper security controls.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>File Upload:</strong> Secure file upload with validation and size limits</li>
                <li><strong>Storage Management:</strong> Organized file storage with proper directory structure</li>
                <li><strong>Polymorphic Relations:</strong> Attach files to any model (requisitions, transactions, etc.)</li>
                <li><strong>File Operations:</strong> Download, view, delete, and manage file operations</li>
                <li><strong>Security:</strong> Access control and permission-based file access</li>
                <li><strong>Evidence Tracking:</strong> Special handling for evidence and supporting documents</li>
                <li><strong>Metadata Management:</strong> File metadata, descriptions, and categorization</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                       ATTACHMENT MANAGEMENT DOMAIN ERD                             │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              ATTACHMENTS                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ id (PK)                                                                             │
│ attachable_type (polymorphic - model class name)                                   │
│ attachable_id (polymorphic - model ID)                                             │
│ uploader_id (FK) [references users.id]                                             │
│ original_name                                                                       │
│ file_name                                                                           │
│ file_path                                                                           │
│ file_size                                                                           │
│ mime_type                                                                           │
│ file_extension                                                                      │
│ description [nullable]                                                              │
│ is_evidence (boolean) [for evidence documents]                                     │
│ uploaded_at_step [workflow step when uploaded]                                     │
│ created_at                                                                          │
│ updated_at                                                                          │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        │ (polymorphic relationships)
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           POLYMORPHIC RELATIONSHIPS                                │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ ATTACHABLE MODELS:                                                                  │
│                                                                                     │
│ ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐     │
│ │    REQUISITIONS     │    │    TRANSACTIONS     │    │       USERS         │     │
│ ├─────────────────────┤    ├─────────────────────┤    ├─────────────────────┤     │
│ │ id (PK)             │    │ id (PK)             │    │ id (PK)             │     │
│ │ requisition_number  │    │ transaction_type    │    │ first_name          │     │
│ │ status              │    │ status              │    │ last_name           │     │
│ │ total_amount        │    │ total_amount        │    │ email               │     │
│ │ ... (other fields)  │    │ ... (other fields)  │    │ ... (other fields)  │     │
│ └─────────────────────┘    └─────────────────────┘    └─────────────────────┘     │
│           │                          │                          │                 │
│           └──────────────────────────┼──────────────────────────┘                 │
│                                      │                                            │
│ ┌─────────────────────┐              │              ┌─────────────────────┐       │
│ │   CASH_FLOATS       │              │              │   ORGANIZATIONS     │       │
│ ├─────────────────────┤              │              ├─────────────────────┤       │
│ │ id (PK)             │              │              │ id (PK)             │       │
│ │ name                │              │              │ name                │       │
│ │ current_balance     │              │              │ contact_email       │       │
│ │ ... (other fields)  │              │              │ ... (other fields)  │       │
│ └─────────────────────┘              │              └─────────────────────┘       │
│           │                          │                          │                 │
│           └──────────────────────────┼──────────────────────────┘                 │
│                                      │                                            │
│                                      ▼                                            │
│                            ┌─────────────────────┐                               │
│                            │       USERS         │                               │
│                            ├─────────────────────┤                               │
│                            │ id (PK)             │                               │
│                            │ first_name          │                               │
│                            │ last_name           │                               │
│                            │ email               │                               │
│                            │ ... (uploader info) │                               │
│                            └─────────────────────┘                               │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘

FILE ORGANIZATION:
storage/app/attachments/
├── requisitions/
│   ├── 2024/01/
│   └── 2024/02/
├── transactions/
│   ├── 2024/01/
│   └── 2024/02/
├── users/
└── organizations/

SUPPORTED FILE TYPES:
• Documents: PDF, DOC, DOCX, TXT
• Images: JPG, JPEG, PNG, GIF
• Spreadsheets: XLS, XLSX, CSV
• Archives: ZIP, RAR

SECURITY FEATURES:
• File type validation
• Size limit enforcement
• Access permission checks
• Secure file paths
• Virus scanning (configurable)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Attachment Management Domain is implemented through the AttachmentService and EloquentAttachmentRepository.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>AttachmentService</h4>
                    <div class="signature">uploadFile($file, $attachable, $description, $isEvidence)</div>
                    <div class="description">Uploads file and creates attachment record with validation</div>
                    
                    <div class="signature">downloadFile($attachmentId, $user)</div>
                    <div class="description">Downloads file with permission checks and access logging</div>
                    
                    <div class="signature">deleteAttachment($attachmentId, $user)</div>
                    <div class="description">Deletes attachment file and database record with permissions</div>
                    
                    <div class="signature">getAttachments($attachable, $withUploader)</div>
                    <div class="description">Retrieves all attachments for an entity</div>
                </div>

                <div class="method-card">
                    <h4>File Operations</h4>
                    <div class="signature">validateFile($file)</div>
                    <div class="description">Validates file type, size, and security constraints</div>
                    
                    <div class="signature">generateSecureFileName($originalName)</div>
                    <div class="description">Generates secure, unique filename for storage</div>
                    
                    <div class="signature">getStoragePath($attachable, $fileName)</div>
                    <div class="description">Determines organized storage path for file</div>
                    
                    <div class="signature">moveToStorage($file, $path)</div>
                    <div class="description">Moves uploaded file to secure storage location</div>
                </div>

                <div class="method-card">
                    <h4>Repository Methods</h4>
                    <div class="signature">create($data)</div>
                    <div class="description">Creates new attachment record in database</div>
                    
                    <div class="signature">findById($id)</div>
                    <div class="description">Finds attachment by ID with relationships</div>
                    
                    <div class="signature">getByAttachable($attachable)</div>
                    <div class="description">Gets all attachments for specific model</div>
                    
                    <div class="signature">getEvidenceByAttachable($attachable)</div>
                    <div class="description">Gets only evidence attachments for model</div>
                </div>

                <div class="method-card">
                    <h4>Security & Permissions</h4>
                    <div class="signature">canUserAccess($user, $attachment)</div>
                    <div class="description">Checks if user can access specific attachment</div>
                    
                    <div class="signature">canUserDelete($user, $attachment)</div>
                    <div class="description">Checks if user can delete specific attachment</div>
                    
                    <div class="signature">logFileAccess($attachment, $user, $action)</div>
                    <div class="description">Logs file access for audit trail</div>
                    
                    <div class="signature">scanForViruses($filePath)</div>
                    <div class="description">Scans uploaded file for malware (if configured)</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>AttachmentService</li>
                        <li>File Validation Logic</li>
                        <li>Security Controls</li>
                        <li>Permission Management</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure</h4>
                    <ul>
                        <li>EloquentAttachmentRepository</li>
                        <li>Laravel File Storage</li>
                        <li>Polymorphic Relationships</li>
                        <li>File System Operations</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Storage</h4>
                    <ul>
                        <li>Local File Storage</li>
                        <li>Organized Directory Structure</li>
                        <li>Secure File Paths</li>
                        <li>File Metadata Storage</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Security</h4>
                    <ul>
                        <li>File Type Validation</li>
                        <li>Size Limit Enforcement</li>
                        <li>Access Control</li>
                        <li>Audit Logging</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Polymorphic Attachment System</h3>
            <p>Files can be attached to any model in the system using Laravel's polymorphic relationships. This provides maximum flexibility while maintaining data integrity and consistent file management across all entities.</p>
            
            <h3>Organized File Storage</h3>
            <p>Files are organized in a hierarchical directory structure:</p>
            <ul>
                <li><strong>By Entity Type:</strong> requisitions/, transactions/, users/</li>
                <li><strong>By Date:</strong> Year/Month subdirectories for easy management</li>
                <li><strong>Secure Naming:</strong> UUID-based filenames prevent conflicts and enhance security</li>
            </ul>
            
            <h3>Comprehensive File Validation</h3>
            <p>Robust validation system includes:</p>
            <ul>
                <li><strong>File Type Validation:</strong> Whitelist of allowed file extensions and MIME types</li>
                <li><strong>Size Limits:</strong> Configurable maximum file sizes</li>
                <li><strong>Security Scanning:</strong> Optional virus scanning integration</li>
                <li><strong>Content Validation:</strong> Verification that file content matches extension</li>
            </ul>
            
            <h3>Evidence Document Support</h3>
            <p>Special handling for evidence documents with:</p>
            <ul>
                <li>Evidence flag for important supporting documents</li>
                <li>Workflow step tracking for audit purposes</li>
                <li>Enhanced security for critical files</li>
                <li>Separate retrieval methods for evidence files</li>
            </ul>
            
            <h3>Access Control & Security</h3>
            <p>Comprehensive security features:</p>
            <ul>
                <li><strong>Permission-based Access:</strong> Users can only access files they're authorized to see</li>
                <li><strong>Audit Logging:</strong> All file operations are logged for security auditing</li>
                <li><strong>Secure Downloads:</strong> Files are served through application with permission checks</li>
                <li><strong>Upload Validation:</strong> Multiple layers of validation prevent malicious uploads</li>
            </ul>
            
            <h3>Metadata Management</h3>
            <p>Rich metadata support including:</p>
            <ul>
                <li>Original filename preservation</li>
                <li>File size and type tracking</li>
                <li>Upload timestamp and user tracking</li>
                <li>Optional descriptions and categorization</li>
                <li>Workflow context (step when uploaded)</li>
            </ul>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
