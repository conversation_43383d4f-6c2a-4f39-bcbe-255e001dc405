<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .relationships {
            background: var(--background-secondary);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .relationships h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .relationship-item {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 15px;
            margin: 10px 0;
        }
        
        .relationship-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > User Management Domain
        </div>

        <div class="header">
            <h1>User Management Domain</h1>
            <p>Authentication, Authorization & User Profile Management</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The User Management Domain is responsible for handling all aspects of user authentication, authorization, role-based access control, and user profile management across the Sippar platform. It provides a secure foundation for all other domains to build upon.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Authentication:</strong> User login, logout, password management, and session handling</li>
                <li><strong>Authorization:</strong> Role-based access control (RBAC) and permission management</li>
                <li><strong>User Management:</strong> User creation, profile updates, and account management</li>
                <li><strong>Email Verification:</strong> Secure email verification with custom codes</li>
                <li><strong>Multi-tenant Support:</strong> User association with organizations and departments</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              USER MANAGEMENT DOMAIN ERD                            │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│         USERS           │    │         ROLES           │    │      PERMISSIONS        │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │    │ id (PK)                 │    │ id (PK)                 │
│ first_name              │    │ name                    │    │ name                    │
│ last_name               │    │ guard_name              │    │ guard_name              │
│ email (UNIQUE)          │    │ created_at              │    │ created_at              │
│ email_verified_at       │    │ updated_at              │    │ updated_at              │
│ password                │    └─────────────────────────┘    └─────────────────────────┘
│ phone_number            │              │                              │
│ avatar                  │              │                              │
│ is_platform_admin       │              │                              │
│ organization_id (FK)    │              │                              │
│ department_id (FK)      │              │                              │
│ branch_id (FK)          │              │                              │
│ email_verification_code │              │                              │
│ created_at              │              │                              │
│ updated_at              │              │                              │
└─────────────────────────┘              │                              │
           │                             │                              │
           │                             │                              │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              MODEL_HAS_ROLES                     │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ role_id (FK)                                    │    │
           │    │ model_type                                      │    │
           │    │ model_id (FK)                                   │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           │    │              ROLE_HAS_PERMISSIONS                    │
           │    ├──────────────────────────────────────────────────────┤
           │    │ permission_id (FK)                                   │
           │    │ role_id (FK)                                         │
           │    └──────────────────────────────────────────────────────┘
           │
           │    ┌─────────────────────────────────────────────────────────┐
           └────│              MODEL_HAS_PERMISSIONS                     │
                ├─────────────────────────────────────────────────────────┤
                │ permission_id (FK)                                      │
                │ model_type                                              │
                │ model_id (FK)                                           │
                └─────────────────────────────────────────────────────────┘

RELATIONSHIPS:
• Users belong to Organizations (organization_id)
• Users belong to Departments (department_id)  
• Users belong to Branches (branch_id)
• Users have many Roles (many-to-many via model_has_roles)
• Roles have many Permissions (many-to-many via role_has_permissions)
• Users can have direct Permissions (many-to-many via model_has_permissions)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The User Management Domain doesn't have a dedicated service class as it primarily uses Laravel's built-in authentication and Spatie's permission system. However, user management is handled through various controllers and middleware.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>Authentication Methods</h4>
                    <div class="signature">Auth::attempt($credentials)</div>
                    <div class="description">Authenticates user with email and password</div>
                    
                    <div class="signature">Auth::logout()</div>
                    <div class="description">Logs out the current user and invalidates session</div>
                    
                    <div class="signature">Auth::user()</div>
                    <div class="description">Returns the currently authenticated user</div>
                </div>

                <div class="method-card">
                    <h4>User Management</h4>
                    <div class="signature">User::create($data)</div>
                    <div class="description">Creates a new user with validation</div>
                    
                    <div class="signature">$user->update($data)</div>
                    <div class="description">Updates user profile information</div>
                    
                    <div class="signature">$user->delete()</div>
                    <div class="description">Soft deletes a user account</div>
                </div>

                <div class="method-card">
                    <h4>Role & Permission Methods</h4>
                    <div class="signature">$user->assignRole($role)</div>
                    <div class="description">Assigns a role to the user</div>
                    
                    <div class="signature">$user->hasRole($role)</div>
                    <div class="description">Checks if user has a specific role</div>
                    
                    <div class="signature">$user->hasPermissionTo($permission)</div>
                    <div class="description">Checks if user has a specific permission</div>
                </div>

                <div class="method-card">
                    <h4>Email Verification</h4>
                    <div class="signature">$user->sendEmailVerificationNotification()</div>
                    <div class="description">Sends email verification with custom code</div>
                    
                    <div class="signature">$user->markEmailAsVerified()</div>
                    <div class="description">Marks user email as verified</div>
                    
                    <div class="signature">$user->hasVerifiedEmail()</div>
                    <div class="description">Checks if user email is verified</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Authentication</h4>
                    <ul>
                        <li>Laravel Sanctum</li>
                        <li>Session-based Auth</li>
                        <li>CSRF Protection</li>
                        <li>Password Hashing</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Authorization</h4>
                    <ul>
                        <li>Spatie Laravel Permission</li>
                        <li>Role-based Access Control</li>
                        <li>Permission Gates</li>
                        <li>Middleware Protection</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>User Model</h4>
                    <ul>
                        <li>Eloquent ORM</li>
                        <li>Soft Deletes</li>
                        <li>Accessors/Mutators</li>
                        <li>Model Relationships</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Validation</h4>
                    <ul>
                        <li>Form Request Validation</li>
                        <li>Custom Validation Rules</li>
                        <li>Email Uniqueness</li>
                        <li>Password Complexity</li>
                    </ul>
                </div>
            </div>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
