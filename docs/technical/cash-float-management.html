<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash Float Management Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Cash Float Management Domain
        </div>

        <div class="header">
            <h1>Cash Float Management Domain</h1>
            <p>Petty Cash Float Tracking & Reconciliation System</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Cash Float Management Domain handles petty cash floats with real-time balance tracking, transaction logging, reconciliation processes, and automated reporting. It provides comprehensive cash management capabilities for organizations managing multiple cash floats across different departments and locations.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Float Management:</strong> Create and manage multiple cash floats per organization</li>
                <li><strong>Balance Tracking:</strong> Real-time tracking of float balances and transactions</li>
                <li><strong>Transaction Logging:</strong> Detailed logging of all cash float transactions</li>
                <li><strong>Reconciliation:</strong> Automated reconciliation processes and variance reporting</li>
                <li><strong>Alert System:</strong> Low balance alerts and threshold monitoring</li>
                <li><strong>Reporting:</strong> Comprehensive reporting and analytics for cash flow management</li>
                <li><strong>Integration:</strong> Seamless integration with disbursement and transaction systems</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                       CASH FLOAT MANAGEMENT DOMAIN ERD                             │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│       CASH_FLOATS       │    │      TRANSACTIONS       │    │   CASH_FLOAT_REPORTS    │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │    │ id (PK)                 │
│ organization_id (FK)    │    │ cash_float_id (FK)      │    │ cash_float_id (FK)      │
│ department_id (FK)      │    │ requisition_id (FK)     │    │ report_type             │
│ branch_id (FK)          │    │ transaction_type        │    │ period_start            │
│ user_id (FK)            │    │ status                  │    │ period_end              │
│ name                    │    │ payment_method          │    │ opening_balance         │
│ description             │    │ total_amount            │    │ closing_balance         │
│ initial_amount          │    │ transaction_cost        │    │ total_inflows           │
│ current_balance         │    │ disbursement_trans_id   │    │ total_outflows          │
│ alert_threshold         │    │ account_details         │    │ variance_amount         │
│ status                  │    │ description             │    │ reconciliation_status   │
│ created_at              │    │ created_by (FK)         │    │ generated_at            │
│ updated_at              │    │ updated_by (FK)         │    │ generated_by (FK)       │
└─────────────────────────┘    │ created_at              │    └─────────────────────────┘
           │                   │ updated_at              │              │
           │                   └─────────────────────────┘              │
           │                             │                              │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              CASH_FLOAT_TRANSACTIONS              │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ id (PK)                                         │    │
           │    │ cash_float_id (FK)                              │    │
           │    │ transaction_id (FK)                             │    │
           │    │ transaction_type (enum: 'inflow', 'outflow')    │    │
           │    │ amount                                          │    │
           │    │ balance_before                                  │    │
           │    │ balance_after                                   │    │
           │    │ description                                     │    │
           │    │ reference_number                                │    │
           │    │ created_by (FK)                                 │    │
           │    │ created_at                                      │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           └────┤                 ORGANIZATIONS                        │
                ├──────────────────────────────────────────────────────┤
                │ id (PK)                                              │
                │ name                                                 │
                │ ... (other organization fields)                     │
                └──────────────────────────────────────────────────────┘

CASH FLOAT LIFECYCLE:
┌─ Float Creation
├─ Initial Funding (Inflow)
├─ Disbursements (Outflows)
├─ Replenishments (Inflows)
├─ Reconciliation
└─ Reporting

TRANSACTION TYPES:
• Inflows: Initial funding, replenishments, returns
• Outflows: Disbursements, expenses, transfers
• Balance adjustments: Reconciliation corrections

RELATIONSHIPS:
• Cash Floats belong to Organizations (N:1)
• Cash Floats belong to Departments (N:1)
• Cash Floats belong to Branches (N:1)
• Cash Floats are managed by Users (N:1)
• Cash Floats have many Transactions (1:N)
• Cash Floats have many Cash Float Transactions (1:N)
• Cash Floats have many Reports (1:N)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>Cash Float Management is primarily handled through Laravel controllers and models with business logic for balance calculations and reconciliation.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>Float Management</h4>
                    <div class="signature">CashFloat::create($data)</div>
                    <div class="description">Creates new cash float with initial balance and settings</div>
                    
                    <div class="signature">$float->updateBalance($amount, $type)</div>
                    <div class="description">Updates float balance for inflow/outflow transactions</div>
                    
                    <div class="signature">$float->checkAlertThreshold()</div>
                    <div class="description">Checks if balance is below alert threshold</div>
                    
                    <div class="signature">$float->deactivate($reason)</div>
                    <div class="description">Deactivates float and handles remaining balance</div>
                </div>

                <div class="method-card">
                    <h4>Transaction Processing</h4>
                    <div class="signature">$float->recordTransaction($transaction)</div>
                    <div class="description">Records transaction and updates float balance</div>
                    
                    <div class="signature">$float->processInflow($amount, $description)</div>
                    <div class="description">Processes cash inflow (funding, replenishment)</div>
                    
                    <div class="signature">$float->processOutflow($amount, $description)</div>
                    <div class="description">Processes cash outflow (disbursement, expense)</div>
                    
                    <div class="signature">$float->validateSufficientFunds($amount)</div>
                    <div class="description">Validates sufficient balance for outflow transactions</div>
                </div>

                <div class="method-card">
                    <h4>Reconciliation</h4>
                    <div class="signature">$float->performReconciliation($physicalCount)</div>
                    <div class="description">Reconciles system balance with physical cash count</div>
                    
                    <div class="signature">$float->calculateVariance($physicalCount)</div>
                    <div class="description">Calculates variance between system and physical balance</div>
                    
                    <div class="signature">$float->adjustBalance($amount, $reason)</div>
                    <div class="description">Adjusts balance for reconciliation corrections</div>
                    
                    <div class="signature">$float->generateReconciliationReport($period)</div>
                    <div class="description">Generates detailed reconciliation report</div>
                </div>

                <div class="method-card">
                    <h4>Reporting & Analytics</h4>
                    <div class="signature">$float->getTransactionHistory($dateRange)</div>
                    <div class="description">Retrieves transaction history for specified period</div>
                    
                    <div class="signature">$float->generateCashFlowReport($period)</div>
                    <div class="description">Generates cash flow analysis report</div>
                    
                    <div class="signature">$float->getBalanceTrend($period)</div>
                    <div class="description">Returns balance trend data for analytics</div>
                    
                    <div class="signature">CashFloat::getOrganizationSummary($orgId)</div>
                    <div class="description">Provides summary of all floats for organization</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Models</h4>
                    <ul>
                        <li>CashFloat Model</li>
                        <li>CashFloatTransaction Model</li>
                        <li>CashFloatReport Model</li>
                        <li>Eloquent Relationships</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Balance Management</h4>
                    <ul>
                        <li>Real-time Balance Updates</li>
                        <li>Transaction Logging</li>
                        <li>Atomic Operations</li>
                        <li>Concurrency Control</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Reconciliation</h4>
                    <ul>
                        <li>Variance Calculations</li>
                        <li>Automated Reconciliation</li>
                        <li>Adjustment Tracking</li>
                        <li>Audit Trail</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Reporting</h4>
                    <ul>
                        <li>Cash Flow Reports</li>
                        <li>Balance Trend Analysis</li>
                        <li>Reconciliation Reports</li>
                        <li>Alert Notifications</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Real-time Balance Tracking</h3>
            <p>Every transaction automatically updates the cash float balance with complete audit trail. The system maintains balance_before and balance_after for each transaction, ensuring data integrity and enabling detailed reconciliation.</p>
            
            <h3>Alert System</h3>
            <p>Configurable alert thresholds notify managers when cash float balances fall below specified levels. This enables proactive cash management and prevents operational disruptions.</p>
            
            <h3>Multi-level Organization</h3>
            <p>Cash floats can be organized by:</p>
            <ul>
                <li><strong>Organization:</strong> Multiple floats per organization</li>
                <li><strong>Department:</strong> Department-specific cash management</li>
                <li><strong>Branch:</strong> Location-based float allocation</li>
                <li><strong>User:</strong> Individual float responsibility assignment</li>
            </ul>
            
            <h3>Comprehensive Reconciliation</h3>
            <p>Built-in reconciliation processes compare system balances with physical cash counts, automatically calculate variances, and generate detailed reconciliation reports with adjustment tracking.</p>
            
            <h3>Integration with Disbursement System</h3>
            <p>Seamless integration with the disbursement domain allows transactions to be funded from cash floats, with automatic balance updates and transaction logging.</p>
            
            <h3>Detailed Reporting</h3>
            <p>Comprehensive reporting includes:</p>
            <ul>
                <li>Cash flow analysis</li>
                <li>Balance trend reports</li>
                <li>Transaction summaries</li>
                <li>Reconciliation reports</li>
                <li>Variance analysis</li>
            </ul>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
