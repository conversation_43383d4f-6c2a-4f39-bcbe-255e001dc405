<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sippar - Technical Documentation</title>
    <style>
        :root {
            /* Color System - Aligned with Sippar Application */
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            
            --card-background: #ffffff;
            --card-foreground: #0f172a;
            
            --border: #e2e8f0;
            --border-muted: #cbd5e1;
            
            --success: #059669;
            --success-light: #10b981;
            --warning: #d97706;
            --warning-light: #f59e0b;
            --info: #0284c7;
            --info-light: #0ea5e9;
            --destructive: #dc2626;
            
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --gradient-success: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
            
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --radius-lg: 1rem;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            text-align: center;
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 40px;
            border-radius: var(--radius-lg);
            margin-bottom: 40px;
        }
        
        .header h1 {
            margin: 0 0 15px 0;
            font-size: 3em;
            font-weight: bold;
        }
        
        .header p {
            margin: 0;
            font-size: 1.3em;
            opacity: 0.9;
        }
        
        .intro {
            background: var(--background-secondary);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 40px;
            border-left: 4px solid var(--primary);
        }
        
        .intro h2 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .architecture-overview {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 30px;
            margin: 30px 0;
        }
        
        .architecture-overview h3 {
            margin: 0 0 20px 0;
            color: var(--primary);
            text-align: center;
        }
        
        .domains-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        
        .domain-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
        }
        
        .domain-card:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }
        
        .domain-card h3 {
            margin: 0 0 15px 0;
            color: var(--primary);
            font-size: 1.4em;
        }
        
        .domain-card p {
            margin: 0 0 20px 0;
            color: var(--foreground-muted);
        }
        
        .domain-card .tech-stack {
            background: var(--background-muted);
            padding: 15px;
            border-radius: var(--radius-sm);
            margin: 15px 0;
        }
        
        .domain-card .tech-stack h4 {
            margin: 0 0 10px 0;
            color: var(--primary-dark);
            font-size: 1em;
        }
        
        .domain-card .tech-stack ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .domain-card .tech-stack li {
            margin: 5px 0;
            font-size: 0.9em;
            color: var(--foreground-muted);
        }
        
        .domain-card a {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        
        .domain-card a:hover {
            background: var(--primary-dark);
        }
        
        .tech-overview {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success-light);
            padding: 30px;
            border-radius: var(--radius);
            margin: 30px 0;
        }
        
        .tech-overview h3 {
            margin: 0 0 20px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
            color: var(--foreground-muted);
        }
        
        .navigation {
            background: var(--background-secondary);
            padding: 25px;
            border-radius: var(--radius);
            margin: 30px 0;
        }
        
        .navigation h3 {
            margin: 0 0 20px 0;
            color: var(--primary-dark);
        }
        
        .nav-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .nav-links a {
            display: block;
            background: var(--card-background);
            color: var(--foreground);
            padding: 15px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: var(--primary);
            color: var(--primary-foreground);
            border-color: var(--primary);
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: var(--background-secondary);
            border-radius: var(--radius);
            border: 1px solid var(--border);
        }
        
        .footer p {
            margin: 5px 0;
            color: var(--foreground-muted);
        }
        
        @media (max-width: 768px) {
            .domains-grid {
                grid-template-columns: 1fr;
            }
            
            .tech-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sippar Technical Documentation</h1>
            <p>Comprehensive Technical Reference for Domain-Driven Architecture</p>
        </div>

        <div class="intro">
            <h2>Architecture Overview</h2>
            <p>Sippar follows <strong>Domain-Driven Design (DDD)</strong> principles with a clean, layered architecture. Each domain encapsulates its business logic, data access, and application services, ensuring maintainability, testability, and scalability.</p>
        </div>

        <div class="architecture-overview">
            <h3>System Architecture Layers</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Presentation Layer</h4>
                    <ul>
                        <li>React 18 + TypeScript</li>
                        <li>Inertia.js for SPA behavior</li>
                        <li>TailwindCSS for styling</li>
                        <li>Laravel Controllers</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Application Layer</h4>
                    <ul>
                        <li>Application Services</li>
                        <li>Use Case Orchestration</li>
                        <li>Cross-Domain Coordination</li>
                        <li>Event Handling</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>Business Logic</li>
                        <li>Domain Models</li>
                        <li>Domain Services</li>
                        <li>Business Rules</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure Layer</h4>
                    <ul>
                        <li>Repository Implementations</li>
                        <li>Database Access (Eloquent)</li>
                        <li>External Integrations</li>
                        <li>File Storage</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tech-overview">
            <h3>Technology Stack</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Backend Framework</h4>
                    <ul>
                        <li>Laravel 11</li>
                        <li>PHP 8.2+</li>
                        <li>Eloquent ORM</li>
                        <li>Laravel Sanctum</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Frontend Framework</h4>
                    <ul>
                        <li>React 18</li>
                        <li>TypeScript</li>
                        <li>Inertia.js</li>
                        <li>Vite Build Tool</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Database & Storage</h4>
                    <ul>
                        <li>SQLite/MySQL</li>
                        <li>File System Storage</li>
                        <li>Database Migrations</li>
                        <li>Model Factories</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Additional Tools</h4>
                    <ul>
                        <li>Spatie Permissions</li>
                        <li>Laravel Events</li>
                        <li>PDF Generation</li>
                        <li>Email Notifications</li>
                    </ul>
                </div>
            </div>
        </div>

        <h2 style="text-align: center; color: var(--primary); margin: 40px 0 30px 0;">Domain Documentation</h2>

        <div class="domains-grid">
            <div class="domain-card">
                <h3>User Management Domain</h3>
                <p>Handles user authentication, authorization, role-based access control, and user profile management across the entire platform.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>User Authentication Service</li>
                        <li>Role & Permission Management</li>
                        <li>Profile Management</li>
                        <li>Email Verification</li>
                    </ul>
                </div>
                <a href="user-management.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Organization Management Domain</h3>
                <p>Manages organizational structure including organizations, branches, departments, and their hierarchical relationships.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Organization Service</li>
                        <li>Branch Management</li>
                        <li>Department Hierarchy</li>
                        <li>Multi-tenant Support</li>
                    </ul>
                </div>
                <a href="organization-management.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Chart of Accounts Domain</h3>
                <p>Provides hierarchical expense categorization system with spending controls, role-based visibility, and budget management.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Account Hierarchy Service</li>
                        <li>Spending Limit Controls</li>
                        <li>Role-based Visibility</li>
                        <li>Budget Tracking</li>
                    </ul>
                </div>
                <a href="chart-of-accounts.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Requisition Management Domain</h3>
                <p>Core expense request system handling creation, validation, approval routing, and status management of expense requisitions.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Requisition Service</li>
                        <li>Multi-item Support</li>
                        <li>Approval Integration</li>
                        <li>Status Tracking</li>
                    </ul>
                </div>
                <a href="requisition-management.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Approval Workflow Domain</h3>
                <p>Dynamic approval workflow engine with templates, multi-step processes, fallback mechanisms, and department-specific routing.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Workflow Engine</li>
                        <li>Template System</li>
                        <li>Approval Routing</li>
                        <li>Fallback Mechanisms</li>
                    </ul>
                </div>
                <a href="approval-workflow.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Cash Float Management Domain</h3>
                <p>Manages petty cash floats with real-time balance tracking, transaction logging, reconciliation, and automated reporting.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Float Management Service</li>
                        <li>Transaction Logging</li>
                        <li>Balance Tracking</li>
                        <li>Reconciliation Reports</li>
                    </ul>
                </div>
                <a href="cash-float-management.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Disbursement Domain</h3>
                <p>Handles transaction processing, payment method management, disbursement workflows, and integration with payment systems.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Transaction Service</li>
                        <li>Payment Processing</li>
                        <li>M-Pesa Integration</li>
                        <li>Disbursement Tracking</li>
                    </ul>
                </div>
                <a href="disbursement.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Attachment Management Domain</h3>
                <p>Secure file upload, storage, and management system with polymorphic relationships and comprehensive file operations.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>File Upload Service</li>
                        <li>Secure Storage</li>
                        <li>Polymorphic Relations</li>
                        <li>File Operations</li>
                    </ul>
                </div>
                <a href="attachment-management.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Notification Domain</h3>
                <p>Comprehensive notification system supporting email, in-app notifications, and workflow-based communication routing.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Notification Service</li>
                        <li>Email Integration</li>
                        <li>In-app Notifications</li>
                        <li>Event-driven Messaging</li>
                    </ul>
                </div>
                <a href="notification.html">View Documentation</a>
            </div>

            <div class="domain-card">
                <h3>Workflow Template Domain</h3>
                <p>Pre-configured workflow templates for quick setup, categorized templates, and workflow configuration management.</p>
                <div class="tech-stack">
                    <h4>Key Components</h4>
                    <ul>
                        <li>Template Service</li>
                        <li>Category Management</li>
                        <li>Quick Setup</li>
                        <li>Template Preview</li>
                    </ul>
                </div>
                <a href="workflow-template.html">View Documentation</a>
            </div>
        </div>

        <div class="navigation">
            <h3>Quick Navigation</h3>
            <div class="nav-links">
                <a href="../Domains.html">Functional Overview</a>
                <a href="../Organization-Setup-Guide.html">Setup Guide</a>
                <a href="database-schema.html">Database Schema</a>
                <a href="api-reference.html">API Reference</a>
            </div>
        </div>

        <div class="footer">
            <h3>Technical Documentation</h3>
            <p>This documentation covers the technical implementation of Sippar's domain-driven architecture.</p>
            <p>Each domain is self-contained with its own services, repositories, and business logic.</p>
            <p style="margin-top: 20px; font-weight: bold; color: var(--primary);">
                For implementation details, refer to individual domain documentation.
            </p>
            <p style="margin-top: 15px; color: var(--foreground-muted); font-size: 0.9em;">
                Last Updated: <span id="currentDate"></span> | 
                Version: 1.0 | 
                Architecture: Domain-Driven Design
            </p>
        </div>
    </div>

    <script>
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString();
    </script>
</body>
</html>
