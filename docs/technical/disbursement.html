<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disbursement Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Disbursement Domain
        </div>

        <div class="header">
            <h1>Disbursement Domain</h1>
            <p>Transaction Processing & Payment Method Management</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Disbursement Domain handles the final stage of the expense management process, converting approved requisitions into actual financial transactions. It manages multiple payment methods, integrates with external payment systems, and provides comprehensive transaction tracking and reporting.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Transaction Processing:</strong> Convert approved requisitions into disbursement transactions</li>
                <li><strong>Payment Method Management:</strong> Support multiple payment methods (M-Pesa, Bank Transfer, Cash)</li>
                <li><strong>M-Pesa Integration:</strong> Direct integration with M-Pesa payment gateway</li>
                <li><strong>Transaction Tracking:</strong> Complete lifecycle tracking from creation to completion</li>
                <li><strong>Cash Float Integration:</strong> Seamless integration with cash float management</li>
                <li><strong>Disbursement Workflow:</strong> Multi-step disbursement process with approvals</li>
                <li><strong>Reporting & Analytics:</strong> Comprehensive transaction reporting and analysis</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           DISBURSEMENT DOMAIN ERD                                  │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│      TRANSACTIONS       │    │   TRANSACTION_ITEMS     │    │   MPESA_TRANSACTIONS    │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │    │ id (PK)                 │
│ requisition_id (FK)     │    │ transaction_id (FK)     │    │ transaction_id (FK)     │
│ cash_float_id (FK)      │    │ chart_of_account_id(FK) │    │ merchant_request_id     │
│ transaction_type        │    │ description             │    │ checkout_request_id     │
│ status                  │    │ quantity                │    │ mpesa_receipt_number    │
│ payment_method          │    │ unit_price              │    │ phone_number            │
│ mpesa_transaction_id    │    │ total_price             │    │ amount                  │
│ account_details         │    │ created_at              │    │ transaction_date        │
│ disbursement_trans_id   │    │ updated_at              │    │ result_code             │
│ approvers_details       │    └─────────────────────────┘    │ result_desc             │
│ total_amount            │              │                   │ status                  │
│ transaction_cost        │              │                   │ created_at              │
│ created_by (FK)         │              │                   │ updated_at              │
│ updated_by (FK)         │              │                   └─────────────────────────┘
│ description             │              │                             │
│ created_at              │              │                             │
│ updated_at              │              │                             │
└─────────────────────────┘              │                             │
           │                             │                             │
           │                             │                             │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              CHART_OF_ACCOUNTS                   │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ id (PK)                                         │    │
           │    │ name                                            │    │
           │    │ ... (other account fields)                     │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           │    │                 REQUISITIONS                         │
           │    ├──────────────────────────────────────────────────────┤
           │    │ id (PK)                                              │
           │    │ requisition_number                                   │
           │    │ status                                               │
           │    │ total_amount                                         │
           │    │ ... (other requisition fields)                      │
           │    └──────────────────────────────────────────────────────┘
           │
           │    ┌──────────────────────────────────────────────────────┐
           └────┤                 CASH_FLOATS                          │
                ├──────────────────────────────────────────────────────┤
                │ id (PK)                                              │
                │ name                                                 │
                │ current_balance                                      │
                │ ... (other cash float fields)                       │
                └──────────────────────────────────────────────────────┘

TRANSACTION STATUS FLOW:
opened → updated → completed

PAYMENT METHODS:
• mpesa: M-Pesa mobile money transfer
• bank: Bank transfer with account details
• cash: Cash disbursement from float

TRANSACTION TYPES:
• requisition_disbursement: From approved requisition
• cash_float_replenishment: Float funding
• direct_payment: Direct disbursement
• refund: Return payment

RELATIONSHIPS:
• Transactions belong to Requisitions (N:1)
• Transactions belong to Cash Floats (N:1)
• Transactions have many Transaction Items (1:N)
• Transactions have one M-Pesa Transaction (1:1)
• Transaction Items belong to Chart of Accounts (N:1)
• Transactions have Attachments (polymorphic)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Disbursement Domain is implemented through the DisbursementService and supporting repositories.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>DisbursementService</h4>
                    <div class="signature">createTransactionFromRequisition($requisition)</div>
                    <div class="description">Creates transaction from approved requisition with items</div>
                    
                    <div class="signature">processPayment($transactionId, $paymentData)</div>
                    <div class="description">Processes payment through selected payment method</div>
                    
                    <div class="signature">updateTransaction($id, $data)</div>
                    <div class="description">Updates transaction details and status</div>
                    
                    <div class="signature">completeTransaction($transactionId, $disbursementId)</div>
                    <div class="description">Marks transaction as completed with disbursement reference</div>
                </div>

                <div class="method-card">
                    <h4>Payment Processing</h4>
                    <div class="signature">processMpesaPayment($transaction, $phoneNumber)</div>
                    <div class="description">Initiates M-Pesa STK push payment</div>
                    
                    <div class="signature">processBankTransfer($transaction, $accountDetails)</div>
                    <div class="description">Processes bank transfer with account information</div>
                    
                    <div class="signature">processCashPayment($transaction, $cashFloatId)</div>
                    <div class="description">Processes cash payment from specified float</div>
                    
                    <div class="signature">validatePaymentMethod($method, $data)</div>
                    <div class="description">Validates payment method and required data</div>
                </div>

                <div class="method-card">
                    <h4>Transaction Management</h4>
                    <div class="signature">getTransaction($id)</div>
                    <div class="description">Retrieves transaction by ID</div>
                    
                    <div class="signature">getTransactionWithItems($id)</div>
                    <div class="description">Retrieves transaction with all items and relationships</div>
                    
                    <div class="signature">getAllTransactions($filters, $perPage)</div>
                    <div class="description">Retrieves transactions with filtering and pagination</div>
                    
                    <div class="signature">getTransactionsForRequester($userId, $filters)</div>
                    <div class="description">Gets transactions for specific requester</div>
                </div>

                <div class="method-card">
                    <h4>M-Pesa Integration</h4>
                    <div class="signature">initiateMpesaPayment($amount, $phoneNumber, $reference)</div>
                    <div class="description">Initiates M-Pesa payment request</div>
                    
                    <div class="signature">handleMpesaCallback($callbackData)</div>
                    <div class="description">Processes M-Pesa payment callback</div>
                    
                    <div class="signature">queryMpesaTransaction($checkoutRequestId)</div>
                    <div class="description">Queries M-Pesa transaction status</div>
                    
                    <div class="signature">validateMpesaResponse($response)</div>
                    <div class="description">Validates M-Pesa API response</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>DisbursementService</li>
                        <li>Payment Processing Logic</li>
                        <li>Transaction State Management</li>
                        <li>Business Rule Validation</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure</h4>
                    <ul>
                        <li>EloquentTransactionRepository</li>
                        <li>EloquentTransactionItemRepository</li>
                        <li>M-Pesa API Integration</li>
                        <li>Database Transactions</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Payment Methods</h4>
                    <ul>
                        <li>M-Pesa STK Push</li>
                        <li>Bank Transfer Processing</li>
                        <li>Cash Float Integration</li>
                        <li>Payment Validation</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Integration</h4>
                    <ul>
                        <li>Requisition Service</li>
                        <li>Cash Float Service</li>
                        <li>Notification Service</li>
                        <li>Event System</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Multi-Payment Method Support</h3>
            <p>The system supports three primary payment methods:</p>
            <ul>
                <li><strong>M-Pesa:</strong> Direct mobile money transfer with STK push integration</li>
                <li><strong>Bank Transfer:</strong> Traditional bank transfers with account details</li>
                <li><strong>Cash:</strong> Cash disbursement from managed cash floats</li>
            </ul>
            
            <h3>M-Pesa Integration</h3>
            <p>Full integration with Safaricom M-Pesa API including:</p>
            <ul>
                <li>STK Push for customer-initiated payments</li>
                <li>Real-time payment status updates</li>
                <li>Automatic transaction reconciliation</li>
                <li>Callback handling and validation</li>
            </ul>
            
            <h3>Transaction Lifecycle Management</h3>
            <p>Complete transaction lifecycle tracking:</p>
            <ul>
                <li><strong>Opened:</strong> Transaction created from approved requisition</li>
                <li><strong>Updated:</strong> Payment details added and approved</li>
                <li><strong>Completed:</strong> Payment processed and disbursement confirmed</li>
            </ul>
            
            <h3>Cash Float Integration</h3>
            <p>Seamless integration with cash float management for cash disbursements, including automatic balance updates and transaction logging.</p>
            
            <h3>Comprehensive Audit Trail</h3>
            <p>Every transaction maintains complete audit trail including:</p>
            <ul>
                <li>Creation and modification timestamps</li>
                <li>User actions and approvals</li>
                <li>Payment method details</li>
                <li>External transaction references</li>
                <li>Status change history</li>
            </ul>
            
            <h3>Flexible Transaction Types</h3>
            <p>Support for various transaction types beyond requisition disbursements:</p>
            <ul>
                <li>Cash float replenishments</li>
                <li>Direct payments</li>
                <li>Refunds and returns</li>
                <li>Administrative adjustments</li>
            </ul>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
