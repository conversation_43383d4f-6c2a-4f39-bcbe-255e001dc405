<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Notification Domain
        </div>

        <div class="header">
            <h1>Notification Domain</h1>
            <p>Comprehensive Communication & Event-driven Messaging System</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Notification Domain provides a comprehensive communication system supporting email notifications, in-app notifications, and workflow-based communication routing. It ensures all stakeholders are informed of relevant events and actions throughout the expense management process.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Email Notifications:</strong> Automated email notifications for workflow events</li>
                <li><strong>In-app Notifications:</strong> Real-time notifications within the application</li>
                <li><strong>Event-driven Messaging:</strong> Automatic notifications triggered by system events</li>
                <li><strong>Template Management:</strong> Customizable notification templates</li>
                <li><strong>Delivery Tracking:</strong> Track notification delivery status and failures</li>
                <li><strong>User Preferences:</strong> User-configurable notification preferences</li>
                <li><strong>Workflow Integration:</strong> Deep integration with approval workflows</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           NOTIFICATION DOMAIN ERD                                  │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│     NOTIFICATIONS       │    │  NOTIFICATION_TEMPLATES │    │ NOTIFICATION_PREFERENCES│
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │    │ id (PK)                 │    │ id (PK)                 │
│ user_id (FK)            │    │ name                    │    │ user_id (FK)            │
│ type                    │    │ type                    │    │ notification_type       │
│ title                   │    │ subject_template        │    │ email_enabled           │
│ message                 │    │ body_template           │    │ in_app_enabled          │
│ data (JSON)             │    │ variables (JSON)        │    │ frequency               │
│ read_at                 │    │ is_active               │    │ created_at              │
│ created_at              │    │ created_at              │    │ updated_at              │
│ updated_at              │    │ updated_at              │    └─────────────────────────┘
└─────────────────────────┘    └─────────────────────────┘              │
           │                              │                              │
           │                              │                              │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              EMAIL_NOTIFICATIONS                 │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ id (PK)                                         │    │
           │    │ notification_id (FK)                            │    │
           │    │ recipient_email                                 │    │
           │    │ subject                                         │    │
           │    │ body                                            │    │
           │    │ status (pending, sent, failed)                 │    │
           │    │ sent_at                                         │    │
           │    │ failed_reason                                   │    │
           │    │ attempts                                        │    │
           │    │ created_at                                      │    │
           │    │ updated_at                                      │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           └────┤                     USERS                            │
                ├──────────────────────────────────────────────────────┤
                │ id (PK)                                              │
                │ first_name, last_name                                │
                │ email                                                │
                │ ... (other user fields)                             │
                └──────────────────────────────────────────────────────┘

NOTIFICATION TYPES:
• requisition_created: New requisition submitted
• requisition_approved: Requisition approved
• requisition_rejected: Requisition rejected
• requisition_returned: Requisition returned for revision
• approval_pending: Approval action required
• transaction_created: Transaction created from requisition
• transaction_completed: Payment processed
• cash_float_low: Cash float below threshold
• system_maintenance: System notifications

EVENT TRIGGERS:
┌─ Requisition Events
│  ├─ Created → Notify approvers
│  ├─ Approved → Notify requester & finance
│  ├─ Rejected → Notify requester
│  └─ Returned → Notify requester
├─ Transaction Events
│  ├─ Created → Notify finance team
│  ├─ Payment Details Added → Notify processors
│  └─ Completed → Notify requester
└─ System Events
   ├─ Cash Float Low → Notify managers
   ├─ Workflow Errors → Notify admins
   └─ System Maintenance → Notify all users

DELIVERY CHANNELS:
• Email: SMTP-based email delivery
• In-app: Database notifications with real-time updates
• Future: SMS, Push notifications (extensible)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Notification Domain uses Laravel's built-in notification system with custom notification classes and event listeners.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>Notification Service</h4>
                    <div class="signature">sendNotification($user, $notification)</div>
                    <div class="description">Sends notification through configured channels</div>
                    
                    <div class="signature">sendBulkNotification($users, $notification)</div>
                    <div class="description">Sends notification to multiple users efficiently</div>
                    
                    <div class="signature">queueNotification($user, $notification, $delay)</div>
                    <div class="description">Queues notification for delayed delivery</div>
                    
                    <div class="signature">getUnreadNotifications($userId)</div>
                    <div class="description">Retrieves unread notifications for user</div>
                </div>

                <div class="method-card">
                    <h4>Event Listeners</h4>
                    <div class="signature">RequisitionCreated::handle($event)</div>
                    <div class="description">Handles requisition creation notifications</div>
                    
                    <div class="signature">RequisitionApproved::handle($event)</div>
                    <div class="description">Handles requisition approval notifications</div>
                    
                    <div class="signature">TransactionCompleted::handle($event)</div>
                    <div class="description">Handles transaction completion notifications</div>
                    
                    <div class="signature">CashFloatLowBalance::handle($event)</div>
                    <div class="description">Handles cash float low balance alerts</div>
                </div>

                <div class="method-card">
                    <h4>Template Management</h4>
                    <div class="signature">getTemplate($type)</div>
                    <div class="description">Retrieves notification template by type</div>
                    
                    <div class="signature">renderTemplate($template, $variables)</div>
                    <div class="description">Renders template with dynamic variables</div>
                    
                    <div class="signature">createTemplate($data)</div>
                    <div class="description">Creates new notification template</div>
                    
                    <div class="signature">updateTemplate($id, $data)</div>
                    <div class="description">Updates existing notification template</div>
                </div>

                <div class="method-card">
                    <h4>User Preferences</h4>
                    <div class="signature">getUserPreferences($userId)</div>
                    <div class="description">Gets user notification preferences</div>
                    
                    <div class="signature">updatePreferences($userId, $preferences)</div>
                    <div class="description">Updates user notification preferences</div>
                    
                    <div class="signature">canSendNotification($user, $type)</div>
                    <div class="description">Checks if user wants to receive notification type</div>
                    
                    <div class="signature">getDeliveryChannels($user, $notification)</div>
                    <div class="description">Determines delivery channels based on preferences</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Laravel Features</h4>
                    <ul>
                        <li>Laravel Notifications</li>
                        <li>Event System</li>
                        <li>Queue System</li>
                        <li>Mail System</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Delivery Channels</h4>
                    <ul>
                        <li>Database Channel</li>
                        <li>Mail Channel</li>
                        <li>Custom Channels</li>
                        <li>Queue Integration</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Templates</h4>
                    <ul>
                        <li>Blade Templates</li>
                        <li>Dynamic Variables</li>
                        <li>HTML/Text Formats</li>
                        <li>Template Inheritance</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Event Integration</h4>
                    <ul>
                        <li>Domain Events</li>
                        <li>Event Listeners</li>
                        <li>Async Processing</li>
                        <li>Error Handling</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Multi-Channel Delivery</h3>
            <p>Notifications are delivered through multiple channels based on user preferences:</p>
            <ul>
                <li><strong>Email:</strong> Rich HTML emails with templates and attachments</li>
                <li><strong>In-app:</strong> Real-time notifications within the application interface</li>
                <li><strong>Extensible:</strong> Framework ready for SMS, push notifications, and other channels</li>
            </ul>
            
            <h3>Event-Driven Architecture</h3>
            <p>Notifications are automatically triggered by system events:</p>
            <ul>
                <li><strong>Workflow Events:</strong> Requisition creation, approval, rejection</li>
                <li><strong>Transaction Events:</strong> Payment processing, completion</li>
                <li><strong>System Events:</strong> Low balances, maintenance, errors</li>
                <li><strong>User Events:</strong> Account changes, security alerts</li>
            </ul>
            
            <h3>Template System</h3>
            <p>Flexible template system with:</p>
            <ul>
                <li><strong>Dynamic Variables:</strong> User names, amounts, dates, links</li>
                <li><strong>HTML/Text Formats:</strong> Rich HTML emails with plain text fallback</li>
                <li><strong>Customizable Content:</strong> Organization-specific branding and messaging</li>
                <li><strong>Multi-language Support:</strong> Ready for internationalization</li>
            </ul>
            
            <h3>User Preferences</h3>
            <p>Comprehensive preference management:</p>
            <ul>
                <li><strong>Channel Selection:</strong> Choose email, in-app, or both</li>
                <li><strong>Frequency Control:</strong> Immediate, daily digest, or weekly summary</li>
                <li><strong>Type Filtering:</strong> Enable/disable specific notification types</li>
                <li><strong>Quiet Hours:</strong> Respect user time zones and preferences</li>
            </ul>
            
            <h3>Delivery Tracking</h3>
            <p>Comprehensive delivery monitoring:</p>
            <ul>
                <li><strong>Status Tracking:</strong> Pending, sent, delivered, failed</li>
                <li><strong>Retry Logic:</strong> Automatic retry for failed deliveries</li>
                <li><strong>Error Logging:</strong> Detailed error tracking and reporting</li>
                <li><strong>Analytics:</strong> Delivery rates and user engagement metrics</li>
            </ul>
            
            <h3>Workflow Integration</h3>
            <p>Deep integration with business processes:</p>
            <ul>
                <li><strong>Approval Notifications:</strong> Notify approvers of pending actions</li>
                <li><strong>Status Updates:</strong> Keep requesters informed of progress</li>
                <li><strong>Escalation Alerts:</strong> Notify managers of overdue approvals</li>
                <li><strong>Completion Confirmations:</strong> Confirm successful transactions</li>
            </ul>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
