<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Management Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 1, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Organization Management Domain
        </div>

        <div class="header">
            <h1>Organization Management Domain</h1>
            <p>Multi-tenant Organizational Structure & Hierarchy Management</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Organization Management Domain handles the hierarchical structure of organizations, branches, and departments within the Sippar platform. It provides multi-tenant support and manages the organizational relationships that form the foundation for all business operations.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Organization Management:</strong> Create and manage multiple organizations with isolation</li>
                <li><strong>Branch Management:</strong> Handle multiple locations/branches per organization</li>
                <li><strong>Department Management:</strong> Manage functional departments within branches</li>
                <li><strong>Hierarchy Maintenance:</strong> Maintain parent-child relationships and organizational structure</li>
                <li><strong>Multi-tenant Support:</strong> Ensure data isolation between organizations</li>
                <li><strong>Template System:</strong> Provide pre-configured department templates for quick setup</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                        ORGANIZATION MANAGEMENT DOMAIN ERD                          │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│      ORGANIZATIONS      │    │        BRANCHES         │    │      DEPARTMENTS        │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │◄───┤ id (PK)                 │
│ name                    │    │ name                    │    │ name                    │
│ contact_email           │    │ organization_id (FK)    │    │ organization_id (FK)    │
│ contact_phone           │    │ location                │    │ branch_id (FK)          │
│ address                 │    │ contact_email           │    │ description             │
│ mpesa_account_name      │    │ contact_phone           │    │ head_of_department_id   │
│ mpesa_phone_number      │    │ address                 │    │ budget_limit            │
│ status                  │    │ status                  │    │ status                  │
│ created_at              │    │ created_at              │    │ created_at              │
│ updated_at              │    │ updated_at              │    │ updated_at              │
└─────────────────────────┘    └─────────────────────────┘    └─────────────────────────┘
           │                              │                              │
           │                              │                              │
           │                              │                              │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              DEPARTMENT_TEMPLATES                 │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ id (PK)                                         │    │
           │    │ name                                            │    │
           │    │ description                                     │    │
           │    │ category                                        │    │
           │    │ default_budget_limit                            │    │
           │    │ is_active                                       │    │
           │    │ created_at                                      │    │
           │    │ updated_at                                      │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           └────┤                     USERS                            │
                ├──────────────────────────────────────────────────────┤
                │ id (PK)                                              │
                │ organization_id (FK)                                 │
                │ branch_id (FK)                                       │
                │ department_id (FK)                                   │
                │ ... (other user fields)                             │
                └──────────────────────────────────────────────────────┘

RELATIONSHIPS:
• Organizations have many Branches (1:N)
• Organizations have many Departments (1:N)
• Organizations have many Users (1:N)
• Branches belong to Organizations (N:1)
• Branches have many Departments (1:N)
• Branches have many Users (1:N)
• Departments belong to Organizations (N:1)
• Departments belong to Branches (N:1)
• Departments have many Users (1:N)
• Departments have one Head of Department (User) (1:1)
• Department Templates are used to create Departments (template pattern)
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>Organization management is primarily handled through Laravel controllers and models, with business logic distributed across the organizational entities.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>Organization Methods</h4>
                    <div class="signature">Organization::create($data)</div>
                    <div class="description">Creates a new organization with validation and setup</div>
                    
                    <div class="signature">$organization->branches()</div>
                    <div class="description">Returns all branches belonging to the organization</div>
                    
                    <div class="signature">$organization->departments()</div>
                    <div class="description">Returns all departments across all branches</div>
                    
                    <div class="signature">$organization->users()</div>
                    <div class="description">Returns all users within the organization</div>
                </div>

                <div class="method-card">
                    <h4>Branch Management</h4>
                    <div class="signature">Branch::create($data)</div>
                    <div class="description">Creates a new branch within an organization</div>
                    
                    <div class="signature">$branch->departments()</div>
                    <div class="description">Returns departments within the branch</div>
                    
                    <div class="signature">$branch->users()</div>
                    <div class="description">Returns users assigned to the branch</div>
                    
                    <div class="signature">$branch->organization()</div>
                    <div class="description">Returns the parent organization</div>
                </div>

                <div class="method-card">
                    <h4>Department Management</h4>
                    <div class="signature">Department::create($data)</div>
                    <div class="description">Creates a new department with template support</div>
                    
                    <div class="signature">$department->users()</div>
                    <div class="description">Returns users assigned to the department</div>
                    
                    <div class="signature">$department->headOfDepartment()</div>
                    <div class="description">Returns the department head (HOD)</div>
                    
                    <div class="signature">$department->assignHead($user)</div>
                    <div class="description">Assigns a user as head of department</div>
                </div>

                <div class="method-card">
                    <h4>Template System</h4>
                    <div class="signature">DepartmentTemplate::getByCategory($category)</div>
                    <div class="description">Returns templates filtered by category</div>
                    
                    <div class="signature">$template->createDepartment($branchId)</div>
                    <div class="description">Creates a department from template</div>
                    
                    <div class="signature">DepartmentTemplate::getActive()</div>
                    <div class="description">Returns all active department templates</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Models</h4>
                    <ul>
                        <li>Organization Model</li>
                        <li>Branch Model</li>
                        <li>Department Model</li>
                        <li>DepartmentTemplate Model</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Relationships</h4>
                    <ul>
                        <li>Eloquent Relationships</li>
                        <li>Foreign Key Constraints</li>
                        <li>Cascading Operations</li>
                        <li>Soft Deletes</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Multi-tenancy</h4>
                    <ul>
                        <li>Organization Scoping</li>
                        <li>Data Isolation</li>
                        <li>Tenant-aware Queries</li>
                        <li>Cross-tenant Prevention</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Validation</h4>
                    <ul>
                        <li>Unique Constraints</li>
                        <li>Hierarchy Validation</li>
                        <li>Business Rule Enforcement</li>
                        <li>Data Integrity Checks</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Multi-tenant Architecture</h3>
            <p>Each organization operates as an isolated tenant with complete data separation. Users, branches, departments, and all related data are scoped to their respective organizations.</p>
            
            <h3>Hierarchical Structure</h3>
            <p>The three-tier hierarchy (Organization → Branch → Department) provides flexibility for organizations of all sizes, from single-location businesses to multi-national corporations.</p>
            
            <h3>Department Templates</h3>
            <p>Pre-configured department templates (Finance, IT, HR, Operations, Marketing) allow for quick setup with appropriate default settings and budget allocations.</p>
            
            <h3>Head of Department (HOD) System</h3>
            <p>Each department can have an assigned head who serves as the primary approver for departmental requisitions and manages department-specific workflows.</p>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
