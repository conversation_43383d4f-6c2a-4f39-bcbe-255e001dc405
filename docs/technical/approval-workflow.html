<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Approval Workflow Domain - Sippar Technical Documentation</title>
    <style>
        :root {
            --primary: #059669;
            --primary-light: #10b981;
            --primary-dark: #065f46;
            --primary-foreground: #ffffff;
            --background: #ffffff;
            --background-muted: #f8fafc;
            --background-secondary: #f1f5f9;
            --foreground: #0f172a;
            --foreground-muted: #64748b;
            --card-background: #ffffff;
            --border: #e2e8f0;
            --success: #059669;
            --warning: #d97706;
            --info: #0284c7;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
            --font-family: 'Instrument Sans', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            --radius: 0.75rem;
            --radius-sm: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        body {
            font-family: var(--font-family);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background-muted);
            color: var(--foreground);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-background);
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
        }
        
        .header {
            background: var(--gradient-primary);
            color: var(--primary-foreground);
            padding: 30px;
            border-radius: var(--radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .nav-breadcrumb {
            background: var(--background-secondary);
            padding: 15px 20px;
            border-radius: var(--radius-sm);
            margin-bottom: 30px;
        }
        
        .nav-breadcrumb a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .nav-breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .section {
            background: var(--background-muted);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 25px;
            margin: 25px 0;
        }
        
        .section h2 {
            margin: 0 0 20px 0;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
        }
        
        .erd-container {
            background: var(--card-background);
            border: 2px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            overflow-x: auto;
        }
        
        .erd-diagram {
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre;
            text-align: left;
            background: var(--background-muted);
            padding: 20px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .service-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: var(--card-background);
            border: 1px solid var(--border);
            border-radius: var(--radius-sm);
            padding: 20px;
        }
        
        .method-card h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-family: monospace;
        }
        
        .method-card .signature {
            background: var(--background-secondary);
            padding: 10px;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.9em;
            margin: 10px 0;
        }
        
        .method-card .description {
            color: var(--foreground-muted);
            font-size: 0.9em;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid var(--success);
            padding: 20px;
            border-radius: var(--radius);
            margin: 20px 0;
        }
        
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: var(--primary-dark);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .tech-item {
            background: var(--card-background);
            padding: 15px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border);
        }
        
        .tech-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary);
            font-size: 1em;
        }
        
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tech-item li {
            margin: 5px 0;
            font-size: 0.9em;
        }
        
        .back-link {
            display: inline-block;
            background: var(--primary);
            color: var(--primary-foreground);
            padding: 12px 24px;
            text-decoration: none;
            border-radius: var(--radius-sm);
            margin: 20px 0;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-breadcrumb">
            <a href="index.html">Technical Documentation</a> > Approval Workflow Domain
        </div>

        <div class="header">
            <h1>Approval Workflow Domain</h1>
            <p>Dynamic Approval Engine with Templates & Fallback Mechanisms</p>
        </div>

        <div class="section">
            <h2>Domain Overview</h2>
            <p>The Approval Workflow Domain provides a sophisticated, configurable approval system that routes requisitions through appropriate approval chains based on organizational structure, amount thresholds, and business rules. It features intelligent fallback mechanisms and template-based workflow creation.</p>
            
            <h3>Core Responsibilities</h3>
            <ul>
                <li><strong>Workflow Engine:</strong> Dynamic routing of approvals based on configurable rules</li>
                <li><strong>Template System:</strong> Pre-configured workflow templates for quick setup</li>
                <li><strong>Multi-step Approvals:</strong> Support for complex, multi-level approval processes</li>
                <li><strong>Fallback Mechanisms:</strong> Intelligent routing when primary approvers are unavailable</li>
                <li><strong>Amount-based Routing:</strong> Different approval paths based on expense amounts</li>
                <li><strong>Department-specific Workflows:</strong> Customized approval chains per department</li>
                <li><strong>Approval History:</strong> Complete audit trail of all approval actions</li>
            </ul>
        </div>

        <div class="section">
            <h2>Entity Relationship Diagram</h2>
            <div class="erd-container">
                <div class="erd-diagram">
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                        APPROVAL WORKFLOW DOMAIN ERD                                │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────┐    ┌─────────────────────────┐    ┌─────────────────────────┐
│   APPROVAL_WORKFLOWS    │    │ APPROVAL_WORKFLOW_STEPS │    │    APPROVAL_HISTORIES   │
├─────────────────────────┤    ├─────────────────────────┤    ├─────────────────────────┤
│ id (PK)                 │◄───┤ id (PK)                 │    │ id (PK)                 │
│ organization_id (FK)    │    │ workflow_id (FK)        │    │ requisition_id (FK)     │
│ name                    │    │ step_order              │    │ workflow_step_id (FK)   │
│ description             │    │ step_name               │    │ approver_id (FK)        │
│ is_active               │    │ approver_role           │    │ action                  │
│ min_amount              │    │ fallback_role           │    │ comments                │
│ max_amount              │    │ requires_all_approvers  │    │ step_description        │
│ department_id (FK)      │    │ is_final_step           │    │ created_at              │
│ created_at              │    │ created_at              │    └─────────────────────────┘
│ updated_at              │    │ updated_at              │              │
└─────────────────────────┘    └─────────────────────────┘              │
           │                              │                              │
           │                              │                              │
           │    ┌─────────────────────────┴─────────────────────────┐    │
           │    │              WORKFLOW_TEMPLATES                  │    │
           │    ├─────────────────────────────────────────────────┤    │
           │    │ id (PK)                                         │    │
           │    │ name                                            │    │
           │    │ category                                        │    │
           │    │ description                                     │    │
           │    │ template_data (JSON)                            │    │
           │    │ is_active                                       │    │
           │    │ created_at                                      │    │
           │    │ updated_at                                      │    │
           │    └─────────────────────────────────────────────────┘    │
           │                                                           │
           │    ┌─────────────────────────────────────────────────────┴┐
           └────┤                 REQUISITIONS                         │
                ├──────────────────────────────────────────────────────┤
                │ id (PK)                                              │
                │ current_step_id (FK)                                 │
                │ status                                               │
                │ total_amount                                         │
                │ department_id (FK)                                   │
                │ ... (other requisition fields)                      │
                └──────────────────────────────────────────────────────┘

WORKFLOW ROUTING LOGIC:
┌─ Requisition Created
│
├─ Amount-based Routing:
│  ├─ < $100: Department Head Only
│  ├─ $100-$1000: Department Head → Finance Manager
│  └─ > $1000: Department Head → Finance Manager → Organization Admin
│
├─ Department-specific Rules:
│  ├─ Finance Dept: Finance Manager → Organization Admin
│  ├─ IT Dept: IT Head → Finance Manager
│  └─ Other Depts: Department Head → Finance Manager
│
└─ Fallback Mechanisms:
   ├─ No Department Head: Route to IT Head
   ├─ No Finance Manager: Route to Organization Admin
   └─ No Organization Admin: Route to Platform Admin

APPROVAL ACTIONS:
• approve: Move to next step or complete workflow
• reject: End workflow with rejection
• return: Send back to requester for revision
• delegate: Forward to another approver
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Core Services & Methods</h2>
            <p>The Approval Workflow Domain is implemented through the ApprovalWorkflowService and supporting repositories.</p>

            <div class="service-methods">
                <div class="method-card">
                    <h4>ApprovalWorkflowService</h4>
                    <div class="signature">initiateWorkflow($requisition)</div>
                    <div class="description">Starts approval workflow for a requisition based on routing rules</div>
                    
                    <div class="signature">processApproval($requisitionId, $approverId, $action, $comments)</div>
                    <div class="description">Processes approval action and moves to next step or completes workflow</div>
                    
                    <div class="signature">getNextApprover($requisition, $currentStep)</div>
                    <div class="description">Determines next approver using routing logic and fallback mechanisms</div>
                    
                    <div class="signature">createWorkflowFromTemplate($templateId, $organizationId)</div>
                    <div class="description">Creates workflow from pre-configured template</div>
                </div>

                <div class="method-card">
                    <h4>Workflow Routing</h4>
                    <div class="signature">determineWorkflow($requisition)</div>
                    <div class="description">Selects appropriate workflow based on amount, department, and rules</div>
                    
                    <div class="signature">applyFallbackRules($step, $department)</div>
                    <div class="description">Applies fallback routing when primary approvers are unavailable</div>
                    
                    <div class="signature">validateApprovalChain($workflow)</div>
                    <div class="description">Validates that all workflow steps have valid approvers</div>
                    
                    <div class="signature">getApprovalPath($requisition)</div>
                    <div class="description">Returns complete approval path for a requisition</div>
                </div>

                <div class="method-card">
                    <h4>Template Management</h4>
                    <div class="signature">getTemplatesByCategory($category)</div>
                    <div class="description">Retrieves workflow templates by category</div>
                    
                    <div class="signature">createTemplate($data)</div>
                    <div class="description">Creates new workflow template with validation</div>
                    
                    <div class="signature">previewTemplate($templateId)</div>
                    <div class="description">Generates preview of workflow steps from template</div>
                    
                    <div class="signature">validateTemplate($templateData)</div>
                    <div class="description">Validates template structure and business rules</div>
                </div>

                <div class="method-card">
                    <h4>History & Tracking</h4>
                    <div class="signature">recordApprovalAction($requisitionId, $stepId, $approverId, $action)</div>
                    <div class="description">Records approval action in history with full audit trail</div>
                    
                    <div class="signature">getApprovalHistory($requisitionId)</div>
                    <div class="description">Retrieves complete approval history for requisition</div>
                    
                    <div class="signature">getPendingApprovals($userId)</div>
                    <div class="description">Gets requisitions pending approval by specific user</div>
                    
                    <div class="signature">getWorkflowMetrics($organizationId, $dateRange)</div>
                    <div class="description">Generates workflow performance metrics and analytics</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>Technical Implementation</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Domain Layer</h4>
                    <ul>
                        <li>ApprovalWorkflowService</li>
                        <li>Workflow Engine Logic</li>
                        <li>Routing Algorithms</li>
                        <li>Fallback Mechanisms</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Infrastructure</h4>
                    <ul>
                        <li>EloquentApprovalWorkflowRepository</li>
                        <li>EloquentApprovalWorkflowStepRepository</li>
                        <li>EloquentApprovalHistoryRepository</li>
                        <li>Template Storage</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Business Rules</h4>
                    <ul>
                        <li>Amount-based Routing</li>
                        <li>Department-specific Rules</li>
                        <li>Role-based Approvals</li>
                        <li>Fallback Logic</li>
                    </ul>
                </div>
                <div class="tech-item">
                    <h4>Integration</h4>
                    <ul>
                        <li>Requisition Service</li>
                        <li>Notification Service</li>
                        <li>User Management</li>
                        <li>Event System</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Key Features</h2>
            
            <h3>Intelligent Routing</h3>
            <p>The workflow engine automatically determines the appropriate approval path based on:</p>
            <ul>
                <li><strong>Amount Thresholds:</strong> Different approval levels for different expense amounts</li>
                <li><strong>Department Rules:</strong> Department-specific approval chains</li>
                <li><strong>Role-based Routing:</strong> Approvals based on organizational roles</li>
                <li><strong>Custom Rules:</strong> Organization-specific business rules</li>
            </ul>
            
            <h3>Fallback Mechanisms</h3>
            <p>When primary approvers are unavailable, the system intelligently routes to alternative approvers:</p>
            <ul>
                <li>Departments without HODs route to IT Head</li>
                <li>Missing Finance Managers route to Organization Admin</li>
                <li>Escalation to Platform Admin when needed</li>
            </ul>
            
            <h3>Template System</h3>
            <p>Pre-configured workflow templates enable quick setup for common scenarios:</p>
            <ul>
                <li><strong>Basic Approval:</strong> Simple single-step approval</li>
                <li><strong>Two-tier Approval:</strong> Department Head → Finance Manager</li>
                <li><strong>Three-tier Approval:</strong> Department Head → Finance Manager → Organization Admin</li>
                <li><strong>Finance-specific:</strong> Specialized workflows for finance department</li>
            </ul>
            
            <h3>Comprehensive Audit Trail</h3>
            <p>Every approval action is recorded with complete details including approver, timestamp, action taken, and comments. This provides full transparency and accountability in the approval process.</p>
        </div>

        <a href="index.html" class="back-link">← Back to Technical Documentation</a>
    </div>
</body>
</html>
