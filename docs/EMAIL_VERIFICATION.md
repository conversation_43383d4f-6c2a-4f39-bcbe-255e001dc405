# Email Verification System

This document describes the email verification system implemented for user registration.

## Overview

The email verification system requires users to verify their email address before completing account creation. Instead of immediately creating user accounts, the system stores registration data temporarily and sends a 6-digit verification code to the user's email.

## Flow

### 1. Registration Process

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant RegisterController
    participant VerificationService
    participant EmailSystem
    participant Database

    User->>Frontend: Submit registration form
    Frontend->>RegisterController: POST /register
    RegisterController->>VerificationService: sendVerificationCode()
    VerificationService->>Database: Store verification code & registration data
    VerificationService->>EmailSystem: Send verification email
    EmailSystem-->>User: Email with 6-digit code
    RegisterController->>Frontend: Redirect to verification page
    Frontend->>User: Show code input form
```

### 2. Verification Process

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant VerificationController
    participant VerificationService
    participant Database

    User->>Frontend: Enter verification code
    Frontend->>VerificationController: POST /verify-email-code
    VerificationController->>VerificationService: verifyCodeAndCreateUser()
    VerificationService->>Database: Validate code
    VerificationService->>Database: Create user account
    VerificationService->>Database: Create organization
    VerificationService->>Database: Assign roles
    VerificationController->>Frontend: Login user & redirect to dashboard
```

## Components

### Models

#### EmailVerificationCode
- Stores temporary verification codes
- Contains registration data as JSON
- Tracks expiration and usage status
- Provides validation and cleanup methods

### Services

#### EmailVerificationService
- Manages verification code lifecycle
- Handles rate limiting (5 attempts per hour)
- Creates users after successful verification
- Integrates with existing user/organization creation logic

### Controllers

#### RegisteredUserController
- Modified to store data temporarily instead of creating users immediately
- Redirects to verification page after registration

#### EmailVerificationCodeController
- Handles verification code input and validation
- Provides resend functionality with rate limiting
- Manages verification success/failure flows

### Notifications

#### EmailVerificationCodeNotification
- Sends verification codes via email
- Logs codes to terminal for development testing
- Uses Laravel's mail system

## Security Features

### Rate Limiting
- Maximum 5 verification code requests per hour per email
- Throttled resend endpoint (3 requests per minute)
- Automatic cleanup of rate limits after successful verification

### Code Security
- 6-digit numeric codes
- 15-minute expiration time
- Single-use codes (marked as used after verification)
- IP address tracking for audit purposes

### Data Protection
- Registration data stored temporarily in encrypted JSON
- Automatic cleanup of expired codes
- Session-based temporary storage for pending verifications

## Configuration

### Email Settings
The system uses Laravel's existing mail configuration. For development:
- Codes are logged to the terminal
- Mail driver can be set to 'log' for testing

### Timeouts
- Verification codes expire after 15 minutes
- Rate limiting resets after 1 hour
- Session data persists until verification or timeout

## Usage

### For Developers

#### Testing Email Verification
```bash
# Create a verification code
php artisan tinker
$service = new App\Services\EmailVerificationService();
$code = $service->sendVerificationCode('<EMAIL>', $registrationData);

# Verify the code
$result = $service->verifyCodeAndCreateUser('<EMAIL>', $code->code);
```

#### Cleanup Expired Codes
```bash
php artisan verification:cleanup
```

### For Users

1. **Registration**: Fill out the registration form
2. **Email Check**: Check email for 6-digit verification code
3. **Verification**: Enter code on verification page
4. **Completion**: Account created and automatically logged in

### Error Handling

#### Common Scenarios
- **Invalid Code**: Clear error message, allow retry
- **Expired Code**: Option to request new code
- **Rate Limited**: Show remaining attempts and timeout
- **Email Issues**: Graceful fallback with support contact

#### Development Debugging
- Verification codes logged to `storage/logs/laravel.log`
- Rate limiting status available via service methods
- Database queries logged in debug mode

## Maintenance

### Scheduled Tasks
Add to your cron schedule for automatic cleanup:
```bash
# Clean up expired verification codes daily
0 2 * * * php artisan verification:cleanup
```

### Monitoring
- Track verification success rates
- Monitor rate limiting triggers
- Watch for email delivery issues

## Integration Notes

### Existing Systems
- Integrates with existing user/organization creation
- Uses current role assignment logic
- Maintains compatibility with existing authentication
- Preserves all existing registration features

### Future Enhancements
- SMS verification option
- Configurable code length/format
- Advanced rate limiting strategies
- Verification analytics dashboard
