# Store Requisition Approval Workflow Implementation

## Overview
This document details the comprehensive approval workflow implementation for store requisitions, including role-based permissions, granular approval logic, status management, and user interface design.

## Approval Workflow Architecture

### Three-Tier User Hierarchy

#### 1. **Employees/HODs** (Requesters Only)
**Permissions:**
- `create-store-requisition`: Can submit new requisitions
- `edit-store-requisition`: Can edit own draft/rejected requisitions
- `view-store-requisitions`: Can view requisition history
- `view-inventory`: Can browse inventory catalog

**Capabilities:**
- ✅ Create and submit requisitions
- ✅ Edit own draft and rejected requisitions
- ✅ View own requisition history
- ✅ Browse inventory items
- ❌ Cannot approve any requisitions
- ❌ Cannot issue items
- ❌ Cannot manage inventory

**Dashboard Access:** Employee Dashboard with limited store requisition functionality

#### 2. **Store Keepers** (Primary Approvers + Requesters)
**Permissions:**
- `store-keep`: Meta-permission granting ALL store-related rights
- Includes: create, edit, view, approve, issue, manage-inventory

**Capabilities:**
- ✅ Create own requisitions (require Overseer approval)
- ✅ Approve Employee/HOD requisitions
- ✅ Issue approved items to requesters
- ✅ Manage inventory (add/edit items, stock adjustments)
- ✅ View all requisitions in organization
- ❌ Cannot approve other Store Keeper requisitions
- ❌ Cannot self-approve own requisitions

**Dashboard Access:** Store Keeper Dashboard with full functionality

#### 3. **Overseers** (Secondary Approvers + Requesters)
**Roles:** Finance Manager, Organization Admin

**Permissions:**
- `approve-store-requisition`: Can approve specific requisition types
- `create-store-requisition`: Can submit own requisitions
- `edit-store-requisition`: Can edit own requisitions
- `view-store-requisitions`: Can view all requisitions
- `view-inventory` + `manage-inventory`: Full inventory access

**Capabilities:**
- ✅ Create own requisitions (require Store Keeper approval)
- ✅ Approve Store Keeper requisitions ONLY
- ✅ View all requisitions (oversight function)
- ✅ Manage inventory
- ❌ Cannot approve Employee/HOD requisitions
- ❌ Cannot issue items
- ❌ Cannot self-approve own requisitions

**Dashboard Access:** Employee Dashboard (redirected from store keeper dashboard)

## Granular Approval Logic (Phase 2 Implementation)

### Approval Matrix

| Requester Type | Can Be Approved By | Cannot Be Approved By |
|----------------|-------------------|----------------------|
| Employee | Store Keepers | Overseers, Self |
| HOD | Store Keepers | Overseers, Self |
| Store Keeper | Overseers (Finance Manager/Org Admin) | Store Keepers, Self |
| Overseer | Store Keepers | Other Overseers, Self |

### Business Rules
1. **Separation of Duties**: Store Keepers need oversight for their own requests
2. **Hierarchical Approval**: Higher-level approvals required for store management requests
3. **No Self-Approval**: Users cannot approve their own requisitions
4. **Single Approval**: Each requisition requires only one approval (no multi-stage approval)

## Requisition Status Lifecycle

### Status Flow
```
Draft → Pending Approval → Approved → Issued → Complete
  ↓           ↓              ↓
Cancelled   Rejected    Partially Issued
```

### Status Definitions
- **Draft**: Requisition created but not submitted
- **Pending Approval**: Submitted and awaiting approval decision
- **Approved**: Approved by authorized user, ready for issuing
- **Rejected**: Denied by approver with comments
- **Issued**: All items distributed to requester
- **Partially Issued**: Some items distributed, remaining items pending
- **Cancelled**: Requisition cancelled by requester (draft only)

### Status Permissions
- **Draft**: Only requester can edit/submit/cancel
- **Pending Approval**: Only authorized approvers can approve/reject
- **Approved**: Only Store Keepers can issue items
- **Rejected**: Requester can edit and resubmit
- **Issued/Partially Issued**: Read-only, audit trail maintained

## User Interface Implementation

### Store Requisition Management Page Tabs

#### **Store Keepers**
1. **"All Requisitions"** (default)
   - Shows: All requisitions in organization
   - Purpose: Complete overview for store management

2. **"My Requisitions"**
   - Shows: Only requisitions created by current store keeper
   - Purpose: Personal requisition management

3. **"Can Approve"**
   - Shows: Employee/HOD requisitions awaiting approval
   - Purpose: Action-required items for approval workflow

#### **Overseers (Finance Manager/Organization Admin)**
1. **"All Requisitions"** (default)
   - Shows: All requisitions in organization
   - Purpose: Complete oversight view

2. **"My Requisitions"**
   - Shows: Only requisitions created by current overseer
   - Purpose: Personal requisition management

3. **"Can Approve"**
   - Shows: Store Keeper requisitions awaiting approval
   - Purpose: Action-required items for approval workflow

4. **"View Only"**
   - Shows: Employee/HOD requisitions (reference only)
   - Purpose: Information/oversight without approval capability

#### **Employees**
1. **"My Requisitions"** (default)
   - Shows: Only requisitions created by current employee
   - Purpose: Primary use case for employees

2. **"All Requisitions"**
   - Shows: All requisitions in organization (view-only)
   - Purpose: Transparency and reference

### Status Filter Options
**Clean status-based filtering (no capability mixing):**
- All Statuses
- Draft
- Pending Approval
- Approved
- Rejected
- Issued
- Partially Issued

**Removed from status filter:**
- ~~Can Approve~~ (moved to tabs)
- ~~View Only~~ (moved to tabs)
- ~~Awaiting Approval~~ (redundant terminology)

## Permission Loading Technical Implementation

### Problem Solved
**Issue**: Spatie permission package causing "Call to a member function merge() on array" errors when loading requester permissions in nested loops.

### Solution: Hybrid Approach
1. **User Model Accessor**: Added `getPermissionNamesAttribute()` using direct database queries
2. **Safe Loading**: Implemented `loadRequesterPermissions()` method with error handling
3. **Frontend Safety**: Added array type checking before using `.includes()` methods
4. **Production Ready**: Comprehensive error handling and fallback mechanisms

### Code Implementation
```php
// User Model - Safe permission accessor
public function getPermissionNamesAttribute(): array
{
    try {
        // Direct database queries to avoid Spatie conflicts
        $permissions = DB::table('model_has_permissions')
            ->join('permissions', 'model_has_permissions.permission_id', '=', 'permissions.id')
            ->where('model_has_permissions.model_id', $this->id)
            ->pluck('permissions.name')->toArray();
            
        $rolePermissions = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('role_has_permissions', 'roles.id', '=', 'role_has_permissions.role_id')
            ->join('permissions', 'role_has_permissions.permission_id', '=', 'permissions.id')
            ->where('model_has_roles.model_id', $this->id)
            ->pluck('permissions.name')->toArray();

        return array_values(array_unique(array_merge($permissions, $rolePermissions)));
    } catch (\Exception $e) {
        \Log::error('Error getting user permission names: ' . $e->getMessage());
        return [];
    }
}
```

## Security Considerations

### Permission Validation
- All approval actions validated server-side
- Frontend UI reflects permissions but backend enforces them
- No client-side permission bypassing possible
- Comprehensive audit trail for all actions

### Data Access Control
- Users can only see requisitions based on their role permissions
- Draft requisitions only visible to creators
- Approval capabilities strictly enforced by business rules
- No unauthorized data exposure through API endpoints

## Testing Strategy

### User Role Testing
- Test each role's capabilities and restrictions
- Verify approval workflow for all user combinations
- Ensure proper permission enforcement
- Test edge cases (self-approval attempts, unauthorized access)

### UI/UX Testing
- Tab functionality across all user roles
- Filter combinations and pagination
- Responsive design on all devices
- Error handling and edge cases

### Performance Testing
- Pagination efficiency with large datasets
- Permission loading performance
- Database query optimization
- Frontend rendering performance

---

**Implementation Status**: Complete and Production Ready  
**Last Updated**: 2025-07-06  
**Version**: 2.0 (Granular Approval Logic)
