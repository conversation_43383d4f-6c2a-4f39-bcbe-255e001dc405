# Store Requisitions Feature Documentation

## Overview
Complete inventory management system with requisition workflow, stock tracking, and permission-based access control. Features granular approval workflow with role-based permissions and comprehensive pagination across all table components.

## Recent Updates (2025-07-06)
- **Granular Approval Workflow**: Implemented Phase 2 approval logic with Store Keepers approving Employee/HOD requisitions and Overseers approving Store Keeper requisitions
- **Permission Loading Fix**: Resolved Spatie permission conflicts with safe database queries and error handling
- **Pagination Implementation**: Added pagination to all table components with smart reset functionality
- **UI/UX Improvements**: Enhanced filtering, tab restructuring, and consistent interface design

## Database Schema

### Tables Created
1. **inventory_items** - Central inventory catalog
2. **store_requisitions** - Main requisition workflow  
3. **store_requisition_items** - Line items linking inventory to requisitions
4. **inventory_transactions** - Complete audit trail for stock movements

### Entity Relationships
```
Organization 1:M InventoryItem
Branch 1:M InventoryItem  
InventoryItem 1:M StoreRequisitionItem
InventoryItem 1:M InventoryTransaction
StoreRequisition 1:M StoreRequisitionItem
User 1:M StoreRequisition (as requester/approver/issuer)
User 1:M InventoryTransaction
```

## Permission System

### Store Inventory Permissions
- **view-inventory**: View stock levels and item catalog
- **manage-inventory**: Create/edit items, make stock adjustments
- **create-store-requisition**: Submit new requisitions
- **edit-store-requisition**: Edit draft, rejected, or returned store requisitions
- **view-store-requisitions**: View requisition history
- **approve-store-requisition**: Approve/reject pending requests
- **issue-store-items**: Fulfill approved requisitions
- **store-keep**: Meta-permission granting ALL store rights

### Role Assignments
- **Store Keeper**: All inventory permissions (store-keep meta-permission)
- **Finance Manager**: view-inventory, manage-inventory, create-store-requisition, edit-store-requisition, view-store-requisitions, approve-store-requisition (Overseer role)
- **Organization Admin**: view-inventory, manage-inventory, create-store-requisition, edit-store-requisition, view-store-requisitions, approve-store-requisition (Overseer role)
- **HOD**: view-inventory, create-store-requisition, edit-store-requisition, view-store-requisitions, manage-inventory
- **Employee**: create-store-requisition, edit-store-requisition, view-store-requisitions, view-inventory
- **Platform Admin**: All permissions

### Approval Workflow Roles
- **Store Keepers**: Can approve Employee/HOD requisitions, create own requisitions requiring Overseer approval
- **Overseers (Finance Manager/Organization Admin)**: Can approve Store Keeper requisitions only
- **Employees/HODs**: Cannot approve any requisitions, can only create and manage own requisitions

## Controllers & API Endpoints

### StoreRequisitionController
**Purpose**: Manages complete requisition lifecycle

#### Endpoints:
- `GET /store-requisitions` - List all requisitions (paginated)
- `POST /store-requisitions` - Create new requisition
- `GET /store-requisitions/pending-items` - Get all pending items across requisitions
- `GET /store-requisitions/{id}` - Show specific requisition
- `POST /store-requisitions/{id}/submit` - Submit draft for approval
- `POST /store-requisitions/{id}/approve` - Approve pending requisition
- `POST /store-requisitions/{id}/reject` - Reject with reason
- `POST /store-requisitions/{id}/issue` - Issue items to requester
- `GET /store-requisitions/{id}/picking-list` - Generate picking list for storekeepers
- `GET /store-requisitions/{id}/goods-issue-note` - Generate goods issue note (post-issuance)

#### Business Logic:
- **Workflow**: Draft → Pending → Approved → Issued/Partially Issued
- **Self-approval Prevention**: Store keepers cannot approve own requisitions
- **Automatic Stock Updates**: Inventory decreases when items issued
- **Transaction Logging**: Every issuance creates audit trail
- **Partial Fulfillment**: Support for issuing less than requested

### InventoryController  
**Purpose**: Manages inventory items and stock operations

#### Endpoints:
- `GET /inventory` - List all inventory items
- `POST /inventory` - Create new inventory item
- `GET /inventory/low-stock` - Items below reorder level
- `GET /inventory/replenishment-suggestions` - Auto-generate purchase requisition data
- `GET /inventory/dashboard-summary` - Store keeper dashboard overview
- `GET /inventory/{id}` - Show item details with transaction history
- `PUT /inventory/{id}` - Update item details
- `DELETE /inventory/{id}` - Delete item (if unused)
- `POST /inventory/{id}/adjust-stock` - Manual stock adjustment
- `POST /inventory/{id}/receive-goods` - Record goods receipt with quality control
- `GET /inventory/{id}/transactions` - Full transaction history

#### Features:
- **Real-time Stock Levels**: quantity_on_hand updated automatically
- **Item Details**: SKU, name, description, unit of measure, reorder levels
- **Stock Adjustments**: Manual stock corrections with audit trail
- **Low Stock Alerts**: Automatic detection of items needing reorder
- **Transaction History**: Complete audit trail per item

## Models & Relationships

### InventoryItem
```php
// Attributes
- organization_id, branch_id (scope)
- sku (unique identifier)
- name, description
- unit_of_measure
- quantity_on_hand (current stock)
- reorder_level (trigger for alerts)

// Relationships  
- belongsTo: Organization, Branch
- hasMany: InventoryTransaction, StoreRequisitionItem
```

### StoreRequisition
```php
// Attributes
- organization_id, branch_id, department_id (scope)
- requester_user_id, approver_user_id, issuer_user_id
- purpose (reason for request)
- status (draft/pending_approval/approved/rejected/issued/partially_issued)
- rejection_reason
- requested_at, approved_at, issued_at

// Relationships
- belongsTo: Organization, Branch, Department
- belongsTo: User (requester, approver, issuer)
- hasMany: StoreRequisitionItem
```

### StoreRequisitionItem
```php
// Attributes
- store_requisition_id, inventory_item_id
- quantity_requested, quantity_issued

// Relationships
- belongsTo: StoreRequisition, InventoryItem
```

### InventoryTransaction
```php
// Attributes
- inventory_item_id, user_id
- transaction_type (issuance/receipt/adjustment)
- quantity_change (+/- amount)
- related_document_id, related_document_type (polymorphic)
- notes, transaction_date

// Relationships
- belongsTo: InventoryItem, User
```

## Current Feature Coverage

### ✅ Implemented Features

#### Inventory Lookup
- ✅ Real-time stock levels via `quantity_on_hand`
- ✅ Complete item details (SKU, name, description, unit of measure)
- ✅ Organization/branch scoping
- ✅ Store keeper dashboard with inventory overview

#### Stock Allocation & Issuance  
- ✅ Automatic stock deduction on item issuance
- ✅ Complete audit trail via InventoryTransaction
- ✅ Partial fulfillment support
- ✅ Status tracking (issued/partially_issued)
- ✅ **Picking list generation** for storekeepers
- ✅ **Digital goods issue notes** (structured JSON data)
- ✅ **Pending items tracking** across all requisitions

#### Replenishment Triggers
- ✅ Low stock alerts (`/inventory/low-stock` endpoint)
- ✅ Configurable reorder levels per item
- ✅ **Automatic purchase requisition suggestions** with quantities
- ✅ **Priority-based replenishment** (critical vs normal)

#### Goods Receiving
- ✅ Stock receipt transactions (transaction_type: 'receipt')
- ✅ Initial stock entry on item creation
- ✅ Manual stock adjustments
- ✅ **Quality control notes** and condition tracking
- ✅ **Supplier information** and reference document tracking
- ✅ **Unit cost tracking** for received goods

### ❌ Remaining Enhancement Opportunities

#### Notifications & Automation
- ❌ Email/SMS notifications for low stock alerts
- ❌ Automated purchase order generation
- ❌ PDF generation for picking lists and goods issue notes

#### Advanced Features
- ❌ Purchase order matching during goods receipt
- ❌ Barcode scanning integration
- ❌ Multi-location inventory tracking

## Business Workflow

### Standard Employee Requisition
1. Employee creates requisition (`create-store-requisition`)
2. Employee submits for approval 
3. Store Keeper approves (`approve-store-requisition`)
4. Store Keeper issues items (`issue-store-items`)
5. Stock automatically decreases, transaction logged

### Store Keeper Requisition  
1. Store Keeper creates requisition (`store-keep`)
2. Store Keeper submits for approval
3. Manager/HOD approves (`approve-store-requisition`) 
4. Store Keeper issues items (`issue-store-items`)
5. Stock automatically decreases, transaction logged

### Stock Management
1. Store Keeper adds/edits inventory items (`manage-inventory`)
2. System monitors stock levels vs reorder points
3. Low stock alerts generated automatically (`view-inventory`)
4. Manual adjustments tracked in transaction log

## Security & Data Isolation

### Permission Enforcement
- All controller methods check user permissions
- Organization-level data isolation enforced
- Role-based access control via Spatie/Laravel-Permission

### Audit Trail
- Every stock movement logged in inventory_transactions
- User attribution for all changes
- Timestamp tracking for all operations
- Complete requisition workflow history

## API Response Examples

### Inventory Item
```json
{
  "id": 1,
  "sku": "A4-PAPER-001",
  "name": "A4 Ream of Paper",
  "description": "80gsm white paper, 500 sheets",
  "unit_of_measure": "ream",
  "quantity_on_hand": "45.00",
  "reorder_level": "10.00",
  "organization": {...},
  "branch": {...}
}
```

### Store Requisition  
```json
{
  "id": 1,
  "purpose": "Office supplies for Q4",
  "status": "approved",
  "requester": {...},
  "approver": {...},
  "items": [
    {
      "inventory_item": {...},
      "quantity_requested": "5.00",
      "quantity_issued": "3.00"
    }
  ]
}
```

### Transaction History
```json
{
  "id": 1,
  "transaction_type": "issuance", 
  "quantity_change": "-3.00",
  "related_document_type": "StoreRequisition",
  "related_document_id": 1,
  "notes": "Issued for requisition #1",
  "user": {...},
  "transaction_date": "2025-06-26T12:34:56Z"
}
```

### Picking List
```json
{
  "requisition_id": 1,
  "requester": "John Doe",
  "department": "IT Department", 
  "branch": "Head Office",
  "purpose": "Office supplies for Q4",
  "requested_at": "2025-06-26T10:00:00Z",
  "items": [
    {
      "sku": "A4-PAPER-001",
      "name": "A4 Ream of Paper",
      "quantity_requested": "5.00",
      "current_stock": "45.00", 
      "location": "Main Store",
      "unit": "ream"
    }
  ],
  "generated_by": "Store Keeper",
  "generated_at": "2025-06-26T12:00:00Z"
}
```

### Dashboard Summary
```json
{
  "inventory_summary": {
    "total_items": 150,
    "low_stock_items": 12,
    "out_of_stock_items": 3,
    "stock_health": 92.0
  },
  "requisition_summary": {
    "pending_approval": 5,
    "awaiting_fulfillment": 8
  },
  "recent_activity": [
    {
      "type": "issuance",
      "item": "A4 Paper",
      "quantity": "-5.00",
      "user": "Store Keeper",
      "date": "2025-06-26T11:30:00Z"
    }
  ]
}
```

### Replenishment Suggestions
```json
{
  "suggestions": [
    {
      "item_id": 1,
      "sku": "A4-PAPER-001", 
      "name": "A4 Ream of Paper",
      "current_stock": "2.00",
      "reorder_level": "10.00",
      "suggested_order_quantity": "18.00",
      "unit_of_measure": "ream",
      "branch": "Head Office",
      "days_out_of_stock": 0,
      "priority": "normal"
    }
  ],
  "total_items": 5,
  "critical_items": 1
}
```

## Technical Implementation Details

### Permission Loading Solution
**Problem**: Spatie permission package conflicts causing "Call to a member function merge() on array" errors.

**Solution**: Implemented hybrid approach with:
- Direct database queries for permission loading
- Safe accessor methods in User model (`getPermissionNamesAttribute()`)
- Comprehensive error handling and fallback mechanisms
- Production-ready implementation with proper array handling

### Pagination Implementation
**Scope**: Added pagination to all table components in store requisition and inventory systems.

**Components Updated**:
- **StoreRequisitionManagement.tsx**: Client-side pagination (10 items/page) with tab reset
- **IssueManagement.tsx**: Client-side pagination (10 items/page) with tab reset
- **StoreRequisitionIndex.tsx**: Server-side pagination controls
- **StoreRequisitionApprovals.tsx**: Client-side pagination (10 items/page) with filter reset
- **InventoryIndex.tsx**: Client-side pagination (15 items/page) with search reset
- **ShowInventory.tsx**: Transaction history pagination (10 items/page)

**Features**:
- Smart pagination reset when switching tabs or changing filters
- Consistent pagination controls across all components
- Performance optimization for large datasets
- Responsive design maintained

### UI/UX Enhancements
- **Tab Restructuring**: Capability-based tabs with clear naming conventions
- **Filter Consolidation**: Removed duplicate filter groups, enhanced status filtering
- **Consistent Design**: Unified pagination controls and responsive layouts
- **Error Handling**: Comprehensive error boundaries and fallback states
