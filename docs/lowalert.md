# 1. Start the Queue Worker (for processing notifications)
php artisan queue:work --tries=3 --timeout=90

# 2. Start the Scheduler (for checking cash floats)
php artisan schedule:work

# Clear all caches before testing
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear

# Check for failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
