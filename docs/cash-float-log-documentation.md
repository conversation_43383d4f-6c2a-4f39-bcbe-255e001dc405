# Cash Float Log Documentation

## Overview

The Cash Float Log is a comprehensive financial tracking system that records all petty cash transactions and provides reconciliation capabilities. This document explains the structure, calculations, and reconciliation process used in the system.

## Table Structure

```mermaid
graph TD
    A[Cash Float Log Table] --> B[Transaction Columns]
    A --> C[Chart of Account Columns]
    A --> D[Financial Columns]
    A --> E[Reconciliation Section]

    B --> B1[Date]
    B --> B2[Particulars]
    B --> B3[Paid To/From]

    C --> C1[Office Supplies]
    C --> C2[Travel Expenses]
    C --> C3[Meals]
    C --> C4[Utilities]
    C --> C5[Other Categories...]

    D --> D1[Transaction Cost]
    D --> D2[Cash Out]
    D --> D3[Cash In]
    D --> D4[Running Balance]

    E --> E1[Total Cash Out/In]
    E --> E2[Cash at Hand]
    E --> E3[Petty Cash Reimbursement]
    E --> E4[Balance Carried Forward]
```

### Main Transaction Columns

#### 1. **Date**
- **Purpose**: Records the transaction date
- **Format**: MM/DD/YY (US format) or DD/MM/YY (depending on locale)
- **Source**: `transaction.created_at`

#### 2. **Particulars**
- **Purpose**: Description of the transaction
- **Logic**:
  - If `is_initial_issuance = true`: "Initial Float Issuance"
  - Else: `requisition.purpose` OR concatenated `item.description` OR formatted `transaction_type`
- **Example**: "Office Supplies Purchase", "Travel Expenses", "Initial Float Issuance"

#### 3. **Paid To / Received From**
- **Purpose**: Identifies the person or entity involved in the transaction
- **Logic**:
  - `requisition.requester.name` OR
  - `transaction.creator.name` OR
  - "Company" (for cash-in transactions) OR
  - "-" (default)

### Dynamic Chart of Account Columns

#### 4. **Chart of Account Categories** (Dynamic)
- **Purpose**: Shows amounts spent in each expense category
- **Calculation**: Sum of `item.total_price` for each `chart_of_account.id`
- **Display**: Currency format or "-" if zero
- **Example Columns**: "Office Supplies", "Travel", "Meals", "Utilities"

#### 5. **Transaction Cost**
- **Purpose**: Additional fees or costs associated with the transaction
- **Source**: `transaction.transaction_cost`
- **Display**: Currency format or "-" if null

#### 6. **Cash Out** (Red Background)
- **Purpose**: Money leaving the petty cash float
- **Source**: `transaction.cash_out`
- **Includes**: Expenses, disbursements, float returns
- **Display**: Currency format or "-" if zero

#### 7. **Cash In** (Green Background)
- **Purpose**: Money entering the petty cash float
- **Source**: `transaction.cash_in`
- **Includes**: Float issuances, reimbursements
- **Special**: Initial issuance shows as `(amount)` in parentheses
- **Display**: Currency format or "-" if zero

#### 8. **Balance** (Blue Background)
- **Purpose**: Running balance after each transaction
- **Source**: `transaction.running_balance`
- **Calculation**: Previous balance + cash_in - cash_out - transaction_cost
- **Alert**: Red text if below alert threshold

## Reconciliation Section

The reconciliation section replaces the simple total row and provides a comprehensive cash verification process.

### Row 1: "Total (Cash Out)/In"
- **Purpose**: Summary header showing totals for all categories
- **Background**: Light gray (#f1f5f9)
- **Calculations**:
  - **Chart of Accounts**: `SUM(item.total_price)` for each account
  - **Transaction Cost**: `SUM(transaction.transaction_cost)`
  - **Cash Out**: `SUM(transaction.cash_out)`
  - **Cash In**: `SUM(transaction.cash_in)`
  - **Balance**: Current float balance

### Row 2: "Cash at Hand"
- **Purpose**: Physical cash verification
- **Background**: White
- **Content**:
  - **Chart of Account Columns**: Green checkmarks (✓) indicating verification
  - **Transaction Cost**: "-" (not applicable)
  - **Cash Out**: "-" (not applicable)
  - **Cash In**: "-" (not applicable)
  - **Balance**: Negative current balance (`-current_balance`)

**Why Negative Balance?**
In accounting, "Cash at Hand" represents the physical cash that needs to be accounted for. The negative balance indicates the amount that should be physically present to reconcile with the books.

### Row 3: "Petty Cash reimbursement"
- **Purpose**: Space for recording reimbursements or adjustments
- **Background**: Light gray (#f7fafc)
- **Content**:
  - **All Columns**: "-" (empty for manual entry)
  - **Balance**: Initial amount (`initial_amount`)

**Usage**: This row can be used to record:
- Reimbursements from the company
- Adjustments for discrepancies
- Additional funding

### Row 4: "Balance Carried forward"
- **Purpose**: Final reconciled balance
- **Background**: White with bold text
- **Content**:
  - **All Columns**: "-" (not applicable)
  - **Balance**: Current balance (`current_balance`)

**Significance**: This represents the final balance that will be carried forward to the next period.

## Calculation Logic

### Running Balance Calculation

```pseudocode
ALGORITHM: Calculate Running Balance
INPUT: initial_amount, transactions[]
OUTPUT: transactions[] with running_balance populated

BEGIN
    running_balance = initial_amount

    FOR each transaction IN transactions DO
        IF transaction.type = 'float_issuance' OR transaction.type = 'reimbursement' THEN
            running_balance = running_balance + transaction.total_amount
        ELSE
            running_balance = running_balance - transaction.total_amount
            IF transaction.transaction_cost EXISTS THEN
                running_balance = running_balance - transaction.transaction_cost
            END IF
        END IF

        transaction.running_balance = running_balance
    END FOR
END
```

### Cash In/Out Logic

#### Cash In Transactions:
- **Float Issuance**: `cash_in = total_amount`
- **Reimbursement**: `cash_in = total_amount`

#### Cash Out Transactions:
- **Expense**: `cash_out = total_amount`
- **Disbursement**: `cash_out = total_amount`
- **Float Return**: `cash_out = total_amount + transaction_cost`

### Chart of Account Totals

```pseudocode
ALGORITHM: Calculate Chart of Account Totals
INPUT: chartOfAccounts[], transactions[]
OUTPUT: account_totals[]

BEGIN
    FOR each account IN chartOfAccounts DO
        total = 0
        FOR each transaction IN transactions DO
            FOR each item IN transaction.items DO
                IF item.chart_of_account_id = account.id THEN
                    total = total + item.total_price
                END IF
            END FOR
        END FOR
        account_totals[account.id] = total
    END FOR
END
```

## Alert System

### Low Balance Alert
- **Trigger**: When `running_balance < alert_threshold`
- **Display**: Red text color for balance
- **Purpose**: Warns when float is running low

### Threshold Configuration
- **Setting**: Configurable per cash float
- **Default**: Usually 10-20% of initial amount
- **Management**: Set by Finance Managers or Organization Admins

## Transaction Types

### 1. **float_issuance**
- **Description**: Money added to the float
- **Effect**: Increases balance
- **Cash Flow**: Cash In

### 2. **expense**
- **Description**: Regular expenses paid from float
- **Effect**: Decreases balance
- **Cash Flow**: Cash Out

### 3. **disbursement**
- **Description**: Approved requisition payments
- **Effect**: Decreases balance
- **Cash Flow**: Cash Out

### 4. **reimbursement**
- **Description**: Money returned to float
- **Effect**: Increases balance
- **Cash Flow**: Cash In

### 5. **float_return**
- **Description**: Unused float returned to company
- **Effect**: Decreases balance
- **Cash Flow**: Cash Out

## Reconciliation Process

```mermaid
flowchart TD
    A[Start Reconciliation] --> B[Count Physical Cash]
    B --> C[Verify Receipts]
    C --> D[Check Transaction Records]
    D --> E{Balances Match?}

    E -->|Yes| F[Mark Verification ✓]
    E -->|No| G[Investigate Discrepancy]

    G --> H[Find Missing Transactions]
    G --> I[Check Calculation Errors]
    G --> J[Verify Physical Count]

    H --> K[Add Missing Transactions]
    I --> L[Correct Calculations]
    J --> M[Recount Cash]

    K --> N[Update Records]
    L --> N
    M --> N

    N --> O[Record Adjustments]
    O --> P[Complete Reconciliation]

    F --> P
    P --> Q[Generate Report]
```

### Daily Reconciliation
1. **Count Physical Cash**: Verify actual cash on hand
2. **Check Receipts**: Ensure all expenses have supporting documentation
3. **Verify Transactions**: Confirm all transactions are recorded
4. **Mark Verification**: Use checkmarks (✓) to indicate verification
5. **Record Discrepancies**: Use reimbursement row for adjustments

### Monthly Reconciliation
1. **Generate Report**: Export PDF for the month
2. **Management Review**: Finance Manager reviews all transactions
3. **Approval Process**: Get necessary approvals for large expenses
4. **Archive Records**: Store completed reconciliation reports

## Best Practices

### For Users
- **Record Immediately**: Enter transactions as soon as they occur
- **Keep Receipts**: Maintain physical receipts for all expenses
- **Regular Counts**: Count cash daily or weekly
- **Document Everything**: Include detailed descriptions

### For Finance Managers
- **Set Appropriate Thresholds**: Configure alerts based on usage patterns
- **Regular Reviews**: Review transactions weekly
- **Audit Trail**: Maintain complete audit trail
- **Training**: Ensure users understand the system

### For Administrators
- **Backup Data**: Regular backups of transaction data
- **User Permissions**: Proper role-based access control
- **System Monitoring**: Monitor for unusual patterns
- **Compliance**: Ensure compliance with accounting standards

## Troubleshooting

### Common Issues

#### Balance Discrepancies
- **Cause**: Missing transactions or incorrect amounts
- **Solution**: Review all transactions and physical receipts

#### Missing Transactions
- **Cause**: User forgot to record expense
- **Solution**: Add missing transaction with correct date

#### Calculation Errors
- **Cause**: System bug or data corruption
- **Solution**: Contact system administrator

### Data Integrity
- **Validation**: System validates all calculations
- **Audit Trail**: Complete history of all changes
- **Backup**: Regular automated backups
- **Recovery**: Point-in-time recovery capabilities

## Reporting Features

### PDF Export
- **Complete Log**: Full transaction history
- **Reconciliation**: Includes reconciliation section
- **Professional Format**: Suitable for audits and management review
- **Date Filtering**: Export specific date ranges

### Dashboard Integration
- **Real-time Balance**: Current balance display
- **Alert Notifications**: Low balance warnings
- **Quick Actions**: Easy access to common functions
- **Summary Statistics**: Key metrics and trends

This documentation provides a comprehensive understanding of the Cash Float Log system, enabling users to effectively manage petty cash operations while maintaining proper financial controls and audit trails.
