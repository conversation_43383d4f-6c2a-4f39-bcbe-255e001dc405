# Store Requisition Notification System

## Overview

The Store Requisition Notification System is a comprehensive event-driven notification framework that provides real-time updates to users throughout the store requisition workflow. The system uses <PERSON><PERSON>'s event-listener pattern to decouple notification logic from business logic, ensuring maintainable and scalable code.

## Architecture

The notification system follows a clean event-driven architecture:

```
Controller Action → Event Dispatch → Listener → Notifications → Users
```

### Core Components

1. **Events** - Represent state changes in the store requisition workflow
2. **Listeners** - Handle events and determine who should be notified
3. **Notifications** - Define the content and delivery channels for messages
4. **Email Templates** - Provide formatted email content for notifications

## Events

The system dispatches four main events that correspond to key workflow transitions:

### 1. StoreRequisitionSubmitted
**Triggered when**: A draft requisition is submitted for approval
**Location**: `StoreRequisitionController::submit()`
**Data**: Store requisition object

### 2. StoreRequisitionApproved  
**Triggered when**: A pending requisition is approved
**Location**: `StoreRequisitionController::approve()`
**Data**: Store requisition object, approver user

### 3. StoreRequisitionRejected
**Triggered when**: A pending requisition is rejected
**Location**: `StoreRequisitionController::reject()`
**Data**: Store requisition object, rejector user, rejection reason

### 4. StoreRequisitionIssued
**Triggered when**: An approved requisition has items issued
**Location**: `StoreRequisitionController::issue()`
**Data**: Store requisition object, issuer user, partial issue flag

## Listener: SendStoreRequisitionNotifications

The central listener (`app/Listeners/SendStoreRequisitionNotifications.php`) handles all store requisition events and implements sophisticated notification routing logic.

### Key Features

- **Role-based notification routing** - Different users receive different notifications based on their roles
- **Smart approver discovery** - Automatically finds the correct approvers based on requester role
- **Duplicate prevention** - Prevents users from receiving multiple notifications for the same action
- **Comprehensive logging** - Logs all notification activities for audit purposes

### Notification Routing Logic

#### For StoreRequisitionSubmitted:
- **Employee/HOD Requisitions**: Notify all Store Keepers in the organization
- **Store Keeper Requisitions**: Notify Finance Managers and Organization Admins

#### For StoreRequisitionApproved:
- **Always**: Notify the original requester
- **If approver is not a store keeper**: Also notify Store Keepers for issuing

#### For StoreRequisitionRejected:
- **Always**: Notify the original requester with rejection reason

#### For StoreRequisitionIssued:
- **Always**: Notify the original requester
- **If different from issuer**: Notify the approver
- **Always**: Notify all overseers (Finance Managers, Organization Admins) except those already involved

## Notifications

The system includes four notification classes, each handling specific workflow states:

### 1. StoreRequisitionPendingApproval
**Purpose**: Inform approvers that a requisition needs their attention
**Recipients**: Store Keepers (for employee requests) or Overseers (for store keeper requests)
**Channels**: Database, Email

### 2. StoreRequisitionApproved
**Purpose**: Confirm approval to requester and inform store keepers for issuing
**Recipients**: Original requester, Store Keepers (if needed)
**Channels**: Database, Email

### 3. StoreRequisitionRejected
**Purpose**: Inform requester of rejection with reason
**Recipients**: Original requester
**Channels**: Database, Email
**Special**: Includes rejection reason in message

### 4. StoreRequisitionIssued
**Purpose**: Confirm item issuance and provide oversight visibility
**Recipients**: Original requester, Approver (if different), Overseers
**Channels**: Database, Email
**Special**: Indicates if partial or full issue

## Notification Structure

Each notification provides consistent data structure:

### Database Notification Format
```json
{
  "title": "Store Requisition [Status]",
  "message": "Descriptive message with context",
  "type": "store_requisition_[action]",
  "store_requisition_id": 123,
  "action_url": "/store-requisitions/123",
  "metadata": {
    "requisition_id": 123,
    "purpose": "Office supplies",
    "total_items": 5,
    "department_name": "IT Department",
    "requester_name": "John Doe",
    "timestamp": "Dec 9, 2024 2:30 PM"
  }
}
```

### Email Notification Features
- **Responsive design** using Laravel's markdown templates
- **Contextual information** including requisition details and action buttons
- **Professional formatting** with organization branding
- **Clear call-to-action** buttons linking to the requisition

## Email Templates

Located in `resources/views/emails/store-requisitions/`:

- `pending-approval.blade.php` - For approval requests
- `approved.blade.php` - For approval confirmations
- `rejected.blade.php` - For rejection notifications
- `issued.blade.php` - For issuance confirmations

Each template includes:
- Requisition summary information
- Relevant action buttons
- Professional styling
- Responsive design for mobile devices

## Role-Based Access Control

The notification system respects the existing role hierarchy:

### Roles and Permissions
- **Employee**: Can create requisitions, receives status updates
- **Store Keeper**: Can approve employee requisitions, issue items, receives approval requests
- **Finance Manager/Organization Admin**: Can approve store keeper requisitions, receives oversight notifications

### Smart Approver Discovery
The system automatically determines the correct approvers:

```php
// For employee requisitions
$approvers = User::whereHas('roles', function ($query) {
    $query->where('name', 'Store Keeper');
})->whereHas('organizations', function ($query) use ($storeRequisition) {
    $query->where('organizations.id', $storeRequisition->organization_id);
})->get();

// For store keeper requisitions  
$approvers = User::whereHas('roles', function ($query) {
    $query->whereIn('name', ['Finance Manager', 'Organization Admin']);
})->whereHas('organizations', function ($query) use ($storeRequisition) {
    $query->where('organizations.id', $storeRequisition->organization_id);
})->get();
```

## Integration Points

### Controller Integration
Events are dispatched at key points in the workflow:

```php
// In StoreRequisitionController
public function submit(StoreRequisition $storeRequisition)
{
    // ... business logic ...
    StoreRequisitionSubmitted::dispatch($storeRequisition);
    // ... response ...
}

public function approve(Request $request, StoreRequisition $storeRequisition)
{
    // ... business logic ...
    StoreRequisitionApproved::dispatch($storeRequisition, $user);
    // ... response ...
}
```

### Event Service Provider Registration
Events and listeners are registered in `app/Providers/EventServiceProvider.php`:

```php
protected $listen = [
    StoreRequisitionSubmitted::class => [
        SendStoreRequisitionNotifications::class,
    ],
    StoreRequisitionApproved::class => [
        SendStoreRequisitionNotifications::class,
    ],
    // ... other events
];
```

## Logging and Monitoring

The system provides comprehensive logging for monitoring and debugging:

### Log Entries Include
- Event reception with requisition and user IDs
- Approver discovery results
- Notification dispatch confirmations
- Error handling and edge cases

### Example Log Output
```
[INFO] StoreRequisitionSubmitted event received
[INFO] Found 3 approvers for store requisition 123
[INFO] Sending pending approval notification to approver 456
[INFO] Sent issued notification to overseer 789 for requisition 123
```

## Error Handling

The system includes robust error handling:

### Graceful Degradation
- **Missing users**: Safely handles deleted or inactive users
- **Permission changes**: Adapts to role/permission modifications
- **Network issues**: Queued notifications retry automatically
- **Invalid data**: Validates all inputs before processing

### Edge Cases Handled
- Requisitions without requesters (system-generated)
- Users with multiple roles
- Organizational structure changes
- Concurrent workflow state changes

## Performance Considerations

### Optimization Features
- **Queued notifications**: Email notifications are queued for background processing
- **Efficient queries**: Optimized database queries for approver discovery
- **Minimal data transfer**: Only essential data included in notifications
- **Caching**: User role information cached where appropriate

### Scalability
- **Event-driven architecture**: Decoupled components for easy scaling
- **Queue workers**: Background processing prevents UI blocking
- **Database indexing**: Optimized for notification queries
- **Batch processing**: Multiple notifications processed efficiently

## Testing

The notification system includes comprehensive test coverage:

- **Unit tests**: Individual notification classes and event handlers
- **Integration tests**: Complete workflow testing
- **Edge case tests**: Error conditions and boundary cases
- **Performance tests**: Load testing for high-volume scenarios

## Configuration

### Environment Variables
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Company Name"
```

### Queue Configuration
```env
QUEUE_CONNECTION=database
# or redis, sqs, etc.
```

## Maintenance

### Regular Tasks
- Monitor queue processing for email delivery
- Review notification logs for errors
- Update email templates as needed
- Verify role-based routing accuracy

### Troubleshooting
- Check queue worker status for email delivery issues
- Verify user roles and permissions for routing problems
- Review event dispatch points for missing notifications
- Validate email configuration for delivery failures

## Future Enhancements

### Planned Features
- **SMS notifications** for urgent approvals
- **Push notifications** for mobile app integration
- **Notification preferences** for user customization
- **Digest notifications** for summary reports
- **Webhook integration** for external systems

### Extensibility
The event-driven architecture makes it easy to add:
- New notification channels
- Additional workflow events
- Custom notification logic
- Third-party integrations
- Advanced routing rules

## Conclusion

The Store Requisition Notification System provides a robust, scalable, and maintainable solution for keeping users informed throughout the requisition workflow. Its event-driven architecture ensures loose coupling, while comprehensive testing guarantees reliability. The system enhances user experience by providing timely, relevant notifications while maintaining security and performance standards.
