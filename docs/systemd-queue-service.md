# Systemd Service for Laravel Queue Worker

## Create Service File
Create `/etc/systemd/system/laravel-queue.service`:

```ini
[Unit]
Description=Laravel Queue Worker
After=network.target

[Service]
Type=simple
User=fayaz
Group=fayaz
WorkingDirectory=/home/<USER>/sippar
ExecStart=/usr/bin/php /home/<USER>/sippar/artisan queue:work --tries=3 --timeout=90
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

## Enable and Start Service
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable service to start on boot
sudo systemctl enable laravel-queue

# Start the service
sudo systemctl start laravel-queue

# Check status
sudo systemctl status laravel-queue
```

## Management Commands
```bash
# Restart service
sudo systemctl restart laravel-queue

# Stop service
sudo systemctl stop laravel-queue

# View logs
sudo journalctl -u laravel-queue -f
```