# Transaction System Documentation

## Overview

The Transaction System is a comprehensive financial management module that handles all monetary movements within the organization. It supports multiple transaction types, from requisition-based disbursements to direct cash float operations, providing complete audit trails and workflow visibility.

**Current Version (v1)**: This version primarily focuses on **disbursement transactions** (from approved requisitions) and **float issuance transactions**. Other transaction types are supported but these two represent the core functionality.

**Future Version (v2)**: Automated M-Pesa transaction processing and enhanced payment integrations are planned for the next major version.

## Transaction Types

**Primary Transaction Types (v1)**:
- **Disbursement** (`disbursement`): Payments for approved requisitions - *Main transaction type*
- **Float Issuance** (`float_issuance`): Money issued to cash floats - *Main transaction type*

**Secondary Transaction Types (v1)**:
- **Reimbursement** (`reimbursement`): Money returned to cash float
- **Direct Expense** (`expense`): Direct expenses from cash float
- **Float Return** (`float_return`): Unused float returned to company
- **Other** (`other`): Miscellaneous transactions

```mermaid
graph TD
    A[Transaction Types v1] --> B[Primary Types]
    A --> C[Secondary Types]

    B --> B1[Disbursement]
    B --> B2[Float Issuance]

    C --> C1[Reimbursement]
    C --> C2[Direct Expense]
    C --> C3[Float Return]
    C --> C4[Other]

    B1 --> B1A[From Requisitions]
    B1 --> B1B[Full Approval Chain]
    B1 --> B1C[Has Line Items]

    B2 --> B2A[Admin Authorization]
    B2 --> B2B[Immediate Processing]
    B2 --> B2C[Balance Updates]

    style B1 fill:#e1f5fe
    style B2 fill:#e1f5fe
    style B fill:#bbdefb
```

### 1. **Disbursement Transactions**
- **Type**: `disbursement`
- **Purpose**: Payments for approved requisitions
- **Workflow**: Requisition → Approval → Transaction Creation → Payment Processing
- **Relationships**: Always linked to a requisition
- **Items**: Contains detailed line items from the requisition

#### Characteristics:
- **Requisition Required**: Yes
- **Approval Workflow**: Full approval chain
- **Items**: Multiple line items with chart of accounts
- **Attachments**: Receipts, invoices, supporting documents
- **Creator**: Usually Finance Manager or Cashier

### 2. **Float Issuance Transactions**
- **Type**: `float_issuance`
- **Purpose**: Initial funding or additional money added to cash floats
- **Workflow**: Direct creation by authorized personnel
- **Relationships**: Linked to cash float, no requisition
- **Items**: Usually none (single amount transaction)

#### Characteristics:
- **Requisition Required**: No
- **Approval Workflow**: None (direct authorization)
- **Items**: Typically empty
- **Attachments**: Authorization documents, bank transfer receipts
- **Creator**: Finance Manager, Organization Admin, or Platform Admin

### 3. **Reimbursement Transactions**
- **Type**: `reimbursement`
- **Purpose**: Money returned to cash float (unused funds, returns)
- **Workflow**: Direct creation when money is returned
- **Relationships**: Linked to cash float
- **Items**: May contain details of returned items

#### Characteristics:
- **Requisition Required**: No
- **Approval Workflow**: Minimal (verification only)
- **Items**: Optional (details of returned items)
- **Attachments**: Return receipts, justification documents
- **Creator**: Cash float holder or Finance Manager

### 4. **Direct Expense Transactions**
- **Type**: `expense`
- **Purpose**: Direct expenses paid from cash float without requisition
- **Workflow**: Direct creation for immediate expenses
- **Relationships**: Linked to cash float
- **Items**: Expense details with chart of accounts

#### Characteristics:
- **Requisition Required**: No
- **Approval Workflow**: Post-transaction approval
- **Items**: Detailed expense breakdown
- **Attachments**: Receipts, invoices
- **Creator**: Cash float holder

### 5. **Float Return Transactions**
- **Type**: `float_return`
- **Purpose**: Unused cash float returned to company
- **Workflow**: End-of-period or project completion return
- **Relationships**: Linked to cash float
- **Items**: Usually none (single amount)

#### Characteristics:
- **Requisition Required**: No
- **Approval Workflow**: Verification and approval
- **Items**: Typically empty
- **Attachments**: Return documentation, reconciliation reports
- **Creator**: Cash float holder or Finance Manager

### 6. **Other Transactions**
- **Type**: `other`
- **Purpose**: Miscellaneous financial transactions
- **Workflow**: Case-by-case basis
- **Relationships**: Variable
- **Items**: Depends on transaction nature

## Transaction Status Flow

### Status Types
1. **`opened`**: Transaction created, pending processing
2. **`updated`**: Transaction modified, pending re-approval
3. **`completed`**: Transaction fully processed and finalized

### Status Transitions

```mermaid
stateDiagram-v2
    [*] --> RequisitionApproved : Disbursement
    [*] --> DirectCreation : Non-Requisition

    RequisitionApproved --> opened : Transaction Created
    DirectCreation --> opened : Transaction Created

    opened --> updated : Payment Details Added
    opened --> completed : Direct Processing
    updated --> completed : Payment Processed

    completed --> [*]

    note right of opened : Pending Processing
    note right of updated : Payment Details Added
    note right of completed : Fully Processed
```

## Transaction Data Structure

```mermaid
erDiagram
    TRANSACTION {
        int id PK
        string transaction_type
        string status
        decimal total_amount
        decimal transaction_cost
        datetime created_at
        datetime updated_at
        int requisition_id FK
        int cash_float_id FK
        int creator_id FK
        string payment_method
        text account_details
        string disbursement_transaction_id
        text description
        string reference_number
    }

    TRANSACTION_ITEM {
        int id PK
        int transaction_id FK
        int chart_of_account_id FK
        string description
        int quantity
        decimal unit_price
        decimal total_price
        string reference_number
    }

    REQUISITION {
        int id PK
        string requisition_number
        text purpose
        int requester_id FK
        int department_id FK
        int organization_id FK
    }

    CASH_FLOAT {
        int id PK
        string name
        decimal current_balance
        int organization_id FK
    }

    ATTACHMENT {
        int id PK
        int transaction_id FK
        string original_name
        int file_size
        string mime_type
        text description
        boolean is_evidence
        datetime created_at
        int uploader_id FK
    }

    TRANSACTION ||--o{ TRANSACTION_ITEM : contains
    TRANSACTION }o--|| REQUISITION : "may have"
    TRANSACTION }o--|| CASH_FLOAT : "may affect"
    TRANSACTION ||--o{ ATTACHMENT : "may have"
```

### Core Fields
- **`id`**: Unique transaction identifier
- **`transaction_type`**: Type of transaction (disbursement, float_issuance, etc.)
- **`status`**: Current status (opened, updated, completed)
- **`total_amount`**: Total transaction amount
- **`transaction_cost`**: Additional fees or processing costs
- **`created_at`**: Transaction creation timestamp
- **`updated_at`**: Last modification timestamp

### Relationship Fields
- **`requisition_id`**: Link to requisition (nullable for non-requisition transactions)
- **`cash_float_id`**: Link to cash float (nullable for non-float transactions)
- **`creator_id`**: User who created the transaction

### Payment Fields
- **`payment_method`**: Method of payment (M-Pesa, Bank Transfer, etc.)
- **`account_details`**: Payment account information
- **`disbursement_transaction_id`**: External payment system reference

### Optional Fields
- **`description`**: Transaction description (for non-requisition transactions)
- **`reference_number`**: External reference number

## Transaction Items

### Structure
Each transaction can have multiple items, each containing:
- **`description`**: Item description
- **`chart_of_account_id`**: Expense category
- **`quantity`**: Number of items
- **`unit_price`**: Price per unit
- **`total_price`**: Total cost for this item
- **`reference_number`**: Item-specific reference

### Chart of Accounts Integration
Items are categorized using the Chart of Accounts system:
- **Office Supplies**: Stationery, equipment, etc.
- **Travel**: Transportation, accommodation
- **Meals**: Food and beverage expenses
- **Utilities**: Phone, internet, electricity
- **Professional Services**: Consulting, legal fees
- **And more**: Customizable categories per organization

## Workflow and Approval System

### Requisition-Based Workflow

```mermaid
sequenceDiagram
    participant E as Employee
    participant S as System
    participant A as Approver
    participant F as Finance Manager
    participant P as Payment System

    E->>S: Create Requisition
    S->>S: Validate Items & Amounts
    S->>A: Send for Approval

    loop Approval Process
        A->>S: Review Requisition
        alt Approved
            A->>S: Approve with Comments
        else Rejected
            A->>S: Reject with Reason
            S->>E: Notify Rejection
        else Needs Changes
            A->>S: Request Modifications
            S->>E: Request Changes
            E->>S: Submit Modifications
        end
    end

    S->>S: Create Transaction (status: opened)
    S->>F: Notify Transaction Created

    F->>S: Add Payment Details
    S->>S: Update Status (status: updated)

    F->>P: Process Payment
    P->>S: Payment Confirmation
    S->>S: Complete Transaction (status: completed)

    S->>E: Notify Completion
```

### Non-Requisition Workflow

```mermaid
sequenceDiagram
    participant U as Authorized User
    participant S as System
    participant F as Finance Team
    participant CF as Cash Float
    participant AS as Accounting System

    U->>S: Create Transaction Directly
    S->>S: Validate User Permissions
    S->>S: Check Amount Limits

    alt Amount Within Limits
        S->>S: Auto-Approve (status: opened)
    else Amount Exceeds Limits
        S->>F: Send for Verification
        F->>S: Review & Approve/Reject
    end

    F->>S: Verify Transaction Details
    F->>S: Review Supporting Documents

    alt Approved
        F->>S: Grant Approval
        S->>CF: Update Cash Float Balance
        S->>AS: Record in Accounting
        S->>S: Mark as Completed
    else Needs Modification
        F->>U: Request Changes
        U->>S: Submit Modifications
    end

    S->>U: Notify Completion
```

## Attachment Management

### Supported Attachments
- **Receipts**: Proof of purchase
- **Invoices**: Vendor bills
- **Authorization**: Approval documents
- **Bank Statements**: Payment confirmations
- **Photos**: Visual evidence
- **Contracts**: Service agreements

### Upload Permissions
- **Platform Admins**: Full access to all transactions
- **Organization Admins**: Access to organization transactions
- **Finance Managers**: Access to financial transactions
- **Cashiers**: Access to cash float transactions
- **Requesters**: Access to their own transactions

### File Management
- **File Types**: PDF, images, documents
- **Size Limits**: Configurable per organization
- **Virus Scanning**: Automatic security checks
- **Version Control**: Track file updates
- **Download Tracking**: Audit file access

## Transaction Show Page Features

### Enhanced Interface

```mermaid
graph TD
    A[Transaction Show Page] --> B[Tab Navigation]

    B --> C[Details Tab]
    B --> D[Items Tab]
    B --> E[Workflow History Tab]
    B --> F[Attachments Tab]

    C --> C1[Transaction Type Info]
    C --> C2[Basic Information]
    C --> C3[Requisition Section]
    C --> C4[Cash Float Section]
    C --> C5[Payment Details]

    D --> D1[Item List Table]
    D --> D2[Chart of Accounts]
    D --> D3[Quantities & Prices]
    D --> D4[Total Calculations]

    E --> E1[Combined Timeline]
    E --> E2[Requisition History]
    E --> E3[Approval History]
    E --> E4[Visual Timeline]

    F --> F1[Attachment List]
    F --> F2[File Upload]
    F --> F3[Permission Controls]
    F --> F4[Download Links]

    C3 -.->|if requisition exists| G[Requisition Link]
    C4 -.->|if cash float exists| H[Cash Float Link]
    D -.->|if items exist| I[Show Items Tab]
    E -.->|if history exists| J[Show History Tab]
```

#### Dynamic Content
- **Transaction Type Specific**: Shows relevant information based on type
- **Conditional Sections**: Only displays applicable sections
- **Smart Navigation**: Links to related records (requisitions, cash floats)

### Transaction Details Tab

#### Basic Information
- **Transaction Type**: Clear labeling with description
- **Status**: Visual status badges
- **Amount**: Total transaction amount
- **Creation Date**: When transaction was created
- **Creator**: Who initiated the transaction

#### Requisition Information (if applicable)
- **Requisition Number**: Clickable link to requisition
- **Purpose**: Business justification
- **Requester**: Employee who requested
- **Department**: Requesting department

#### Cash Float Information (if applicable)
- **Float Name**: Clickable link to cash float
- **Current Balance**: Real-time balance
- **Organization**: Float's organization

#### Payment Details
- **Payment Method**: How payment was made
- **Transaction Cost**: Additional fees
- **Disbursement ID**: External payment reference
- **Account Details**: Payment account information

### Workflow History Tab

#### Complete Timeline
- **Chronological Order**: All events from creation to completion
- **Visual Timeline**: Icons and connecting lines
- **User Actions**: Who did what and when
- **Comments**: Feedback and notes at each step

#### History Types
- **Requisition History**: Creation, modifications, submissions
- **Approval History**: Each approval step with details
- **Transaction History**: Payment processing steps

#### Visual Elements
- **Icons**: Different icons for different actions
- **Color Coding**: Status-based color schemes
- **Badges**: Action type indicators
- **Timestamps**: Precise timing information

### Attachments Tab

#### File Listing
- **File Names**: Original file names preserved
- **Upload Information**: Who uploaded and when
- **File Sizes**: Storage space tracking
- **File Types**: MIME type identification

#### Upload Interface
- **Drag and Drop**: Easy file upload
- **Multiple Files**: Batch upload support
- **Progress Tracking**: Upload status indication
- **Error Handling**: Clear error messages

#### Permission-Based Access
- **View Only**: For users with read access
- **Upload Enabled**: For users with write access
- **Delete Capability**: For authorized users only

## Security and Permissions

### Access Control

#### Transaction Viewing
- **Platform Admins**: All transactions across all organizations
- **Organization Admins**: All transactions within their organization
- **Finance Managers**: Financial transactions within their scope
- **Department Heads**: Transactions related to their department
- **Employees**: Their own transaction requests

#### Transaction Creation
- **Finance Roles**: Can create most transaction types
- **Cash Float Holders**: Can create expense and reimbursement transactions
- **Authorized Personnel**: Can create float issuance transactions

#### Transaction Modification
- **Before Completion**: Limited modifications allowed
- **After Completion**: No modifications (audit integrity)
- **Correction Process**: New correcting transactions required

### Data Protection

#### Sensitive Information
- **Account Details**: Encrypted storage
- **Payment Information**: PCI compliance
- **Personal Data**: GDPR compliance
- **Audit Trails**: Immutable records

#### Access Logging
- **View Tracking**: Who viewed what and when
- **Modification Logs**: All changes recorded
- **Download Tracking**: File access monitoring
- **Failed Attempts**: Security incident logging

## Integration Points

```mermaid
graph TD
    A[Transaction System] --> B[Cash Float System]
    A --> C[Requisition System]
    A --> D[Chart of Accounts]
    A --> E[Payment Systems]

    B --> B1[Balance Updates]
    B --> B2[Transaction Logging]
    B --> B3[Reconciliation]
    B --> B4[Low Balance Alerts]

    C --> C1[Auto Transaction Creation]
    C --> C2[Status Synchronization]
    C --> C3[Item Mapping]
    C --> C4[Approval Inheritance]

    D --> D1[Expense Categorization]
    D --> D2[Financial Reporting]
    D --> D3[Budget Tracking]
    D --> D4[Compliance]

    E --> E1[M-Pesa Integration - v2]
    E --> E2[Bank APIs - v2]
    E --> E3[Payment Confirmation]
    E --> E4[Reconciliation]

    A -.->|Real-time| F[Notification System]
    A -.->|Audit| G[Logging System]
    A -.->|Reports| H[Reporting Engine]
```

### Cash Float System
- **Balance Updates**: Real-time balance calculations
- **Transaction Logging**: All movements recorded
- **Reconciliation**: Automated balance verification
- **Alerts**: Low balance notifications

### Requisition System
- **Automatic Creation**: Approved requisitions create transactions
- **Status Synchronization**: Bidirectional status updates
- **Item Mapping**: Requisition items become transaction items
- **Approval Inheritance**: Approval history carried forward

### Chart of Accounts
- **Expense Categorization**: Items mapped to account categories
- **Reporting Integration**: Financial reports by category
- **Budget Tracking**: Spending against budgets
- **Compliance**: Accounting standards adherence

### Payment Systems
- **Current (v1)**: Manual payment processing with reference tracking
- **Future (v2)**: Automated M-Pesa integration, bank APIs
- **Transaction Confirmation**: Manual confirmation (v1), automated status updates (v2)
- **Reconciliation**: Manual payment matching (v1), automated matching (v2)
- **Error Handling**: Basic error management (v1), comprehensive failed payment management (v2)

## Reporting and Analytics

### Transaction Reports
- **Summary Reports**: High-level transaction summaries
- **Detailed Reports**: Line-by-line transaction details
- **Trend Analysis**: Spending patterns over time
- **Exception Reports**: Unusual or problematic transactions

### Financial Analytics
- **Spending by Category**: Chart of accounts analysis
- **Department Spending**: Cost center analysis
- **Cash Flow**: Money movement tracking
- **Budget Variance**: Actual vs. planned spending

### Audit Reports
- **Complete Audit Trail**: All transaction activities
- **Approval Compliance**: Workflow adherence
- **Document Completeness**: Attachment verification
- **Regulatory Compliance**: Standards adherence

## Best Practices

### For Users
- **Complete Documentation**: Always attach supporting documents
- **Accurate Descriptions**: Clear, detailed transaction descriptions
- **Timely Processing**: Process transactions promptly
- **Regular Reconciliation**: Verify balances regularly

### For Finance Managers
- **Review Procedures**: Establish clear review processes
- **Approval Limits**: Set appropriate authorization limits
- **Regular Audits**: Conduct periodic transaction audits
- **Training**: Ensure users understand the system

### For Administrators
- **System Monitoring**: Monitor system performance and usage
- **Security Updates**: Keep security measures current
- **Backup Procedures**: Ensure data backup and recovery
- **User Management**: Maintain proper user access controls

## Transaction Processing Algorithm

```pseudocode
ALGORITHM: Process Transaction
INPUT: transaction_data, user_permissions
OUTPUT: processed_transaction OR error_message

BEGIN
    // Validate input data
    IF NOT validate_transaction_data(transaction_data) THEN
        RETURN error_message("Invalid transaction data")
    END IF

    // Check user permissions
    IF NOT check_user_permissions(user_permissions, transaction_data.type) THEN
        RETURN error_message("Insufficient permissions")
    END IF

    // Create transaction record
    transaction = create_transaction(transaction_data)

    // Process based on transaction type
    SWITCH transaction.type
        CASE "disbursement":
            result = process_disbursement(transaction)
        CASE "float_issuance":
            result = process_float_issuance(transaction)
        CASE "reimbursement":
            result = process_reimbursement(transaction)
        CASE "expense":
            result = process_expense(transaction)
        CASE "float_return":
            result = process_float_return(transaction)
        DEFAULT:
            result = process_other_transaction(transaction)
    END SWITCH

    // Update related systems
    IF result.success THEN
        update_cash_float_balance(transaction)
        update_chart_of_accounts(transaction)
        create_audit_log(transaction)
        send_notifications(transaction)
        RETURN result.transaction
    ELSE
        rollback_transaction(transaction)
        RETURN result.error_message
    END IF
END

FUNCTION process_disbursement(transaction)
BEGIN
    // Verify requisition is approved
    IF NOT requisition_is_approved(transaction.requisition_id) THEN
        RETURN error("Requisition not approved")
    END IF

    // Create transaction items from requisition
    items = map_requisition_items(transaction.requisition_id)
    transaction.items = items

    // Set initial status
    transaction.status = "opened"

    // Notify finance team
    notify_finance_team(transaction)

    RETURN success(transaction)
END

FUNCTION process_float_issuance(transaction)
BEGIN
    // Verify cash float exists
    cash_float = get_cash_float(transaction.cash_float_id)
    IF cash_float IS NULL THEN
        RETURN error("Cash float not found")
    END IF

    // Update cash float balance
    cash_float.current_balance = cash_float.current_balance + transaction.total_amount
    save_cash_float(cash_float)

    // Set status to completed (immediate processing)
    transaction.status = "completed"

    // Log cash movement
    log_cash_movement(transaction, "CASH_IN")

    RETURN success(transaction)
END
```

## Troubleshooting

### Common Issues

#### Transaction Creation Errors
- **Insufficient Permissions**: Check user roles and permissions
- **Missing Required Fields**: Ensure all mandatory fields completed
- **Invalid Amounts**: Verify amount formats and limits
- **System Connectivity**: Check network and system status

#### Workflow Problems
- **Stuck Approvals**: Check approver availability and notifications
- **Missing History**: Verify workflow configuration
- **Permission Errors**: Review user access rights
- **Status Inconsistencies**: Check system synchronization

#### Attachment Issues
- **Upload Failures**: Check file size and type restrictions
- **Permission Denied**: Verify user upload permissions
- **File Corruption**: Re-upload files if corrupted
- **Storage Limits**: Check available storage space

### Support Procedures
- **User Support**: First-level support for common issues
- **Technical Support**: System-level problem resolution
- **Escalation Process**: Complex issue handling
- **Documentation**: Maintain troubleshooting guides

## Version Roadmap

### Current Version (v1)
**Focus**: Core transaction processing with manual payment handling

**Primary Features**:
- **Disbursement Transactions**: Complete workflow from requisition approval to payment processing
- **Float Issuance Transactions**: Cash float funding and management
- Manual payment processing with reference tracking
- Comprehensive audit trails and workflow history
- Attachment management for supporting documents

**Transaction Types Emphasis**:
- **`disbursement`**: Main transaction type for approved requisitions
- **`float_issuance`**: Primary method for cash float funding
- Other types (`reimbursement`, `expense`, `float_return`, `other`) supported but secondary

### Future Version (v2)
**Focus**: Automated payment processing and enhanced integrations

**Planned Features**:
- **Automated M-Pesa Integration**: Direct API integration for payment processing
- **Real-time Payment Confirmation**: Automatic status updates from payment providers
- **Enhanced Reconciliation**: Automated matching of payments with transactions
- **Advanced Analytics**: Detailed spending patterns and financial insights
- **Multi-currency Support**: Support for different currencies and exchange rates
- **Bulk Payment Processing**: Batch processing for multiple transactions

**M-Pesa Integration (v2)**:
- Direct integration with M-Pesa API for disbursements
- Automatic transaction confirmation and status updates
- Real-time balance checking and validation
- Automated reconciliation with M-Pesa transaction records
- Enhanced error handling and retry mechanisms

This comprehensive transaction system provides robust financial management capabilities while maintaining security, compliance, and user-friendly operation across all transaction types and workflows.
