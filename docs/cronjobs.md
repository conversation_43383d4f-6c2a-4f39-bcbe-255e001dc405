# Cron Jobs Guide for Cash Float Alert System

## 📋 Overview
This guide covers the automated cash float monitoring system using cron jobs to detect and alert when cash float balances fall below configured thresholds.

## 🔧 System Components

### Files Responsible for Cron Jobs
- **Main Command**: `/home/<USER>/sippar/app/Console/Commands/CronCashFloatCheck.php`
- **Job Logic**: `/home/<USER>/sippar/app/Jobs/CheckLowCashFloats.php`
- **Notification**: `/home/<USER>/sippar/app/Notifications/LowCashFloatAlert.php`
- **Model Logic**: `/home/<USER>/sippar/app/Models/CashFloat.php` (shouldSendLowBalanceNotification method)
- **Cron Configuration**: System crontab (managed via `crontab -e`)

## 🚀 Setting Up Cron Jobs

### 1. View Current Cron Jobs
```bash
crontab -l
```

### 2. Edit Cron Jobs
```bash
crontab -e
```

### 3. Add Cron Jobs (Replace paths for production)

**⚠️ IMPORTANT: Replace `/home/<USER>/sippar` with your actual production path**

**Laravel Queue Processing (Every minute):**
```bash
* * * * * cd /home/<USER>/sippar && php artisan schedule:run >> /dev/null 2>&1
```

**Cash Float Monitoring (Every 30 minutes):**
```bash
*/30 * * * * cd /home/<USER>/sippar && php artisan cash-float:cron-check >/dev/null 2>&1
```

**For Production Deployment:**
```bash
# Example production paths - adjust as needed
* * * * * cd /var/www/your-app && /usr/bin/php artisan schedule:run >> /dev/null 2>&1
*/30 * * * * cd /var/www/your-app && /usr/bin/php artisan cash-float:cron-check >/dev/null 2>&1
```

**Automated Setup:**
Use the setup script for automatic path detection:
```bash
./scripts/setup-cron.sh
```

### 4. Cron Job Format Explanation
```
*/30 * * * * command
 │   │ │ │ │
 │   │ │ │ └── Day of week (0-7, Sunday = 0 or 7)
 │   │ │ └──── Month (1-12)
 │   │ └────── Day of month (1-31)
 │   └──────── Hour (0-23)
 └──────────── Minute (0-59)
```

## 🔍 Monitoring & Verification

### 1. Check if Cron Service is Running
```bash
# Check cron service status
sudo systemctl status cron

# Start cron service if not running
sudo systemctl start cron

# Enable cron to start on boot
sudo systemctl enable cron
```

### 2. View System Cron Logs
```bash
# View recent cron activity
grep CRON /var/log/syslog | tail -10

# Monitor cron logs in real-time
tail -f /var/log/syslog | grep CRON
```

### 3. Check Application Logs
```bash
# View cash float check logs
grep "scheduled cash float check" /home/<USER>/sippar/storage/logs/laravel.log

# Monitor application logs in real-time
tail -f /home/<USER>/sippar/storage/logs/laravel.log | grep "cash float"

# View all recent logs
tail -20 /home/<USER>/sippar/storage/logs/laravel.log
```

### 4. Verify Cash Float Monitoring
```bash
# Check current cash float status
cd /home/<USER>/sippar && php artisan cash-float:status

# View recent notifications
cd /home/<USER>/sippar && php artisan cash-float:check-notifications

# Check specific cash float notifications
cd /home/<USER>/sippar && php artisan cash-float:check-notifications --cash-float-id=15
```

## 🧪 Testing Commands

### 1. Manual Testing
```bash
# Test the cron command manually
cd /home/<USER>/sippar && php artisan cash-float:cron-check

# Test with debug output
cd /home/<USER>/sippar && php artisan cash-float:check-low --debug

# Check if notifications were created
cd /home/<USER>/sippar && php artisan cash-float:check-notifications
```

### 2. Continuous Monitoring (Alternative to Cron)
```bash
# Run continuous monitor (for development/testing)
cd /home/<USER>/sippar && php artisan cash-float:monitor --interval=300

# Run in background
cd /home/<USER>/sippar && nohup php artisan cash-float:monitor --interval=3600 > cash_float_monitor.log 2>&1 &
```

## 📊 How to Know Cron is Working

### 1. Check Cron Execution
```bash
# Look for your specific cron job in system logs
grep "cash-float:cron-check" /var/log/syslog

# Check if cron daemon is processing jobs
grep CRON /var/log/syslog | tail -5
```

### 2. Check Application Activity
```bash
# Look for scheduled execution logs
grep "Starting scheduled cash float check via cron" /home/<USER>/sippar/storage/logs/laravel.log

# Check completion logs
grep "Completed scheduled cash float check via cron" /home/<USER>/sippar/storage/logs/laravel.log
```

### 3. Verify Notifications
```bash
# Check database for recent notifications
cd /home/<USER>/sippar && php artisan tinker --execute="
echo 'Recent notifications: ' . DB::table('notifications')->where('type', 'App\\\Notifications\\\LowCashFloatAlert')->where('created_at', '>=', now()->subHours(2))->count() . PHP_EOL;
"
```

## 🚨 Troubleshooting

### 1. Cron Not Running
```bash
# Check if cron service is active
sudo systemctl status cron

# Restart cron service
sudo systemctl restart cron

# Check cron configuration
crontab -l
```

### 2. Command Not Executing
```bash
# Test command manually
cd /home/<USER>/sippar && php artisan cash-float:cron-check

# Check PHP path
which php

# Check Laravel installation
cd /home/<USER>/sippar && php artisan --version
```

### 3. No Notifications Generated
```bash
# Check cash float status
cd /home/<USER>/sippar && php artisan cash-float:status

# Test notification logic
cd /home/<USER>/sippar && php artisan cash-float:check-low --debug

# Check recent notifications
cd /home/<USER>/sippar && php artisan cash-float:check-notifications
```

### 4. Permission Issues
```bash
# Check file permissions
ls -la /home/<USER>/sippar/artisan

# Make artisan executable
chmod +x /home/<USER>/sippar/artisan

# Check storage permissions
ls -la /home/<USER>/sippar/storage/logs/
```

## 📝 Log File Locations

### System Logs
- **Cron Logs**: `/var/log/syslog` or `/var/log/cron.log`
- **System Messages**: `/var/log/messages`

### Application Logs
- **Laravel Logs**: `/home/<USER>/sippar/storage/logs/laravel.log`
- **Custom Monitor Logs**: `/home/<USER>/sippar/cash_float_monitor.log` (if using continuous monitor)

## ⚙️ Configuration

### Current Setup
- **Frequency**: Every 30 minutes (`*/30 * * * *`)
- **Command**: `php artisan cash-float:cron-check`
- **Working Directory**: `/home/<USER>/sippar`
- **Output**: Redirected to `/dev/null` (silent execution)

### Notification Settings
- **Cooldown Period**: 24 hours between notifications for same cash float
- **Target Recipients**: Finance Managers + Assigned Users
- **Notification Channels**: Email + Database (in-app notifications)

## 🔄 Maintenance Commands

### Daily Maintenance
```bash
# Check system status
cd /home/<USER>/sippar && php artisan cash-float:status

# View recent activity
grep "cash float check" /home/<USER>/sippar/storage/logs/laravel.log | tail -5
```

### Weekly Maintenance
```bash
# Clean old logs (optional)
cd /home/<USER>/sippar && php artisan log:clear

# Check cron job status
crontab -l
```

### Emergency Commands
```bash
# Stop continuous monitor (if running)
pkill -f "cash-float:monitor"

# Remove cron job temporarily
crontab -e  # Comment out the line with #

# Manual emergency check
cd /home/<USER>/sippar && php artisan cash-float:check-low --debug
```

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review application logs: `/home/<USER>/sippar/storage/logs/laravel.log`
3. Verify cron service is running: `sudo systemctl status cron`
4. Test commands manually before adding to cron

---

**Last Updated**: 2025-06-04  
**System Status**: ✅ Active and Monitoring  
**Next Check**: Every 30 minutes automatically

##
let add a notification from the Organization Admin to the users after they onboard the user,,,they send the username and email and tell them to change their password later
the organization admin is the one who adds other users to the platform
study all the files and then make the changes
#