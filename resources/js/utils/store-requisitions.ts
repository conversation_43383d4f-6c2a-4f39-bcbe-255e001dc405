import {
  StoreRequisition,
  StoreRequisitionItem,
  StoreRequisitionStatus,
  STORE_REQUISITION_STATUSES
} from '@/types/store-requisitions';

//  FORMATTING UTILITIES 

/**
 * Format currency amount with proper locale formatting
 */
export function formatCurrency(amount: number, currency: string = 'USD', locale: string = 'en-US'): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format number with thousands separator
 */
export function formatNumber(value: number, decimals: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

/**
 * Format date with locale formatting
 */
export function formatDate(date: string | Date, options?: Intl.DateTimeFormatOptions): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  
  return dateObj.toLocaleDateString('en-US', { ...defaultOptions, ...options });
}

/**
 * Format date and time
 */
export function formatDateTime(date: string | Date): string {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  
  return formatDate(dateObj);
}

//  CALCULATION UTILITIES 

/**
 * Calculate total quantity requested for store requisition items
 */
export function calculateTotalQuantityRequested(items: StoreRequisitionItem[]): number {
  return items.reduce((total, item) => total + item.quantity_requested, 0);
}

/**
 * Calculate total quantity issued for store requisition items
 */
export function calculateTotalQuantityIssued(items: StoreRequisitionItem[]): number {
  return items.reduce((total, item) => total + (item.quantity_issued || 0), 0);
}

/**
 * Calculate fulfillment percentage for a store requisition
 */
export function calculateFulfillmentPercentage(items: StoreRequisitionItem[]): number {
  const totalRequested = calculateTotalQuantityRequested(items);
  const totalIssued = calculateTotalQuantityIssued(items);
  
  if (totalRequested === 0) return 0;
  return Math.round((totalIssued / totalRequested) * 100);
}

/**
 * Check if all items in a store requisition are fully issued
 */
export function isFullyIssued(items: StoreRequisitionItem[]): boolean {
  return items.every(item => (item.quantity_issued || 0) >= item.quantity_requested);
}

/**
 * Check if any items in a store requisition are partially issued
 */
export function isPartiallyIssued(items: StoreRequisitionItem[]): boolean {
  return items.some(item => (item.quantity_issued || 0) > 0 && (item.quantity_issued || 0) < item.quantity_requested);
}

//  STATUS UTILITIES 

/**
 * Get human-readable status label for store requisitions
 */
export function getStoreRequisitionStatusLabel(status: StoreRequisitionStatus): string {
  return STORE_REQUISITION_STATUSES[status] || status;
}

/**
 * Get status color classes for badges (deprecated - use STORE_STATUS_STYLES instead)
 */
export function getStoreRequisitionStatusColor(status: StoreRequisitionStatus): string {
  // Fallback color mapping for legacy usage
  const colorMap: Record<StoreRequisitionStatus, string> = {
    draft: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
    pending_approval: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    approved: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    rejected: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    returned_for_revision: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    issued: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    partially_issued: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
  };

  return colorMap[status] || 'bg-gray-100 text-gray-800';
}

/**
 * Check if store requisition can be edited
 * Now supports draft, rejected, and returned_for_revision statuses
 */
export function canEditStoreRequisition(requisition: StoreRequisition, currentUserId: number): boolean {
  return (
    requisition.requester_user_id === currentUserId &&
    ['draft', 'rejected', 'returned_for_revision'].includes(requisition.status)
  );
}

/**
 * Check if store requisition can be submitted
 */
export function canSubmitStoreRequisition(requisition: StoreRequisition, currentUserId: number): boolean {
  return (
    requisition.requester_user_id === currentUserId &&
    requisition.status === 'draft'
  );
}

/**
 * Check if store requisition can be approved based on role-based workflow rules
 *
 * Approval Rules:
 * - Employee requisitions: Only Store Keepers can approve
 * - Store Keeper requisitions: Only Overseers, Finance Managers, or Organization Admins can approve
 * - Self-approval is always prevented
 */
export function canApproveStoreRequisition(
  requisition: StoreRequisition,
  currentUserId: number,
  userPermissions: string[],
  userRoles: string[],
  requesterPermissions?: string[]
): boolean {
  // Basic checks
  if (requisition.status !== 'pending_approval' || requisition.requester_user_id === currentUserId) {
    return false;
  }

  // Must have some form of approval permission
  const hasApprovalPermission = userPermissions.includes('approve-store-requisition') ||
                               userPermissions.includes('store-keep');

  if (!hasApprovalPermission) {
    return false;
  }

  // Determine if requester is a store keeper
  const requesterIsStoreKeeper = requesterPermissions?.includes('store-keep') || false;

  // Role-based approval logic
  if (requesterIsStoreKeeper) {
    // Store keeper requisitions can be approved by:
    // - Overseers (have approve-store-requisition but not store-keep)
    // - Finance Managers
    // - Organization Admins
    return (userPermissions.includes('approve-store-requisition') && !userPermissions.includes('store-keep')) ||
           userRoles.includes('Finance Manager') ||
           userRoles.includes('Organization Admin');
  } else {
    // Employee requisitions can only be approved by store keepers
    return userPermissions.includes('store-keep');
  }
}

/**
 * Check if store requisition can be rejected (same rules as approval)
 */
export function canRejectStoreRequisition(
  requisition: StoreRequisition,
  currentUserId: number,
  userPermissions: string[],
  userRoles: string[],
  requesterPermissions?: string[]
): boolean {
  return canApproveStoreRequisition(requisition, currentUserId, userPermissions, userRoles, requesterPermissions);
}

/**
 * Check if store requisition can be issued
 */
export function canIssueStoreRequisition(requisition: StoreRequisition): boolean {
  // Only approved requisitions can be issued
  return requisition.status === 'approved';
}

/**
 * Check if store requisition can be returned for revision
 */
export function canReturnStoreRequisitionForRevision(requisition: StoreRequisition, currentUserId: number): boolean {
  return (
    requisition.status === 'pending_approval' &&
    requisition.requester_user_id !== currentUserId
  );
}

/**
 * Get the appropriate edit route for a store requisition based on its status
 */
export function getStoreRequisitionEditRoute(requisition: StoreRequisition): string {
  if (requisition.status === 'draft') {
    return route('store-requisitions.edit', requisition.id);
  } else if (['rejected', 'returned_for_revision'].includes(requisition.status)) {
    return route('store-requisitions.edit-rejected', requisition.id);
  }
  throw new Error(`Cannot edit store requisition with status: ${requisition.status}`);
}

/**
 * Check if store requisition can be deleted
 */
export function canDeleteStoreRequisition(requisition: StoreRequisition, currentUserId: number): boolean {
  return (
    requisition.requester_user_id === currentUserId &&
    requisition.status === 'draft'
  );
}

//  VALIDATION UTILITIES 

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-()]+$/;
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}

/**
 * Check if value is a positive number
 */
export function isPositiveNumber(value: unknown): boolean {
  const num = Number(value);
  return !isNaN(num) && num > 0;
}

//  STRING UTILITIES 

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Convert string to title case
 */
export function toTitleCase(str: string): string {
  return str.replace(/\w\S*/g, (txt) =>
    txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
  );
}

/**
 * Generate initials from name
 */
export function getInitials(firstName: string, lastName: string): string {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
}

//  ARRAY UTILITIES 

/**
 * Sort array by multiple fields
 */
export function sortBy<T>(array: T[], ...sortKeys: (keyof T)[]): T[] {
  return [...array].sort((a, b) => {
    for (const key of sortKeys) {
      const aVal = a[key];
      const bVal = b[key];
      
      if (aVal < bVal) return -1;
      if (aVal > bVal) return 1;
    }
    return 0;
  });
}

/**
 * Group array by field
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = String(item[key]);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

//  URL UTILITIES 

/**
 * Build query string from object
 */
export function buildQueryString(params: Record<string, unknown>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(v => searchParams.append(key, String(v)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });
  
  return searchParams.toString();
}

/**
 * Parse query string to object
 */
export function parseQueryString(queryString: string): Record<string, unknown> {
  const params = new URLSearchParams(queryString);
  const result: Record<string, unknown> = {};
  
  for (const [key, value] of params.entries()) {
    if (result[key]) {
      if (Array.isArray(result[key])) {
        result[key].push(value);
      } else {
        result[key] = [result[key], value];
      }
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

//  FILE UTILITIES 

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * Check if file type is allowed
 */
export function isAllowedFileType(filename: string, allowedTypes: string[]): boolean {
  const extension = getFileExtension(filename).toLowerCase();
  return allowedTypes.includes(extension);
}

//  DEBOUNCE UTILITY 

/**
 * Debounce function to limit API calls
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

//  HISTORY UTILITIES

/**
 * Format store requisition history action for display
 */
export function formatStoreRequisitionHistoryAction(action: string): string {
  const actionMap: Record<string, string> = {
    'created_as_draft': 'Created as Draft',
    'created_and_submitted': 'Created and Submitted',
    'edited': 'Edited',
    'edited_and_submitted': 'Edited and Submitted',
    'edited_and_resubmitted': 'Edited and Resubmitted',
    'submitted_for_approval': 'Submitted for Approval',
    'approved': 'Approved',
    'rejected': 'Rejected',
    'returned_for_revision': 'Returned for Revision',
    'issued': 'Items Issued',
  };

  return actionMap[action] || action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Get icon name for store requisition history action
 */
export function getStoreRequisitionHistoryIconName(action: string): string {
  switch (action) {
    case 'created_as_draft':
    case 'created_and_submitted':
      return 'PlusCircle';
    case 'edited':
    case 'edited_and_submitted':
    case 'edited_and_resubmitted':
      return 'Edit';
    case 'submitted_for_approval':
      return 'Send';
    case 'approved':
      return 'CheckCircle';
    case 'rejected':
      return 'XCircle';
    case 'returned_for_revision':
      return 'RotateCcw';
    case 'issued':
      return 'Package';
    default:
      return 'Clock';
  }
}

/**
 * Get badge variant for store requisition history action
 */
export function getStoreRequisitionHistoryBadgeVariant(action: string): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (action) {
    case 'created_as_draft':
    case 'edited':
      return 'secondary';
    case 'created_and_submitted':
    case 'edited_and_submitted':
    case 'edited_and_resubmitted':
    case 'submitted_for_approval':
      return 'outline';
    case 'approved':
    case 'issued':
      return 'default';
    case 'rejected':
      return 'destructive';
    case 'returned_for_revision':
      return 'outline';
    default:
      return 'outline';
  }
}
