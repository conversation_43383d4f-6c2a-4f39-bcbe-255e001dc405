/**
 * Password validation utilities for client-side validation
 */

export interface PasswordRequirement {
    id: string;
    label: string;
    test: (password: string) => boolean;
    met: boolean;
}

export interface PasswordValidationResult {
    isValid: boolean;
    requirements: PasswordRequirement[];
    errors: string[];
}

/**
 * Common passwords to reject
 */
const COMMON_PASSWORDS = [
    'password', 'password123', '123456', '123456789', 'qwerty', 'abc123',
    'password1', 'admin', 'letmein', 'welcome', 'monkey', '1234567890',
    'dragon', 'master', 'hello', 'freedom', 'whatever', 'qazwsx',
    'trustno1', 'jordan', 'harley', 'robert', 'matthew', 'daniel',
    'andrew', 'joshua', 'anthony', 'william', 'david', 'charles',
    'thomas', 'christopher', 'joseph', 'michael', 'john', 'james',
    'superman', 'batman', 'football', 'baseball', 'basketball',
    'soccer', 'hockey', 'tennis', 'golf', 'swimming', 'running'
];

/**
 * Password requirements configuration
 */
const PASSWORD_REQUIREMENTS: Omit<PasswordRequirement, 'met'>[] = [
    {
        id: 'length',
        label: 'At least 8 characters',
        test: (password: string) => password.length >= 8,
    },
    {
        id: 'uppercase',
        label: 'At least one uppercase letter (A-Z)',
        test: (password: string) => /[A-Z]/.test(password),
    },
    {
        id: 'lowercase',
        label: 'At least one lowercase letter (a-z)',
        test: (password: string) => /[a-z]/.test(password),
    },
    {
        id: 'number',
        label: 'At least one number (0-9)',
        test: (password: string) => /[0-9]/.test(password),
    },
    {
        id: 'special',
        label: 'At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)',
        test: (password: string) => /[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password),
    },
    {
        id: 'common',
        label: 'Not a commonly used password',
        test: (password: string) => !COMMON_PASSWORDS.includes(password.toLowerCase()),
    },
];

/**
 * Validate a password against all requirements
 */
export function validatePassword(password: string): PasswordValidationResult {
    const requirements = PASSWORD_REQUIREMENTS.map(req => ({
        ...req,
        met: req.test(password),
    }));

    const unmetRequirements = requirements.filter(req => !req.met);
    const errors = unmetRequirements.map(req => req.label);

    return {
        isValid: unmetRequirements.length === 0,
        requirements,
        errors,
    };
}

/**
 * Get a simple validation message for display
 */
export function getPasswordValidationMessage(password: string): string {
    if (!password) {
        return '';
    }

    const result = validatePassword(password);
    
    if (result.isValid) {
        return '';
    }

    if (result.errors.length === 1) {
        return `Password must contain ${result.errors[0].toLowerCase()}.`;
    }

    if (result.errors.length === 2) {
        return `Password must contain ${result.errors[0].toLowerCase()} and ${result.errors[1].toLowerCase()}.`;
    }

    const lastError = result.errors.pop()!;
    return `Password must contain ${result.errors.map(e => e.toLowerCase()).join(', ')}, and ${lastError.toLowerCase()}.`;
}

/**
 * Check if passwords match
 */
export function validatePasswordConfirmation(password: string, confirmation: string): boolean {
    return password === confirmation;
}

/**
 * Get password confirmation validation message
 */
export function getPasswordConfirmationMessage(password: string, confirmation: string): string {
    if (!confirmation) {
        return '';
    }

    if (!validatePasswordConfirmation(password, confirmation)) {
        return 'Passwords do not match.';
    }

    return '';
}

/**
 * Calculate password strength score (0-100)
 */
export function calculatePasswordStrength(password: string): number {
    if (!password) return 0;

    const result = validatePassword(password);
    const metRequirements = result.requirements.filter(req => req.met).length;
    const totalRequirements = result.requirements.length;

    return Math.round((metRequirements / totalRequirements) * 100);
}

/**
 * Get password strength label
 */
export function getPasswordStrengthLabel(score: number): string {
    if (score === 0) return '';
    if (score < 40) return 'Weak';
    if (score < 70) return 'Fair';
    if (score < 90) return 'Good';
    return 'Strong';
}

/**
 * Get password strength color class
 */
export function getPasswordStrengthColor(score: number): string {
    if (score === 0) return '';
    if (score < 40) return 'text-red-500';
    if (score < 70) return 'text-yellow-500';
    if (score < 90) return 'text-blue-500';
    return 'text-green-500';
}
