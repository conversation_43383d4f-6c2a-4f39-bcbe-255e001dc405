import {
    StoreRequisition,
    StoreRequisitionItem,
    StoreRequisitionIssueItemData,
    StockValidationResponse,
    StockValidationError,
    StockValidationWarning,
    InventoryItem
} from '@/types/store-requisitions';

/**
 * Validate stock availability for issuing items
 */
export async function validateStockAvailability(
    requisitionId: number,
    items: StoreRequisitionIssueItemData[]
): Promise<StockValidationResponse> {
    try {
        const response = await fetch('/api/store-requisitions/validate-stock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: JSON.stringify({
                requisition_id: requisitionId,
                items: items
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Stock validation failed:', error);
        return {
            valid: false,
            errors: [{
                item_id: 0,
                item_name: 'System',
                available: 0,
                requested: 0,
                message: 'Failed to validate stock. Please try again.'
            }],
            warnings: []
        };
    }
}

/**
 * Check if an item can be issued with the specified quantity
 */
export function canIssueItem(
    item: StoreRequisitionItem,
    quantityToIssue: number
): { canIssue: boolean; reason?: string } {
    const inventoryItem = (item as { inventory_item?: InventoryItem; inventoryItem?: InventoryItem }).inventory_item || (item as { inventoryItem?: InventoryItem }).inventoryItem;

    if (!inventoryItem) {
        return { canIssue: false, reason: 'Inventory item not found' };
    }

    // Check if quantity is positive
    if (quantityToIssue <= 0) {
        return { canIssue: false, reason: 'Quantity must be greater than 0' };
    }

    // Check if sufficient stock is available
    if (inventoryItem.quantity_on_hand < quantityToIssue) {
        return {
            canIssue: false,
            reason: `Insufficient stock. Available: ${inventoryItem.quantity_on_hand}, Requested: ${quantityToIssue}`
        };
    }

    // Check if issuing exceeds requested quantity
    const currentlyIssued = item.quantity_issued || 0;
    const totalToBeIssued = currentlyIssued + quantityToIssue;
    
    if (totalToBeIssued > item.quantity_requested) {
        return { 
            canIssue: false, 
            reason: `Cannot issue more than requested. Requested: ${item.quantity_requested}, Already issued: ${currentlyIssued}` 
        };
    }

    return { canIssue: true };
}

/**
 * Get the maximum quantity that can be issued for an item
 */
export function getMaxIssuableQuantity(item: StoreRequisitionItem): number {
    const inventoryItem = (item as { inventory_item?: InventoryItem; inventoryItem?: InventoryItem }).inventory_item || (item as { inventoryItem?: InventoryItem }).inventoryItem;

    if (!inventoryItem) {
        return 0;
    }

    const currentlyIssued = item.quantity_issued || 0;
    const remainingToIssue = item.quantity_requested - currentlyIssued;
    const availableStock = inventoryItem.quantity_on_hand;

    return Math.min(remainingToIssue, availableStock);
}

/**
 * Check if issuing will trigger low stock warning
 */
export function willTriggerLowStockWarning(
    item: StoreRequisitionItem,
    quantityToIssue: number
): boolean {
    const inventoryItem = (item as { inventory_item?: InventoryItem; inventoryItem?: InventoryItem }).inventory_item || (item as { inventoryItem?: InventoryItem }).inventoryItem;

    if (!inventoryItem || inventoryItem.reorder_level <= 0) {
        return false;
    }

    const remainingStock = inventoryItem.quantity_on_hand - quantityToIssue;
    return remainingStock <= inventoryItem.reorder_level;
}

/**
 * Get issue status for an item
 */
export function getItemIssueStatus(
    item: StoreRequisitionItem,
    quantityToIssue: number
): { label: string; variant: 'default' | 'secondary' | 'outline' | 'destructive' } {
    const currentlyIssued = item.quantity_issued || 0;
    const totalToBeIssued = currentlyIssued + quantityToIssue;
    
    if (totalToBeIssued >= item.quantity_requested) {
        return { label: 'Full Issue', variant: 'default' };
    } else if (quantityToIssue > 0) {
        return { label: 'Partial Issue', variant: 'secondary' };
    } else {
        return { label: 'Not Issued', variant: 'outline' };
    }
}

/**
 * Calculate issue summary statistics
 */
export function calculateIssueSummary(
    requisition: StoreRequisition,
    issueItems: StoreRequisitionIssueItemData[]
): {
    totalItems: number;
    itemsToIssue: number;
    fullyIssuedItems: number;
    partiallyIssuedItems: number;
    notIssuedItems: number;
} {
    const totalItems = requisition.items?.length || 0;
    const itemsToIssue = issueItems.filter(item => item.quantity_issued > 0).length;
    
    let fullyIssuedItems = 0;
    let partiallyIssuedItems = 0;
    let notIssuedItems = 0;

    requisition.items?.forEach(item => {
        const issueItem = issueItems.find(i => i.id === item.id);
        const quantityToIssue = issueItem?.quantity_issued || 0;
        const currentlyIssued = item.quantity_issued || 0;
        const totalToBeIssued = currentlyIssued + quantityToIssue;

        if (totalToBeIssued >= item.quantity_requested) {
            fullyIssuedItems++;
        } else if (quantityToIssue > 0) {
            partiallyIssuedItems++;
        } else {
            notIssuedItems++;
        }
    });

    return {
        totalItems,
        itemsToIssue,
        fullyIssuedItems,
        partiallyIssuedItems,
        notIssuedItems
    };
}

/**
 * Format error messages for display
 */
export function formatStockValidationErrors(errors: StockValidationError[]): string[] {
    return errors.map(error => error.message);
}

/**
 * Format warning messages for display
 */
export function formatStockValidationWarnings(warnings: StockValidationWarning[]): string[] {
    return warnings.map(warning => warning.message);
}

/**
 * Check if all items in a requisition can be fully issued
 */
export function canFullyIssueRequisition(requisition: StoreRequisition): boolean {
    if (!requisition.items || requisition.items.length === 0) {
        return false;
    }

    return requisition.items.every(item => {
        const inventoryItem = (item as { inventory_item?: InventoryItem; inventoryItem?: InventoryItem }).inventory_item || (item as { inventoryItem?: InventoryItem }).inventoryItem;
        if (!inventoryItem) return false;

        const currentlyIssued = item.quantity_issued || 0;
        const remainingToIssue = item.quantity_requested - currentlyIssued;

        return inventoryItem.quantity_on_hand >= remainingToIssue;
    });
}

/**
 * Get items that cannot be fully issued due to insufficient stock
 */
export function getInsufficientStockItems(requisition: StoreRequisition): StoreRequisitionItem[] {
    if (!requisition.items) return [];

    return requisition.items.filter(item => {
        const inventoryItem = (item as { inventory_item?: InventoryItem; inventoryItem?: InventoryItem }).inventory_item || (item as { inventoryItem?: InventoryItem }).inventoryItem;
        if (!inventoryItem) return true;

        const currentlyIssued = item.quantity_issued || 0;
        const remainingToIssue = item.quantity_requested - currentlyIssued;

        return inventoryItem.quantity_on_hand < remainingToIssue;
    });
}
