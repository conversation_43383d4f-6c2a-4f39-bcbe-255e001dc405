/**
 * Password generator utility for creating secure passwords
 */

export interface PasswordGeneratorOptions {
    length?: number;
    includeUppercase?: boolean;
    includeLowercase?: boolean;
    includeNumbers?: boolean;
    includeSpecialChars?: boolean;
    excludeSimilar?: boolean;
    useReadableWords?: boolean;
}

/**
 * Word lists for readable password generation
 */
const WORD_LISTS = {
    adjectives: [
        'bright', 'clever', 'swift', 'brave', 'calm', 'bold', 'wise', 'kind',
        'quick', 'smart', 'strong', 'happy', 'lucky', 'magic', 'royal', 'super',
        'golden', 'silver', 'crystal', 'diamond', 'emerald', 'ruby', 'sapphire',
        'cosmic', 'stellar', 'lunar', 'solar', 'ocean', 'forest', 'mountain',
        'thunder', 'lightning', 'storm', 'wind', 'fire', 'ice', 'snow', 'rain'
    ],
    nouns: [
        'tiger', 'eagle', 'lion', 'wolf', 'bear', 'shark', 'falcon', 'dragon',
        'phoenix', 'unicorn', 'knight', 'wizard', 'warrior', 'guardian', 'hero',
        'champion', 'legend', 'master', 'sage', 'oracle', 'crown', 'sword',
        'shield', 'castle', 'tower', 'bridge', 'river', 'ocean', 'mountain',
        'valley', 'forest', 'garden', 'flower', 'star', 'moon', 'sun', 'comet',
        'galaxy', 'planet', 'cosmos', 'thunder', 'lightning', 'storm', 'wind'
    ]
};

/**
 * Character sets for password generation
 */
const CHARACTER_SETS = {
    uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    lowercase: 'abcdefghijklmnopqrstuvwxyz',
    numbers: '0123456789',
    specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
    // Characters that might be confused with each other
    similarChars: 'il1Lo0O',
};

/**
 * Default options for password generation
 */
const DEFAULT_OPTIONS: Required<PasswordGeneratorOptions> = {
    length: 12,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSpecialChars: true,
    excludeSimilar: true,
};

/**
 * Generate a secure random password
 */
export function generatePassword(options: PasswordGeneratorOptions = {}): string {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    
    // Build character set based on options
    let charset = '';
    const requiredChars: string[] = [];
    
    if (opts.includeUppercase) {
        const chars = opts.excludeSimilar 
            ? CHARACTER_SETS.uppercase.replace(/[LO]/g, '')
            : CHARACTER_SETS.uppercase;
        charset += chars;
        requiredChars.push(getRandomChar(chars));
    }
    
    if (opts.includeLowercase) {
        const chars = opts.excludeSimilar 
            ? CHARACTER_SETS.lowercase.replace(/[ilo]/g, '')
            : CHARACTER_SETS.lowercase;
        charset += chars;
        requiredChars.push(getRandomChar(chars));
    }
    
    if (opts.includeNumbers) {
        const chars = opts.excludeSimilar 
            ? CHARACTER_SETS.numbers.replace(/[10]/g, '')
            : CHARACTER_SETS.numbers;
        charset += chars;
        requiredChars.push(getRandomChar(chars));
    }
    
    if (opts.includeSpecialChars) {
        charset += CHARACTER_SETS.specialChars;
        requiredChars.push(getRandomChar(CHARACTER_SETS.specialChars));
    }
    
    if (charset.length === 0) {
        throw new Error('At least one character type must be selected');
    }
    
    // Generate password ensuring at least one character from each required type
    const password: string[] = [...requiredChars];
    
    // Fill remaining length with random characters
    for (let i = requiredChars.length; i < opts.length; i++) {
        password.push(getRandomChar(charset));
    }
    
    // Shuffle the password array to avoid predictable patterns
    return shuffleArray(password).join('');
}

/**
 * Generate multiple password options
 */
export function generatePasswordOptions(count: number = 3, options: PasswordGeneratorOptions = {}): string[] {
    const passwords: string[] = [];
    for (let i = 0; i < count; i++) {
        passwords.push(generatePassword(options));
    }
    return passwords;
}

/**
 * Get a random character from a string
 */
function getRandomChar(str: string): string {
    return str.charAt(Math.floor(Math.random() * str.length));
}

/**
 * Shuffle an array using Fisher-Yates algorithm
 */
function shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // Fallback for older browsers or non-secure contexts
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            return result;
        }
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        return false;
    }
}

/**
 * Generate a readable password using words, numbers, and special characters
 */
export function generateReadablePassword(): string {
    const adjective = getRandomWord(WORD_LISTS.adjectives);
    const noun = getRandomWord(WORD_LISTS.nouns);
    const number = Math.floor(Math.random() * 999) + 1; // 1-999
    const specialChars = '!@#$%^&*';
    const specialChar = specialChars[Math.floor(Math.random() * specialChars.length)];

    // Capitalize first letter of each word
    const capitalizedAdjective = adjective.charAt(0).toUpperCase() + adjective.slice(1);
    const capitalizedNoun = noun.charAt(0).toUpperCase() + noun.slice(1);

    return `${capitalizedAdjective}${capitalizedNoun}${number}${specialChar}`;
}

/**
 * Get a random word from a word list
 */
function getRandomWord(wordList: string[]): string {
    return wordList[Math.floor(Math.random() * wordList.length)];
}

/**
 * Generate a secure password that meets all validation requirements
 * Uses readable words by default, falls back to random if needed
 */
export function generateSecurePassword(useReadable: boolean = true): string {
    if (useReadable) {
        return generateReadablePassword();
    } else {
        return generatePassword({
            length: 12,
            includeUppercase: true,
            includeLowercase: true,
            includeNumbers: true,
            includeSpecialChars: true,
            excludeSimilar: true,
        });
    }
}

/**
 * Check if a password meets the minimum security requirements
 */
export function isSecurePassword(password: string): boolean {
    return (
        password.length >= 8 &&
        /[A-Z]/.test(password) &&
        /[a-z]/.test(password) &&
        /[0-9]/.test(password) &&
        /[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password)
    );
}
