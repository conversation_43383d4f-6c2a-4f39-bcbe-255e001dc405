/**
 * @jest-environment jsdom
 */

import {
    generatePassword,
    generatePasswordOptions,
    generateSecurePassword,
    isSecurePassword,
    copyToClipboard,
} from '../passwordGenerator';

// Mock clipboard API
Object.assign(navigator, {
    clipboard: {
        writeText: jest.fn(() => Promise.resolve()),
    },
});

// Mock document.execCommand for fallback
document.execCommand = jest.fn(() => true);

describe('Password Generator Utils', () => {
    describe('generatePassword', () => {
        it('should generate password with default options', () => {
            const password = generatePassword();
            
            expect(password).toHaveLength(12);
            expect(/[A-Z]/.test(password)).toBe(true); // uppercase
            expect(/[a-z]/.test(password)).toBe(true); // lowercase
            expect(/[0-9]/.test(password)).toBe(true); // numbers
            expect(/[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password)).toBe(true); // special chars
        });

        it('should generate password with custom length', () => {
            const password = generatePassword({ length: 16 });
            expect(password).toHaveLength(16);
        });

        it('should generate password with only specified character types', () => {
            const password = generatePassword({
                length: 10,
                includeUppercase: true,
                includeLowercase: true,
                includeNumbers: false,
                includeSpecialChars: false,
            });
            
            expect(password).toHaveLength(10);
            expect(/[A-Z]/.test(password)).toBe(true);
            expect(/[a-z]/.test(password)).toBe(true);
            expect(/[0-9]/.test(password)).toBe(false);
            expect(/[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password)).toBe(false);
        });

        it('should exclude similar characters when option is enabled', () => {
            const passwords = Array.from({ length: 50 }, () => 
                generatePassword({ 
                    length: 20, 
                    excludeSimilar: true 
                })
            );
            
            // Check that similar characters are not present in any generated password
            const similarChars = 'il1Lo0O';
            passwords.forEach(password => {
                similarChars.split('').forEach(char => {
                    expect(password).not.toContain(char);
                });
            });
        });

        it('should throw error when no character types are selected', () => {
            expect(() => {
                generatePassword({
                    includeUppercase: false,
                    includeLowercase: false,
                    includeNumbers: false,
                    includeSpecialChars: false,
                });
            }).toThrow('At least one character type must be selected');
        });

        it('should ensure at least one character from each selected type', () => {
            // Generate multiple passwords to test consistency
            for (let i = 0; i < 10; i++) {
                const password = generatePassword({
                    length: 8,
                    includeUppercase: true,
                    includeLowercase: true,
                    includeNumbers: true,
                    includeSpecialChars: true,
                });
                
                expect(/[A-Z]/.test(password)).toBe(true);
                expect(/[a-z]/.test(password)).toBe(true);
                expect(/[0-9]/.test(password)).toBe(true);
                expect(/[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password)).toBe(true);
            }
        });
    });

    describe('generatePasswordOptions', () => {
        it('should generate multiple password options', () => {
            const passwords = generatePasswordOptions(5);
            
            expect(passwords).toHaveLength(5);
            expect(new Set(passwords).size).toBe(5); // All passwords should be unique
        });

        it('should generate passwords with custom options', () => {
            const passwords = generatePasswordOptions(3, { length: 8 });
            
            passwords.forEach(password => {
                expect(password).toHaveLength(8);
            });
        });
    });

    describe('generateSecurePassword', () => {
        it('should generate a secure password meeting all requirements', () => {
            const password = generateSecurePassword();
            
            expect(password).toHaveLength(12);
            expect(isSecurePassword(password)).toBe(true);
        });

        it('should generate unique passwords on multiple calls', () => {
            const passwords = Array.from({ length: 10 }, () => generateSecurePassword());
            const uniquePasswords = new Set(passwords);
            
            expect(uniquePasswords.size).toBe(10);
        });
    });

    describe('isSecurePassword', () => {
        it('should validate secure passwords correctly', () => {
            const securePasswords = [
                'MySecure123!',
                'Complex@Pass1',
                'Strong#Password2',
                'Valid$123Pass',
            ];

            securePasswords.forEach(password => {
                expect(isSecurePassword(password)).toBe(true);
            });
        });

        it('should invalidate insecure passwords correctly', () => {
            const insecurePasswords = [
                'short',                    // Too short
                'nouppercase123!',         // No uppercase
                'NOLOWERCASE123!',         // No lowercase
                'NoNumbers!',              // No numbers
                'NoSpecialChars123',       // No special characters
                'weakpass',                // Multiple issues
            ];

            insecurePasswords.forEach(password => {
                expect(isSecurePassword(password)).toBe(false);
            });
        });
    });

    describe('copyToClipboard', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should copy text using clipboard API when available', async () => {
            const text = 'test password';
            const result = await copyToClipboard(text);
            
            expect(navigator.clipboard.writeText).toHaveBeenCalledWith(text);
            expect(result).toBe(true);
        });

        it('should handle clipboard API errors gracefully', async () => {
            (navigator.clipboard.writeText as jest.Mock).mockRejectedValue(new Error('Clipboard error'));
            
            const text = 'test password';
            const result = await copyToClipboard(text);
            
            expect(result).toBe(false);
        });

        it('should use fallback method when clipboard API is not available', async () => {
            // Temporarily remove clipboard API
            const originalClipboard = navigator.clipboard;
            delete (navigator as { clipboard?: typeof navigator.clipboard }).clipboard;
            
            const text = 'test password';
            const result = await copyToClipboard(text);
            
            expect(document.execCommand).toHaveBeenCalledWith('copy');
            expect(result).toBe(true);
            
            // Restore clipboard API
            (navigator as { clipboard?: typeof navigator.clipboard }).clipboard = originalClipboard;
        });
    });
});
