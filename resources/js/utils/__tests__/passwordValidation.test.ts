/**
 * @jest-environment jsdom
 */

import {
    validatePassword,
    getPasswordValidationMessage,
    getPasswordConfirmationMessage,
    calculatePasswordStrength,
    getPasswordStrengthLabel,
    getPasswordStrengthColor,
} from '../passwordValidation';

describe('Password Validation Utils', () => {
    describe('validatePassword', () => {
        it('should validate strong passwords correctly', () => {
            const strongPasswords = [
                'MySecure123!',
                'Complex@Pass1',
                'Strong#Password2',
                'Valid$123Pass',
            ];

            strongPasswords.forEach(password => {
                const result = validatePassword(password);
                expect(result.isValid).toBe(true);
                expect(result.errors).toHaveLength(0);
                expect(result.requirements.every(req => req.met)).toBe(true);
            });
        });

        it('should invalidate weak passwords correctly', () => {
            const weakPasswords = [
                { password: 'short', expectedErrors: 5 }, // Too short, missing requirements
                { password: 'nouppercase123!', expectedErrors: 1 }, // No uppercase
                { password: 'NOLOWERCASE123!', expectedErrors: 1 }, // No lowercase
                { password: 'NoNumbers!', expectedErrors: 1 }, // No numbers
                { password: 'NoSpecialChars123', expectedErrors: 1 }, // No special characters
                { password: 'password', expectedErrors: 4 }, // Common password + missing requirements
            ];

            weakPasswords.forEach(({ password }) => {
                const result = validatePassword(password);
                expect(result.isValid).toBe(false);
                expect(result.errors.length).toBeGreaterThanOrEqual(1);
            });
        });

        it('should check all requirements individually', () => {
            const result = validatePassword('Test123!');
            
            expect(result.requirements).toHaveLength(6);
            expect(result.requirements.find(req => req.id === 'length')?.met).toBe(true);
            expect(result.requirements.find(req => req.id === 'uppercase')?.met).toBe(true);
            expect(result.requirements.find(req => req.id === 'lowercase')?.met).toBe(true);
            expect(result.requirements.find(req => req.id === 'number')?.met).toBe(true);
            expect(result.requirements.find(req => req.id === 'special')?.met).toBe(true);
            expect(result.requirements.find(req => req.id === 'common')?.met).toBe(true);
        });
    });

    describe('getPasswordValidationMessage', () => {
        it('should return empty string for valid passwords', () => {
            const message = getPasswordValidationMessage('ValidPass123!');
            expect(message).toBe('');
        });

        it('should return appropriate message for invalid passwords', () => {
            const message = getPasswordValidationMessage('weak');
            expect(message).toContain('Password must contain');
        });

        it('should return empty string for empty password', () => {
            const message = getPasswordValidationMessage('');
            expect(message).toBe('');
        });
    });

    describe('getPasswordConfirmationMessage', () => {
        it('should return empty string for matching passwords', () => {
            const message = getPasswordConfirmationMessage('password123', 'password123');
            expect(message).toBe('');
        });

        it('should return error message for non-matching passwords', () => {
            const message = getPasswordConfirmationMessage('password123', 'different');
            expect(message).toBe('Passwords do not match.');
        });

        it('should return empty string for empty confirmation', () => {
            const message = getPasswordConfirmationMessage('password123', '');
            expect(message).toBe('');
        });
    });

    describe('calculatePasswordStrength', () => {
        it('should return 0 for empty password', () => {
            const strength = calculatePasswordStrength('');
            expect(strength).toBe(0);
        });

        it('should return 100 for strong password', () => {
            const strength = calculatePasswordStrength('StrongPass123!');
            expect(strength).toBe(100);
        });

        it('should return partial score for partially valid password', () => {
            const strength = calculatePasswordStrength('weakpass'); // Only lowercase and length
            expect(strength).toBeGreaterThan(0);
            expect(strength).toBeLessThan(100);
        });
    });

    describe('getPasswordStrengthLabel', () => {
        it('should return correct labels for different scores', () => {
            expect(getPasswordStrengthLabel(0)).toBe('');
            expect(getPasswordStrengthLabel(30)).toBe('Weak');
            expect(getPasswordStrengthLabel(60)).toBe('Fair');
            expect(getPasswordStrengthLabel(80)).toBe('Good');
            expect(getPasswordStrengthLabel(100)).toBe('Strong');
        });
    });

    describe('getPasswordStrengthColor', () => {
        it('should return correct color classes for different scores', () => {
            expect(getPasswordStrengthColor(0)).toBe('');
            expect(getPasswordStrengthColor(30)).toBe('text-red-500');
            expect(getPasswordStrengthColor(60)).toBe('text-yellow-500');
            expect(getPasswordStrengthColor(80)).toBe('text-blue-500');
            expect(getPasswordStrengthColor(100)).toBe('text-green-500');
        });
    });
});
