import { Button } from '@/components/ui/button';
import { Bell } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

// Define the custom event type
declare global {
  interface WindowEventMap {
    'notification-badge-update': CustomEvent<{ count: number }>;
  }
}

interface NotificationBadgeProps {
  className?: string;
}

export default function NotificationBadge({ className }: NotificationBadgeProps) {
    const [unreadCount, setUnreadCount] = useState(0);

    useEffect(() => {
        // Initial load
        fetchUnreadCount();

        // Listen for updates from enhanced polling
        const handleBadgeUpdate = (event: CustomEvent<{ count: number }>) => {
            if (event.detail && typeof event.detail.count === 'number') {
                const newCount = event.detail.count;
                if (newCount < 0) {
                    // Handle decremental update
                    setUnreadCount(prev => Math.max(0, prev + newCount));
                } else {
                    // Handle absolute count update
                    setUnreadCount(newCount);
                }
            }
        };

        window.addEventListener('notification-badge-update', handleBadgeUpdate);

        // Fallback polling (much slower)
        const fallbackInterval = setInterval(fetchUnreadCount, 60000); // 1 minute

        return () => {
            window.removeEventListener('notification-badge-update', handleBadgeUpdate);
            clearInterval(fallbackInterval);
        };
    }, []);

    const fetchUnreadCount = async () => {
        try {
            const response = await fetch(route('notifications.unread-count'));
            const data = await response.json();
            setUnreadCount(data.count);
        } catch (error) {
            console.error('Failed to fetch unread count:', error);
        }
    };

    return (
        <Button variant="ghost" size="icon" asChild className={cn("relative", className)}>
            <Link href={route('notifications.index')}>
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full h-5 w-5 flex items-center justify-center">
                        {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                )}
            </Link>
        </Button>
    );
}