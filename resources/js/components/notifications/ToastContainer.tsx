import React, { useState, useCallback, useEffect } from 'react';
import ToastNotification from './ToastNotification';

interface Toast {
    id: string;
    title: string;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    duration?: number;
    actionUrl?: string;
    dismissible?: boolean;
}

export default function ToastContainer() {
    const [toasts, setToasts] = useState<Toast[]>([]);

    const addToast = useCallback((toast: Omit<Toast, 'id'>) => {
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        setToasts(prev => [...prev, { ...toast, id }]);
    }, []);

    const removeToast = useCallback((id: string) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    }, []);

    // Handle new notifications from polling service
    const handleNewNotification = useCallback((event: Event) => {
        const customEvent = event as CustomEvent;
        const notification = customEvent.detail;
        
        // Server-side tracking prevents duplicates, so we can show toast directly
        if (notification.toast) {
            console.log('Showing toast for notification:', notification.id, notification.toast.title);
            addToast({
                title: notification.toast.title,
                message: notification.toast.message,
                type: notification.toast.type,
                duration: notification.toast.duration,
                actionUrl: notification.toast.action_url,
                dismissible: notification.toast.dismissible
            });
        }
    }, [addToast]);

    // Handle show-toast events (for version updates, etc.)
    const handleShowToast = useCallback((event: Event) => {
        const customEvent = event as CustomEvent;
        addToast(customEvent.detail);
    }, [addToast]);

    // Expose globally for easy access and set up event listeners
    useEffect(() => {
        console.log('ToastContainer ready - window.showToast available');
        window.showToast = addToast;
        
        // Listen for new notifications
        window.addEventListener('new-notification', handleNewNotification);
        window.addEventListener('show-toast', handleShowToast);
        
        return () => {
            if ('showToast' in window) {
                delete (window as Window & { showToast?: typeof addToast }).showToast;
            }
            window.removeEventListener('new-notification', handleNewNotification);
            window.removeEventListener('show-toast', handleShowToast);
        };
    }, [addToast, handleNewNotification, handleShowToast]);

    // Server-side tracking handles deduplication, no client-side cleanup needed

    return (
        <div className="fixed top-4 right-4 z-50 space-y-2 pointer-events-none">
            <div className="space-y-2 pointer-events-auto">
                {toasts.map(toast => (
                    <ToastNotification
                        key={toast.id}
                        {...toast}
                        onDismiss={removeToast}
                    />
                ))}
            </div>
        </div>
    );
}
