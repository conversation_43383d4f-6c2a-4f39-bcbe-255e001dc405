import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { Link } from '@inertiajs/react';
import { Bell } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function NotificationSidebarItem() {
    const [unreadCount, setUnreadCount] = useState(0);

    useEffect(() => {
        // Fetch unread count on component mount
        fetch(route('notifications.unread-count'))
            .then(response => response.json())
            .then(data => setUnreadCount(data.count))
            .catch(console.error);

        // Optional: Set up polling for real-time updates
        const interval = setInterval(() => {
            fetch(route('notifications.unread-count'))
                .then(response => response.json())
                .then(data => setUnreadCount(data.count))
                .catch(console.error);
        }, 30000); // Poll every 30 seconds

        return () => clearInterval(interval);
    }, []);

    return (
        <SidebarMenuItem>
            <SidebarMenuButton asChild>
                <Link href={route('notifications.index')} prefetch>
                    <Bell className="h-4 w-4 mr-1" />
                    <span>Notifications</span>
                    {unreadCount > 0 && (
                        <span className="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </span>
                    )}
                </Link>
            </SidebarMenuButton>
        </SidebarMenuItem>
    );
}
