import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Notification } from '@/types/notification';
import { router } from '@inertiajs/react';
import { Bell, CheckCircle, Clock, FileText, XCircle, AlertTriangle, DollarSign, Trash2 } from 'lucide-react';

interface NotificationItemProps {
    notification: Notification;
    onMarkAsRead: (id: string) => void;
    onDelete: (id: string) => void;
}

export default function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
    const isUnread = !notification.read_at;
    const data = notification.data || {};

    const formatDateTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString(undefined, {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
            timeZone: 'Africa/Nairobi' // Set to EAT timezone
        });
    };

    const getIcon = () => {
        switch (data.type) {
            case 'requisition_pending_approval':
                return <Clock className="h-5 w-5 text-warning" />;
            case 'requisition_approved':
                return <CheckCircle className="h-5 w-5 text-success" />;
            case 'requisition_rejected':
                return <XCircle className="h-5 w-5 text-destructive" />;
            case 'requisition_fully_approved':
                return <CheckCircle className="h-5 w-5 text-success" />;
            case 'low_cash_float_alert':
                return <DollarSign className="h-5 w-5 text-warning" />;
            case 'requisition_submission_confirmation':
                return <CheckCircle className="h-5 w-5 text-success" />;
            case 'requisition_moved_to_transaction':
                return <FileText className="h-5 w-5 text-info" />;
            case 'transaction_ready_for_disbursement':
                return <DollarSign className="h-5 w-5 text-info" />;
            case 'disbursement_completed':
                return <CheckCircle className="h-5 w-5 text-success" />;
            case 'disbursement_completed_for_finance':
                return <CheckCircle className="h-5 w-5 text-info" />;
            case 'evidence_upload_reminder':
                return <AlertTriangle className="h-5 w-5 text-warning" />;
            default:
                return <Bell className="h-5 w-5 text-info" />;
        }
    };

    const getBadgeVariant = () => {
        switch (data.type) {
            case 'requisition_pending_approval':
                return 'default';
            case 'requisition_approved':
            case 'requisition_fully_approved':
            case 'requisition_submission_confirmation':
            case 'disbursement_completed':
            case 'disbursement_completed_for_finance':
                return 'secondary';
            case 'requisition_rejected':
                return 'destructive';
            case 'low_cash_float_alert':
            case 'evidence_upload_reminder':
                return 'outline';
            case 'requisition_moved_to_transaction':
            case 'transaction_ready_for_disbursement':
                return 'default';
            default:
                return 'outline';
        }
    };

    const getBadgeCustomStyle = () => {
        switch (data.type) {
            case 'low_cash_float_alert':
            case 'evidence_upload_reminder':
                return 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-800';
            case 'requisition_pending_approval':
                return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800';
            case 'requisition_approved':
            case 'requisition_fully_approved':
            case 'disbursement_completed':
                return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800';
            case 'requisition_moved_to_transaction':
            case 'transaction_ready_for_disbursement':
            case 'disbursement_completed_for_finance':
                return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800';
            case 'requisition_submission_confirmation':
                return 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700';
        }
    };

    const getNotificationBorderColor = () => {
        switch (data.type) {
            case 'requisition_pending_approval':
                return 'border-l-warning';
            case 'requisition_approved':
            case 'requisition_fully_approved':
            case 'disbursement_completed':
                return 'border-l-success';
            case 'requisition_rejected':
                return 'border-l-destructive';
            case 'low_cash_float_alert':
            case 'evidence_upload_reminder':
                return 'border-l-warning';
            case 'requisition_moved_to_transaction':
            case 'transaction_ready_for_disbursement':
            case 'disbursement_completed_for_finance':
                return 'border-l-info';
            default:
                return 'border-l-primary';
        }
    };

    return (
        <Card className={`transition-all duration-200 hover:shadow-md ${isUnread ? `border-l-4 ${getNotificationBorderColor()} bg-muted/30` : 'bg-card'}`}>
            <CardContent className="p-3 sm:p-4">
                <div className="flex items-start gap-2 sm:gap-3">
                    <div className="flex-shrink-0 mt-0.5">{getIcon()}</div>

                    <div className="min-w-0 flex-1 space-y-2">
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-start sm:justify-between sm:gap-3">
                            <div className="flex-1 min-w-0">
                                <h4 className="text-base font-medium text-foreground break-words">
                                    {data.title || 'Notification'}
                                </h4>
                                <p className="text-base text-muted-foreground mt-1 break-words">
                                    {data.message || 'No message available'}
                                </p>
                            </div>

                            <div className="flex items-center gap-2 flex-shrink-0">
                                <Badge 
                                    variant={getBadgeVariant()}
                                    className={`${getBadgeCustomStyle()} text-sm whitespace-nowrap`}
                                >
                                    {data.type ? data.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Notification'}
                                </Badge>
                                {isUnread && (
                                    <div className="w-2 h-2 bg-primary rounded-full animate-pulse flex-shrink-0"></div>
                                )}
                            </div>
                        </div>

                        {data.metadata && (
                            <div className="text-sm text-muted-foreground space-y-1">
                                {data.metadata.department_name && (
                                    <div className="break-words">
                                        <span className="font-medium">Department:</span> {data.metadata.department_name}
                                    </div>
                                )}
                                <div className="flex flex-col gap-1 sm:flex-row sm:gap-4">
                                    {data.metadata.current_balance && (
                                        <span className="whitespace-nowrap">
                                            <span className="font-medium">Balance:</span> KSH {
                                                typeof data.metadata.current_balance === 'number' 
                                                    ? data.metadata.current_balance.toLocaleString() 
                                                    : data.metadata.current_balance
                                            }
                                        </span>
                                    )}
                                    {data.metadata.alert_threshold && (
                                        <span className="whitespace-nowrap">
                                            <span className="font-medium">Threshold:</span> KSH {
                                                typeof data.metadata.alert_threshold === 'number' 
                                                    ? data.metadata.alert_threshold.toLocaleString() 
                                                    : data.metadata.alert_threshold
                                            }
                                        </span>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Footer section with timestamp and actions */}
                        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                            <span className="text-sm text-muted-foreground order-2 sm:order-1">
                                {formatDateTime(notification.created_at)}
                            </span>

                            <div className="flex items-center gap-1 sm:gap-2 order-1 sm:order-2 flex-wrap">
                                {data.action_url && (
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-sm h-8 px-3"
                                        onClick={async () => {
                                            // Mark as read if unread using AJAX
                                            if (isUnread) {
                                                try {
                                                    const response = await fetch(route('notifications.mark-as-read-ajax', notification.id), {
                                                        method: 'POST',
                                                        headers: {
                                                            'Content-Type': 'application/json',
                                                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                                                        },
                                                    });
                                                    
                                                    if (response.ok) {
                                                         await response.json();
                                                        // Update the notification as read in the UI
                                                        onMarkAsRead(notification.id);
                                                        
                                                        // Update the badge count
                                                        if (window.enhancedPollingService) {
                                                            window.enhancedPollingService.triggerImmediateCheck();
                                                        }
                                                    }
                                                } catch (error) {
                                                    console.error('Failed to mark notification as read:', error);
                                                }
                                            }
                                            
                                            // Navigate using Inertia router for smooth transition
                                            if (data.action_url) {
                                                router.visit(data.action_url);
                                            }
                                        }}
                                    >
                                        <FileText className="h-4 w-4 mr-1" />
                                        <span className="hidden xs:inline">View</span>
                                    </Button>
                                )}

                                {isUnread && (
                                    <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        className="text-sm h-8 px-3"
                                        onClick={() => onMarkAsRead(notification.id)}
                                    >
                                        <span className="hidden sm:inline">Mark as read</span>
                                        <span className="sm:hidden">Read</span>
                                    </Button>
                                )}

                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-destructive hover:text-destructive hover:bg-destructive/10 text-sm h-8 px-3"
                                    onClick={() => {
                                        if (confirm('Are you sure you want to delete this notification?')) {
                                            onDelete(notification.id);
                                        }
                                    }}
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}