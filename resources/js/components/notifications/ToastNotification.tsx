import React, { useState, useEffect, useCallback } from 'react';
import { X, CheckCircle, XCircle, AlertTriangle, Info, ExternalLink } from 'lucide-react';
import { router } from '@inertiajs/react';

interface ToastProps {
    id: string;
    title: string;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    duration?: number;
    actionUrl?: string;
    dismissible?: boolean;
    onDismiss: (id: string) => void;
}

export default function ToastNotification({
    id, title, message, type, duration = 5000, actionUrl, dismissible = true, onDismiss
}: ToastProps) {
    const [isExiting, setIsExiting] = useState(false);

    const handleDismiss = useCallback(() => {
        if (!dismissible) return;
        setIsExiting(true);
        setTimeout(() => onDismiss(id), 300);
    }, [dismissible, onDismiss, id]);

    useEffect(() => {
    if (duration > 0) {
        const timer = setTimeout(() => {
            if (dismissible) {
                setIsExiting(true);
                setTimeout(() => onDismiss(id), 300);
            }
        }, duration);
        return () => clearTimeout(timer);
    }
// eslint-disable-next-line react-hooks/exhaustive-deps
}, [duration, handleDismiss]);

    const handleClick = async () => {
        if (actionUrl) {
            // Check if this is a notification action URL that should mark as read
            if (actionUrl.includes('/notifications/') && actionUrl.includes('/mark-as-read')) {
                try {
                    // Mark as read via API call
                    await fetch(actionUrl, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                        credentials: 'include'
                    });
                    
                    // Trigger immediate notification check to update badge
                    if (window.enhancedPollingService) {
                        window.enhancedPollingService.triggerImmediateCheck();
                    }
                } catch (error) {
                    console.error('Failed to mark notification as read:', error);
                }
            } else {
                // For regular action URLs, just visit them
                router.visit(actionUrl);
            }
            handleDismiss();
        }
    };

    const getIcon = () => {
        const iconClass = "h-4 w-4 flex-shrink-0";
        switch (type) {
            case 'success': return <CheckCircle className={`${iconClass} text-success`} />;
            case 'error': return <XCircle className={`${iconClass} text-destructive`} />;
            case 'warning': return <AlertTriangle className={`${iconClass} text-warning`} />;
            case 'info': return <Info className={`${iconClass} text-info`} />;
        }
    };

    const getStyles = () => {
        const base = "border shadow-lg rounded-lg bg-card";
        switch (type) {
            case 'success': return `${base} border-l-4 border-l-success`;
            case 'error': return `${base} border-l-4 border-l-destructive`;
            case 'warning': return `${base} border-l-4 border-l-warning`;
            case 'info': return `${base} border-l-4 border-l-info`;
        }
    };

    return (
        <div className={`
            transform transition-all duration-300 ease-in-out mb-3
            ${isExiting ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'}
        `}>
            <div className={`
                ${getStyles()} p-3 max-w-xs w-full
                ${actionUrl ? 'cursor-pointer hover:shadow-xl transition-shadow' : ''}
            `} onClick={actionUrl ? handleClick : undefined}>
                <div className="flex items-start gap-2">
                    {getIcon()}
                    <div className="flex-1 min-w-0">
                        <h4 className="text-xs font-semibold text-foreground mb-1">
                            {title}
                        </h4>
                        <p className="text-xs text-muted-foreground leading-relaxed line-clamp-2">
                            {message}
                        </p>
                        {actionUrl && (
                            <div className="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
                                <ExternalLink className="h-3 w-3" />
                                <span>Click to view details</span>
                            </div>
                        )}
                    </div>
                    {dismissible && (
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                handleDismiss();
                            }}
                            className="flex-shrink-0 text-muted-foreground hover:text-foreground transition-colors"
                        >
                            <X className="h-4 w-4" />
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}