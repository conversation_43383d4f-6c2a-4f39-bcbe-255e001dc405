import { cn } from '@/lib/utils';

interface PieChartData {
    label: string;
    value: number;
    color: string;
}

interface PieChartProps {
    data: PieChartData[];
    size?: number;
    centerContent?: React.ReactNode;
    showLegend?: boolean;
    className?: string;
}

export function PieChart({ 
    data, 
    size = 128, 
    centerContent, 
    showLegend = true, 
    className 
}: PieChartProps) {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    if (total === 0) {
        return (
            <div className={cn("flex flex-col items-center", className)}>
                <div 
                    className="rounded-full bg-muted flex items-center justify-center"
                    style={{ width: size, height: size }}
                >
                    <span className="text-muted-foreground text-sm">No data</span>
                </div>
                {showLegend && (
                    <div className="mt-4 space-y-2 text-sm">
                        <div className="text-muted-foreground">No data available</div>
                    </div>
                )}
            </div>
        );
    }

    let currentAngle = 0;
    const segments = data.map(item => {
        const percentage = (item.value / total) * 100;
        const angle = (item.value / total) * 360;
        const segment = {
            ...item,
            percentage,
            startAngle: currentAngle,
            endAngle: currentAngle + angle
        };
        currentAngle += angle;
        return segment;
    });

    // Create conic gradient
    const gradientStops = segments.map(segment => 
        `${segment.color} ${segment.startAngle}deg ${segment.endAngle}deg`
    ).join(', ');

    return (
        <div className={cn("flex flex-col items-center", className)}>
            <div className="relative mb-4">
                {/* Pie Chart */}
                <div 
                    className="rounded-full relative overflow-hidden"
                    style={{
                        width: size,
                        height: size,
                        background: `conic-gradient(from 0deg, ${gradientStops})`
                    }}
                >
                    {/* Center circle */}
                    {centerContent && (
                        <div 
                            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background rounded-full flex items-center justify-center"
                            style={{ 
                                width: size * 0.5, 
                                height: size * 0.5 
                            }}
                        >
                            {centerContent}
                        </div>
                    )}
                </div>
            </div>
            
            {/* Legend */}
            {showLegend && (
                <div className="space-y-2 text-sm">
                    {segments.map((segment, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <div 
                                className="w-3 h-3 rounded-full flex-shrink-0"
                                style={{ backgroundColor: segment.color }}
                            />
                            <span className="text-sm">
                                {segment.label} ({segment.value})
                            </span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}

// Predefined color schemes
export const pieChartColors = {
    default: ['#10b981', '#f59e0b', '#ef4444', '#3b82f6', '#8b5cf6'],
    status: {
        success: '#10b981',
        warning: '#f59e0b', 
        danger: '#ef4444',
        info: '#3b82f6',
        primary: '#8b5cf6'
    }
};
