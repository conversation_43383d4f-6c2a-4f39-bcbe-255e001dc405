import * as React from "react";
import { Link } from "@inertiajs/react";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationComponent
  extends React.ForwardRefExoticComponent<
    React.HTMLAttributes<HTMLDivElement> & React.RefAttributes<HTMLDivElement>
  > {
  Item: typeof PaginationItem;
  Prev: typeof PaginationPrev;
  Next: typeof PaginationNext;
}

const Pagination = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center justify-center gap-1", className)}
    {...props}
  />
)) as PaginationComponent;
Pagination.displayName = "Pagination";

const PaginationItem = React.forwardRef<
  React.ElementRef<typeof Link>,
  React.ComponentPropsWithoutRef<typeof Link> & {
    isActive?: boolean;
  }
>(({ className, isActive, ...props }, ref) => (
  <Link
    ref={ref}
    aria-current={isActive ? "page" : undefined}
    className={cn(
      "inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      isActive
        ? "bg-accent text-background/100 pointer-events-none"
        : "text-background/100",
      className
    )}
    {...props}
  />
));
PaginationItem.displayName = "PaginationItem";

const PaginationPrev = React.forwardRef<
  React.ElementRef<typeof Link>,
  React.ComponentPropsWithoutRef<typeof Link> & {
    disabled?: boolean;
  }
>(({ className, disabled, ...props }, ref) => (
  <Link
    ref={ref}
    aria-disabled={disabled}
    className={cn(
      "inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-secondary/100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      disabled && "pointer-events-none opacity-50",
      className
    )}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span className="sr-only">Previous</span>
  </Link>
));
PaginationPrev.displayName = "PaginationPrev";

const PaginationNext = React.forwardRef<
  React.ElementRef<typeof Link>,
  React.ComponentPropsWithoutRef<typeof Link> & {
    disabled?: boolean;
  }
>(({ className, disabled, ...props }, ref) => (
  <Link
    ref={ref}
    aria-disabled={disabled}
    className={cn(
      "inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-background/100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      disabled && "pointer-events-none opacity-50",
      className
    )}
    {...props}
  >
    <span className="sr-only">Next</span>
    <ChevronRight className="h-4 w-4" />
  </Link>
));
PaginationNext.displayName = "PaginationNext";

Pagination.Item = PaginationItem;
Pagination.Prev = PaginationPrev;
Pagination.Next = PaginationNext;

export { Pagination, PaginationItem, PaginationPrev, PaginationNext };