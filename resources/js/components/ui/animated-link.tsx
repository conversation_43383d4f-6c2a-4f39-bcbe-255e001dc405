import { motion } from "framer-motion"
import { <PERSON> } from "@inertiajs/react"
import type { ReactNode } from "react"

interface AnimatedLinkProps {
  href: string
  children: ReactNode
  className?: string
  underlineColor?: string
  external?: boolean
  onClick?: (e: React.MouseEvent) => void
}

export function AnimatedLink({
  href,
  children,
  className = "",
  underlineColor = "var(--primary)",
  external = false,
  onClick,
}: AnimatedLinkProps) {
  const linkProps = external ? { target: "_blank", rel: "noopener noreferrer" } : {}

  return (
    <Link href={href} className={`group relative inline-block overflow-hidden ${className}`} {...linkProps} onClick={onClick}>
      {children}
      <motion.span
        className="absolute bottom-0 left-0 h-0.5 w-full origin-left transform"
        style={{ backgroundColor: underlineColor }}
        initial={{ scaleX: 0 }}
        whileHover={{ scaleX: 1 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      />
    </Link>
  )
}