"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface SimpleCalendarProps {
  selected?: Date
  onSelect: (date: Date | undefined) => void
  fromDate?: Date
  toDate?: Date
  disabled?: (date: Date) => boolean
  className?: string
}

const MONTHS = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
]

const DAYS = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

export function SimpleCalendar({
  selected,
  onSelect,
  fromDate,
  toDate,
  disabled,
  className
}: SimpleCalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(selected || new Date())
  
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  
  // Get first day of the month and how many days in the month
  const firstDayOfMonth = new Date(year, month, 1)
  const lastDayOfMonth = new Date(year, month + 1, 0)
  const daysInMonth = lastDayOfMonth.getDate()
  const startingDayOfWeek = firstDayOfMonth.getDay()
  
  // Generate calendar days
  const calendarDays = []
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null)
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    // Create date at noon to avoid timezone issues
    const date = new Date(year, month, day, 12, 0, 0, 0)
    calendarDays.push(date)
  }
  
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(year, month - 1, 1))
  }
  
  const goToNextMonth = () => {
    setCurrentDate(new Date(year, month + 1, 1))
  }
  
  const isDateDisabled = (date: Date) => {
    if (disabled && disabled(date)) return true
    
    // Compare dates without time components
    const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    
    if (fromDate) {
      const fromDateOnly = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate())
      if (dateOnly < fromDateOnly) return true
    }
    
    if (toDate) {
      const toDateOnly = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate())
      if (dateOnly > toDateOnly) return true
    }
    
    return false
  }
  
  const isDateSelected = (date: Date) => {
    if (!selected) return false
    return (
      date.getDate() === selected.getDate() &&
      date.getMonth() === selected.getMonth() &&
      date.getFullYear() === selected.getFullYear()
    )
  }
  
  const isToday = (date: Date) => {
    const today = new Date()
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    )
  }
  
  return (
    <div className={cn("p-3", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={goToPreviousMonth}
          className="h-7 w-7 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <h2 className="text-sm font-medium">
          {MONTHS[month]} {year}
        </h2>
        
        <Button
          variant="outline"
          size="sm"
          onClick={goToNextMonth}
          className="h-7 w-7 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Days of week header */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {DAYS.map((day) => (
          <div
            key={day}
            className="h-8 w-8 flex items-center justify-center text-xs font-medium text-muted-foreground"
          >
            {day}
          </div>
        ))}
      </div>
      
      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((date, index) => {
          if (!date) {
            return <div key={index} className="h-8 w-8" />
          }
          
          const disabled = isDateDisabled(date)
          const selected = isDateSelected(date)
          const today = isToday(date)
          
          return (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 w-8 p-0 font-normal",
                selected && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground",
                today && !selected && "bg-accent text-accent-foreground font-semibold",
                disabled && "text-muted-foreground opacity-50 cursor-not-allowed"
              )}
              disabled={disabled}
              onClick={() => {
                if (!disabled) {
                  // Create a clean date object at midnight to avoid timezone issues
                  const cleanDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
                  onSelect(cleanDate)
                }
              }}
            >
              {date.getDate()}
            </Button>
          )
        })}
      </div>
    </div>
  )
}