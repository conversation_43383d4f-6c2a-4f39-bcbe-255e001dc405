import * as React from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useIsMobile } from "@/hooks/use-mobile"

interface MobileTableProps {
  children: React.ReactNode
  className?: string
}

interface MobileTableRowProps {
  children: React.ReactNode
  className?: string
  primaryContent: React.ReactNode
  expandedContent?: React.ReactNode
  showExpandButton?: boolean
}

interface MobileTableCellProps {
  children: React.ReactNode
  className?: string
  label?: string
  hideOnMobile?: boolean
}

const MobileTable = React.forwardRef<HTMLDivElement, MobileTableProps>(
  ({ className, children, ...props }, ref) => {
    const isMobile = useIsMobile()

    if (!isMobile) {
      // On desktop, render as regular table
      return (
        <div className="relative w-full overflow-auto">
          <table
            ref={ref as React.RefObject<HTMLTableElement>}
            className={cn("w-full caption-bottom text-sm", className)}
            {...props}
          >
            {children}
          </table>
        </div>
      )
    }

    // On mobile, render as card-based layout
    return (
      <div
        ref={ref}
        className={cn("w-full space-y-2", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileTable.displayName = "MobileTable"

const MobileTableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, children, ...props }, ref) => {
  const isMobile = useIsMobile()

  if (!isMobile) {
    return (
      <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props}>
        {children}
      </thead>
    )
  }

  // Hide header on mobile
  return null
})
MobileTableHeader.displayName = "MobileTableHeader"

const MobileTableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, children, ...props }, ref) => {
  const isMobile = useIsMobile()

  if (!isMobile) {
    return (
      <tbody
        ref={ref}
        className={cn("[&_tr:last-child]:border-0", className)}
        {...props}
      >
        {children}
      </tbody>
    )
  }

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={cn("space-y-2", className)}
      {...props}
    >
      {children}
    </div>
  )
})
MobileTableBody.displayName = "MobileTableBody"

const MobileTableRow = React.forwardRef<HTMLTableRowElement, MobileTableRowProps>(
  ({ className, children, primaryContent, expandedContent, showExpandButton = true, ...props }, ref) => {
    const isMobile = useIsMobile()
    const [isExpanded, setIsExpanded] = React.useState(false)

    if (!isMobile) {
      return (
        <tr
          ref={ref}
          className={cn(
            "border-b transition-colors hover:bg-muted/50 dark:hover:bg-accent/70 data-[state=selected]:bg-muted",
            className
          )}
          {...props}
        >
          {children}
        </tr>
      )
    }

    return (
      <div
        ref={ref as React.RefObject<HTMLDivElement>}
        className={cn(
          "rounded-lg border bg-card p-4 shadow-sm dark:bg-foreground/40 hover:bg-muted/50 dark:hover:bg-accent/70 transition-colors",
          className
        )}
        {...props}
      >
        {/* Primary content always visible */}
        <div className="space-y-2">
          {primaryContent}
        </div>

        {/* Expandable content */}
        {expandedContent && (
          <>
            {isExpanded && (
              <div className="mt-3 space-y-2 border-t pt-3">
                {expandedContent}
              </div>
            )}

            {showExpandButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-2 h-8 w-full justify-center gap-2 text-xs"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-3 w-3" />
                    Show less
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-3 w-3" />
                    Show more
                  </>
                )}
              </Button>
            )}
          </>
        )}
      </div>
    )
  }
)
MobileTableRow.displayName = "MobileTableRow"

const MobileTableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile()

  if (!isMobile) {
    return (
      <th
        ref={ref}
        className={cn(
          "h-12 px-4 text-left align-middle font-medium text-muted-foreground  [&:has([role=checkbox])]:pr-0",
          className
        )}
        {...props}
      />
    )
  }

  return null
})
MobileTableHead.displayName = "MobileTableHead"

const MobileTableCell = React.forwardRef<HTMLTableCellElement, MobileTableCellProps>(
  ({ className, children, label, hideOnMobile = false, ...props }, ref) => {
    const isMobile = useIsMobile()

    if (!isMobile) {
      return (
        <td
          ref={ref}
          className={cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className)}
          {...props}
        >
          {children}
        </td>
      )
    }

    if (hideOnMobile) {
      return null
    }

    return (
      <div
        ref={ref as React.RefObject<HTMLDivElement>}
        className={cn("flex justify-between items-center", className)}
        {...props}
      >
        {label && (
          <span className="text-sm font-medium text-muted-foreground ">
            {label}:
          </span>
        )}
        <span className="text-sm ">{children}</span>
      </div>
    )
  }
)
MobileTableCell.displayName = "MobileTableCell"

export {
  MobileTable,
  MobileTableHeader,
  MobileTableBody,
  MobileTableRow,
  MobileTableHead,
  MobileTableCell,
}
