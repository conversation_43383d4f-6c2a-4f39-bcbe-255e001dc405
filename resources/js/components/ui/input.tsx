import * as React from "react"
import { cn } from "@/lib/utils"

function Input({ className, type = 'text', value, defaultValue, onChange, ...props }: React.ComponentProps<"input">) {
  // Determine if this should be controlled or uncontrolled
  const isControlled = value !== undefined

  // For uncontrolled components, use internal state initialized with defaultValue
  const [internalValue, setInternalValue] = React.useState(defaultValue || '')

  // Use controlled value if provided, otherwise use internal state
  const currentValue = isControlled ? value : internalValue

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Update internal state for uncontrolled components
    if (!isControlled) {
      setInternalValue(e.target.value)
    }

    // Call external onChange handler if provided
    onChange?.(e)
  }

  // const filled = currentValue ? currentValue.length > 0 : false

  return (
    <input
      type={type}
      data-slot="input"
      value={currentValue}
      onChange={handleChange}
      className={cn(
        "border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-colors outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-foreground",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Input }
