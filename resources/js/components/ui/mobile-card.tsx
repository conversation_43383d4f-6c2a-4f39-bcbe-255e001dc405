import * as React from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useIsMobile } from "@/hooks/use-mobile"

interface MobileCardProps {
  children?: React.ReactNode
  className?: string
  primaryInfo: React.ReactNode
  secondaryInfo?: React.ReactNode
  expandedContent?: React.ReactNode
  actions?: React.ReactNode
  showExpandButton?: boolean
  expandButtonText?: string
  collapseButtonText?: string
  defaultExpanded?: boolean
}

interface MobileCardSectionProps {
  children: React.ReactNode
  className?: string
  title?: string
  hideOnMobile?: boolean
  priority?: 'high' | 'medium' | 'low'
}

interface MobileCardInfoProps {
  label?: string
  value: React.ReactNode
  className?: string
  hideOnMobile?: boolean
  priority?: 'high' | 'medium' | 'low'
}

const MobileCard = React.forwardRef<HTMLDivElement, MobileCardProps>(
  ({
    className,
    children,
    primaryInfo,
    secondaryInfo,
    expandedContent,
    actions,
    showExpandButton = true,
    expandButtonText = "View more",
    collapseButtonText = "View less",
    defaultExpanded = false,
    ...props
  }, ref) => {
    const isMobile = useIsMobile()
    const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)

    if (!isMobile) {
      // On desktop, show all content in a standard card
      return (
        <div
          ref={ref}
          className={cn(
            "rounded-lg border bg-card p-6 shadow-sm dark:bg-foreground/40",
            className
          )}
          {...props}
        >
          {children}
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn(
          "rounded-lg border bg-card p-4 shadow-sm dark:bg-foreground/40",
          className
        )}
        {...props}
      >
        {/* Primary Information - Always Visible */}
        <div className="space-y-2">
          {primaryInfo}
          {secondaryInfo && (
            <div className="text-sm text-muted-foreground dark:text-secondary/80">
              {secondaryInfo}
            </div>
          )}
        </div>

        {/* Actions - Always Visible */}
        {actions && (
          <div className="mt-3 flex flex-wrap gap-2">
            {actions}
          </div>
        )}

        {/* Expandable Content */}
        {expandedContent && (
          <>
            {isExpanded && (
              <div className="mt-4 space-y-3 border-t pt-4">
                {expandedContent}
              </div>
            )}

            {showExpandButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-3 h-8 w-full justify-center gap-2 text-xs"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-3 w-3" />
                    {collapseButtonText}
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-3 w-3" />
                    {expandButtonText}
                  </>
                )}
              </Button>
            )}
          </>
        )}
      </div>
    )
  }
)
MobileCard.displayName = "MobileCard"

const MobileCardSection = React.forwardRef<HTMLDivElement, MobileCardSectionProps>(
  ({ className, children, title, hideOnMobile = false, priority = 'medium', ...props }, ref) => {
    const isMobile = useIsMobile()

    if (isMobile && hideOnMobile) {
      return null
    }

    return (
      <div
        ref={ref}
        className={cn("space-y-2", className)}
        data-priority={priority}
        {...props}
      >
        {title && (
          <h4 className="text-sm font-medium text-muted-foreground ">
            {title}
          </h4>
        )}
        <div className="space-y-1">
          {children}
        </div>
      </div>
    )
  }
)
MobileCardSection.displayName = "MobileCardSection"

const MobileCardInfo = React.forwardRef<HTMLDivElement, MobileCardInfoProps>(
  ({ label, value, className, hideOnMobile = false, priority = 'medium', ...props }, ref) => {
    const isMobile = useIsMobile()

    if (isMobile && hideOnMobile) {
      return null
    }

    if (!isMobile) {
      return (
        <div
          ref={ref}
          className={cn("flex justify-between items-start", className)}
          {...props}
        >
          {label && (
            <span className="text-sm font-medium text-muted-foreground dark:text-muted/100 mr-2">
              {label}:
            </span>
          )}
          <span className="text-sm dark:text-muted/100 text-right flex-1">
            {value}
          </span>
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn("space-y-1", className)}
        data-priority={priority}
        {...props}
      >
        {label && (
          <div className="text-xs font-medium text-muted-foreground dark:text-secondary/80">
            {label}
          </div>
        )}
        <div className="text-sm ">
          {value}
        </div>
      </div>
    )
  }
)
MobileCardInfo.displayName = "MobileCardInfo"

// Helper component for creating responsive layouts
const MobileCardGrid = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "grid gap-4",
          "grid-cols-1", // Mobile: single column
          "md:grid-cols-2", // Tablet: two columns
          "lg:grid-cols-3", // Desktop: three columns
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileCardGrid.displayName = "MobileCardGrid"

export {
  MobileCard,
  MobileCardSection,
  MobileCardInfo,
  MobileCardGrid,
}
