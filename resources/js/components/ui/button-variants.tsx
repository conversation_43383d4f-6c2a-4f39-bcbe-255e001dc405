import { Link } from "@inertiajs/react"
import type { ReactNode } from "react"

interface ButtonProps {
  children: ReactNode
  onClick?: () => void
  className?: string
  disabled?: boolean
  type?: "button" | "submit" | "reset"
  variant?: "primary" | "secondary" | "outline" | "ghost" | "link"
  size?: "sm" | "md" | "lg"
}

interface ButtonLinkProps extends Omit<ButtonProps, "onClick" | "type"> {
  href: string
  external?: boolean
}

const variantStyles = {
  primary:
    "bg-gradient-to-r from-[var(--primary)] to-[var(--primary-dark)] text-[var(--primary-foreground)] shadow-md hover:shadow-lg hover:from-[var(--primary-dark)] hover:to-[var(--primary)] focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2",
  secondary:
    "bg-[var(--secondary)] text-[var(--secondary-foreground)] hover:bg-[var(--secondary)]/80 focus:ring-2 focus:ring-[var(--secondary)] focus:ring-offset-2",
  outline:
    "border-2 border-[var(--primary)] bg-transparent text-[var(--primary)] hover:bg-[var(--primary)] hover:text-[var(--primary-foreground)] focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2",
  ghost: "bg-transparent hover:bg-[var(--secondary)] text-[var(--foreground)] hover:text-[var(--foreground)]",
  link: "bg-transparent underline-offset-4 hover:underline text-[var(--primary)] hover:text-[var(--primary-dark)]",
}

const sizeStyles = {
  sm: "px-3 py-1.5 text-sm rounded-md",
  md: "px-4 py-2 text-sm rounded-lg",
  lg: "px-6 py-3 text-base rounded-xl",
}

export function Button({
  children,
  onClick,
  className = "",
  disabled = false,
  type = "button",
  variant = "primary",
  size = "md",
}: ButtonProps) {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center font-medium transition-all duration-200 transform hover:-translate-y-0.5 ${variantStyles[variant]} ${sizeStyles[size]} ${disabled ? "opacity-50 cursor-not-allowed" : ""} ${className}`}
    >
      {children}
    </button>
  )
}

export function ButtonLink({
  children,
  href,
  className = "",
  disabled = false,
  variant = "primary",
  size = "md",
  external = false,
}: ButtonLinkProps) {
  const linkProps = external ? { target: "_blank", rel: "noopener noreferrer" } : {}

  return (
    <Link
      href={disabled ? "#" : href}
      className={`inline-flex items-center justify-center font-medium transition-all duration-200 transform hover:-translate-y-0.5 ${variantStyles[variant]} ${sizeStyles[size]} ${disabled ? "opacity-50 cursor-not-allowed" : ""} ${className}`}
      {...linkProps}
    >
      {children}
    </Link>
  )
}