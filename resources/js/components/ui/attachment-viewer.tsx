import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  X,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCw,
  ExternalLink,
  ChevronLeft,
  ChevronRight,
  Loader2
} from 'lucide-react';

interface Attachment {
  id: number;
  original_name: string;
  file_size: number;
  mime_type: string;
  description?: string;
  is_evidence: boolean;
  uploaded_at_step?: string;
  created_at: string;
  uploader: {
    id: number;
    first_name: string;
    last_name: string;
  };
}

interface AttachmentViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  attachment: Attachment | null;
  attachments?: Attachment[];
  onNavigate?: (attachment: Attachment) => void;
}

export const AttachmentViewer: React.FC<AttachmentViewerProps> = ({
  open,
  onOpenChange,
  attachment,
  attachments = [],
  onNavigate,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);

  const isImage = attachment?.mime_type.startsWith('image/');
  const isPDF = attachment?.mime_type === 'application/pdf';
  const isViewable = isImage || isPDF;

  const currentIndex = attachments.findIndex(att => att.id === attachment?.id);
  const canNavigate = attachments.length > 1;
  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex < attachments.length - 1;

  useEffect(() => {
    if (attachment && open) {
      setLoading(true);
      setError(null);
      setZoom(1);
      setRotation(0);
    }
  }, [attachment, open]);

  const handleLoad = useCallback(() => {
    setLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setLoading(false);
    setError('Failed to load file');
  }, []);

  const handleDownload = useCallback(() => {
    if (attachment) {
      window.open(`/attachments/${attachment.id}/download`, '_blank');
    }
  }, [attachment]);

  const handleOpenInNewTab = useCallback(() => {
    if (attachment) {
      window.open(`/attachments/${attachment.id}/view`, '_blank');
    }
  }, [attachment]);

  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.2, 0.5));
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  const handlePrevious = useCallback(() => {
    if (hasPrevious && onNavigate) {
      onNavigate(attachments[currentIndex - 1]);
    }
  }, [hasPrevious, onNavigate, attachments, currentIndex]);

  const handleNext = useCallback(() => {
    if (hasNext && onNavigate) {
      onNavigate(attachments[currentIndex + 1]);
    }
  }, [hasNext, onNavigate, attachments, currentIndex]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!open) return;

    switch (event.key) {
      case 'Escape':
        onOpenChange(false);
        break;
      case 'ArrowLeft':
        if (hasPrevious) handlePrevious();
        break;
      case 'ArrowRight':
        if (hasNext) handleNext();
        break;
      case '+':
      case '=':
        if (isImage) handleZoomIn();
        break;
      case '-':
        if (isImage) handleZoomOut();
        break;
      case 'r':
      case 'R':
        if (isImage) handleRotate();
        break;
    }
  }, [open, onOpenChange, hasPrevious, hasNext, handlePrevious, handleNext, isImage, handleZoomIn, handleZoomOut, handleRotate]);

  useEffect(() => {
    if (open) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [open, handleKeyDown]);

  if (!attachment) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 overflow-hidden">
        {/* Header */}
        <DialogHeader className="flex flex-row items-center justify-between p-4 border-b bg-background">
          <div className="flex-1 min-w-0">
            <DialogTitle className="text-lg font-semibold truncate">
              {attachment.original_name}
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground mt-1">
              {attachment.uploader.first_name} {attachment.uploader.last_name} • {' '}
              {new Date(attachment.created_at).toLocaleDateString()}
              {attachment.description && ` • ${attachment.description}`}
            </DialogDescription>
          </div>

          {/* Navigation Controls */}
          {canNavigate && (
            <div className="flex items-center gap-2 mx-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={!hasPrevious}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm text-muted-foreground px-2">
                {currentIndex + 1} of {attachments.length}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNext}
                disabled={!hasNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {isImage && (
              <>
                <Button variant="outline" size="sm" onClick={handleZoomOut} title="Zoom out">
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={handleZoomIn} title="Zoom in">
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={handleRotate} title="Rotate">
                  <RotateCw className="h-4 w-4" />
                </Button>
              </>
            )}
            <Button variant="outline" size="sm" onClick={handleDownload} title="Download file">
              <Download className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleOpenInNewTab} title="Open in new tab">
              <ExternalLink className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={() => onOpenChange(false)} title="Close">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex-1 relative overflow-hidden bg-muted/30">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          )}

          {error && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <p className="text-destructive mb-4">{error}</p>
                <Button onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download File
                </Button>
              </div>
            </div>
          )}

          {!error && isViewable && (
            <>
              {isImage && (
                <div className="w-full h-full flex items-center justify-center p-4 overflow-auto">
                  <img
                    src={`/attachments/${attachment.id}/view`}
                    alt={attachment.original_name}
                    onLoad={handleLoad}
                    onError={handleError}
                    style={{
                      transform: `scale(${zoom}) rotate(${rotation}deg)`,
                      transition: 'transform 0.2s ease',
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'contain',
                    }}
                  />
                </div>
              )}
              {isPDF && (
                <div className="w-full h-full">
                  <iframe
                    src={`/attachments/${attachment.id}/view#toolbar=0`}
                    className="w-full h-full border-0"
                    onLoad={handleLoad}
                    onError={handleError}
                    title={attachment.original_name}
                  />
                </div>
              )}
            </>
          )}

          {!isViewable && !error && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <p className="text-muted-foreground mb-4">
                  This file type cannot be previewed in the browser.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={handleDownload}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <Button variant="outline" onClick={handleOpenInNewTab}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open in New Tab
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer with keyboard shortcuts */}
        <div className="px-4 py-2 border-t bg-muted/30 text-xs text-muted-foreground">
          <div className="flex justify-between items-center">
            <span>
              {attachment.file_size ? `${(attachment.file_size / 1024).toFixed(1)} KB` : ''}
            </span>
            <span>
              ESC: Close • ←→: Navigate • +/-: Zoom • R: Rotate
            </span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
