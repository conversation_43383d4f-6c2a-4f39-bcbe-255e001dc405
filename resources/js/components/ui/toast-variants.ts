import { cva } from 'class-variance-authority';

export const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] hover:shadow-xl",
  {
    variants: {
      variant: {
        default: "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700",
        success: "bg-success text-success-foreground border-success",
        destructive: "bg-destructive text-destructive-foreground border-destructive",
        warning: "bg-warning text-warning-foreground border-warning",
        info: "bg-info text-info-foreground border-info",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);
