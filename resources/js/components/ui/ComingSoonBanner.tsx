import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Construction, X } from 'lucide-react';

interface ComingSoonBannerProps {
    feature: string;
    onClose: () => void;
}

export default function ComingSoonBanner({ feature, onClose }: ComingSoonBannerProps) {
    return (
        <Alert className="border-orange-200 bg-orange-50">
            <Construction className="h-4 w-4 text-orange-600" />
            <AlertDescription className="flex items-center justify-between">
                <span className="text-orange-800">
                    <strong>{feature}</strong> feature is coming soon! We're working hard to bring this to you.
                </span>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="h-6 w-6 p-0 text-orange-600 hover:text-orange-800"
                >
                    <X className="h-4 w-4" />
                </Button>
            </AlertDescription>
        </Alert>
    );
}