import { useState, useEffect } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { router } from '@inertiajs/react';

interface DashboardContext {
  type: string;
  key: string;
  label: string;
  organization?: {
    id: number;
    name: string;
  } | null;
  department?: {
    id: number;
    name: string;
  } | null;
  priority: number;
  role_id?: number;
  department_id?: number;
}

interface DashboardSwitcherProps {
  availableContexts: DashboardContext[];
  currentContext?: DashboardContext;
}

export default function DashboardSwitcher({ availableContexts, currentContext }: DashboardSwitcherProps) {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<string>('');
  const [displayValue, setDisplayValue] = useState<string>('');

  // Find the current context based on URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const roleParam = urlParams.get('role');
    const departmentParam = urlParams.get('department');
    const roleIdParam = urlParams.get('role_id');
    const organizationIdParam = urlParams.get('organization_id');

    // If we have a currentContext prop, use that
    if (currentContext) {
      setValue(getContextValue(currentContext));
      setDisplayValue(getContextDisplayName(currentContext));
      return;
    }

    // Otherwise try to determine from URL params
    let matchedContext: DashboardContext | undefined;

    for (const context of availableContexts) {
      const matchesRole = !roleParam || context.key === roleParam;
      const matchesDepartment = !departmentParam || 
        (context.department && context.department.id.toString() === departmentParam);
      const matchesRoleId = !roleIdParam || 
        (context.role_id && context.role_id.toString() === roleIdParam);
      const matchesOrganization = !organizationIdParam || 
        (context.organization && context.organization.id.toString() === organizationIdParam);

      if (matchesRole && matchesDepartment && matchesRoleId && matchesOrganization) {
        matchedContext = context;
        break;
      }
    }

    // If we found a match, set it as the current value
    if (matchedContext) {
      setValue(getContextValue(matchedContext));
      setDisplayValue(getContextDisplayName(matchedContext));
    } else if (availableContexts.length > 0) {
      // Default to first context if no match found
      setValue(getContextValue(availableContexts[0]));
      setDisplayValue(getContextDisplayName(availableContexts[0]));
    }
  }, [availableContexts, currentContext]);

  // Helper function to get a unique value for each context
  const getContextValue = (context: DashboardContext): string => {
    if (context.key === 'platform_admin') {
      return 'platform_admin';
    } else if (context.role_id) {
      return `role_${context.role_id}`;
    } else if (context.department_id) {
      return `department_${context.department_id}`;
    } else {
      return `${context.key}_${context.organization?.id || 0}`;
    }
  };

  // Helper function to get display name for a context
  const getContextDisplayName = (context: DashboardContext): string => {
    let name = context.label;
    
    if (context.organization) {
      name += ` - ${context.organization.name}`;
    }
    
    if (context.department) {
      name += ` (${context.department.name})`;
    }
    
    return name;
  };

  // Handle context switch
  const handleContextSwitch = (selectedContext: DashboardContext) => {
    const params: Record<string, string> = {};
    
    if (selectedContext.key) {
      params.role = selectedContext.key;
    }
    
    if (selectedContext.department_id) {
      params.department = selectedContext.department_id.toString();
    }
    
    if (selectedContext.role_id) {
      params.role_id = selectedContext.role_id.toString();
    }
    
    if (selectedContext.organization?.id) {
      params.organization_id = selectedContext.organization.id.toString();
    }
    
    router.get('/dashboard', params);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {displayValue || "Select dashboard..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search dashboard..." />
          <CommandEmpty>No dashboard found.</CommandEmpty>
          <CommandGroup>
            {availableContexts.map((context) => {
              const contextValue = getContextValue(context);
              const displayName = getContextDisplayName(context);
              
              return (
                <CommandItem
                  key={contextValue}
                  value={contextValue}
                  onSelect={() => {
                    setValue(contextValue);
                    setDisplayValue(displayName);
                    setOpen(false);
                    handleContextSwitch(context);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === contextValue ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {displayName}
                </CommandItem>
              );
            })}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}