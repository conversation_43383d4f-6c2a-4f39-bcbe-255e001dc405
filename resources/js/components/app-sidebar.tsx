import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarGroup, SidebarGroupLabel } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { LayoutGrid, Building2, Users, UserCog, Briefcase, GitBranch, FileText, CreditCard, ClipboardCheck, User, LogOut, ListOrdered, BarChart3, ChevronDown, ChevronRight, Wallet, Package, Boxes, Send } from 'lucide-react';
// import { BookOpen, Folder, LayoutGrid, Building2, Users, UserCog, Briefcase, GitBranch } from 'lucide-react';
import AppLogo from './app-logo';
import NotificationSidebarItem from './notifications/NotificationSidebarItem';
import { useState } from 'react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

// Common navigation items for all users
const commonNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

// Navigation items for platform admin
const platformAdminNavItems: NavItem[] = [
    {
        title: 'Organizations',
        href: '/organizations',
        icon: Building2,
    },
    {
        title: 'Users',
        href: '/users',
        icon: Users,
    },
    {
        title: 'Roles',
        href: '/roles',
        icon: UserCog,
    },
];

// Navigation items for organization admin
const organizationAdminNavItems: NavItem[] = [
    {
        title: 'Branches',
        href: '/branches',
        icon: GitBranch,
    },
    {
        title: 'Departments',
        href: '/departments',
        icon: Briefcase,
    },
    {
        title: 'Users',
        href: '/users',
        icon: Users,
    },
    {
        title: 'Roles',
        href: '/roles',
        icon: UserCog,
    },
    {
        title: 'Workflows',
        href: '/approval-workflows',
        icon: ListOrdered,
    },
    {
        title: 'Chart of Accounts',
        href: '/chart-of-accounts',
        icon: BarChart3,
    },
];

// Navigation items for HOD
const hodNavItems: NavItem[] = [
    {
        title: 'Users',
        href: '/users',
        icon: Users,
    },
];

// Navigation items for Finance Manager
const financeManagerNavItems: NavItem[] = [
    {
        title: 'Cash Floats',
        href: '/cash-floats',
        icon: Wallet,
    },
    {
        title: 'Transactions',
        href: '/transactions',
        icon: CreditCard,
    },
    {
        title: 'Chart of Accounts',
        href: '/chart-of-accounts',
        icon: BarChart3,
    },
];

const footerNavItems: NavItem[] = [
    // {
    //     title: 'Repository',
    //     href: 'https://github.com/laravel/react-starter-kit',
    //     icon: Folder,
    // },
    // {
    //     title: 'Documentation',
    //     href: 'https://laravel.com/docs/starter-kits',
    //     icon: BookOpen,
    // },
];

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const user = auth.user;
    // const cashFloats = pageProps.cashFloats as Array<unknown> || [];
    
    // State for collapsible sections
    const [isNotificationsOpen, setIsNotificationsOpen] = useState(true);
    const [isRequisitionsOpen, setIsRequisitionsOpen] = useState(true);
    const [isDisbursementOpen, setIsDisbursementOpen] = useState(true);
    const [isAccountOpen, setIsAccountOpen] = useState(true);
    const [isPlatformAdminOpen, setIsPlatformAdminOpen] = useState(true);
    const [isOrgAdminOpen, setIsOrgAdminOpen] = useState(true);
    const [isHODOpen, setIsHODOpen] = useState(true);
    const [isFinanceManagerOpen, setIsFinanceManagerOpen] = useState(true);

    // Get roles and permissions as string arrays (as they come from backend)
    const roles = (user.roles as unknown as string[]) || [];
    const permissions = (user.permissions as unknown as string[]) || [];
    
    // Check user roles
    const isPlatformAdmin = user.is_platform_admin;
    const isOrgAdmin = roles.includes('Organization Admin');
    const isHOD = roles.includes('HOD');
    const isFinanceManager = roles.includes('Finance Manager');

    // Check if user has approval privileges
    const hasApprovalPrivileges = roles.includes('approver') || 
                                 permissions.includes('approve_requisitions') || 
                                 permissions.includes('approver');

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                {/* Always show Dashboard */}
                <NavMain items={commonNavItems} />

                {/* Platform Admin Section - Collapsible */}
                {isPlatformAdmin && (
                    <Collapsible open={isPlatformAdminOpen} onOpenChange={setIsPlatformAdminOpen}>
                        <SidebarGroup className="px-2 py-0">
                            <CollapsibleTrigger asChild>
                                <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                    <span>Platform Admin</span>
                                    {isPlatformAdminOpen ? (
                                        <ChevronDown className="h-4 w-4" />
                                    ) : (
                                        <ChevronRight className="h-4 w-4" />
                                    )}
                                </SidebarGroupLabel>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenu>
                                    {platformAdminNavItems.map((item) => (
                                        <SidebarMenuItem key={item.href}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.href} prefetch>
                                                    {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </CollapsibleContent>
                        </SidebarGroup>
                    </Collapsible>
                )}

                {/* Organization Admin Section - Collapsible */}
                {isOrgAdmin && !isPlatformAdmin && (
                    <Collapsible open={isOrgAdminOpen} onOpenChange={setIsOrgAdminOpen}>
                        <SidebarGroup className="px-2 py-0">
                            <CollapsibleTrigger asChild>
                                <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                    <span>Organization Admin</span>
                                    {isOrgAdminOpen ? (
                                        <ChevronDown className="h-4 w-4" />
                                    ) : (
                                        <ChevronRight className="h-4 w-4" />
                                    )}
                                </SidebarGroupLabel>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenu>
                                    {organizationAdminNavItems.map((item) => (
                                        <SidebarMenuItem key={item.href}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.href} prefetch>
                                                    {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </CollapsibleContent>
                        </SidebarGroup>
                    </Collapsible>
                )}

                {/* Finance Manager Section - Collapsible */}
                {isFinanceManager && !isOrgAdmin && !isPlatformAdmin && (
                    <Collapsible open={isFinanceManagerOpen} onOpenChange={setIsFinanceManagerOpen}>
                        <SidebarGroup className="px-2 py-0">
                            <CollapsibleTrigger asChild>
                                <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                    <span>Finance Management</span>
                                    {isFinanceManagerOpen ? (
                                        <ChevronDown className="h-4 w-4" />
                                    ) : (
                                        <ChevronRight className="h-4 w-4" />
                                    )}
                                </SidebarGroupLabel>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenu>
                                    {financeManagerNavItems.filter(item => {
                                        // Filter out Chart of Accounts if user doesn't have permission
                                        if (item.href === '/chart-of-accounts') {
                                            return permissions.includes('view-chart-of-accounts') || 
                                                   permissions.includes('manage-chart-of-accounts') ||
                                                   permissions.includes('view-finances');
                                        }
                                        return true;
                                    }).map((item) => (
                                        <SidebarMenuItem key={item.href}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.href} prefetch>
                                                    {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </CollapsibleContent>
                        </SidebarGroup>
                    </Collapsible>
                )}

                {/* HOD Section - Collapsible */}
                {isHOD && !isFinanceManager && !isOrgAdmin && !isPlatformAdmin && (
                    <Collapsible open={isHODOpen} onOpenChange={setIsHODOpen}>
                        <SidebarGroup className="px-2 py-0">
                            <CollapsibleTrigger asChild>
                                <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                    <span>Department Management</span>
                                    {isHODOpen ? (
                                        <ChevronDown className="h-4 w-4" />
                                    ) : (
                                        <ChevronRight className="h-4 w-4" />
                                    )}
                                </SidebarGroupLabel>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenu>
                                    {hodNavItems.map((item) => (
                                        <SidebarMenuItem key={item.href}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.href} prefetch>
                                                    {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </CollapsibleContent>
                        </SidebarGroup>
                    </Collapsible>
                )}

                {/* Notifications Section - Collapsible */}
                <Collapsible open={isNotificationsOpen} onOpenChange={setIsNotificationsOpen}>
                    <SidebarGroup className="px-2 py-0">
                        <CollapsibleTrigger asChild>
                            <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                <span>Notifications</span>
                                {isNotificationsOpen ? (
                                    <ChevronDown className="h-4 w-4" />
                                ) : (
                                    <ChevronRight className="h-4 w-4" />
                                )}
                            </SidebarGroupLabel>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <SidebarMenu>
                                <NotificationSidebarItem />
                            </SidebarMenu>
                        </CollapsibleContent>
                    </SidebarGroup>
                </Collapsible>

                {/* Requisitions Section - Collapsible */}
                <Collapsible open={isRequisitionsOpen} onOpenChange={setIsRequisitionsOpen}>
                    <SidebarGroup className="px-2 py-0">
                        <CollapsibleTrigger asChild>
                            <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                <span>Requisitions</span>
                                {isRequisitionsOpen ? (
                                    <ChevronDown className="h-4 w-4" />
                                ) : (
                                    <ChevronRight className="h-4 w-4" />
                                )}
                            </SidebarGroupLabel>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <SidebarMenu>
                                <SidebarMenuItem>
                                    <SidebarMenuButton asChild>
                                        <Link href={route('requisitions.history')} prefetch>
                                            <FileText className="h-4 w-4 mr-1" />
                                            <span>Requisitions</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                                {hasApprovalPrivileges && (
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild>
                                            <Link href="/requisitions/approvals" prefetch>
                                                <ClipboardCheck className="h-4 w-4 mr-1" />
                                                <span>Approvals</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                )}

                                {/* Store Requisitions */}
                                {(permissions.includes('view-store-requisitions') || permissions.includes('store-keep')) && (
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild>
                                            <Link href="/store-requisitions" prefetch>
                                                <Package className="h-4 w-4 mr-1" />
                                                <span>Store Requisitions</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                )}

                                {/* Item Distribution */}
                                {(permissions.includes('issue-store-items') || permissions.includes('store-keep')) && (
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild>
                                            <Link href="/store-requisitions/issue" prefetch>
                                                <Send className="h-4 w-4 mr-1" />
                                                <span>Item Distribution</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                )}

                                {/* Inventory Management */}
                                {(permissions.includes('view-inventory') || permissions.includes('store-keep')) && (
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild>
                                            <Link href="/inventory" prefetch>
                                                <Boxes className="h-4 w-4 mr-1" />
                                                <span>Inventory Management</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                )}




                            </SidebarMenu>
                        </CollapsibleContent>
                    </SidebarGroup>
                </Collapsible>

                {/* Disbursement section - Collapsible */}
                <Collapsible open={isDisbursementOpen} onOpenChange={setIsDisbursementOpen}>
                    <SidebarGroup className="px-2 py-0">
                        <CollapsibleTrigger asChild>
                            <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                <span>Disbursement</span>
                                {isDisbursementOpen ? (
                                    <ChevronDown className="h-4 w-4" />
                                ) : (
                                    <ChevronRight className="h-4 w-4" />
                                )}
                            </SidebarGroupLabel>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <SidebarMenu>
                                <SidebarMenuItem>
                                    <SidebarMenuButton asChild>
                                        <Link href={route('disbursement.index')} prefetch>
                                            <CreditCard className="h-4 w-4 mr-1" />
                                            <span>Disbursements</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            </SidebarMenu>
                        </CollapsibleContent>
                    </SidebarGroup>
                </Collapsible>

                {/* Account Section - Collapsible */}
                <Collapsible open={isAccountOpen} onOpenChange={setIsAccountOpen}>
                    <SidebarGroup className="px-2 py-0">
                        <CollapsibleTrigger asChild>
                            <SidebarGroupLabel className="cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors flex items-center justify-between">
                                <span>Account</span>
                                {isAccountOpen ? (
                                    <ChevronDown className="h-4 w-4" />
                                ) : (
                                    <ChevronRight className="h-4 w-4" />
                                )}
                            </SidebarGroupLabel>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <SidebarMenu>
                                <SidebarMenuItem>
                                    <SidebarMenuButton asChild>
                                        <Link href={route('profile.edit')} prefetch>
                                            <User className="h-4 w-4 mr-1" />
                                            <span>View Profile</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                                <SidebarMenuItem>
                                    <SidebarMenuButton asChild>
                                        <Link href={route('logout')} method="post" as="button">
                                            <LogOut className="h-4 w-4 mr-1" />
                                            <span>Logout</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            </SidebarMenu>
                        </CollapsibleContent>
                    </SidebarGroup>
                </Collapsible>
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}