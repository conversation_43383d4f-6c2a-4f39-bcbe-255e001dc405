import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { router } from '@inertiajs/react';
import { ErrorBoundary } from './ErrorBoundary';
import { cn } from "@/lib/utils";
import { Upload, File, X } from 'lucide-react';

interface FileUploadProps {
  entityType: 'requisition' | 'transaction';
  entityId: number;
  uploadedAtStep?: string;
  onUploadSuccess?: (attachments: unknown[]) => void;
  className?: string;
}

interface FileWithDescription {
  file: File;
  description: string;
}

const FileUploadContent: React.FC<FileUploadProps> = ({
  entityType,
  entityId,
  uploadedAtStep,
  onUploadSuccess,
  className = '',
}) => {
  const [selectedFiles, setSelectedFiles] = useState<FileWithDescription[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mounted = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mounted.current = false;
    };
  }, []);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files) return;

    try {
      const files = Array.from(event.target.files);
      const newFiles = files.map(file => ({
        file,
        description: '',
      }));

      if (mounted.current) {
        setSelectedFiles(prev => [...prev, ...newFiles]);
        setError(null);
      }
    } catch (err) {
      console.error('Error handling file selection:', err);
      if (mounted.current) {
        setError('Failed to process selected files. Please try again.');
      }
    }
  };

  const removeFile = (index: number) => {
    if (mounted.current) {
      setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updateDescription = (index: number, description: string) => {
    if (mounted.current) {
      setSelectedFiles(prev =>
        prev.map((item, i) => (i === index ? { ...item, description } : item))
      );
    }
  };

  const resetForm = () => {
    if (mounted.current) {
      setSelectedFiles([]);
      setError(null);
      if (fileInputRef.current) {
        try {
          fileInputRef.current.value = '';
        } catch (err) {
          console.error('Error resetting file input:', err);
        }
      }
    }
  };

  const handleUpload = async () => {
    if (!mounted.current) return;

    if (selectedFiles.length === 0) {
      setError('Please select at least one file to upload.');
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();

      selectedFiles.forEach((item, index) => {
        formData.append(`files[${index}]`, item.file);
        formData.append(`descriptions[${index}]`, item.description);
      });

      if (uploadedAtStep) {
        formData.append('uploaded_at_step', uploadedAtStep);
      }

      const uploadUrl =
        entityType === 'requisition' ? `/attachments/requisitions/${entityId}/upload` : `/attachments/transactions/${entityId}/upload`;

      router.post(uploadUrl, formData, {
        onSuccess: () => {
          if (mounted.current) {
            resetForm();
            if (onUploadSuccess) {
              onUploadSuccess([]);
            }
          }
        },
        onError: (errors) => {
          if (mounted.current) {
            setError(errors.upload || errors.files?.[0] || 'Failed to upload files. Please try again.');
          }
        },
        onFinish: () => {
          if (mounted.current) {
            setIsUploading(false);
          }
        },
      });
    } catch (err) {
      console.error('Error during upload:', err);
      if (mounted.current) {
        setError('An unexpected error occurred. Please try again.');
        setIsUploading(false);
      }
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Evidence Files
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">{error}</div>}

        <div>
          <Label htmlFor="file-upload">Select Files</Label>
          <input
            ref={fileInputRef}
            id="file-upload"
            name="file-upload"
            type="file"
            multiple
            accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.txt"
            onChange={handleFileSelect}
            disabled={isUploading}
            className={cn(
              "mt-1 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0",
              "file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground",
              "hover:file:bg-primary/90",
              "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1",
              "text-sm shadow-sm transition-colors file:border-0 file:bg-transparent",
              "file:text-foreground/100",
              "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
              "disabled:cursor-not-allowed disabled:opacity-50",
              "dark:file:bg-primary/10 dark:hover:file:bg-primary/20"
            )}
          />
          <p className="text-sm text-gray-500 mt-1">
            Supported formats: PDF, Images, Word, Excel, Text files (Max 10MB each)
          </p>
        </div>

        {selectedFiles.length > 0 && (
          <div className="space-y-3">
            <Label>Selected Files</Label>
            {selectedFiles.map((item, index) => (
              <div key={index} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <File className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">{item.file.name}</span>
                    <span className="text-xs text-gray-500">
                      ({formatFileSize(item.file.size)})
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div>
                  <Label htmlFor={`description-${index}`} className="text-xs">
                    Description (optional)
                  </Label>
                  <Textarea
                    id={`description-${index}`}
                    placeholder="Describe this file..."
                    value={item.description}
                    onChange={(e) => updateDescription(index, e.target.value)}
                    className="mt-1"
                    rows={2}
                    disabled={isUploading}
                  />
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedFiles.length > 0 && (
          <Button
            onClick={handleUpload}
            disabled={isUploading}
            className="w-full"
          >
            {isUploading ? 'Uploading...' : `Upload ${selectedFiles.length} File(s)`}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export const FileUpload: React.FC<FileUploadProps> = (props) => {
  return (
    <ErrorBoundary>
      <FileUploadContent {...props} />
    </ErrorBoundary>
  );
};
