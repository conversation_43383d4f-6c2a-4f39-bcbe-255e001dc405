import React, { useMemo, useCallback } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import StoreRequisitionItemsManager from '@/components/StoreRequisitions/StoreRequisitionItemsManager';
import { StoreRequisitionFormData, StoreRequisitionItemFormData, InventoryItem } from '@/types/store-requisitions';

interface Department {
    id: number;
    name: string;
    branch_id: number;
    organization_id: number;
}

interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface StoreRequisitionFormProps {
    data: StoreRequisitionFormData;
    setData: (key: keyof StoreRequisitionFormData, value: string | number | StoreRequisitionItemFormData[] | undefined) => void;
    errors: Record<string, string>;
    clearErrors: (field?: string) => void;
    setError?: (field: string, message: string) => void;
    inventoryItems: InventoryItem[];
    branches: Branch[];
    departments: Department[];
    userBranchId?: number;
    userDepartmentId?: number;
    userDepartments: Department[];
}

export function StoreRequisitionForm({
    data,
    setData,
    errors,
    clearErrors,
    setError,
    inventoryItems,
    branches,
    departments,
    userDepartments,
}: StoreRequisitionFormProps) {

    // Memoize filtered departments to prevent unnecessary recalculations
    const filteredDepartments = useMemo(() => {
        return data.branch_id
            ? departments.filter(dept => dept.branch_id === data.branch_id)
            : departments.length > 0 ? departments : userDepartments;
    }, [data.branch_id, departments, userDepartments]);

    // Handle branch change with useCallback for performance
    const handleBranchChange = useCallback((branchId: string) => {
        const newBranchId = parseInt(branchId);
        setData('branch_id', newBranchId);

        // Reset department if it doesn't belong to the new branch
        const branchDepartments = departments.filter(dept => dept.branch_id === newBranchId);
        if (data.department_id && !branchDepartments.find(dept => dept.id === data.department_id)) {
            setData('department_id', undefined);
        }

        clearErrors('branch_id');
    }, [data.department_id, departments, setData, clearErrors]);

    // Handle department change with useCallback
    const handleDepartmentChange = useCallback((departmentId: string) => {
        setData('department_id', parseInt(departmentId));
        clearErrors('department_id');
    }, [setData, clearErrors]);

    // Handle purpose change with useCallback
    const handlePurposeChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setData('purpose', e.target.value);
        clearErrors('purpose');
    }, [setData, clearErrors]);

    // Handle item changes with useCallback
    const handleItemChange = useCallback((index: number, field: keyof StoreRequisitionItemFormData, value: string) => {
        const updatedItems = [...data.items];
        if (field === 'inventory_item_id') {
            updatedItems[index] = { ...updatedItems[index], [field]: value ? parseInt(value) : null };
        } else if (field === 'quantity_requested') {
            updatedItems[index] = { ...updatedItems[index], [field]: parseInt(value) || 1 };
        }
        setData('items', updatedItems);
        clearErrors(`items.${index}.${field}`);
    }, [data.items, setData, clearErrors]);

    // Add new item with useCallback
    const handleAddItem = useCallback(() => {
        setData('items', [...data.items, { inventory_item_id: null, quantity_requested: 1 }]);
    }, [data.items, setData]);

    // Remove item with validation
    const handleRemoveItem = (index: number) => {
        if (data.items.length === 1) {
            setError?.('items', 'At least one item is required');
            return;
        }
        const updatedItems = data.items.filter((_, i) => i !== index);
        setData('items', updatedItems);
    };

    return (
        <div className="space-y-6">
            {/* Branch and Department Selection */}
            <Card>
                <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-lg sm:text-xl lg:text-2xl">Requisition Information</CardTitle>
                    <CardDescription className="text-sm sm:text-base text-muted-foreground">
                        Select the branch and department for this requisition
                    </CardDescription>
                </CardHeader>
                <CardContent className="p-4 sm:p-6 pt-0 sm:pt-0">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                        {/* Branch Selection */}
                        <div className="space-y-3">
                            <Label htmlFor="branch_id" className="text-sm sm:text-base font-medium">
                                Branch <span className="text-destructive">*</span>
                            </Label>
                            <Select
                                value={data.branch_id?.toString() || ''}
                                onValueChange={handleBranchChange}
                            >
                                <SelectTrigger
                                    className={`h-11 sm:h-12 ${errors.branch_id ? 'border-destructive' : ''}`}
                                    id="branch_id"
                                >
                                    <SelectValue placeholder="Select a branch" />
                                </SelectTrigger>
                                <SelectContent>
                                    {branches.map((branch) => (
                                        <SelectItem key={branch.id} value={branch.id.toString()}>
                                            {branch.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.branch_id && (
                                <p className="text-sm text-destructive flex items-start gap-1">
                                    <span className="text-destructive">⚠</span>
                                    {errors.branch_id}
                                </p>
                            )}
                        </div>

                        {/* Department Selection */}
                        <div className="space-y-3">
                            <Label htmlFor="department_id" className="text-sm sm:text-base font-medium">
                                Department <span className="text-destructive">*</span>
                            </Label>
                            <Select
                                value={data.department_id?.toString() || ''}
                                onValueChange={handleDepartmentChange}
                                disabled={!data.branch_id}
                            >
                                <SelectTrigger
                                    className={`h-11 sm:h-12 ${errors.department_id ? 'border-destructive' : ''} ${!data.branch_id ? 'opacity-50' : ''}`}
                                    id="department_id"
                                >
                                    <SelectValue placeholder={!data.branch_id ? "Select a branch first" : "Select a department"} />
                                </SelectTrigger>
                                <SelectContent>
                                    {filteredDepartments.map((department) => (
                                        <SelectItem key={department.id} value={department.id.toString()}>
                                            {department.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.department_id && (
                                <p className="text-sm text-destructive flex items-start gap-1">
                                    <span className="text-destructive">⚠</span>
                                    {errors.department_id}
                                </p>
                            )}
                        </div>
                    </div>

                    {/* Purpose */}
                    <div className="space-y-3 mt-6 lg:col-span-2">
                        <Label htmlFor="purpose" className="text-sm sm:text-base font-medium">
                            Purpose <span className="text-destructive">*</span>
                        </Label>
                        <Textarea
                            id="purpose"
                            placeholder="Describe the purpose of this requisition..."
                            value={data.purpose}
                            onChange={handlePurposeChange}
                            className={`min-h-[120px] sm:min-h-[140px] resize-none ${errors.purpose ? 'border-destructive' : ''}`}
                        />
                        {errors.purpose && (
                            <p className="text-sm text-destructive flex items-start gap-1">
                                <span className="text-destructive">⚠</span>
                                {errors.purpose}
                            </p>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Items Card */}
            <Card>
                <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-lg sm:text-xl lg:text-2xl">Requested Items</CardTitle>
                    <CardDescription className="text-sm sm:text-base text-muted-foreground">
                        Select the items you need from the inventory
                    </CardDescription>
                </CardHeader>
                <CardContent className="p-4 sm:p-6 pt-0 sm:pt-0">
                    <StoreRequisitionItemsManager
                        items={data.items}
                        inventoryItems={inventoryItems}
                        errors={errors}
                        onItemChange={handleItemChange}
                        onAddItem={handleAddItem}
                        onRemoveItem={handleRemoveItem}
                    />
                </CardContent>
            </Card>
        </div>
    );
}
