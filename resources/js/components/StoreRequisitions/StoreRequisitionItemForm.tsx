import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Trash, AlertTriangle, Check, ChevronsUpDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { StoreRequisitionItemFormData, InventoryItem } from '@/types/store-requisitions';

interface StoreRequisitionItemFormProps {
    item: StoreRequisitionItemFormData;
    index: number;
    inventoryItems: InventoryItem[];
    errors: Record<string, string>;
    onItemChange: (index: number, field: keyof StoreRequisitionItemFormData, value: string) => void;
    onRemoveItem: (index: number) => void;
    canRemove: boolean;
}

export default function StoreRequisitionItemForm({
    item,
    index,
    inventoryItems,
    errors,
    onItemChange,
    onRemoveItem,
    canRemove
}: StoreRequisitionItemFormProps) {
    const [open, setOpen] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    // Local state for input display value to handle UX while keeping data model as number
    const [inputValue, setInputValue] = useState(item.quantity_requested.toString());

    const inventoryItem = inventoryItems.find(invItem => invItem.id === item.inventory_item_id);

    // Check if requested quantity exceeds available stock
    const isQuantityExceedsStock = inventoryItem && item.quantity_requested > inventoryItem.quantity_on_hand;

    // Filter inventory items based on search
    const filteredItems = inventoryItems.filter(invItem =>
        invItem.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        invItem.sku.toLowerCase().includes(searchValue.toLowerCase())
    );

    // Sync input value when item quantity changes externally
    useEffect(() => {
        setInputValue(item.quantity_requested.toString());
    }, [item.quantity_requested]);
    


    return (
        <Card className="overflow-hidden border border-border/50 hover:border-border transition-colors">
            <CardContent className="p-4 sm:p-6">
                <div className="grid gap-4 sm:gap-6 lg:grid-cols-3">
                    <div className="space-y-3 lg:col-span-2">
                        <Label htmlFor={`item-${index}`} className="text-sm sm:text-base font-medium">
                            Inventory Item <span className="text-destructive">*</span>
                        </Label>
                        <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="outline"
                                    role="combobox"
                                    aria-expanded={open}
                                    className={cn(
                                        "min-h-[3rem] sm:min-h-[3.5rem] w-full justify-between text-left",
                                        "hover:bg-muted hover:text-foreground transition-colors",
                                        errors[`items.${index}.inventory_item_id`] && "border-destructive",
                                        !inventoryItem && "text-muted-foreground"
                                    )}
                                >
                                    {inventoryItem ? (
                                        <div className="flex flex-col items-start justify-start w-full min-w-0 text-left">
                                            <div className="flex items-center gap-2 w-full justify-start">
                                                <span className="font-medium text-sm sm:text-base truncate flex-1 min-w-0 text-left">
                                                    {inventoryItem.name}
                                                </span>
                                            </div>
                                            <span className="text-xs text-muted-foreground truncate w-full text-left block">
                                                {inventoryItem.sku} • {inventoryItem.quantity_on_hand} {inventoryItem.unit_of_measure} available
                                            </span>
                                        </div>
                                    ) : (
                                        <span className="text-left">Search and select inventory item...</span>
                                    )}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[400px] sm:w-[500px] p-0 z-50" align="start">
                                <Command>
                                    <div className="flex items-center border-b px-3">
                                        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                                        <CommandInput
                                            placeholder="Search by name or SKU..."
                                            value={searchValue}
                                            onValueChange={setSearchValue}
                                            className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                                        />
                                    </div>
                                    <CommandList className="max-h-[300px] overflow-y-auto">
                                        <CommandEmpty>No inventory items found.</CommandEmpty>
                                        <CommandGroup>
                                            {filteredItems.map((invItem) => (
                                                <CommandItem
                                                    key={invItem.id}
                                                    value={`${invItem.name} ${invItem.sku}`}
                                                    onSelect={() => {
                                                        onItemChange(index, 'inventory_item_id', invItem.id.toString());
                                                        setOpen(false);
                                                        setSearchValue('');
                                                    }}
                                                    className="py-3 hover:bg-muted hover:text-foreground cursor-pointer transition-colors"
                                                >
                                                    <Check
                                                        className={cn(
                                                            "mr-2 h-4 w-4",
                                                            inventoryItem?.id === invItem.id ? "opacity-100" : "opacity-0"
                                                        )}
                                                    />
                                                    <div className="flex flex-col w-full min-w-0 text-left">
                                                        <div className="flex items-center gap-2 flex-wrap justify-start">
                                                            <span className="font-medium text-sm sm:text-base truncate flex-1 min-w-0 text-left">
                                                                {invItem.name}
                                                            </span>
                                                        </div>
                                                        <div className="text-xs sm:text-sm text-muted-foreground mt-1 flex flex-wrap gap-2 text-left justify-start">
                                                            <span className="flex-shrink-0 text-left">SKU: {invItem.sku}</span>
                                                            <span className="flex-shrink-0">•</span>
                                                            <span className="flex-shrink-0 text-left">
                                                                {invItem.quantity_on_hand} {invItem.unit_of_measure} available
                                                            </span>
                                                        </div>
                                                    </div>
                                                </CommandItem>
                                            ))}
                                        </CommandGroup>
                                    </CommandList>
                                </Command>
                            </PopoverContent>
                        </Popover>
                        {errors[`items.${index}.inventory_item_id`] && (
                            <p className="text-xs sm:text-sm text-destructive flex items-start gap-1">
                                <span className="text-destructive">⚠</span>
                                {errors[`items.${index}.inventory_item_id`]}
                            </p>
                        )}
                    </div>

                    <div className="space-y-3">
                        <Label htmlFor={`quantity-${index}`} className="text-sm sm:text-base font-medium">
                            Quantity <span className="text-destructive">*</span>
                        </Label>
                        <div className="flex items-center gap-2">
                            <Input
                                id={`quantity-${index}`}
                                type="number"
                                min="0.01"
                                step="0.01"
                                value={inputValue}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    setInputValue(value);

                                    // Convert to number for data model, but allow empty during editing
                                    if (value === '') {
                                        // Don't update the data model while user is clearing the field
                                        return;
                                    }

                                    const numValue = parseFloat(value);
                                    if (!isNaN(numValue) && numValue > 0) {
                                        onItemChange(index, 'quantity_requested', numValue.toString());
                                    }
                                }}
                                onBlur={(e) => {
                                    const value = e.target.value;
                                    // If empty or invalid, set to 1 and update both local state and data model
                                    if (!value || isNaN(Number(value)) || Number(value) <= 0) {
                                        setInputValue('1');
                                        onItemChange(index, 'quantity_requested', '1');
                                    } else {
                                        // Ensure data model is updated with the final value
                                        onItemChange(index, 'quantity_requested', value);
                                    }
                                }}
                                onFocus={(e) => e.target.select()}
                                placeholder="Enter quantity"
                                className={`h-11 sm:h-12 text-foreground ${errors[`items.${index}.quantity_requested`] ? 'border-destructive' : ''} ${
                                    isQuantityExceedsStock ? 'border-warning' : ''
                                }`}
                            />
                            {canRemove && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onRemoveItem(index)}
                                    className="h-11 w-11 sm:h-12 sm:w-12 flex-shrink-0 text-destructive hover:bg-destructive/10 hover:text-destructive transition-colors"
                                >
                                    <Trash className="h-4 w-4" />
                                </Button>
                            )}
                        </div>
                        
                        {/* Quantity validation messages */}
                        {inventoryItem && (
                            <div className="space-y-1">
                                <p className="text-xs text-muted-foreground">
                                    Unit: {inventoryItem.unit_of_measure}
                                </p>
                                {isQuantityExceedsStock && (
                                    <div className="flex items-center gap-1 text-warning">
                                        <AlertTriangle className="h-3 w-3" />
                                        <p className="text-xs">
                                            Requested quantity exceeds available stock ({inventoryItem.quantity_on_hand} {inventoryItem.unit_of_measure})
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                        
                        {errors[`items.${index}.quantity_requested`] && (
                            <p className="text-xs sm:text-sm text-destructive flex items-start gap-1">
                                <span className="text-destructive">⚠</span>
                                {errors[`items.${index}.quantity_requested`]}
                            </p>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
