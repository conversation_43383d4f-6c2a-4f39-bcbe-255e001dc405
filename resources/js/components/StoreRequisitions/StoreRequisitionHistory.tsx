import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { StoreRequisitionHistoryEntry } from '@/types/store-requisitions';
import {
  formatStoreRequisitionHistoryAction,
  getStoreRequisitionHistoryIconName,
  getStoreRequisitionHistoryBadgeVariant,
  formatDateTime
} from '@/utils/store-requisitions';
import {
  PlusCircle,
  Edit,
  Send,
  CheckCircle,
  XCircle,
  RotateCcw,
  Package,
  Clock
} from 'lucide-react';

interface StoreRequisitionHistoryProps {
  histories: StoreRequisitionHistoryEntry[];
}

// Helper function to render the appropriate icon
const renderHistoryIcon = (iconName: string) => {
  const iconProps = { className: "h-3 w-3" };

  switch (iconName) {
    case 'PlusCircle':
      return <PlusCircle {...iconProps} />;
    case 'Edit':
      return <Edit {...iconProps} />;
    case 'Send':
      return <Send {...iconProps} />;
    case 'CheckCircle':
      return <CheckCircle {...iconProps} />;
    case 'XCircle':
      return <XCircle {...iconProps} />;
    case 'RotateCcw':
      return <RotateCcw {...iconProps} />;
    case 'Package':
      return <Package {...iconProps} />;
    case 'Clock':
    default:
      return <Clock {...iconProps} />;
  }
};

export function StoreRequisitionHistory({ histories }: StoreRequisitionHistoryProps) {
  if (!histories || histories.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>History</CardTitle>
          <CardDescription>
            Complete history from creation to completion
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground text-center py-8">
            No history available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>History</CardTitle>
        <CardDescription>
          Complete history from creation to completion
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {histories.map((entry, index) => (
            <div key={entry.id} className="relative pl-8">
              {/* Timeline line */}
              {index < histories.length - 1 && (
                <div className="bg-border absolute top-6 left-3 h-full w-px" />
              )}

              {/* Timeline dot with appropriate icon */}
              <div className="bg-background absolute top-1 left-0 flex h-6 w-6 items-center justify-center rounded-full border">
                {renderHistoryIcon(getStoreRequisitionHistoryIconName(entry.action))}
              </div>

              {/* History content */}
              <div className="flex flex-col space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-foreground">
                    {entry.user.first_name} {entry.user.last_name}
                  </span>
                  <Badge 
                    variant={getStoreRequisitionHistoryBadgeVariant(entry.action)} 
                    className="text-foreground/90"
                  >
                    {formatStoreRequisitionHistoryAction(entry.action)}
                  </Badge>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  {formatDateTime(entry.created_at)}
                </div>
                
                {entry.comments && (
                  <div className="mt-2 rounded-md bg-muted/50 p-3">
                    <p className="text-sm text-foreground">{entry.comments}</p>
                  </div>
                )}
                
                {entry.changes && Object.keys(entry.changes).length > 0 && (
                  <div className="mt-2 rounded-md bg-muted/30 p-3">
                    <p className="text-xs font-medium text-muted-foreground mb-1">Changes:</p>
                    <div className="text-xs text-muted-foreground">
                      {Object.entries(entry.changes).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="capitalize">{key.replace(/_/g, ' ')}:</span>
                          <span className="font-medium">{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
