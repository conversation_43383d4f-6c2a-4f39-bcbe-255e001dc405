import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Package, Calendar, User, Building, Eye } from 'lucide-react';
import { Link } from '@inertiajs/react';

import { StoreRequisition } from '@/types/store-requisitions';

interface StoreRequisitionCardProps {
    requisition: StoreRequisition;
}

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'draft':
            return 'bg-[var(--status-draft)] text-[var(--status-draft-foreground)]';
        case 'pending':
            return 'bg-[var(--status-pending)] text-[var(--status-pending-foreground)]';
        case 'approved':
            return 'bg-[var(--status-approved)] text-[var(--status-approved-foreground)]';
        case 'issued':
            return 'bg-[var(--status-issued)] text-[var(--status-issued-foreground)]';
        case 'rejected':
            return 'bg-[var(--status-rejected)] text-[var(--status-rejected-foreground)]';
        case 'partial':
            return 'bg-[var(--status-partial)] text-[var(--status-partial-foreground)]';
        default:
            return 'bg-secondary text-secondary-foreground';
    }
};

const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
};

export default function StoreRequisitionCard({ requisition }: StoreRequisitionCardProps) {
    return (
        <Card className="hover:shadow-md transition-shadow duration-200">
            <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                    <CardTitle className="text-lg flex items-center gap-2">
                        <Package className="h-5 w-5 text-primary" />
                        SR-{requisition.id.toString().padStart(4, '0')}
                    </CardTitle>
                    <Badge className={getStatusColor(requisition.status)}>
                        {getStatusText(requisition.status)}
                    </Badge>
                </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
                {/* Purpose */}
                <div>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                        {requisition.purpose}
                    </p>
                </div>

                {/* Details */}
                <div className="space-y-2 text-sm">
                    {requisition.requester && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <User className="h-4 w-4" />
                            <span>{requisition.requester.first_name} {requisition.requester.last_name}</span>
                        </div>
                    )}

                    {(requisition.department || requisition.branch) && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Building className="h-4 w-4" />
                            <span>
                                {requisition.department?.name}
                                {requisition.department?.name && requisition.branch?.name && ' • '}
                                {requisition.branch?.name}
                            </span>
                        </div>
                    )}
                    
                    <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>{requisition.requested_at ? new Date(requisition.requested_at).toLocaleDateString() : 'Not requested'}</span>
                    </div>
                    
                    {requisition.items && requisition.items.length > 0 && (
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Package className="h-4 w-4" />
                            <span>{requisition.items.length} item{requisition.items.length !== 1 ? 's' : ''}</span>
                        </div>
                    )}
                </div>

                {/* Actions */}
                <div className="pt-2 border-t">
                    <Button variant="outline" size="sm" className="w-full" asChild>
                        <Link href={`/store-requisitions/${requisition.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
