import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { InventoryItem } from '@/types/store-requisitions';
import { Plus, Trash2 } from 'lucide-react';

interface RequisitionItem {
    inventory_item_id: number | null;
    quantity_requested: number;
}

interface ItemsTableProps {
    items: RequisitionItem[];
    inventoryItems: InventoryItem[];
    onItemsChange: (items: RequisitionItem[]) => void;
    error?: string;
}

export default function ItemsTable({ items, inventoryItems, onItemsChange, error }: ItemsTableProps) {
    const addItem = () => {
        const newItems = [...items, { inventory_item_id: null, quantity_requested: 1 }];
        onItemsChange(newItems);
    };

    const updateItem = (index: number, field: keyof RequisitionItem, value: number) => {
        const newItems = [...items];
        newItems[index] = { ...newItems[index], [field]: value };
        onItemsChange(newItems);
    };

    const removeItem = (index: number) => {
        const newItems = items.filter((_, i) => i !== index);
        onItemsChange(newItems);
    };

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                    Requested Items <span className="text-destructive">*</span>
                </Label>
                <Button type="button" variant="outline" size="sm" onClick={addItem}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                </Button>
            </div>

            {items.length > 0 ? (
                <div className="border rounded-lg overflow-hidden">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Item</TableHead>
                                <TableHead className="w-32">Quantity</TableHead>
                                <TableHead className="w-20">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {items.map((item, index) => (
                                <TableRow key={index}>
                                    <TableCell>
                                        <Select
                                            value={item.inventory_item_id?.toString() || ''}
                                            onValueChange={(value) => updateItem(index, 'inventory_item_id', parseInt(value))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select an item" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {inventoryItems.map((invItem) => (
                                                    <SelectItem key={invItem.id} value={invItem.id.toString()}>
                                                        <div className="flex flex-col">
                                                            <span className="font-medium">{invItem.name}</span>
                                                            <span className="text-xs text-muted-foreground">
                                                                {invItem.sku} • Stock: {invItem.quantity_on_hand} {invItem.unit_of_measure}
                                                            </span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </TableCell>
                                    <TableCell>
                                        <Input
                                            type="number"
                                            min="0.01"
                                            step="0.01"
                                            value={item.quantity_requested}
                                            onChange={(e) => updateItem(index, 'quantity_requested', parseFloat(e.target.value) || 0)}
                                            className="w-full"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => removeItem(index)}
                                            className="text-destructive hover:text-destructive"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            ) : (
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <p className="text-muted-foreground">No items added yet. Click "Add Item" to get started.</p>
                </div>
            )}
            {error && <p className="text-sm text-destructive">{error}</p>}
        </div>
    );
}