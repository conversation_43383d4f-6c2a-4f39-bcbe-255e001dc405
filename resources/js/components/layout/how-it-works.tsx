'use client';

import { motion } from 'framer-motion';
import { Building, CheckCircle, Rocket, Settings, UserPlus, Users } from 'lucide-react';

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

const steps = [
    {
        number: 1,
        icon: UserPlus,
        title: 'Organization Registration',
        description: 'Register your organization with basic details, choose your plan, and verify your account.',
        details: ['Company information setup', 'Plan selection', 'Email verification', 'Initial admin account'],
    },
    {
        number: 2,
        icon: Building,
        title: 'Structure Setup',
        description: 'Define your organizational structure, departments, and branches.',
        details: ['Create departments', 'Set up branches', 'Define cost centers', 'Configure hierarchies'],
    },
    {
        number: 3,
        icon: Settings,
        title: 'System Configuration',
        description: 'Configure roles, permissions, approval workflows, and integrations.',
        details: ['Role creation', 'Permission assignment', 'Workflow setup', 'M-Pesa integration'],
    },
    {
        number: 4,
        icon: Users,
        title: 'Employee Onboarding',
        description: 'Add employees, assign roles, and set up their access permissions.',
        details: ['Bulk user import', 'Role assignment', 'Access configuration', 'Training materials'],
    },
    {
        number: 5,
        icon: CheckCircle,
        title: 'Testing & Validation',
        description: 'Test workflows, validate configurations, and ensure everything works correctly.',
        details: ['Workflow testing', 'Permission validation', 'Integration testing', 'User acceptance'],
    },
    {
        number: 6,
        icon: Rocket,
        title: 'Go Live',
        description: 'Launch your financial management system and start managing your organization.',
        details: ['System launch', 'User training', 'Support activation', 'Ongoing optimization'],
    },
];

export function HowItWorksSection() {
    return (
        <motion.section
            className="ring-primary/20 dark:ring-primary/20 relative overflow-hidden rounded-3xl border-2 border-white/80 bg-white/70 py-20 shadow-2xl ring-2 backdrop-blur-2xl sm:py-24 dark:border-slate-700/60 dark:bg-slate-900/80"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
        >
            {/* Acrylic edge effect */}
            <div
                className="ring-primary/20 dark:ring-primary/20 pointer-events-none absolute inset-0 rounded-3xl border-2 border-white/80 shadow-2xl ring-2 backdrop-blur-2xl dark:border-slate-700/60"
                style={{ zIndex: 2 }}
            />
            <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <motion.div className="mb-16 text-center" variants={fadeInUp}>
                    <h2 className="text-primary mb-4 text-3xl font-extrabold tracking-tight sm:text-4xl">From Registration to Full Operation</h2>
                    <p className="mx-auto max-w-2xl text-lg text-gray-900 dark:text-white">
                        Complete step-by-step process from organization registration to employee management and system operation.
                    </p>
                </motion.div>
                <div className="relative">
                    <div className="from-primary/20 via-primary/30 to-primary/20 dark:from-primary/80 dark:via-primary/70 dark:to-primary/80 absolute top-16 right-0 left-0 hidden h-0.5 bg-gradient-to-r lg:block" />
                    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {steps.map((step, index) => {
                            const IconComponent = step.icon;
                            return (
                                <motion.div key={step.number} className="relative" variants={fadeInUp}>
                                    {/* Step card */}
                                    <div className="hover:border-primary/60 relative z-10 rounded-2xl border border-white/60 bg-white/80 p-8 shadow-lg transition-all duration-300 hover:shadow-xl dark:border-slate-700/40 dark:bg-slate-800/80">
                                        {/* Step number and icon */}
                                        <div className="mb-6 flex items-center justify-center">
                                            <div className="relative">
                                                <div className="from-primary to-primary/80 text-primary-foreground flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br text-xl font-bold shadow-lg">
                                                    {step.number}
                                                </div>
                                                <div className="bg-card absolute -right-2 -bottom-2 rounded-full p-2 shadow-md">
                                                    <IconComponent className="text-primary h-4 w-4" />
                                                </div>
                                            </div>
                                        </div>

                                        <h3 className="mb-3 text-center text-xl font-semibold text-gray-900 dark:text-white">{step.title}</h3>
                                        <p className="dark:text-muted-foreground mb-4 text-center leading-relaxed text-gray-700">
                                            {step.description}
                                        </p>

                                        {/* Step details */}
                                        <ul className="space-y-2">
                                            {step.details.map((detail, detailIndex) => (
                                                <li key={detailIndex} className="dark:text-muted-foreground flex items-center text-sm text-gray-700">
                                                    <div className="bg-primary mr-3 h-1.5 w-1.5 flex-shrink-0 rounded-full" />
                                                    {detail}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>

                                    {/* Connecting arrow for mobile */}
                                    {index < steps.length - 1 && (
                                        <div className="my-4 flex justify-center lg:hidden">
                                            <div className="from-primary/30 to-primary dark:from-primary/70 dark:to-primary h-8 w-0.5 bg-gradient-to-b" />
                                        </div>
                                    )}
                                </motion.div>
                            );
                        })}
                    </div>
                </div>

                {/* Timeline summary */}
                <motion.div className="mt-16 text-center" variants={fadeInUp}>
                    <div className="rounded-2xl border border-white/60 bg-white/80 p-8 shadow-lg dark:border-slate-700/40 dark:bg-slate-800/80">
                        <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">Complete Setup in 5-7 Business Days</h3>
                        <p className="dark:text-muted-foreground mb-6 text-gray-700">
                            Our dedicated implementation team will guide you through each step to ensure a smooth transition.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4">
                            <span className="bg-primary/10 text-primary rounded-full px-4 py-2 text-sm font-medium">Dedicated Support</span>
                            <span className="rounded-full bg-blue-500/10 px-4 py-2 text-sm font-medium text-blue-500">Training Included</span>
                            <span className="bg-accent/10 text-accent rounded-full px-4 py-2 text-sm font-medium">Data Migration</span>
                        </div>
                    </div>
                </motion.div>
            </div>
        </motion.section>
    );
}
