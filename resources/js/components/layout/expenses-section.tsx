'use client';

import { motion } from 'framer-motion';
import { BarChart3, Bell, FileSpreadsheet, Mail, PieChart, TrendingUp } from 'lucide-react';

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

const expenseFeatures = [
    {
        icon: PieChart,
        title: 'Chart of Accounts',
        description: '5 comprehensive account categories: Assets, Liabilities, Equity, Revenue, and Expenses with sub-account management.',
    },
    {
        icon: BarChart3,
        title: 'Financial Reports',
        description: 'Generate P&L statements, balance sheets, cash flow reports, and custom financial reports instantly.',
    },
    {
        icon: TrendingUp,
        title: 'Real-time Dashboards',
        description: 'Live financial dashboards with KPIs, expense tracking, budget vs actual, and trend analysis.',
    },
    {
        icon: Bell,
        title: 'Smart Notifications',
        description: 'Automated alerts for budget overruns, pending approvals, payment due dates, and compliance deadlines.',
    },
    {
        icon: FileSpreadsheet,
        title: 'Advanced Reports',
        description: 'Customizable reports with filters, drill-down capabilities, and export to Excel, PDF, or CSV.',
    },
    {
        icon: Mail,
        title: 'Email Integration',
        description: 'Automated email reports, expense notifications, and approval workflows with customizable templates.',
    },
];

const chartOfAccounts = [
    { category: 'Assets', color: 'bg-primary', items: ['Current Assets', 'Fixed Assets', 'Investments'] },
    { category: 'Liabilities', color: 'bg-blue-500', items: ['Current Liabilities', 'Long-term Debt', 'Accruals'] },
    { category: 'Equity', color: 'bg-accent', items: ['Share Capital', 'Retained Earnings', 'Reserves'] },
    { category: 'Revenue', color: 'bg-green-500', items: ['Sales Revenue', 'Service Income', 'Other Income'] },
    {
        category: 'Expenses',
        color: 'bg-destructive',
        items: ['Operating Expenses', 'Cost of Sales', 'Administrative'],
    },
];

export function ExpensesSection() {
    return (
        <motion.section
            className="ring-primary/20 dark:ring-primary/20 relative overflow-hidden rounded-3xl border-2 border-foreground/90 bg-foreground/90 py-20 shadow-2xl ring-2 backdrop-blur-2xl sm:py-24 dark:border-slate-700/70 dark:bg-slate-900/90"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
        >
            {/* Acrylic edge effect */}
            <div
                className="ring-primary/20 dark:ring-primary/20 pointer-events-none absolute inset-0 rounded-3xl border-2 border-foreground/90 shadow-2xl ring-2 backdrop-blur-2xl dark:border-slate-700/70"
                style={{ zIndex: 2 }}
            />
            <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <motion.div className="mb-16 text-center" variants={fadeInUp}>
                    <h2 className="mb-4 text-3xl font-extrabold tracking-tight text-muted-foreground/90 sm:text-4xl dark:text-foreground">Expense Management</h2>
                    <p className="dark:text-muted-foreground mx-auto max-w-2xl text-lg text-muted-foreground/70">
                        Track, control, and optimize your organization's expenses with real-time analytics and approval workflows.
                    </p>
                </motion.div>

                {/* Chart of Accounts */}
                <motion.div className="mb-16" variants={fadeInUp}>
                    <h3 className="text-foreground mb-8 text-center text-2xl font-bold">5-Category Chart of Accounts</h3>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-5">
                        {chartOfAccounts.map((account, index) => (
                            <motion.div
                                key={index}
                                className="rounded-xl border-2 border-foreground/80 bg-foreground/80 shadow-lg backdrop-blur-2xl transition-shadow duration-300 hover:shadow-xl dark:border-slate-700/50 dark:bg-slate-800/80"
                                variants={fadeInUp}
                            >
                                <div className={`h-2 w-full ${account.color} mb-4 rounded-full`} />
                                <div className="px-4 pb-4">
                                    <h4 className="mb-3 text-lg font-semibold text-muted-foreground/90 dark:text-foreground text-center">{account.category}</h4>
                                    <ul className="space-y-2">
                                        {account.items.map((item, itemIndex) => (
                                            <li key={itemIndex} className="dark:text-muted-foreground flex items-center text-sm text-muted-foreground/70 justify-center">
                                                <div className="bg-muted-foreground mr-2 h-1.5 w-1.5 rounded-full" />
                                                {item}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {expenseFeatures.map((feature, index) => {
                        const IconComponent = feature.icon;
                        return (
                            <motion.div
                                key={index}
                                className="group rounded-2xl border-2 border-foreground/80 bg-foreground/80 p-8 shadow-lg backdrop-blur-2xl transition-all duration-300 hover:border-emerald-300 hover:shadow-2xl dark:border-slate-700/50 dark:bg-slate-800/80"
                                variants={fadeInUp}
                            >
                                <div className="from-primary text-primary-foreground mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br to-blue-500 shadow-lg transition-transform duration-300 group-hover:scale-110">
                                    <IconComponent className="h-8 w-8" />
                                </div>
                                <h3 className="mb-3 text-xl font-semibold text-muted-foreground/90 dark:text-foreground">{feature.title}</h3>
                                <p className="dark:text-muted-foreground leading-relaxed text-muted-foreground/70">{feature.description}</p>
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </motion.section>
    );
}
