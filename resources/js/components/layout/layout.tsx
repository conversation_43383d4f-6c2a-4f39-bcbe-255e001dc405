import { User } from '@/types';
import { Head } from '@inertiajs/react';
import Footer from './footer';
import { Navbar } from './navbar';

interface LayoutProps {
    title: string;
    children: React.ReactNode;
    user?: User;
}

export default function Layout({ title, children, user }: LayoutProps) {
    return (
        <div className="flex min-h-screen flex-col">
            <Head title={title} />
            <Navbar user={user} />

            <main className="flex-grow">{children}</main>

            <Footer />
        </div>
    );
}
