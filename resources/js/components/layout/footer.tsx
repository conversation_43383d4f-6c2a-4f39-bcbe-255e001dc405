import { FacebookIcon, LinkedinIcon, MailIcon, MapPinIcon, PhoneIcon, TwitterIcon } from '@/components/svg/icons';
import { AnimatedLink } from '@/components/ui/animated-link';
import { motion, easeOut } from 'framer-motion';

const linkHover = {
    hover: {
        scale: 1.05,
        transition: { duration: 0.3, ease: easeOut },
    },
};

const footerLinks = {
    products: [
        { name: 'Petty Cash Management', href: '#features' },
        { name: 'Document Management', href: '#features' },
        { name: 'Expense Management', href: '#features' },
        { name: 'User Management', href: '#users' },
        { name: 'M-Pesa Integration', href: '#features' },
        { name: 'Approval Workflows', href: '#features' },
    ],
    company: [
        { name: 'About Us', href: '/about' },
        { name: 'Contact', href: '/contact' },
        { name: 'Blog', href: '/blog' },
    ],
    support: [
        { name: 'Help Center', href: '/help' },
        { name: 'Contact Support', href: '/contact' },
        { name: 'Community', href: '/community' },
    ],
};


const fadeIn = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: easeOut } },
};

export default function Footer() {
    return (
        <motion.footer
            className="border-border from-muted-foreground/5 to-muted-foreground/10 border-t bg-gradient-to-r"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
        >
            <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
                {/* Main footer content */}
                <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-5">
                    {/* Company info */}
                    <div className="lg:col-span-2">
                        <AnimatedLink href="/" className="group relative inline-flex items-center mb-6 whitespace-nowrap">
                            <div className="from-primary to-primary/80 flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br shadow-md">
                                <img src="/logo.png" alt="Sippar Logo" className="h-8 w-8 object-contain" />
                            </div>
                            <span className="dark:text-muted-foreground/100 ml-3 text-2xl font-bold">Sippar</span>
                        </AnimatedLink>
                        <p className="dark:text-muted-foreground mb-6 leading-relaxed text-muted-foreground/60">
                            The most comprehensive financial management platform designed specifically for Kenyan organizations. Streamline
                            operations, ensure compliance, and drive growth.
                        </p>

                        {/* Contact info */}
                        <div className="space-y-3">
                            <div className="dark:text-muted-foreground flex items-center">
                                <MailIcon className="text-primary mr-3 h-5 w-5" />
                                <span><EMAIL></span>
                            </div>
                            <div className="dark:text-muted-foreground flex items-center">
                                <PhoneIcon className="text-primary mr-3 h-5 w-5" />
                                <span>+254 748 902 779</span>
                            </div>
                            <div className="dark:text-muted-foreground flex items-center">
                                <MapPinIcon className="text-primary mr-3 h-5 w-5" />
                                <span>Kisumu, Kenya</span>
                            </div>
                        </div>
                    </div>

                    {/* Products */}
                    <div>
                        <h3 className="mb-6 text-lg font-semibold text-foreground/80">Products</h3>
                        <ul className="space-y-3">
                            {footerLinks.products.map((link) => (
                                <li key={link.name}>
                                    <AnimatedLink
                                        href={link.href}
                                        className="hover:text-primary text-muted-foreground/100 transition-colors duration-200"
                                    >
                                        {link.name}
                                    </AnimatedLink>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Company */}
                    <div>
                        <h3 className="mb-6 text-lg font-semibold text-muted-foreground/50 dark:text-white">Company</h3>
                        <ul className="space-y-3">
                            {footerLinks.company.map((link) => (
                                <li key={link.name}>
                                    <AnimatedLink
                                        href={link.href}
                                        className="hover:text-primary text-muted-foreground/100 transition-colors duration-200"
                                    >
                                        {link.name}
                                    </AnimatedLink>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Support */}
                    <div>
                        <h3 className="mb-6 text-lg font-semibold text-muted-foreground/50 dark:text-white">Support</h3>
                        <ul className="space-y-3">
                            {footerLinks.support.map((link) => (
                                <li key={link.name}>
                                    <AnimatedLink
                                        href={link.href}
                                        className="hover:text-primary text-muted-foreground/100 transition-colors duration-200"
                                    >
                                        {link.name}
                                    </AnimatedLink>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>

                {/* Newsletter signup with glass/acrylic effect */}
                <div className="border-border mb-8 border-t pt-8">
                    <div className="rounded-2xl border-2 border-white/90 bg-white/90 p-8 text-center shadow-lg backdrop-blur-2xl dark:border-slate-700/70 dark:bg-slate-900/90">
                        <h3 className="mb-4 text-2xl font-bold text-muted-foreground/50 dark:text-white">Stay Updated with Sippar</h3>
                        <p className="dark:text-muted-foreground mb-6 text-muted-foreground/60">
                            Get the latest updates on new features, financial management tips, and Kenyan business insights.
                        </p>
                        <div className="mx-auto flex max-w-md flex-col gap-4 sm:flex-row">
                            <input
                                type="email"
                                placeholder="Enter your email"
                                className="border-border focus:border-primary focus:ring-primary flex-1 rounded-lg border bg-white/80 px-4 py-3 text-muted-foreground/50 focus:ring-2 dark:bg-slate-800/80 dark:text-white"
                            />
                            <button className="from-primary to-primary/80 text-primary-foreground hover:from-primary/80 hover:to-primary rounded-lg bg-gradient-to-r px-6 py-3 font-medium transition-all duration-200">
                                Subscribe
                            </button>
                        </div>
                    </div>
                </div>

                {/* Social links and copyright */}
                <div className="border-border flex flex-col items-center justify-between border-t pt-8 md:flex-row">
                    <div className="mb-4 flex space-x-6 md:mb-0">
                        <motion.a
                            href="https://www.facebook.com/Zone01Kisumu/?locale=sw_KE"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-primary transition-colors duration-200"
                            variants={linkHover}
                            whileHover="hover"
                        >
                            <FacebookIcon className="h-6 w-6" />
                        </motion.a>
                        <motion.a
                            href="https://x.com/zone01kisumu"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-primary transition-colors duration-200"
                            variants={linkHover}
                            whileHover="hover"
                        >
                            <TwitterIcon className="h-6 w-6" />
                        </motion.a>
                        <motion.a
                            href="https://ke.linkedin.com/company/zone01kisumu"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-primary transition-colors duration-200"
                            variants={linkHover}
                            whileHover="hover"
                        >
                            <LinkedinIcon className="h-6 w-6" />
                        </motion.a>
                    </div>

                    <div className="flex flex-col items-center space-y-2 md:flex-row md:space-y-0 md:space-x-6">
                        <p className="dark:text-muted-foreground text-sm text-muted-foreground/60">
                            &copy; {new Date().getFullYear()} <span className="text-primary font-semibold">Sippar</span> Inc. All rights reserved.
                        </p>
                        <div className="flex space-x-4 text-sm">
                            <AnimatedLink href="/privacy" className="dark:text-muted-foreground hover:text-primary text-muted-foreground/60">
                                Privacy Policy
                            </AnimatedLink>
                            <AnimatedLink href="/terms" className="dark:text-muted-foreground hover:text-primary text-muted-foreground/60">
                                Terms of Service
                            </AnimatedLink>
                        </div>
                    </div>
                </div>
            </div>
        </motion.footer>
    );
}
