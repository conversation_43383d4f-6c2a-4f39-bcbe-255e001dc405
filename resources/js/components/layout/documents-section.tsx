'use client';

import { motion } from 'framer-motion';
import { Archive, FileCheck, FileText, Mail, Printer, Receipt } from 'lucide-react';

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

const documentTypes = [
    {
        icon: FileText,
        title: 'Local Purchase Orders (LPOs)',
        description: 'Create, approve, and track LPOs with automated workflows and vendor management.',
    },
    {
        icon: FileCheck,
        title: 'Local Service Orders (LSOs)',
        description: 'Manage service contracts and orders with milestone tracking and payment schedules.',
    },
    {
        icon: Receipt,
        title: 'Digital Receipts',
        description: 'Generate and store digital receipts with automatic categorization and expense tracking.',
    },
    {
        icon: Printer,
        title: 'ETR Integration',
        description: "Seamless integration with KRA's Electronic Tax Register for compliant invoicing.",
    },
    {
        icon: Mail,
        title: 'Invoice Management',
        description: 'Create, send, and track invoices with automated reminders and payment tracking.',
    },
    {
        icon: Archive,
        title: 'Document Archive',
        description: 'Secure cloud storage with version control, search, and compliance-ready archiving.',
    },
];

export function DocumentsSection() {
    return (
        <motion.section
            className="ring-primary/20 dark:ring-primary/20 relative overflow-hidden rounded-3xl border-2 border-white/90 bg-white/90 py-20 shadow-2xl ring-2 backdrop-blur-2xl sm:py-24 dark:border-slate-700/70 dark:bg-slate-900/90"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
        >
            {/* Acrylic edge effect */}
            <div
                className="ring-primary/20 dark:ring-primary/20 pointer-events-none absolute inset-0 rounded-3xl border-2 border-white/90 shadow-2xl ring-2 backdrop-blur-2xl dark:border-slate-700/70"
                style={{ zIndex: 2 }}
            />
            <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <motion.div className="mb-16 text-center" variants={fadeInUp}>
                    <h2 className="mb-4 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                        Complete Document Management
                    </h2>
                    <p className="dark:text-muted-foreground mx-auto max-w-2xl text-lg text-gray-800">
                        Digitize and streamline all your business documents with KRA-compliant workflows and automated processes.
                    </p>
                </motion.div>

                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {documentTypes.map((doc, index) => {
                        const IconComponent = doc.icon;
                        return (
                            <motion.div
                                key={index}
                                className="group hover:border-primary/60 rounded-2xl border border-white/70 bg-white/90 p-8 shadow-lg transition-all duration-300 hover:shadow-2xl dark:border-slate-700/40 dark:bg-slate-800/90"
                                variants={fadeInUp}
                            >
                                <div className="from-primary text-primary-foreground mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br to-blue-500 shadow-lg transition-transform duration-300 group-hover:scale-110">
                                    <IconComponent className="h-8 w-8" />
                                </div>
                                <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">{doc.title}</h3>
                                <p className="dark:text-muted-foreground leading-relaxed text-gray-800">{doc.description}</p>
                            </motion.div>
                        );
                    })}
                </div>

                <motion.div className="mt-16 text-center" variants={fadeInUp}>
                    <div className="rounded-2xl border border-white/70 bg-white/90 p-8 shadow-lg dark:border-slate-700/40 dark:bg-slate-800/90">
                        <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">KRA Compliance Made Easy</h3>
                        <p className="dark:text-muted-foreground mb-6 text-gray-800">
                            All document types are automatically configured for KRA compliance with built-in ETR integration, tax calculations, and
                            audit-ready reporting.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4">
                            <span className="bg-primary/10 text-primary rounded-full px-4 py-2 text-sm font-medium">ETR Compliant</span>
                            <span className="rounded-full bg-blue-500/10 px-4 py-2 text-sm font-medium text-blue-500">VAT Ready</span>
                            <span className="bg-accent/10 text-accent rounded-full px-4 py-2 text-sm font-medium">Audit Trail</span>
                        </div>
                    </div>
                </motion.div>
            </div>
        </motion.section>
    );
}
