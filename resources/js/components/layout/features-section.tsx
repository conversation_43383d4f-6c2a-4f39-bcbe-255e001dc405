import {
    BuildingIcon,
    CheckCircleIcon,
    CreditCardIcon,
    GitBranchIcon,
    LayoutGridIcon,
    ShieldIcon,
    SmartphoneIcon,
    UserCheckIcon,
} from '@/components/svg/icons';
import { motion } from 'framer-motion';

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

const features = [
    {
        icon: BuildingIcon,
        title: 'Multi-Tenant Architecture',
        description: 'Securely host multiple independent organizations, each with isolated data, users, and settings.',
    },
    {
        icon: UserCheckIcon,
        title: 'User Management',
        description: 'Complete user lifecycle management with role-based access control, department assignments, and permission matrices.',
    },
    {
        icon: GitBranchIcon,
        title: 'Branch Management',
        description: 'Manage multiple branches, locations, and subsidiaries with centralized control and local autonomy.',
    },
    {
        icon: CheckCircleIcon,
        title: 'Multi-Level Approvals',
        description: 'Configure complex approval workflows with multiple levels, conditional routing, and escalation rules.',
    },
    {
        icon: SmartphoneIcon,
        title: 'M-Pesa Integration',
        description: 'Native M-Pesa integration for payments, collections, and reconciliation with real-time transaction tracking.',
    },
    {
        icon: CreditCardIcon,
        title: 'Integrated Payroll',
        description: 'Process payroll with KRA compliance, NSSF, NHIF deductions, and direct bank transfers.',
    },
    {
        icon: LayoutGridIcon,
        title: 'Advanced Reporting',
        description: 'Real-time dashboards, financial reports, and analytics with customizable KPIs and metrics.',
    },
    {
        icon: ShieldIcon,
        title: 'Enterprise Security',
        description: 'Bank-grade security with encryption, audit trails, and compliance with Kenyan data protection laws.',
    },
];

export function FeaturesSection() {
    return (
        <motion.section
            id="features"
            className="ring-primary/20 dark:ring-primary/20 relative overflow-hidden rounded-3xl border-2 border-white/80 bg-white/70 py-20 shadow-2xl ring-2 backdrop-blur-2xl sm:py-24 dark:border-slate-700/60 dark:bg-slate-900/80"
            style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10), 0 1.5px 8px 0 rgba(80, 120, 255, 0.08)' }}
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
        >
            {/* Acrylic edge effect */}
            <div
                className="ring-primary/20 dark:ring-primary/20 pointer-events-none absolute inset-0 rounded-3xl border-2 border-white/80 shadow-2xl ring-2 backdrop-blur-2xl dark:border-slate-700/60"
                style={{ zIndex: 2 }}
            />
            <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <motion.div className="mb-16 text-center" variants={fadeInUp}>
                    <h2 className="text-primary text-lg leading-7 font-bold">Core Features</h2>
                    <p className="mt-2 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
                        Everything You Need to Manage Finances
                    </p>
                    <p className="dark:text-muted-foreground mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-700">
                        Comprehensive financial management tools designed specifically for Kenyan organizations.
                    </p>
                </motion.div>

                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                    {features.map((feature, index) => {
                        const IconComponent = feature.icon;
                        return (
                            <motion.div
                                key={index}
                                className="group hover:border-primary/60 relative transform rounded-2xl border border-white/60 bg-white/80 p-6 shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-xl dark:border-slate-700/40 dark:bg-slate-800/80"
                                variants={fadeInUp}
                            >
                                <div className="bg-primary text-primary-foreground mb-5 flex h-12 w-12 items-center justify-center rounded-xl shadow-md transition-transform duration-300 group-hover:scale-110">
                                    <IconComponent className="h-6 w-6" />
                                </div>
                                <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                                <p className="dark:text-muted-foreground text-sm leading-relaxed text-gray-700">{feature.description}</p>
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </motion.section>
    );
}
