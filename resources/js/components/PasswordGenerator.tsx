import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Co<PERSON>, Eye, EyeOff, Wand2 } from 'lucide-react';
import { generateSecurePassword, copyToClipboard } from '@/utils/passwordGenerator';
import { validatePassword } from '@/utils/passwordValidation';

interface PasswordGeneratorProps {
    value: string;
    onChange: (password: string) => void;
    disabled?: boolean;
    error?: string;
}

export default function PasswordGenerator({
    value,
    onChange,
    disabled = false,
    error
}: PasswordGeneratorProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [isPasswordGenerated, setIsPasswordGenerated] = useState(false);
    const [copySuccess, setCopySuccess] = useState<string>('');
    const [isGenerating, setIsGenerating] = useState(false);

    const handleGeneratePassword = () => {
        setIsGenerating(true);
        // Add a small delay to show loading state
        setTimeout(() => {
            const newPassword = generateSecurePassword();
            onChange(newPassword);
            setIsPasswordGenerated(true);
            setIsGenerating(false);
        }, 200);
    };

    const handleCopyPassword = async (password: string) => {
        const success = await copyToClipboard(password);
        if (success) {
            setCopySuccess(password);
            setTimeout(() => setCopySuccess(''), 2000);
        }
    };

    const handleManualPasswordChange = (newPassword: string) => {
        onChange(newPassword);
        setIsPasswordGenerated(false);
    };

    const passwordValidation = value ? validatePassword(value) : null;

    return (
        <div className="space-y-2">
            <div className="flex items-center justify-between">
                <Label htmlFor="password" className="text-sm font-medium">
                    Password
                </Label>
                <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleGeneratePassword}
                    disabled={disabled || isGenerating}
                    className="h-8 px-2 text-xs"
                >
                    <Wand2 className={`mr-1 h-3 w-3 ${isGenerating ? 'animate-spin' : ''}`} />
                    {isGenerating ? 'Generating...' : 'Generate'}
                </Button>
            </div>

            <div className="relative">
                <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={value}
                    onChange={(e) => handleManualPasswordChange(e.target.value)}
                    disabled={disabled}
                    className="pr-20"
                    placeholder="Enter password or generate one"
                />
                <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-2">
                    {value && (
                        <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyPassword(value)}
                            className="h-6 w-6 p-0"
                            title="Copy password"
                        >
                            <Copy className="h-3 w-3" />
                        </Button>
                    )}
                    <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowPassword(!showPassword)}
                        className="h-6 w-6 p-0"
                        title={showPassword ? 'Hide password' : 'Show password'}
                    >
                        {showPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                    </Button>
                </div>
            </div>

            {/* Password status indicators */}
            <div className="flex items-center space-x-2">
                {isPasswordGenerated && (
                    <Badge variant="outline" className="text-xs text-foreground/90">
                        Auto-generated
                    </Badge>
                )}
                {passwordValidation && (
                    <Badge 
                        variant={passwordValidation.isValid ? "default" : "destructive"} 
                        className="text-xs"
                    >
                        {passwordValidation.isValid ? 'Strong' : 'Weak'}
                    </Badge>
                )}
                {copySuccess === value && (
                    <Badge variant="outline" className="text-xs text-green-600">
                        Copied!
                    </Badge>
                )}
            </div>

            {error && <p className="text-destructive text-sm">{error}</p>}
        </div>
    );
}
