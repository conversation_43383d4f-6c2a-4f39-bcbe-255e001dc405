import React from 'react';
import { usePage } from '@inertiajs/react';
import { SharedData } from '@/types';

interface UserRole {
    id: number;
    name: string;
}

interface UserPermission {
    id: number;
    name: string;
}

interface PermissionWrapperProps {
    /** Single permission or array of permissions to check */
    permission?: string | string[];
    /** Specific role to check for */
    role?: string;
    /** Require all permissions (AND logic) vs any permission (OR logic) */
    requireAll?: boolean;
    /** Fallback component to render when permission check fails */
    fallback?: React.ReactNode;
    /** Children to render when permission check passes */
    children: React.ReactNode;
    /** Custom permission check function */
    customCheck?: (userPermissions: string[], userRoles?: string[]) => boolean;
}

/**
 * PermissionWrapper component for role-based rendering
 * 
 * Supports store requisition specific permissions:
 * - store-keep: Meta-permission for all store operations
 * - approve-store-requisition: Specific store approval permission
 * - view-store-requisitions: View store requisitions
 * - create-store-requisition: Create store requisitions
 * - edit-store-requisition: Edit store requisitions
 * - issue-store-items: Issue inventory items
 * - view-inventory: View inventory
 * - manage-inventory: Manage inventory
 */
export function PermissionWrapper({ 
    permission, 
    role, 
    requireAll = false, 
    fallback = null, 
    children,
    customCheck
}: PermissionWrapperProps) {
    const { auth } = usePage<SharedData>().props;

    // Handle both string[] and {id, name}[] formats for permissions and roles
    const userPermissions = auth.user.permissions
        ? (Array.isArray(auth.user.permissions) && typeof auth.user.permissions[0] === 'string'
            ? auth.user.permissions as string[]
            : (auth.user.permissions as UserPermission[]).map(p => p.name))
        : [];

    const userRoles = auth.user.roles
        ? (Array.isArray(auth.user.roles) && typeof auth.user.roles[0] === 'string'
            ? auth.user.roles as string[]
            : (auth.user.roles as UserRole[]).map(r => r.name))
        : [];

    // Custom permission check
    if (customCheck) {
        const hasPermission = customCheck(userPermissions, userRoles);
        return hasPermission ? <>{children}</> : <>{fallback}</>;
    }

    // Role-based check
    if (role) {
        const hasRole = userRoles.includes(role);
        return hasRole ? <>{children}</> : <>{fallback}</>;
    }

    // Permission-based check
    if (permission) {
        const permissions = Array.isArray(permission) ? permission : [permission];
        
        let hasPermission: boolean;
        
        if (requireAll) {
            // AND logic - user must have ALL specified permissions
            hasPermission = permissions.every(perm => userPermissions.includes(perm));
        } else {
            // OR logic - user must have ANY of the specified permissions
            hasPermission = permissions.some(perm => userPermissions.includes(perm));
        }
        
        return hasPermission ? <>{children}</> : <>{fallback}</>;
    }

    // No permission or role specified, render children by default
    return <>{children}</>;
}

/**
 * Hook for checking permissions in components
 */
export function usePermissions() {
    const { auth } = usePage<SharedData>().props;

    // Handle both string[] and {id, name}[] formats for permissions and roles
    const userPermissions = auth.user.permissions
        ? (Array.isArray(auth.user.permissions) && typeof auth.user.permissions[0] === 'string'
            ? auth.user.permissions as string[]
            : (auth.user.permissions as UserPermission[]).map(p => p.name))
        : [];

    const userRoles = auth.user.roles
        ? (Array.isArray(auth.user.roles) && typeof auth.user.roles[0] === 'string'
            ? auth.user.roles as string[]
            : (auth.user.roles as UserRole[]).map(r => r.name))
        : [];

    const hasPermission = (permission: string | string[], requireAll = false): boolean => {
        const permissions = Array.isArray(permission) ? permission : [permission];
        
        if (requireAll) {
            return permissions.every(perm => userPermissions.includes(perm));
        } else {
            return permissions.some(perm => userPermissions.includes(perm));
        }
    };

    const hasRole = (role: string | string[]): boolean => {
        const roles = Array.isArray(role) ? role : [role];
        return roles.some(r => userRoles.includes(r));
    };

    const isStoreKeeper = (): boolean => {
        return userPermissions.includes('store-keep');
    };

    const isOverseer = (): boolean => {
        return userPermissions.includes('approve-store-requisition') && !userPermissions.includes('store-keep');
    };

    const canApproveStoreRequisitions = (): boolean => {
        return userPermissions.includes('approve-store-requisition') || userPermissions.includes('store-keep');
    };

    const canCreateStoreRequisitions = (): boolean => {
        return userPermissions.includes('create-store-requisition') || userPermissions.includes('store-keep');
    };

    const canViewStoreRequisitions = (): boolean => {
        return userPermissions.includes('view-store-requisitions') || userPermissions.includes('store-keep');
    };

    const canEditStoreRequisitions = (): boolean => {
        return userPermissions.includes('edit-store-requisition') || userPermissions.includes('store-keep');
    };

    const canIssueStoreItems = (): boolean => {
        return userPermissions.includes('issue-store-items') || userPermissions.includes('store-keep');
    };

    const canViewInventory = (): boolean => {
        return userPermissions.includes('view-inventory') || userPermissions.includes('store-keep');
    };

    const canManageInventory = (): boolean => {
        return userPermissions.includes('manage-inventory') || userPermissions.includes('store-keep');
    };

    return {
        userPermissions,
        userRoles,
        hasPermission,
        hasRole,
        isStoreKeeper,
        isOverseer,
        canApproveStoreRequisitions,
        canCreateStoreRequisitions,
        canViewStoreRequisitions,
        canEditStoreRequisitions,
        canIssueStoreItems,
        canViewInventory,
        canManageInventory,
    };
}

/**
 * Store Keeper specific permission wrapper
 */
export function StoreKeeperOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
    return (
        <PermissionWrapper permission="store-keep" fallback={fallback}>
            {children}
        </PermissionWrapper>
    );
}

/**
 * Overseer specific permission wrapper (has approve-store-requisition but not store-keep)
 */
export function OverseerOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
    return (
        <PermissionWrapper 
            customCheck={(permissions) => 
                permissions.includes('approve-store-requisition') && !permissions.includes('store-keep')
            }
            fallback={fallback}
        >
            {children}
        </PermissionWrapper>
    );
}

/**
 * Store Approver wrapper (either store keeper or overseer)
 */
export function StoreApproverOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
    return (
        <PermissionWrapper 
            permission={['store-keep', 'approve-store-requisition']} 
            fallback={fallback}
        >
            {children}
        </PermissionWrapper>
    );
}

/**
 * Store Access wrapper (any store-related permission)
 */
export function StoreAccessOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
    return (
        <PermissionWrapper 
            permission={[
                'store-keep',
                'approve-store-requisition',
                'create-store-requisition',
                'view-store-requisitions',
                'edit-store-requisition',
                'issue-store-items',
                'view-inventory',
                'manage-inventory'
            ]} 
            fallback={fallback}
        >
            {children}
        </PermissionWrapper>
    );
}
