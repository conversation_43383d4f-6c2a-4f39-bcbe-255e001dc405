import React, { useEffect, useState } from 'react';
import { Link } from '@inertiajs/react';
import { SidebarMenuItem, SidebarMenuButton } from '@/components/ui/sidebar';
import { ClipboardCheck } from 'lucide-react';
import axios from 'axios';
import { usePage } from '@inertiajs/react';
import { SharedData } from '@/types';

export function ApprovalsButton() {
    const { auth } = usePage<SharedData>().props;
    const [pendingCount, setPendingCount] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(true);
    const hasApproverPermission = (auth.user.permissions as unknown as string[]).includes('approver');

    useEffect(() => {
        const fetchPendingCount = async () => {
            try {
                const response = await axios.get(route('api.pending-approvals-count'));
                setPendingCount(response.data.count);
            } catch (error) {
                console.error('Error fetching pending approvals count:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchPendingCount();

        // Set up polling every 60 seconds to keep the count updated
        const intervalId = setInterval(fetchPendingCount, 60000);

        return () => clearInterval(intervalId);
    }, []);

    // Always show the approvals button, but disable it for users without the approver permission
    return (
        <SidebarMenuItem>
            <SidebarMenuButton
                asChild
                disabled={!hasApproverPermission}
                className={!hasApproverPermission ? 'opacity-70 cursor-not-allowed' : ''}
            >
                <Link
                    href={hasApproverPermission ? route('requisitions.my-approvals') : '#'}
                    prefetch={hasApproverPermission}
                    onClick={(e) => !hasApproverPermission && e.preventDefault()}
                    className={!hasApproverPermission ? 'pointer-events-none' : ''}
                >
                    <ClipboardCheck className="h-4 w-4 mr-1" />
                    <span>Approvals</span>
                    {!loading && pendingCount > 0 && hasApproverPermission && (
                        <span className="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            {pendingCount}
                        </span>
                    )}
                </Link>
            </SidebarMenuButton>
        </SidebarMenuItem>
    );
}
