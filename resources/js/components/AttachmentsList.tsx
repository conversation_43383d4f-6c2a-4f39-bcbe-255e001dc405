import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { AttachmentViewer } from '@/components/ui/attachment-viewer';
import { File, Download, Trash2, User, Calendar, FileText, Eye } from 'lucide-react';
import { router } from '@inertiajs/react';

interface Attachment {
  id: number;
  original_name: string;
  file_size: number;
  mime_type: string;
  description?: string;
  is_evidence: boolean;
  uploaded_at_step?: string;
  created_at: string;
  uploader: {
    id: number;
    first_name: string;
    last_name: string;
  };
}

interface AttachmentsListProps {
  attachments: Attachment[];
  canDelete?: boolean;
  onAttachmentDeleted?: (attachmentId: number) => void;
  className?: string;
}

export const AttachmentsList: React.FC<AttachmentsListProps> = ({
  attachments,
  canDelete = false,
  onAttachmentDeleted,
  className = '',
}) => {
  const [deletingIds, setDeletingIds] = useState<Set<number>>(new Set());
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [attachmentToDelete, setAttachmentToDelete] = useState<number | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentAttachment, setCurrentAttachment] = useState<Attachment | null>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) {
      return <File className="h-4 w-4 text-blue-500" />;
    } else if (mimeType === 'application/pdf') {
      return <File className="h-4 w-4 text-red-500" />;
    } else if (mimeType.includes('word') || mimeType.includes('document')) {
      return <FileText className="h-4 w-4 text-blue-600" />;
    } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
      return <File className="h-4 w-4 text-green-600" />;
    } else {
      return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleView = (attachmentId: number) => {
    const attachment = attachments.find(att => att.id === attachmentId);
    if (attachment) {
      setCurrentAttachment(attachment);
      setViewerOpen(true);
    }
  };

  const handleNavigateAttachment = (attachment: Attachment) => {
    setCurrentAttachment(attachment);
  };

  const isViewable = (mime: string) => {
    return mime.startsWith('image/') || mime === 'application/pdf' || mime.startsWith('text/');
  };

  const handleDownload = (attachmentId: number) => {
    window.open(`/attachments/${attachmentId}/download`, '_blank');
  };

  const handleDeleteClick = (attachmentId: number) => {
    setAttachmentToDelete(attachmentId);
    setConfirmDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!attachmentToDelete) return;

    setDeletingIds(prev => new Set(prev).add(attachmentToDelete));

    router.delete(`/attachments/${attachmentToDelete}`, {
      onSuccess: () => {
        // The page will be refreshed automatically by Inertia
        if (onAttachmentDeleted) {
          onAttachmentDeleted(attachmentToDelete);
        }
      },
      onError: (errors) => {
        alert(errors.delete || 'Failed to delete attachment. Please try again.');
      },
      onFinish: () => {
        setDeletingIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(attachmentToDelete);
          return newSet;
        });
        setAttachmentToDelete(null);
      },
    });
  };

  if (attachments.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <File className="h-5 w-5" />
            Attachments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-4">No attachments uploaded yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <File className="h-5 w-5" />
          Attachments ({attachments.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  {getFileIcon(attachment.mime_type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm truncate">
                        {attachment.original_name}
                      </h4>
                      {attachment.is_evidence && (
                        <Badge variant="secondary" className="text-xs">
                          Evidence
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                      <span>{formatFileSize(attachment.file_size)}</span>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {attachment.uploader.first_name} {attachment.uploader.last_name}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(attachment.created_at)}
                      </div>
                      {attachment.uploaded_at_step && (
                        <Badge variant="outline" className="text-xs">
                          {attachment.uploaded_at_step}
                        </Badge>
                      )}
                    </div>

                    {attachment.description && (
                      <p className="text-sm text-muted-foreground mt-2">
                        {attachment.description}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2 ml-4">
                  {isViewable(attachment.mime_type) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleView(attachment.id);
                      }}
                      title="View in app"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(attachment.id)}
                    title="Download file"
                  >
                    <Download className="h-4 w-4" />
                  </Button>

                  {canDelete && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteClick(attachment.id)}
                      disabled={deletingIds.has(attachment.id)}
                      className="text-destructive hover:text-destructive/80"
                      title="Delete attachment"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <ConfirmationDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        title="Delete Attachment"
        description="Are you sure you want to delete this attachment? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleConfirmDelete}
        isLoading={attachmentToDelete ? deletingIds.has(attachmentToDelete) : false}
      />

      <AttachmentViewer
        open={viewerOpen}
        onOpenChange={setViewerOpen}
        attachment={currentAttachment}
        attachments={attachments.filter(att => isViewable(att.mime_type))}
        onNavigate={handleNavigateAttachment}
      />
    </Card>
  );
};
