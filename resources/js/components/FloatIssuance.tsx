import React, { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface CashFloat {
    id: number;
    name: string;
    current_balance: number;
    department?: {
        id: number;
        name: string;
    };
    branch?: {
        id: number;
        name: string;
    };
    user?: {
        id: number;
        name: string;
    };
}

interface FloatIssuanceProps {
    isOpen: boolean;
    onClose: () => void;
    selectedFloat?: CashFloat | null;
    showAsCard?: boolean;
    availableFloats?: CashFloat[];
}

export default function FloatIssuance({ isOpen, onClose, selectedFloat, showAsCard = false, availableFloats = [] }: FloatIssuanceProps) {
    const [currentFloat, setCurrentFloat] = useState<CashFloat | null>(selectedFloat || null);
    
    const { data, setData, post, processing, errors, reset } = useForm({
        transaction_type: 'float_issuance' as const,
        total_amount: '',
        description: '',
        payment_method: '',
        reference_number: '',
        account_details: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (currentFloat) {
            post(route('cash-floats.transactions.store', currentFloat.id), {
                onSuccess: () => {
                    onClose();
                    reset();
                    setCurrentFloat(selectedFloat || null);
                },
            });
        }
    };

    const FormContent = () => (
        <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-4">
                {!selectedFloat && availableFloats.length > 0 && (
                    <div className="space-y-2">
                        <Label htmlFor="float_selection">Select Cash Float</Label>
                        <Select 
                            value={currentFloat?.id.toString() || ''} 
                            onValueChange={(value) => {
                                const float = availableFloats.find(f => f.id.toString() === value);
                                setCurrentFloat(float || null);
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select a cash float" />
                            </SelectTrigger>
                            <SelectContent>
                                {availableFloats.map((float) => (
                                    <SelectItem key={float.id} value={float.id.toString()}>
                                        {float.name} - Current: ${float.current_balance.toFixed(2)}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                )}
                
                <div className="space-y-2">
                    <Label htmlFor="total_amount">Amount</Label>
                    <Input
                        id="total_amount"
                        type="number"
                        step="0.01"
                        value={data.total_amount}
                        onChange={(e) => setData('total_amount', e.target.value)}
                        placeholder="Enter amount to issue"
                        required
                    />
                    {errors.total_amount && (
                        <p className="text-sm text-red-500">{errors.total_amount}</p>
                    )}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                        id="description"
                        value={data.description}
                        onChange={(e) => setData('description', e.target.value)}
                        placeholder="Enter description or reason for float issuance"
                        required
                    />
                    {errors.description && (
                        <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="payment_method">Payment Method</Label>
                    <Select value={data.payment_method} onValueChange={(value) => setData('payment_method', value)}>
                        <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="cash">Cash</SelectItem>
                            <SelectItem value="mpesa">M-Pesa</SelectItem>
                            <SelectItem value="bank">Bank Transfer</SelectItem>
                        </SelectContent>
                    </Select>
                    {errors.payment_method && (
                        <p className="text-sm text-red-500">{errors.payment_method}</p>
                    )}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="reference_number">Reference Number</Label>
                    <Input
                        id="reference_number"
                        value={data.reference_number}
                        onChange={(e) => setData('reference_number', e.target.value)}
                        placeholder="Transaction ID, receipt number, etc."
                    />
                    {errors.reference_number && (
                        <p className="text-sm text-red-500">{errors.reference_number}</p>
                    )}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="account_details">Account Details</Label>
                    <Input
                        id="account_details"
                        value={data.account_details}
                        onChange={(e) => setData('account_details', e.target.value)}
                        placeholder="Source account or funding details"
                    />
                    {errors.account_details && (
                        <p className="text-sm text-red-500">{errors.account_details}</p>
                    )}
                </div>
            </div>
            
            <div className="flex justify-end space-x-4">
                <Button variant="outline" type="button" onClick={onClose}>
                    Cancel
                </Button>
                <Button type="submit" disabled={processing || !currentFloat}>
                    {processing ? 'Processing...' : 'Issue Float'}
                </Button>
            </div>
        </form>
    );

    if (showAsCard) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Issue Float{currentFloat ? ` - ${currentFloat.name}` : ''}</CardTitle>
                    <CardDescription>
                        Issue additional funds to{currentFloat ? ' this cash float' : ' a selected cash float'}.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <FormContent />
                </CardContent>
            </Card>
        );
    }

    return (
    <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent>
    <DialogHeader>
    <DialogTitle>Issue Float{currentFloat ? ` - ${currentFloat.name}` : ''}</DialogTitle>
    <DialogDescription>
    Issue additional funds to{currentFloat ? ' this cash float' : ' a selected cash float'}.
    </DialogDescription>
    </DialogHeader>
    <FormContent />
    </DialogContent>
    </Dialog>
    );
}
