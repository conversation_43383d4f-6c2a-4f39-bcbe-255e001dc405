import React, { useEffect, useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { SidebarMenuItem, SidebarMenuButton } from '@/components/ui/sidebar';
import { <PERSON>lipboardCheck, CheckCircle, XCircle, Clock } from 'lucide-react';
import axios from 'axios';
import { SharedData } from '@/types';

interface StatusCounts {
  pending: number;
  approved: number;
  rejected: number;
  pendingApproval: number;
}

export function RequisitionStatusButtons() {
  const { auth } = usePage<SharedData>().props;
  const hasApproverPermission = (auth.user.permissions as unknown as string[]).includes('approver');
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    pending: 0,
    approved: 0,
    rejected: 0,
    pendingApproval: 0
  });
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchStatusCounts = async () => {
      try {
        const response = await axios.get(route('api.requisition-status-counts'));
        setStatusCounts(response.data);
      } catch (error) {
        console.error('Error fetching requisition status counts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStatusCounts();

    // Set up polling every 60 seconds to keep the counts updated
    const intervalId = setInterval(fetchStatusCounts, 60000);

    return () => clearInterval(intervalId);
  }, []);

  if (loading) {
    return null;
  }

  return (
    <>
      {/* Pending Approvals Button (shown to all users, but only enabled for those with approver permission) */}
      {statusCounts.pendingApproval > 0 && (
        <SidebarMenuItem>
          <SidebarMenuButton
            asChild
            disabled={!hasApproverPermission}
            className={!hasApproverPermission ? 'opacity-70 cursor-not-allowed' : ''}
          >
            <Link
              href={hasApproverPermission ? route('requisitions.my-approvals') : '#'}
              prefetch={hasApproverPermission}
              onClick={(e) => !hasApproverPermission && e.preventDefault()}
              className={!hasApproverPermission ? 'pointer-events-none' : ''}
            >
              <ClipboardCheck className="h-4 w-4 mr-1" />
              <span>Pending Approvals</span>
              <span className="ml-auto bg-accent/80 text-white text-xs font-bold px-2 py-1 rounded-full">
                {statusCounts.pendingApproval}
              </span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      )}

      {/* Pending Requisitions Button */}
      {statusCounts.pending > 0 && (
        <SidebarMenuItem>
          <SidebarMenuButton asChild>
            <Link href={route('requisitions.history')} prefetch>
              <Clock className="h-4 w-4 mr-1" />
              <span>Pending Requisitions</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      )}

      {/* Approved Requisitions Button */}
      {statusCounts.approved > 0 && (
        <SidebarMenuItem>
          <SidebarMenuButton asChild>
            <Link href={route('requisitions.history')} prefetch>
              <CheckCircle className="h-4 w-4 mr-1" />
              <span>Approved Requisitions</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      )}

      {/* Rejected Requisitions Button */}
      {statusCounts.rejected > 0 && (
        <SidebarMenuItem>
          <SidebarMenuButton asChild>
            <Link href={route('requisitions.history')} prefetch>
              <XCircle className="h-4 w-4 mr-1" />
              <span>Rejected Requisitions</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      )}
    </>
  );
}
