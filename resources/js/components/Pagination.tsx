import React from 'react';
import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

interface PaginationProps {
  links: {
    url: string | null;
    label: string;
    active: boolean;
  }[];
}

const Pagination: React.FC<PaginationProps> = ({ links }) => {
  // Don't render pagination if there's only 1 page
  if (links.length <= 3) {
    return null;
  }

  // Filter out the "Next" and "Previous" links
  const pageLinks = links.filter(
    (link) => link.label !== '&laquo; Previous' && link.label !== 'Next &raquo;'
  );

  const prevLink = links.find((link) => link.label === '&laquo; Previous');
  const nextLink = links.find((link) => link.label === 'Next &raquo;');

  return (
    <div className="flex items-center justify-center space-x-1 mt-4">
      {/* Previous button */}
      <Button
        variant="outline"
        size="icon"
        disabled={!prevLink?.url}
        asChild={prevLink?.url ? true : false}
      >
        {prevLink?.url ? (
          <Link href={prevLink.url}>
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous page</span>
          </Link>
        ) : (
          <span>
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous page</span>
          </span>
        )}
      </Button>

      {/* Page numbers */}
      {pageLinks.map((link, i) => {
        // For ellipsis
        if (link.label === '...') {
          return (
            <Button
              key={`ellipsis-${i}`}
              variant="outline"
              size="icon"
              disabled
              className="cursor-default"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">More pages</span>
            </Button>
          );
        }

        return (
          <Button
            key={link.label}
            variant={link.active ? 'default' : 'outline'}
            size="icon"
            asChild={!link.active && !!link.url}
            className="h-8 w-8"
          >
            {!link.active && link.url ? (
              <Link href={link.url}>
                {link.label}
              </Link>
            ) : (
              <span>{link.label}</span>
            )}
          </Button>
        );
      })}

      {/* Next button */}
      <Button
        variant="outline"
        size="icon"
        disabled={!nextLink?.url}
        asChild={nextLink?.url ? true : false}
      >
        {nextLink?.url ? (
          <Link href={nextLink.url}>
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next page</span>
          </Link>
        ) : (
          <span>
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next page</span>
          </span>
        )}
      </Button>
    </div>
  );
};

export default Pagination;
