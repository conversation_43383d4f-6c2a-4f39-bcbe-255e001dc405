import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertTriangle, TrendingUp } from 'lucide-react';

interface InventoryHealthModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    stockVsDemandData: Array<{
        inventory_item_id: number;
        name: string;
        sku: string;
        current_stock: number;
        stock_status: 'healthy' | 'low' | 'critical' | 'out_of_stock';
    }>;
    topRequestedItems: Array<{
        inventory_item_id: number;
        name: string;
        sku: string;
        total_request_count: number;
        approval_rate: number;
    }>;
}

export default function InventoryHealthModal({ 
    open, 
    onOpenChange, 
    stockVsDemandData, 
    topRequestedItems 
}: InventoryHealthModalProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'out_of_stock':
                return <Badge variant="destructive">Out of Stock</Badge>;
            case 'critical':
                return <Badge className="bg-orange-100 text-orange-800 border-orange-300">Critical</Badge>;
            case 'low':
                return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">Low Stock</Badge>;
            default:
                return <Badge className="bg-green-100 text-green-800 border-green-300">Healthy</Badge>;
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="w-[95vw] max-w-[1400px] max-h-[95vh] overflow-y-auto p-4 sm:p-6">
                <DialogHeader>
                    <DialogTitle className="text-foreground text-lg sm:text-xl font-bold">Inventory Health Report</DialogTitle>
                </DialogHeader>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    <div className="min-w-0">
                        <h3 className="flex items-center gap-2 font-semibold mb-3 sm:mb-4 text-foreground text-sm sm:text-base">
                            <AlertTriangle className="h-4 w-4 text-orange-600 flex-shrink-0" />
                            <span className="truncate">Stock Status Overview</span>
                        </h3>
                        <div className="border rounded-lg overflow-hidden">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="text-foreground font-medium text-xs sm:text-sm min-w-[120px]">
                                                Item
                                            </TableHead>
                                            <TableHead className="text-foreground font-medium text-xs sm:text-sm w-16 sm:w-20">
                                                Stock
                                            </TableHead>
                                            <TableHead className="text-foreground font-medium text-xs sm:text-sm w-20 sm:w-24">
                                                Status
                                            </TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {stockVsDemandData.slice(0, 12).map((item) => (
                                            <TableRow key={item.inventory_item_id}>
                                                <TableCell className="min-w-0">
                                                    <div>
                                                        <div className="font-medium text-xs sm:text-sm text-foreground truncate">
                                                            {item.name}
                                                        </div>
                                                        <div className="text-xs text-muted-foreground truncate">
                                                            {item.sku}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-foreground font-medium text-xs sm:text-sm">
                                                    {item.current_stock}
                                                </TableCell>
                                                <TableCell>{getStatusBadge(item.stock_status)}</TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </div>

                    <div className="min-w-0">
                        <h3 className="flex items-center gap-2 font-semibold mb-3 sm:mb-4 text-foreground text-sm sm:text-base">
                            <TrendingUp className="h-4 w-4 text-blue-600 flex-shrink-0" />
                            <span className="truncate">Most Requested Items</span>
                        </h3>
                        <div className="border rounded-lg overflow-hidden">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="text-foreground font-medium text-xs sm:text-sm min-w-[120px]">
                                                Item
                                            </TableHead>
                                            <TableHead className="text-foreground font-medium text-xs sm:text-sm w-16 sm:w-20">
                                                Requests
                                            </TableHead>
                                            <TableHead className="text-foreground font-medium text-xs sm:text-sm w-20 sm:w-24">
                                                Approval Rate
                                            </TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {topRequestedItems.slice(0, 12).map((item) => (
                                            <TableRow key={item.inventory_item_id}>
                                                <TableCell className="min-w-0">
                                                    <div>
                                                        <div className="font-medium text-xs sm:text-sm text-foreground truncate">
                                                            {item.name}
                                                        </div>
                                                        <div className="text-xs text-muted-foreground truncate">
                                                            {item.sku}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-foreground font-medium text-xs sm:text-sm">
                                                    {item.total_request_count}
                                                </TableCell>
                                                <TableCell>
                                                    <span className={`font-medium text-xs sm:text-sm ${
                                                        item.approval_rate >= 80 ? 'text-green-600' :
                                                        item.approval_rate >= 60 ? 'text-yellow-600' : 'text-red-600'
                                                    }`}>
                                                        {item.approval_rate}%
                                                    </span>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}