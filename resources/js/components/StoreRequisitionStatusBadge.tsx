import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { StoreRequisitionStatus, StoreRequisitionStatusBadgeProps, STORE_STATUS_STYLES } from '@/types/store-requisitions';
import { getStoreRequisitionStatusLabel } from '@/utils/store-requisitions';

/**
 * StoreRequisitionStatusBadge Component
 * 
 * A reusable component for displaying store requisition status with consistent styling
 * across all interfaces. Provides proper colors, accessibility, and responsive design.
 */
export function StoreRequisitionStatusBadge({ status, className }: StoreRequisitionStatusBadgeProps) {
  const label = getStoreRequisitionStatusLabel(status);
  const statusStyle = STORE_STATUS_STYLES[status];

  return (
    <Badge
      style={statusStyle}
      className={cn(
        'font-medium text-xs px-2 py-1 rounded-full',
        'whitespace-nowrap',
        className
      )}
      aria-label={`Status: ${label}`}
    >
      {label}
    </Badge>
  );
}

/**
 * StoreRequisitionStatusBadgeWithIcon Component
 * 
 * Enhanced version with status icons for better visual recognition
 */
interface StoreRequisitionStatusBadgeWithIconProps extends StoreRequisitionStatusBadgeProps {
  showIcon?: boolean;
}

export function StoreRequisitionStatusBadgeWithIcon({
  status,
  className,
  showIcon = true
}: StoreRequisitionStatusBadgeWithIconProps) {
  const label = getStoreRequisitionStatusLabel(status);
  const statusStyle = STORE_STATUS_STYLES[status];
  
  // Status icons mapping for store requisitions
  const getStatusIcon = (status: StoreRequisitionStatus) => {
    switch (status) {
      case 'draft':
        return '📝';
      case 'pending_approval':
        return '⏳';
      case 'approved':
        return '✅';
      case 'rejected':
        return '❌';
      case 'issued':
        return '📦';
      case 'partially_issued':
        return '📋';
      default:
        return '📄';
    }
  };
  
  return (
    <Badge
      style={statusStyle}
      className={cn(
        'font-medium text-xs px-2 py-1 rounded-full',
        'whitespace-nowrap flex items-center gap-1',
        className
      )}
      aria-label={`Status: ${label}`}
    >
      {showIcon && (
        <span className="text-xs" aria-hidden="true">
          {getStatusIcon(status)}
        </span>
      )}
      {label}
    </Badge>
  );
}

export default StoreRequisitionStatusBadge;
