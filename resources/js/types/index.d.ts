import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';
import type { Organization } from './organizations';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string | null;
    avatar: string | null;
    is_platform_admin: boolean;
    status: 'active' | 'invited' | 'inactive';
    last_login_at: string | null;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    roles?: { id: number; name: string; }[];
    permissions?: { id: number; name: string; }[];
}


export {
    Organization,
};