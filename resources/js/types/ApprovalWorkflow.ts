export interface Organization {
    id: number;
    name: string;
}

export interface Branch {
    id: number;
    name: string;
}

export interface Department {
    id: number;
    name: string;
    branch_id: number;
}

export interface Role {
    id: number;
    name: string;
    description?: string;
}

export interface User {
    id: number;
    name: string;
    email: string;
    roles: string;
    display_name: string;
}

export interface WorkflowStep {
    id?: number;
    step_number: number;
    role_id: number | null;
    approver_user_id?: number | null;
    description?: string;
    role?: Role;
    approver?: User;
}

export interface ApprovalWorkflow {
    id?: number;
    name: string;
    organization_id?: number;
    branch_id?: number;
    department_id?: number;
    is_default: boolean;
    description?: string;
    steps: WorkflowStep[];
    organization?: Organization;
    branch?: Branch;
    department?: Department;
}
