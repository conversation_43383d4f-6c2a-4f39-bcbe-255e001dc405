export interface Notification {
    id: string;
    type: string;
    data: {
        title: string;
        message: string;
        type: string;
        requisition_id?: number;
        workflow_step_id?: number;
        transaction_id?: number;
        action_url?: string;
        metadata?: {
            requisition_number?: string;
            purpose?: string;
            total_amount?: number;
            step_number?: number;
            requester_name?: string;
            department_name?: string;
            transaction_id?: number;
            disbursement_transaction_id?: string;
            // Cash float alert metadata
            current_balance?: string | number;
            alert_threshold?: string | number;
            percentage_remaining?: string;
            organization_name?: string;
            branch_name?: string;
            assigned_to?: string;
        };
    };
    read_at: string | null;
    created_at: string;
}

export interface PaginatedNotifications {
    data: Notification[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}