import { Organization, Branch, Department, Role, User } from '@/types/ApprovalWorkflow';

export interface WorkflowTemplate {
    id: number;
    name: string;
    category: string;
    description?: string;
    template_data: WorkflowTemplateData;
    validation_rules: Record<string, string>;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

export { Organization, Branch, Department, Role, User };

export interface WorkflowTemplateData {
    name: string;
    organization_id?: string;
    branch_id?: string | null;
    department_id?: string | null;
    is_default: boolean;
    description?: string;
    steps: WorkflowTemplateStep[];
}

export interface WorkflowTemplateStep {
    step_number: number;
    role_id: string | null;
    approver_user_id?: number | null;
    description: string;
}

export interface TemplatesByCategory {
    [category: string]: WorkflowTemplate[];
}

export interface TemplateContext {
    organization_id: string;
    branch_id?: string | null;
    department_id?: string | null;
    custom_name?: string;
    is_default?: boolean;
}

export interface TemplateValidation {
    valid: boolean;
    errors: Record<string, string[]>;
    data: WorkflowTemplateData;
}

export interface TemplatePreviewData {
    template: WorkflowTemplate;
    previewData: WorkflowTemplateData;
    validation: TemplateValidation;
    context: TemplateContext;
}
