interface NotificationResponse {
    notifications: Array<{
        id: string;
        type: string;
        data: Record<string, unknown>;
        read_at: string | null;
        created_at: string;
        toast: {
            title: string;
            message: string;
            type: 'success' | 'error' | 'warning' | 'info';
            duration: number;
            action_url?: string;
            dismissible: boolean;
        };
    }>;
    unread_count: number;
    timestamp: string;
    has_new: boolean;
}

class EnhancedPollingService {
    private _isActive: boolean = false;
    private _timeoutId: number | null = null;
    private _errorCount: number = 0;
    private _lastCheckTime: string = new Date().toISOString();
    private readonly _maxErrors: number = 5; 
    private readonly _baseDelay: number = 5000; // 5 seconds base delay
    private readonly _maxDelay: number = 60000; // 1 minute max delay
    private _lastVersionCheck: number = 0;
    private readonly _minVersionCheckInterval: number = 600000; // 10 minutes
    private _sessionChecked: boolean = false;

    constructor() {
        this._handleVisibilityChange = this._handleVisibilityChange.bind(this);
    }

    get isActive(): boolean {
        return this._isActive;
    }

    get lastCheckTime(): string {
        return this._lastCheckTime;
    }

    set errorCount(count: number) {
        this._errorCount = count;
    }

    start(): void {
        if (this._isActive) return;
        this._isActive = true;
        this._errorCount = 0;
        this._lastVersionCheck = Date.now();
        this.checkImmediately();
        this.setupVisibilityHandling();
    }

    stop(): void {
        this._isActive = false;
        if (this._timeoutId !== null) {
            window.clearTimeout(this._timeoutId);
            this._timeoutId = null;
        }
        document.removeEventListener('visibilitychange', this._handleVisibilityChange);
    }

    private checkImmediately(): void {
        if (!this._isActive) return;
        this.checkForNewNotifications();
    }

    // Public method to trigger immediate check after user actions
    public triggerImmediateCheck(): void {
        if (!this._isActive) return;
        
        // Cancel any pending check
        if (this._timeoutId !== null) {
            window.clearTimeout(this._timeoutId);
            this._timeoutId = null;
        }
        
        // Check immediately
        this.checkForNewNotifications();
    }

    private scheduleNextCheck(): void {
        if (!this._isActive) return;
        
        // Calculate delay based on error count
        const delay = Math.min(this._baseDelay * Math.pow(2, this._errorCount), this._maxDelay);
        
        this._timeoutId = window.setTimeout(() => {
            this.checkForNewNotifications();
        }, delay);
    }

    private shouldCheckVersion(): boolean {
        const now = Date.now();
        if (now - this._lastVersionCheck >= this._minVersionCheckInterval) {
            this._lastVersionCheck = now;
            return true;
        }
        return false;
    }

    private async checkForNewNotifications(): Promise<void> {
        try {
            const url = new URL(route('notifications.latest'));
            url.searchParams.append('since', this.lastCheckTime);

            const xsrfToken = document.cookie
                .split('; ')
                .find(row => row.startsWith('XSRF-TOKEN='))
                ?.split('=')[1];

            const headers: HeadersInit = {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest',
            };

            if (xsrfToken) {
                headers['X-XSRF-TOKEN'] = decodeURIComponent(xsrfToken);
            }

            // Only include Inertia headers if we should check version
            if (this.shouldCheckVersion()) {
                headers['X-Inertia'] = 'true';
                const version = document.querySelector('meta[name="version"]')?.getAttribute('content');
                if (version) {
                    headers['X-Inertia-Version'] = version;
                }
            }

            const response = await fetch(url.toString(), {
                method: 'GET',
                headers,
                credentials: 'include'
            });

            // Handle 409 Conflict (Inertia version mismatch)
            if (response.status === 409) {
                // Silently reload without toast notification
                window.location.reload();
                return;
            }

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data: NotificationResponse = await response.json();
            this.errorCount = 0; // Reset on success
            this.handleResponse(data);

        } catch (error) {
            this._handleError(error);
        }
    }

    private handleResponse(data: NotificationResponse): void {
        // Update timestamp for next check
        this._lastCheckTime = data.timestamp;

        // Dispatch events for new notifications 
        if (data.has_new && data.notifications.length > 0) {
            // Process notifications in order of creation in descending order
            const sortedNotifications = [...data.notifications].sort((a, b) => 
                new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
            );

            sortedNotifications.forEach((notification, index) => {
                // Add a small delay between notifications to prevent overwhelming
                setTimeout(() => {
                    const event = new CustomEvent('new-notification', {
                        detail: notification
                    });
                    window.dispatchEvent(event);
                }, index * 50); // 50ms delay between each notification
            });
        }

        // Update badge count
        const badgeEvent = new CustomEvent('notification-badge-update', {
            detail: { count: data.unread_count }
        });
        window.dispatchEvent(badgeEvent);

        this.scheduleNextCheck();
    }

    private async _handleError(error: unknown): Promise<void> {
        console.error('Polling error:', error);
        this._errorCount++;

        // Handle session expiry (401 errors)
        if (error instanceof Response && error.status === 401) {
            if (!this._sessionChecked) {
                this._sessionChecked = true;
                // Reload page to re-establish session
                window.location.reload();
                return;
            }
        }

        // Calculate backoff delay using exponential strategy
        const backoffDelay = Math.min(
            this._maxDelay,
            this._baseDelay * Math.pow(2, this._errorCount - 1)
        );

        if (this._errorCount >= this._maxErrors) {
            console.error(`Max polling errors (${this._maxErrors}) reached. Stopping polling.`);
            this.stop();
            return;
        }

        // Schedule next attempt with backoff
        this._timeoutId = window.setTimeout(() => this.checkForNewNotifications(), backoffDelay);
    }

    private _resetErrorCount(): void {
        this._errorCount = 0;
        this._sessionChecked = false;
    }

    private _handleVisibilityChange(): void {
        if (document.visibilityState === 'visible') {
            this.checkImmediately();
        }
    }

    private setupVisibilityHandling(): void {
        document.addEventListener('visibilitychange', this._handleVisibilityChange);
    }
}

const enhancedPollingService = new EnhancedPollingService();

// Expose globally for easy access from components
declare global {
    interface Window {
        enhancedPollingService: EnhancedPollingService;
    }
}

if (typeof window !== 'undefined') {
    window.enhancedPollingService = enhancedPollingService;
}

export default enhancedPollingService;