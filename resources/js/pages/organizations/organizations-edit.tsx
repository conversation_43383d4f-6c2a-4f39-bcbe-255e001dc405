import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';

interface Organization {
    id: number;
    name: string;
    contact_email: string | null;
    contact_phone: string | null;
    address: string | null;
    mpesa_account_details: string | null;
    status: string;
    admin?: {
        first_name: string;
        last_name: string;
        email: string;
        phone: string | null;
        username: string;
    };
}

interface OrganizationsEditProps {
    organization: Organization;
}

export default function OrganizationsEdit({ organization }: OrganizationsEditProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Organizations',
            href: '/organizations',
        },
        {
            title: organization.name,
            href: `/organizations/${organization.id}`,
        },
        {
            title: 'Edit',
            href: `/organizations/${organization.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        name: organization.name,
        contact_email: organization.contact_email || '',
        contact_phone: organization.contact_phone || '',
        address: organization.address || '',
        mpesa_account_details: organization.mpesa_account_details || '',
        status: organization.status,
        admin_first_name: organization.admin?.first_name || '',
        admin_last_name: organization.admin?.last_name || '',
        admin_email: organization.admin?.email || '',
        admin_phone: organization.admin?.phone || '',
        admin_username: organization.admin?.username || '',
        admin_password: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/organizations/${organization.id}`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Organization: ${organization.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold text-gray-800 md:text-3xl dark:text-gray-100">Edit Organization</h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <div className="space-y-6">
                        <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                            <CardHeader className="p-6">
                                <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Organization Information</CardTitle>
                                <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                    Update the details for the organization
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Organization Name
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                    />
                                    {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="contact_email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Contact Email
                                        </Label>
                                        <Input
                                            id="contact_email"
                                            type="email"
                                            value={data.contact_email}
                                            onChange={(e) => setData('contact_email', e.target.value)}
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.contact_email && <p className="text-sm text-red-500">{errors.contact_email}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="contact_phone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Contact Phone
                                        </Label>
                                        <Input
                                            id="contact_phone"
                                            value={data.contact_phone}
                                            onChange={(e) => setData('contact_phone', e.target.value)}
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.contact_phone && <p className="text-sm text-red-500">{errors.contact_phone}</p>}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="address" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Address (Optional)
                                    </Label>
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        className="mt-1 block min-h-[100px] w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                    />
                                    {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="mpesa_account_details" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        M-Pesa Account Details (Optional)
                                    </Label>
                                    <Input
                                        id="mpesa_account_details"
                                        value={data.mpesa_account_details}
                                        onChange={(e) => setData('mpesa_account_details', e.target.value)}
                                        className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                    />
                                    {errors.mpesa_account_details && <p className="text-sm text-red-500">{errors.mpesa_account_details}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Status
                                    </Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger className="w-full border-gray-300 bg-white text-gray-900 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100">
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent className="border-gray-200 bg-white text-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100">
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && <p className="text-sm text-red-500">{errors.status}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                            <CardHeader className="p-6">
                                <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Organization Administrator</CardTitle>
                                <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                    {organization.admin
                                        ? 'Update the admin user for this organization'
                                        : 'Assign a new admin user for this organization'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="admin_first_name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            First Name
                                        </Label>
                                        <Input
                                            id="admin_first_name"
                                            value={data.admin_first_name}
                                            onChange={(e) => setData('admin_first_name', e.target.value)}
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.admin_first_name && <p className="text-sm text-red-500">{errors.admin_first_name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="admin_last_name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Last Name
                                        </Label>
                                        <Input
                                            id="admin_last_name"
                                            value={data.admin_last_name}
                                            onChange={(e) => setData('admin_last_name', e.target.value)}
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.admin_last_name && <p className="text-sm text-red-500">{errors.admin_last_name}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="admin_email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Email
                                        </Label>
                                        <Input
                                            id="admin_email"
                                            type="email"
                                            value={data.admin_email}
                                            onChange={(e) => setData('admin_email', e.target.value)}
                                            className="focus:border-primary focus:ring-primary mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.admin_email && <p className="text-sm text-red-500">{errors.admin_email}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="admin_phone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Phone
                                        </Label>
                                        <Input
                                            id="admin_phone"
                                            value={data.admin_phone}
                                            onChange={(e) => setData('admin_phone', e.target.value)}
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.admin_phone && <p className="text-sm text-red-500">{errors.admin_phone}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="admin_username" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Username
                                        </Label>
                                        <Input
                                            id="admin_username"
                                            value={data.admin_username}
                                            onChange={(e) => setData('admin_username', e.target.value)}
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.admin_username && <p className="text-sm text-red-500">{errors.admin_username}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="admin_password" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Password (Optional)
                                        </Label>
                                        <Input
                                            id="admin_password"
                                            type="password"
                                            value={data.admin_password}
                                            onChange={(e) => setData('admin_password', e.target.value)}
                                            placeholder="Leave blank to keep current password"
                                            className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                        />
                                        {errors.admin_password && <p className="text-sm text-red-500">{errors.admin_password}</p>}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <div className="mt-6 flex justify-end space-x-2">
                            <Button
                                variant="outline"
                                type="button"
                                onClick={() => window.history.back()}
                                className="focus:ring-primary inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-900 dark:focus:ring-offset-gray-800"
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={processing}
                                className="bg-primary hover:bg-primary/80 focus:ring-primary dark:bg-primary dark:hover:bg-primary/80 inline-flex items-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white shadow-sm focus:ring-2 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800"
                            >
                                Update Organization
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
