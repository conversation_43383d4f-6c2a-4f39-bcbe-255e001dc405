import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase, Building2, GitBranch, Mail, MapPin, Pencil, Phone, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Admin {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    avatar?: string;
}

interface Branch {
    id: number;
    name: string;
    is_active: boolean;
}

interface Department {
    id: number;
    name: string;
    branch?: {
        name: string;
    };
}

interface Organization {
    id: number;
    name: string;
    description: string | null;
    status: string;
    is_active: boolean;
    address?: string;
    contact_email?: string;
    contact_phone?: string;
    mpesa_account_details?: string;
    branches: Branch[];
    departments: Department[];
}

interface OrganizationDetailsProps {
    organization: Organization;
    admins: Admin[];
}

const useInitials = () => {
    return (name: string) => {
        return name
            .split(' ')
            .map((part) => part[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };
};

const OrganizationDetails = ({ organization, admins }: OrganizationDetailsProps) => {
    const getInitials = useInitials();
    const navigate = useNavigate();

    return (
        <div className=" flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-gray-800 md:text-3xl dark:text-gray-100">Organization Details</h1>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={() => navigate(`/organizations/${organization.id}/edit`)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        Edit
                    </Button>
                    <Button variant="destructive">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                    </Button>
                </div>
            </div>

            <div className="grid gap-4 md:grid-cols-3">
                <Card className="md:col-span-1">
                    <CardHeader>
                        <CardTitle>Organization Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6 p-6">
                        <div className="flex items-center">
                            <div className="bg-primary/10 dark:bg-primary/90 flex h-10 w-10 items-center justify-center rounded-full">
                                <Building2 className="text-primary/60 dark:text-primary/40 h-5 w-5" />
                            </div>
                            <div className="ml-4">
                                <h2 className="text-muted-foreground/80 dark:text-muted-foreground/100 text-xl font-semibold">{organization.name}</h2>
                                {organization.description && (
                                    <p className="text-muted-foreground/50 dark:text-muted-foreground/40 text-sm">{organization.description}</p>
                                )}
                            </div>
                        </div>

                        <div>
                            <h3 className="text-muted-foreground text-sm font-medium">Status</h3>
                            <Badge variant={organization.status === 'active' ? 'success' : 'destructive'} className="mt-1">
                                {organization.status}
                            </Badge>
                        </div>

                        {organization.address && (
                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Address</h3>
                                <div className="mt-1 flex items-start">
                                    <MapPin className="text-muted-foreground mt-1 mr-2 h-4 w-4" />
                                    <p className="text-sm">{organization.address}</p>
                                </div>
                            </div>
                        )}

                        {(organization.contact_email || organization.contact_phone) && (
                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Contact Information</h3>
                                {organization.contact_email && (
                                    <div className="mt-1 flex items-center">
                                        <Mail className="text-muted-foreground mr-2 h-4 w-4" />
                                        <a href={`mailto:${organization.contact_email}`} className="text-primary text-sm hover:underline">
                                            {organization.contact_email}
                                        </a>
                                    </div>
                                )}
                                {organization.contact_phone && (
                                    <div className="mt-1 flex items-center">
                                        <Phone className="text-muted-foreground mr-2 h-4 w-4" />
                                        <a href={`tel:${organization.contact_phone}`} className="text-primary text-sm hover:underline">
                                            {organization.contact_phone}
                                        </a>
                                    </div>
                                )}
                            </div>
                        )}

                        {organization.mpesa_account_details && (
                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">M-Pesa Account Details</h3>
                                <p className="mt-1 text-sm">{organization.mpesa_account_details}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                <Card className="md:col-span-2">
                    <CardHeader>
                        <CardTitle>Organization Administrators</CardTitle>
                        <CardDescription>Users with Organization Admin role</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {admins.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-8">
                                <p className="text-muted-foreground text-sm">No organization administrators found.</p>
                                <Button className="mt-4" onClick={() => navigate('/users/create')}>
                                    Add Admin
                                </Button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {admins.map((admin) => (
                                    <div key={admin.id} className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <Avatar className="h-8 w-8">
                                                <AvatarImage src={admin.avatar || undefined} alt={`${admin.first_name} ${admin.last_name}`} />
                                                <AvatarFallback>{getInitials(`${admin.first_name} ${admin.last_name}`)}</AvatarFallback>
                                            </Avatar>
                                            <div className="ml-4">
                                                <p className="text-sm font-medium">
                                                    {admin.first_name} {admin.last_name}
                                                </p>
                                                <p className="text-muted-foreground text-sm">{admin.email}</p>
                                            </div>
                                        </div>
                                        <Button variant="outline" size="sm" onClick={() => navigate(`/users/${admin.id}`)}>
                                            View
                                        </Button>
                                    </div>
                                ))}

                                <div className="pt-4">
                                    <Button onClick={() => navigate('/users/create')}>Add New Admin</Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle>Branches</CardTitle>
                        <CardDescription>Branches in this organization</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {organization.branches.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-8">
                                <GitBranch className="text-muted-foreground h-12 w-12" />
                                <h3 className="mt-4 text-lg font-medium">No branches found</h3>
                                <p className="text-muted-foreground text-sm">Create branches for this organization to see them here.</p>
                                <Button className="mt-4" onClick={() => navigate('/branches/create')}>
                                    Create Branch
                                </Button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {organization.branches.map((branch) => (
                                    <div key={branch.id} className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                                <GitBranch className="text-primary h-4 w-4" />
                                            </div>
                                            <div className="ml-4">
                                                <p className="text-sm font-medium">{branch.name}</p>
                                                <Badge variant={branch.is_active ? 'success' : 'destructive'} className="mt-1">
                                                    {branch.is_active ? 'Active' : 'Inactive'}
                                                </Badge>
                                            </div>
                                        </div>
                                        <Button variant="outline" size="sm" onClick={() => navigate(`/branches/${branch.id}`)}>
                                            View
                                        </Button>
                                    </div>
                                ))}

                                <div className="pt-4">
                                    <Button onClick={() => navigate('/branches/create')}>Add New Branch</Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Departments</CardTitle>
                        <CardDescription>Departments in this organization</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {organization.departments.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-8">
                                <Briefcase className="text-muted-foreground h-12 w-12" />
                                <h3 className="mt-4 text-lg font-medium">No departments found</h3>
                                <p className="text-muted-foreground text-sm">Create departments for this organization to see them here.</p>
                                <Button className="mt-4" onClick={() => navigate('/departments/create')}>
                                    Create Department
                                </Button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {organization.departments.map((department) => (
                                    <div key={department.id} className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                                <Briefcase className="text-primary h-4 w-4" />
                                            </div>
                                            <div className="ml-4">
                                                <p className="text-sm font-medium">{department.name}</p>
                                                {department.branch && (
                                                    <div className="flex items-center">
                                                        <Building2 className="text-muted-foreground mr-1 h-3 w-3" />
                                                        <p className="text-muted-foreground text-xs">{department.branch.name}</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <Button variant="outline" size="sm" onClick={() => navigate(`/departments/${department.id}`)}>
                                            View
                                        </Button>
                                    </div>
                                ))}

                                <div className="pt-4">
                                    <Button onClick={() => navigate('/departments/create')}>Add New Department</Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default OrganizationDetails;
