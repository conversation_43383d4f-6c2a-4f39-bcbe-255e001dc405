import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Building2, Mail, Phone, PlusCircle } from 'lucide-react';

interface OrganizationsIndexProps {
    organizations: {
        id: number;
        name: string;
        contact_email: string | null;
        contact_phone: string | null;
        address: string | null;
        status: string;
    }[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Organizations',
        href: '/organizations',
    },
];

export default function OrganizationsIndex({ organizations }: OrganizationsIndexProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Organizations" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/100 text-background/100 text-2xl font-bold text-gray-800 md:text-3xl dark:text-gray-100">
                        Organizations
                    </h1>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href="/organizations/create">
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                New Organization
                            </span>
                        </Link>
                    </Button>
                </div>

                <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                    <CardHeader className="p-6">
                        <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">All Organizations</CardTitle>
                        <CardDescription className="text-sm text-gray-500 dark:text-gray-400">Manage organizations on the platform</CardDescription>
                    </CardHeader>
                    <CardContent className="p-6">
                        <div className="space-y-4">
                            {organizations.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Building2 className="h-12 w-12 text-gray-500 dark:text-gray-400" />
                                    <h3 className="mt-4 text-lg font-medium text-gray-800 dark:text-gray-100">No organizations found</h3>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Get started by creating a new organization.</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                        <thead className="bg-gray-50 dark:bg-gray-800">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                                                >
                                                    Organization
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                                                >
                                                    Contact
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                                                >
                                                    Status
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400"
                                                >
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                                            {organizations.map((organization) => (
                                                <tr key={organization.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className="bg-primary/10 dark:bg-primary/90 flex h-8 w-8 items-center justify-center rounded-full">
                                                                <Building2 className="text-primary/60 dark:text-primary/40 h-4 w-4" />
                                                            </div>
                                                            <div className="ml-4">
                                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                                    {organization.name}
                                                                </div>
                                                                {organization.address && (
                                                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                                                        {organization.address}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {organization.contact_email && (
                                                            <div className="flex items-center text-sm text-gray-900 dark:text-gray-100">
                                                                <Mail className="mr-1 h-3 w-3 text-gray-500 dark:text-gray-400" />
                                                                {organization.contact_email}
                                                            </div>
                                                        )}
                                                        {organization.contact_phone && (
                                                            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                                <Phone className="mr-1 h-3 w-3 text-gray-500 dark:text-gray-400" />
                                                                {organization.contact_phone}
                                                            </div>
                                                        )}
                                                        {!organization.contact_email && !organization.contact_phone && (
                                                            <div className="text-sm text-gray-500 dark:text-gray-400">No contact info</div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <Badge
                                                            variant={organization.status === 'active' ? 'default' : 'destructive'}
                                                            className={`${
                                                                organization.status === 'active'
                                                                    ? 'bg-primary/100 text-primary-800'
                                                                    : 'bg-red-100 text-red-800'
                                                            }`}
                                                        >
                                                            {organization.status}
                                                        </Badge>
                                                    </td>
                                                    <td className="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                asChild
                                                                className="border-muted-foreground/30 text-muted-foreground/70 hover:bg-muted-foreground/5 focus:ring-primary/50 dark:border-muted-foreground/70 dark:bg-muted-foreground/80 dark:text-muted-foreground/30 dark:hover:bg-muted-foreground/90 dark:focus:ring-offset-muted-foreground/80 inline-flex items-center rounded-md border bg-white px-3 py-1 text-sm font-medium focus:ring-2 focus:ring-offset-2 focus:outline-none"
                                                            >
                                                                <Link href={`/organizations/${organization.id}`}>View</Link>
                                                            </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                asChild
                                                                className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-900 dark:focus:ring-offset-gray-800"
                                                            >
                                                                <Link href={`/organizations/${organization.id}/edit`}>Edit</Link>
                                                            </Button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
