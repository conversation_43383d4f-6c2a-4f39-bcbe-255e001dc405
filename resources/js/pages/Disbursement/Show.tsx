import { AttachmentsList } from '@/components/AttachmentsList';
import { FileUpload } from '@/components/FileUpload';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Edit, Home } from 'lucide-react';
import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { formatCurrency } from '@/lib/utils';

interface TransactionItem {
    id: number;
    chart_of_account_id: number;
    description: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    reference_number: string;
}

interface ApproverDetail {
    id: number;
    name: string;
    department: string;
}

interface Attachment {
    id: number;
    original_name: string;
    file_size: number;
    mime_type: string;
    description?: string;
    is_evidence: boolean;
    uploaded_at_step?: string;
    created_at: string;
    uploader: {
        id: number;
        first_name: string;
        last_name: string;
    };
}

interface Transaction {
    id: number;
    requisition_id: number;
    status: string;
    account_details: string | null;
    disbursement_transaction_id: string | null;
    approvers_details: ApproverDetail[];
    total_amount: number;
    created_at: string;
    updated_at: string;
    items: TransactionItem[];
    attachments?: Attachment[];
    requisition: {
        requisition_number: string;
        purpose: string;
        requester?: {
            id: number;
            first_name: string;
            last_name: string;
            email: string;
        }
    };
}

interface ShowProps {
    transaction: Transaction;
    chartOfAccounts: Record<number, { id: number; name: string; account_type: string }>;
    canUpdate: boolean;
    canAttachFiles: boolean;
}

const Show: React.FC<ShowProps> = ({ transaction, chartOfAccounts, canUpdate, canAttachFiles }) => {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Disbursements',
            href: '/disbursement',
        },
        {
            title: `Disbursement #${transaction.id}`,
            href: `/disbursement/${transaction.id}`,
        },
    ];
    // Format date for display
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    // Get chart of account type
    const getChartOfAccountType = (id: number) => {
        return chartOfAccounts[id]?.account_type || 'Unknown';
    };

    // Get status badge
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'opened':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-accent/100 dark:border-accent/60 dark:bg-accent/90/20 text-accent/80"
                    >
                        Opened
                    </Badge>
                );
            case 'updated':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-blue-100 text-blue-800 dark:border-blue-600 dark:bg-blue-900/20"
                    >
                        Updated
                    </Badge>
                );
            case 'completed':
                return (
                    <Badge variant="outline" className="bg-primary/100 text-primary-800">
                        Completed
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    // Parse account details if available
    const accountDetails = transaction.account_details ? JSON.parse(transaction.account_details) : null;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Disbursement #${transaction.id}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <Card>
                    <CardHeader className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                        <div>
                            <CardTitle className="text-2xl">
                                Disbursement #{transaction.id}
                                <span className="ml-3">{getStatusBadge(transaction.status)}</span>
                            </CardTitle>
                            <CardDescription>For Requisition: {transaction.requisition.requisition_number}</CardDescription>
                        </div>
                        <div className="flex flex-col md:items-end">
                            <div className="text-muted-foreground text-sm">Created on</div>
                            <div>{formatDate(transaction.created_at)}</div>
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Transaction Details */}
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div>
                                <h3 className="mb-2 text-lg font-medium">Requisition Details</h3>
                                <div className="space-y-2">
                                    <div>
                                        <Label className="text-muted-foreground text-sm">Purpose</Label>
                                        <p>{transaction.requisition.purpose}</p>
                                    </div>
                                    <div>
                                        <Label className="text-muted-foreground text-sm">Requested By</Label>
                                        <div className="mt-1">
                                            {transaction.requisition.requester ? (
                                                <div className="flex flex-col space-y-1">
                                                    <p className="text-sm font-medium">
                                                        {transaction.requisition.requester.first_name} {transaction.requisition.requester.last_name}
                                                    </p>
                                                </div>
                                            ) : (
                                                <p className="text-sm text-muted-foreground">No requester information available</p>
                                            )}
                                        </div>
                                    </div>
                                    <div>
                                        <Label className="text-muted-foreground text-sm">Total Amount</Label>
                                        <p className="font-semibold text-sm">{formatCurrency(transaction.total_amount)}</p>
                                    </div>
                                </div>
                            </div>

                            {accountDetails && (
                                <div>
                                    <h3 className="mb-2 text-lg font-medium">Account Details</h3>
                                    <div className="space-y-2">
                                        <div>
                                            <Label className="text-muted-foreground text-sm">Mpesa Account Name</Label>
                                            <p>{accountDetails.account_name}</p>
                                        </div>
                                        <div>
                                            <Label className="text-muted-foreground text-sm">Mpesa Account Number</Label>
                                            <p>{accountDetails.account_number}</p>
                                        </div>
                                       
                        
                                    </div>
                                </div>
                            )}
                        </div>


                        <Separator />

                        {/* Transaction Items */}
                        <div>
                            <div className="mb-4 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
                                <h3 className="text-lg font-medium">Items</h3>
                                {canUpdate && (
                                    <Button asChild className="w-full sm:w-auto">
                                        <Link href={`/disbursement/${transaction.id}/edit`}>
                                            <Edit className="mr-2 h-4 w-4" />
                                            Provide Account Details
                                        </Link>
                                    </Button>
                                )}
                            </div>
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Type</TableHead>
                                            <TableHead>Description</TableHead>
                                            <TableHead className="text-right">Quantity</TableHead>
                                            <TableHead className="text-right">Unit Price</TableHead>
                                            <TableHead className="text-right">Total Price</TableHead>
                                            <TableHead>Reference</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {transaction.items.map((item) => (
                                            <TableRow key={item.id}>
                                                <TableCell>{getChartOfAccountType(item.chart_of_account_id)}</TableCell>
                                                <TableCell>{item.description}</TableCell>
                                                <TableCell className="text-right">{item.quantity}</TableCell>
                                                <TableCell className="text-right">{formatCurrency(item.unit_price)}</TableCell>
                                                <TableCell className="text-right">{formatCurrency(item.total_price)}</TableCell>
                                                <TableCell>{item.reference_number}</TableCell>
                                            </TableRow>
                                        ))}
                                        <TableRow className="bg-primary/10 font-medium">
                                            <TableCell colSpan={4} className="text-right">
                                                Total:
                                            </TableCell>
                                            <TableCell className="text-left font-bold">{formatCurrency(transaction.total_amount)}</TableCell>
                                            <TableCell></TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>

                        {/* Attachments Section */}
                        <div className="border-t pt-6">
                            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                {/* Existing Attachments */}
                                <AttachmentsList attachments={transaction.attachments || []} canDelete={canAttachFiles} className="w-full" />

                                {/* File Upload */}
                                {canAttachFiles && (
                                    <FileUpload
                                        entityType="transaction"
                                        entityId={transaction.id}
                                        uploadedAtStep={transaction.status}
                                        className="w-full"
                                    />
                                )}
                            </div>
                        </div>
                    </CardContent>
                    <CardFooter className="bg-muted/50 flex flex-col gap-4 border-t p-4 sm:flex-row sm:justify-start">
                        <div className="flex w-full flex-col gap-2 sm:w-auto sm:flex-row">
                            <Button variant="outline" asChild className="w-full sm:w-auto">
                                <Link href="/disbursement">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Disbursements
                                </Link>
                            </Button>
                            <Button variant="outline" asChild className="w-full sm:w-auto">
                                <Link href="/dashboard">
                                    <Home className="mr-2 h-4 w-4" />
                                    Dashboard
                                </Link>
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </div>
        </AppLayout>
    );
};

export default Show;
