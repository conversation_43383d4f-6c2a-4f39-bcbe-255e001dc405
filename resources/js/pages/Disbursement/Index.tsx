import Pagination from '@/components/Pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { CheckCircle, Edit, Eye, Search } from 'lucide-react';
import React from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Disbursements',
        href: '/disbursement',
    },
];

interface Transaction {
    id: number;
    requisition_id: number;
    status: string;
    total_amount: number;
    created_at: string;
    disbursement_transaction_id: string | null;
    requisition: {
        requisition_number: string;
    };
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface IndexProps {
    transactions: {
        data: Transaction[];
        links: PaginationLink[];
        total: number;
    };
    filters: {
        search: string;
        status: string;
        sort: string;
        direction: string;
    };
}

const Index: React.FC<IndexProps> = ({ transactions, filters }) => {
    // Format date for display
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'opened':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-accent/100 dark:border-accent/60 dark:bg-accent/90/20 text-accent/80"
                    >
                        Opened
                    </Badge>
                );
            case 'updated':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-blue-100 text-blue-800 dark:border-blue-600 dark:bg-blue-900/20"
                    >
                        Updated
                    </Badge>
                );
            case 'completed':
                return (
                    <Badge variant="outline" className="bg-primary/100 text-primary-800">
                        Completed
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Disbursements" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <Card>
                    <CardHeader>
                        <CardTitle>Disbursements</CardTitle>
                        <CardDescription>Manage your disbursements and track their status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {/* Filters */}
                        <div className="mb-6 flex flex-col gap-4 md:flex-row">
                            <div className="flex-1">
                                <form method="get">
                                    <div className="relative">
                                        <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                                        <Input
                                            type="search"
                                            name="search"
                                            placeholder="Search disbursements..."
                                            className="pl-8"
                                            defaultValue={filters.search}
                                        />
                                    </div>
                                </form>
                            </div>
                            <div className="w-full md:w-48">
                                <Select
                                    name="status"
                                    defaultValue={filters.status || 'all'}
                                    onValueChange={(value) => {
                                        window.location.href = `?status=${value}`;
                                    }}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Filter by status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        <SelectItem value="opened">Opened</SelectItem>
                                        <SelectItem value="updated">Updated</SelectItem>
                                        <SelectItem value="completed">Completed</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {/* Transactions Table */}
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>ID</TableHead>
                                        <TableHead>Requisition</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {transactions.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={6} className="text-muted-foreground py-8 text-center">
                                                No disbursements found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        transactions.data.map((transaction) => (
                                            <TableRow key={transaction.id}>
                                                <TableCell>{transaction.id}</TableCell>
                                                <TableCell>{transaction.requisition.requisition_number}</TableCell>
                                                <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                                                <TableCell>${transaction.total_amount}</TableCell>
                                                <TableCell>{formatDate(transaction.created_at)}</TableCell>
                                                <TableCell>
                                                    <div className="flex space-x-2">
                                                        <Button variant="ghost" size="icon" asChild>
                                                            <Link href={`/disbursement/${transaction.id}`}>
                                                                <Eye className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                        {transaction.status === 'opened' && (
                                                            <Button variant="ghost" size="icon" asChild>
                                                                <Link href={`/disbursement/${transaction.id}/edit`}>
                                                                    <Edit className="h-4 w-4" />
                                                                </Link>
                                                            </Button>
                                                        )}
                                                        {transaction.status === 'updated' && (
                                                            <Button variant="ghost" size="icon" asChild>
                                                                <Link href={`/disbursement/${transaction.id}`}>
                                                                    <CheckCircle className="h-4 w-4" />
                                                                </Link>
                                                            </Button>
                                                        )}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        <div className="mt-4">
                            <Pagination links={transactions.links} />
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
};

export default Index;
