import { DocumentsSection } from '@/components/layout/documents-section';
import { ExpensesSection } from '@/components/layout/expenses-section';
import { FeaturesSection } from '@/components/layout/features-section';
import { HeroSection } from '@/components/layout/hero-section';
import { HowItWorksSection } from '@/components/layout/how-it-works';
import Layout from '@/components/layout/layout';
import { Navbar } from '@/components/layout/navbar';
import { UsersSection } from '@/components/layout/user-section';
import { User } from '@/types';

interface HomeProps {
    auth?: {
        user: User;
    };
}

export default function Homepage({ auth }: HomeProps) {
    return (
        <Layout title="Home" user={auth?.user}>
            {/* Navigation Bar */}
            <Navbar />

            {/* Hero Section */}
            <HeroSection />

            {/* Key Features Section */}
            <FeaturesSection />

            {/* How It Works Section */}
            <HowItWorksSection />

            {/* Document Management Section */}
            <DocumentsSection />

            {/* Expense Management Section */}
            <ExpensesSection />

            {/* User Management Section */}
            <UsersSection />
        </Layout>
    );
}
