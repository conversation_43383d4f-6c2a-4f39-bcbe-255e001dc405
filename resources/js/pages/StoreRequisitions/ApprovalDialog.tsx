import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, XCircle, AlertTriangle, Package, User, Building, Calendar } from 'lucide-react';
import { StoreRequisition } from '@/types/store-requisitions';

interface ApprovalDialogProps {
    requisition: StoreRequisition;
    action: 'approve' | 'reject';
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (comments?: string) => void;
    isSubmitting?: boolean;
}

export function ApprovalDialog({
    requisition,
    action,
    isOpen,
    onClose,
    onSubmit,
    isSubmitting = false
}: ApprovalDialogProps) {
    const [comments, setComments] = useState('');

    // Calculate summary information
    const summaryInfo = useMemo(() => {
        const totalItems = requisition.items?.length || 0;
        const totalQuantity = requisition.items?.reduce((sum, item) => sum + Number(item.quantity_requested), 0) || 0;

        // Note: Cost calculation would require unit prices from inventory items
        // For now, we'll focus on quantity-based metrics

        // Get urgency level based on requested date (simple heuristic)
        const requestedDate = new Date(requisition.requested_at || requisition.created_at);
        const daysSinceRequest = Math.floor((Date.now() - requestedDate.getTime()) / (1000 * 60 * 60 * 24));
        const urgencyLevel = daysSinceRequest > 7 ? 'High' : daysSinceRequest > 3 ? 'Medium' : 'Normal';

        return {
            totalItems,
            totalQuantity,
            urgencyLevel,
            daysSinceRequest
        };
    }, [requisition]);

    const handleSubmit = () => {
        if (action === 'reject' && !comments.trim()) {
            return; // Validation handled by disabled state
        }

        onSubmit(comments.trim() || undefined);
    };

    const handleClose = () => {
        if (!isSubmitting) {
            setComments('');
            onClose();
        }
    };

    const getDialogIcon = () => {
        switch (action) {
            case 'approve':
                return <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />;
            case 'reject':
                return <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />;
            default:
                return <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />;
        }
    };

    const getDialogTitle = () => {
        switch (action) {
            case 'approve':
                return 'Approve Store Requisition';
            case 'reject':
                return 'Reject Store Requisition';
            default:
                return 'Store Requisition Action';
        }
    };

    const getDialogDescription = () => {
        switch (action) {
            case 'approve':
                return 'Are you sure you want to approve this store requisition? This action will allow the items to be issued.';
            case 'reject':
                return 'Are you sure you want to reject this store requisition? Please provide a reason for rejection.';
            default:
                return 'Please confirm your action.';
        }
    };

    const getButtonText = () => {
        if (isSubmitting) {
            return action === 'approve' ? 'Approving...' : 'Rejecting...';
        }
        return action === 'approve' ? 'Approve' : 'Reject';
    };

    const getButtonVariant = () => {
        return action === 'approve' ? 'default' : 'destructive';
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <div className="flex items-center gap-3">
                        {getDialogIcon()}
                        <div>
                            <DialogTitle className="text-xl font-semibold">
                                {getDialogTitle()}
                            </DialogTitle>
                            <p className="text-sm text-muted-foreground mt-1">
                                Review the details below before making your decision
                            </p>
                        </div>
                    </div>
                </DialogHeader>

                <div className="space-y-6 py-4">
                    {/* Quick Summary Cards */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-muted/50 rounded-lg p-3 text-center">
                            <div className="flex items-center justify-center mb-1">
                                <Package className="h-4 w-4 text-primary" />
                            </div>
                            <div className="font-bold text-lg text-foreground">{summaryInfo.totalItems}</div>
                            <div className="text-xs text-muted-foreground">Items</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-3 text-center">
                            <div className="flex items-center justify-center mb-1">
                                <Calendar className="h-4 w-4 text-primary" />
                            </div>
                            <div className="font-bold text-lg text-foreground">{summaryInfo.daysSinceRequest}</div>
                            <div className="text-xs text-muted-foreground">Days Ago</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-3 text-center">
                            <div className="flex items-center justify-center mb-1">
                                <AlertTriangle className={`h-4 w-4 ${
                                    summaryInfo.urgencyLevel === 'High' ? 'text-red-500' :
                                    summaryInfo.urgencyLevel === 'Medium' ? 'text-yellow-500' : 'text-green-500'
                                }`} />
                            </div>
                            <div className="font-bold text-sm text-foreground">{summaryInfo.urgencyLevel}</div>
                            <div className="text-xs text-muted-foreground">Urgency</div>
                        </div>
                        <div className="bg-muted/50 rounded-lg p-3 text-center">
                            <div className="flex items-center justify-center mb-1">
                                <Package className="h-4 w-4 text-primary" />
                            </div>
                            <div className="font-bold text-lg text-foreground">{summaryInfo.totalQuantity}</div>
                            <div className="text-xs text-muted-foreground">Total Qty</div>
                        </div>
                    </div>

                    {/* Requisition Details */}
                    <div className="bg-muted/30 rounded-lg p-4 space-y-4">
                        <div className="flex items-center gap-2 mb-3">
                            <h3 className="font-semibold text-lg text-foreground">Requisition Details</h3>
                            <Badge variant="outline" className="text-xs">
                                #{requisition.id}
                            </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-3">
                                <div className="flex items-start gap-2">
                                    <User className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Requester</Label>
                                        <p className="font-medium text-foreground">
                                            {requisition.requester?.first_name} {requisition.requester?.last_name}
                                        </p>
                                        <p className="text-sm text-muted-foreground">{requisition.requester?.email}</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-2">
                                    <Building className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Department</Label>
                                        <p className="font-medium text-foreground">{requisition.department?.name || 'Not specified'}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <div className="flex items-start gap-2">
                                    <Calendar className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Requested Date</Label>
                                        <p className="font-medium text-foreground">
                                            {new Date(requisition.requested_at || requisition.created_at).toLocaleDateString()}
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            {new Date(requisition.requested_at || requisition.created_at).toLocaleTimeString()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <Separator />

                        <div>
                            <Label className="text-sm font-medium text-muted-foreground">Purpose</Label>
                            <p className="text-sm text-foreground bg-background p-3 rounded border mt-1">{requisition.purpose}</p>
                        </div>
                    </div>

                    {/* Items List */}
                    {requisition.items && requisition.items.length > 0 && (
                        <div className="bg-muted/30 rounded-lg p-4">
                            <h3 className="font-semibold text-lg text-foreground mb-3">Requested Items</h3>
                            <div className="space-y-2 max-h-48 overflow-y-auto">
                                {requisition.items.map((item, index) => (
                                    <div key={index} className="flex items-center justify-between p-2 bg-background rounded border">
                                        <div className="flex-1">
                                            <div className="font-medium text-foreground">{item.inventoryItem?.name || item.inventory_item?.name || 'Unknown Item'}</div>
                                            <div className="text-sm text-muted-foreground">
                                                SKU: {item.inventoryItem?.sku || item.inventory_item?.sku || 'N/A'}
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="font-bold text-foreground">{item.quantity_requested}</div>
                                            <div className="text-sm text-muted-foreground">
                                                {item.inventoryItem?.unit_of_measure || item.inventory_item?.unit_of_measure || 'units'}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Action Description */}
                    <div className="bg-muted/50 border border-muted rounded-lg p-3">
                        <div className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-primary mt-0.5" />
                            <div className="text-sm text-foreground">
                                <strong>
                                    {action === 'approve' ? 'Approval Confirmation:' : 'Rejection Confirmation:'}
                                </strong>
                                <p className="mt-1">{getDialogDescription()}</p>
                            </div>
                        </div>
                    </div>

                    {/* Comments Field */}
                    <div className="space-y-2">
                        <Label htmlFor="comments" className="text-sm font-medium">
                            {action === 'approve' 
                                ? 'Approval Comments (Optional)' 
                                : 'Rejection Reason (Required)'
                            }
                        </Label>
                        <Textarea
                            id="comments"
                            value={comments}
                            onChange={(e) => setComments(e.target.value)}
                            placeholder={action === 'approve'
                                ? 'Add any comments about this approval...'
                                : 'Please explain why this requisition is being rejected...'
                            }
                            rows={3}
                            disabled={isSubmitting}
                            className={`text-foreground bg-background ${action === 'reject' && !comments.trim() ? 'border-destructive' : ''}`}
                        />
                        {action === 'reject' && !comments.trim() && (
                            <p className="text-sm text-destructive">
                                Rejection reason is required
                            </p>
                        )}
                    </div>
                </div>
                
                <DialogFooter className="flex gap-2">
                    <Button 
                        variant="outline" 
                        onClick={handleClose} 
                        disabled={isSubmitting}
                        className="flex-1 sm:flex-none"
                    >
                        Cancel
                    </Button>
                    <Button
                        variant={getButtonVariant()}
                        onClick={handleSubmit}
                        disabled={isSubmitting || (action === 'reject' && !comments.trim())}
                        className="flex-1 sm:flex-none"
                    >
                        {getButtonText()}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
