import { Head } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PageProps } from '@inertiajs/core';
import { Link, usePage, router } from '@inertiajs/react';
import { ArrowLeft, Package, User, Calendar, FileText, Send, CheckCircle, XCircle} from "lucide-react";
import { useState, useMemo } from 'react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition, StoreRequisitionItem, InventoryItem, StoreRequisitionHistoryEntry } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadgeWithIcon } from '@/components/StoreRequisitionStatusBadge';
import { canEditStoreRequisition, getStoreRequisitionEditRoute } from '@/utils/store-requisitions';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';
import { StoreRequisitionHistory } from '@/components/StoreRequisitions/StoreRequisitionHistory';

/**
 * Helper function to get inventory item from store requisition item.
 * Handles both camelCase (inventoryItem) and snake_case (inventory_item) naming conventions
 * that may come from the backend.
 *
 * @param item - The store requisition item
 * @returns The inventory item or undefined if not found
 */
const getInventoryItem = (item: StoreRequisitionItem): InventoryItem | undefined => {
    return item.inventoryItem || (item as StoreRequisitionItem & { inventory_item?: InventoryItem }).inventory_item;
};

interface ShowStoreRequisitionPageProps extends PageProps {
    store_requisition: StoreRequisition;
    histories: StoreRequisitionHistoryEntry[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
        roles?: string[];
    };
}

// Status icon logic moved to StoreRequisitionStatusBadgeWithIcon component

export default function ShowStoreRequisition() {
    const {
        store_requisition,
        histories,
        user
    } = usePage<ShowStoreRequisitionPageProps>().props;



    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: `Requisition #${store_requisition.id}`,
            href: `/store-requisitions/${store_requisition.id}`,
        },
    ];

    const canEdit = canEditStoreRequisition(store_requisition, user.id);
    const canSubmit = store_requisition.status === 'draft' && store_requisition.requester_user_id === user.id;
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Approval functionality
    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { approveRequisition, rejectRequisition, isSubmitting: isApprovalSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
            // Refresh the page to show updated status
            router.reload();
        }
    });

    // Check if user can approve this requisition using granular role-based logic
    const canApprove = useMemo(() => {
        if (store_requisition.status !== 'pending_approval' || store_requisition.requester_user_id === user.id) {
            return false;
        }

        // Safely check if requester is a store keeper
        const requesterPermissions = store_requisition.requester?.permissions;
        const requesterIsStoreKeeper = Array.isArray(requesterPermissions)
            ? requesterPermissions.includes('store-keep')
            : false;

        if (requesterIsStoreKeeper) {
            // Store keeper requisitions can only be approved by Finance Manager or Organization Admin
            return user.roles?.includes('Finance Manager') || user.roles?.includes('Organization Admin');
        } else {
            // Employee/HOD requisitions can only be approved by store keepers
            return user.permissions.includes('store-keep');
        }
    }, [store_requisition, user]);

    const openApprovalDialog = () => {
        setSelectedRequisition(store_requisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = () => {
        setSelectedRequisition(store_requisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition || !dialogAction) return;

        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject') {
            rejectRequisition(selectedRequisition.id, comments || '');
        }
    };

    const handleSubmit = () => {
        if (isSubmitting) return;

        setIsSubmitting(true);
        router.post(`/store-requisitions/${store_requisition.id}/submit`, {}, {
            onSuccess: () => {
                setIsSubmitting(false);
            },
            onError: () => {
                setIsSubmitting(false);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Store Requisition #${store_requisition.id}`} />
            
            <div className="bg-background/90 flex h-full flex-1 flex-col gap-4 sm:gap-6 p-3 sm:p-4 lg:p-6">
                {/* Header Section - Mobile Responsive */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
                        <Button variant="outline" size="sm" className="w-fit" asChild>
                            <Link href="/store-requisitions">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                <span className="hidden sm:inline">Back to Store Requisitions</span>
                                <span className="sm:hidden">Back</span>
                            </Link>
                        </Button>
                        <div className="min-w-0 flex-1">
                            <h1 className="text-xl sm:text-2xl font-bold text-foreground truncate">
                                Store Requisition #{store_requisition.id}
                            </h1>
                            <p className="text-sm sm:text-base text-muted-foreground">
                                View store requisition details and status
                            </p>
                        </div>
                    </div>

                    {/* Actions Section - Mobile Responsive */}
                    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
                        <StoreRequisitionStatusBadgeWithIcon
                            status={store_requisition.status}
                            showIcon={false}
                            className="w-fit"
                        />

                        {/* Action Buttons */}
                        <div className="flex flex-col gap-2 sm:flex-row sm:gap-2">
                            {canSubmit && (
                                <Button
                                    onClick={handleSubmit}
                                    disabled={isSubmitting}
                                    className="gap-2 text-sm"
                                    size="sm"
                                >
                                    <Send className="h-4 w-4" />
                                    <span className="hidden sm:inline">{isSubmitting ? 'Submitting...' : 'Submit for Approval'}</span>
                                    <span className="sm:hidden">{isSubmitting ? 'Submitting...' : 'Submit'}</span>
                                </Button>
                            )}
                            {canEdit && (
                                <Button asChild variant="outline" size="sm" className="text-sm">
                                    <Link href={getStoreRequisitionEditRoute(store_requisition)}>
                                        Edit
                                    </Link>
                                </Button>
                            )}
                            {canApprove && (
                                <div className="flex gap-2">
                                    <Button
                                        onClick={openApprovalDialog}
                                        disabled={isApprovalSubmitting}
                                        className="gap-2 text-sm flex-1 sm:flex-none"
                                        size="sm"
                                    >
                                        <CheckCircle className="h-4 w-4" />
                                        Approve
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={openRejectionDialog}
                                        disabled={isApprovalSubmitting}
                                        className="gap-2 text-sm flex-1 sm:flex-none"
                                        size="sm"
                                    >
                                        <XCircle className="h-4 w-4" />
                                        Reject
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <Tabs defaultValue="details" className="space-y-4 sm:space-y-6">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="details" className="text-sm">
                            <span className="hidden sm:inline">Requisition Details</span>
                            <span className="sm:hidden">Details</span>
                        </TabsTrigger>
                        {histories.length > 0 && (
                            <TabsTrigger value="history" className="text-sm">History</TabsTrigger>
                        )}
                    </TabsList>

                    <TabsContent value="details" className="space-y-4 sm:space-y-6">
                        <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
                            {/* Requisition Details */}
                            <Card>
                                <CardHeader className="pb-3 sm:pb-6">
                                    <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                        <FileText className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        <span className="truncate">Requisition Details</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 sm:space-y-4">
                                    <div>
                                        <label className="text-xs sm:text-sm font-medium text-muted-foreground">Purpose</label>
                                        <p className="mt-1 text-sm sm:text-base break-words">{store_requisition.purpose}</p>
                                    </div>

                                    <Separator />

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Department</label>
                                            <p className="mt-1 text-sm sm:text-base truncate">{store_requisition.department?.name}</p>
                                        </div>
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Branch</label>
                                            <p className="mt-1 text-sm sm:text-base truncate">{store_requisition.branch?.name}</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Requested Date</label>
                                            <p className="mt-1 flex items-center gap-2 text-sm sm:text-base">
                                                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                <span className="truncate">
                                                    {store_requisition.requested_at ? new Date(store_requisition.requested_at).toLocaleDateString() : 'Not requested'}
                                                </span>
                                            </p>
                                        </div>
                                        {store_requisition.approved_at && (
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Approved Date</label>
                                                <p className="mt-1 flex items-center gap-2 text-sm sm:text-base">
                                                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                    <span className="truncate">
                                                        {new Date(store_requisition.approved_at).toLocaleDateString()}
                                                    </span>
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {store_requisition.issued_at && (
                                        <div>
                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Issued Date</label>
                                            <p className="mt-1 flex items-center gap-2 text-sm sm:text-base">
                                                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                                <span className="truncate">
                                                    {new Date(store_requisition.issued_at).toLocaleDateString()}
                                                </span>
                                            </p>
                                        </div>
                                    )}

                                    {store_requisition.rejection_reason && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Rejection Reason</label>
                                                <p className="mt-1 text-sm sm:text-base text-destructive break-words">{store_requisition.rejection_reason}</p>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>

                            {/* People Involved */}
                            <Card>
                                <CardHeader className="pb-3 sm:pb-6">
                                    <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                        <User className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        <span className="truncate">People Involved</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3 sm:space-y-4">
                                    <div>
                                        <label className="text-xs sm:text-sm font-medium text-muted-foreground">Requester</label>
                                        <p className="mt-1 text-sm sm:text-base font-medium truncate">
                                            {store_requisition.requester?.first_name} {store_requisition.requester?.last_name}
                                        </p>
                                        <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                            {store_requisition.requester?.email}
                                        </p>
                                    </div>

                                    {store_requisition.approver && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Approver</label>
                                                <p className="mt-1 text-sm sm:text-base font-medium truncate">
                                                    {store_requisition.approver.first_name} {store_requisition.approver.last_name}
                                                </p>
                                                <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                                    {store_requisition.approver.email}
                                                </p>
                                            </div>
                                        </>
                                    )}

                                    {store_requisition.issuer && (
                                        <>
                                            <Separator />
                                            <div>
                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Issuer</label>
                                                <p className="mt-1 text-sm sm:text-base font-medium truncate">
                                                    {store_requisition.issuer.first_name} {store_requisition.issuer.last_name}
                                                </p>
                                                <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                                    {store_requisition.issuer.email}
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Requested Items */}
                        <Card>
                            <CardHeader className="pb-3 sm:pb-6">
                                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                                    <Package className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span className="truncate">Requested Items ({store_requisition.items?.length || 0})</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {store_requisition.items && store_requisition.items.length > 0 ? (
                                    <div className="space-y-3 sm:space-y-4">
                                        {store_requisition.items.map((item, index) => {
                                            const inventoryItem = getInventoryItem(item);
                                            return (
                                                <div key={index} className="border rounded-lg p-3 sm:p-4">
                                                    {/* Mobile-first layout */}
                                                    <div className="space-y-3 sm:space-y-0 sm:grid sm:gap-4 lg:grid-cols-4">
                                                        {/* Item Details - Full width on mobile, 2 columns on larger screens */}
                                                        <div className="sm:col-span-2 lg:col-span-2">
                                                            <label className="text-xs sm:text-sm font-medium text-muted-foreground">Item</label>
                                                            <p className="mt-1 text-sm sm:text-base font-medium break-words">{inventoryItem?.name}</p>
                                                            {inventoryItem?.description && (
                                                                <p className="text-xs sm:text-sm text-muted-foreground mt-1 break-words">{inventoryItem.description}</p>
                                                            )}
                                                            <p className="text-xs text-muted-foreground mt-1">SKU: {inventoryItem?.sku}</p>
                                                        </div>

                                                        {/* Quantity Information - Responsive grid */}
                                                        <div className="grid grid-cols-1 gap-3 sm:gap-4 sm:grid-cols-2 lg:grid-cols-1 lg:col-span-2">
                                                            <div>
                                                                <label className="text-xs sm:text-sm font-medium text-muted-foreground">Quantity Requested</label>
                                                                <p className="mt-1 text-sm sm:text-base font-medium">
                                                                    {item.quantity_requested} {inventoryItem?.unit_of_measure}
                                                                </p>
                                                            </div>
                                                            {item.quantity_issued !== undefined && (
                                                                <div>
                                                                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Quantity Issued</label>
                                                                    <p className="mt-1 text-sm sm:text-base font-medium">
                                                                        {item.quantity_issued} {inventoryItem?.unit_of_measure}
                                                                    </p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                ) : (
                                    <div className="text-center py-6 sm:py-8">
                                        <Package className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-3 sm:mb-4" />
                                        <p className="text-sm sm:text-base text-muted-foreground">No items found</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="history" className="space-y-4 sm:space-y-6">
                        <StoreRequisitionHistory histories={histories} />
                    </TabsContent>
                </Tabs>
            </div>

            {/* Approval Dialog */}
            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={selectedRequisition}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => setIsDialogOpen(false)}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isApprovalSubmitting}
                />
            )}
        </AppLayout>
    );
}
