import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { PageProps } from '@inertiajs/core';
import { Link, usePage, router } from '@inertiajs/react';
import { Eye, FileText } from "lucide-react";
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadge } from '@/components/StoreRequisitionStatusBadge';
import { useEffect } from 'react';
import { MobileTable, MobileTableHeader, MobileTableBody, MobileTableRow, MobileTableHead, MobileTableCell } from '@/components/ui/mobile-table';

interface StoreRequisitionIndexPageProps extends PageProps {
    store_requisitions: {
        data: StoreRequisition[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
    };
    flash?: {
        success?: string;
        error?: string;
    };
}





// Helper functions - removed duplicate status badge logic, now using StoreRequisitionStatusBadge component

// Helper function to format date with appropriate label
const formatRequisitionDate = (requisition: StoreRequisition) => {
    const date = requisition.requested_at || requisition.created_at;
    const formattedDate = new Date(date).toLocaleDateString();

    // Show different labels based on status
    if (requisition.status === 'draft') {
        return `Created: ${formattedDate}`;
    } else {
        return `Requested: ${formattedDate}`;
    }
};

export default function StoreRequisitionIndex() {
    const {
        store_requisitions,
        user,
        flash
    } = usePage<StoreRequisitionIndexPageProps>().props;



    // Handle flash messages
    useEffect(() => {
        if (flash?.success) {
            window.showToast?.({
                title: 'Success',
                message: flash.success,
                type: 'success'
            });
        }
        if (flash?.error) {
            window.showToast?.({
                title: 'Error',
                message: flash.error,
                type: 'error'
            });
        }
    }, [flash]);

    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
    ];

    const canCreateStoreRequisition = user.permissions.includes('create-store-requisition') ||
                                     user.permissions.includes('store-keep');

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Store Requisitions" />

            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-3 sm:p-4 md:p-6">
                {/* Header Section */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div className="flex-1 min-w-0">
                        <h1 className="text-foreground/100 text-background/100 text-xl sm:text-2xl font-bold truncate">Store Requisitions</h1>
                    </div>
                    {canCreateStoreRequisition && (
                        <Button asChild className="w-full sm:w-auto">
                            <Link href="/store-requisitions/create">
                                <span className="sm:hidden">Create</span>
                                <span className="hidden sm:inline">Create Store Requisition</span>
                            </Link>
                        </Button>
                    )}
                </div>

                <Card>
                    <CardHeader className="px-4 sm:px-6">
                        <CardTitle className="text-lg sm:text-xl">Your Store Requisitions</CardTitle>
                        <CardDescription className="text-sm sm:text-base">
                            Store inventory requisition requests you have submitted
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="px-4 sm:px-6">
                        {store_requisitions.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center">
                                <FileText className="text-muted-foreground mb-4 h-10 w-10 sm:h-12 sm:w-12" />
                                <h3 className="text-base sm:text-lg font-medium">No store requisitions found</h3>
                                <p className="text-muted-foreground text-xs sm:text-sm mt-1">
                                    Create your first store requisition to see it here.
                                </p>
                                {canCreateStoreRequisition && (
                                    <Button asChild className="mt-4 w-full sm:w-auto">
                                        <Link href="/store-requisitions/create">
                                            <span className="sm:hidden">Create Requisition</span>
                                            <span className="hidden sm:inline">Create Store Requisition</span>
                                        </Link>
                                    </Button>
                                )}
                            </div>
                        ) : (
                            <>
                                <MobileTable>
                                    <MobileTableHeader>
                                        <MobileTableRow primaryContent={<></>}>
                                            <MobileTableHead>Requisition #</MobileTableHead>
                                            <MobileTableHead>Date</MobileTableHead>
                                            <MobileTableHead className="hidden sm:table-cell">Purpose</MobileTableHead>
                                            <MobileTableHead className="hidden md:table-cell">Department</MobileTableHead>
                                            <MobileTableHead className="hidden lg:table-cell text-right">Items</MobileTableHead>
                                            <MobileTableHead className="text-center">Status</MobileTableHead>
                                            <MobileTableHead className="text-right">Actions</MobileTableHead>
                                        </MobileTableRow>
                                    </MobileTableHeader>
                                    <MobileTableBody>
                                        {store_requisitions.data.map((requisition) => (
                                            <MobileTableRow
                                                key={requisition.id}
                                                primaryContent={
                                                    <div className="space-y-2">
                                                        <div className="flex items-center justify-between">
                                                            <span className="font-medium">SR-{requisition.id.toString().padStart(4, '0')}</span>
                                                            <StoreRequisitionStatusBadge status={requisition.status} />
                                                        </div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {requisition.purpose}
                                                        </div>
                                                        <div className="flex justify-between items-center text-sm">
                                                            <span className="text-muted-foreground">{formatRequisitionDate(requisition)}</span>
                                                            <Button variant="outline" size="sm" asChild>
                                                                <Link href={`/store-requisitions/${requisition.id}`}>
                                                                    <Eye className="h-4 w-4 mr-1" />
                                                                    View
                                                                </Link>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                }
                                                expandedContent={
                                                    <div className="space-y-2">
                                                        <div className="flex justify-between">
                                                            <span className="text-sm font-medium">Department:</span>
                                                            <span className="text-sm">{requisition.department?.name || '-'}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-sm font-medium">Items:</span>
                                                            <span className="text-sm">{requisition.items?.length || 0}</span>
                                                        </div>
                                                    </div>
                                                }
                                            >
                                                <MobileTableCell className="font-medium">
                                                    <div className="flex flex-col">
                                                        <span>SR-{requisition.id.toString().padStart(4, '0')}</span>
                                                        <span className="sm:hidden text-xs text-muted-foreground truncate max-w-[120px]">
                                                            {requisition.purpose}
                                                        </span>
                                                    </div>
                                                </MobileTableCell>
                                                <MobileTableCell>
                                                    <div className="flex flex-col">
                                                        <span className="text-sm">{formatRequisitionDate(requisition)}</span>
                                                        <span className="md:hidden text-xs text-muted-foreground">
                                                            {requisition.department?.name || '-'}
                                                        </span>
                                                    </div>
                                                </MobileTableCell>
                                                <MobileTableCell className="hidden sm:table-cell">
                                                    <div className="max-w-[200px] truncate" title={requisition.purpose}>
                                                        {requisition.purpose}
                                                    </div>
                                                </MobileTableCell>
                                                <MobileTableCell className="hidden md:table-cell">
                                                    {requisition.department?.name || '-'}
                                                </MobileTableCell>
                                                <MobileTableCell className="hidden lg:table-cell text-right">
                                                    {requisition.items?.length || 0}
                                                </MobileTableCell>
                                                <MobileTableCell className="text-center">
                                                    <StoreRequisitionStatusBadge status={requisition.status} />
                                                </MobileTableCell>
                                                <MobileTableCell className="text-right">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={`/store-requisitions/${requisition.id}`}>
                                                            <Eye className="h-4 w-4 sm:mr-1" />
                                                            <span className="hidden sm:inline">View</span>
                                                        </Link>
                                                    </Button>
                                                </MobileTableCell>
                                            </MobileTableRow>
                                        ))}
                                    </MobileTableBody>
                                </MobileTable>

                                {/* Pagination */}
                                {store_requisitions.last_page > 1 && (
                                    <div className="mt-4 flex justify-center">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm text-muted-foreground">
                                                Page {store_requisitions.current_page} of {store_requisitions.last_page}
                                            </span>
                                            {/* Pagination Controls */}
                                            {store_requisitions.last_page > 1 && (
                                                <div className="flex items-center justify-between px-4 py-3 border-t">
                                                    <div className="text-sm text-muted-foreground">
                                                        Showing {((store_requisitions.current_page - 1) * store_requisitions.per_page) + 1} to {Math.min(store_requisitions.current_page * store_requisitions.per_page, store_requisitions.total)} of {store_requisitions.total} requisitions
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => router.get(window.location.pathname, { page: store_requisitions.current_page - 1 })}
                                                            disabled={store_requisitions.current_page === 1}
                                                        >
                                                            Previous
                                                        </Button>
                                                        <span className="text-sm text-muted-foreground">
                                                            Page {store_requisitions.current_page} of {store_requisitions.last_page}
                                                        </span>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => router.get(window.location.pathname, { page: store_requisitions.current_page + 1 })}
                                                            disabled={store_requisitions.current_page === store_requisitions.last_page}
                                                        >
                                                            Next
                                                        </Button>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
