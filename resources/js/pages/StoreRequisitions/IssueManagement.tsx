import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Package,
    Clock,
    CheckCircle,
    AlertTriangle,
    Users,
    Calendar,
    ArrowLeft,
    Send
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadgeWithIcon } from '@/components/StoreRequisitionStatusBadge';
import { IssueDialog } from './IssueDialog';

interface IssueManagementPageProps {
    approved_requisitions: StoreRequisition[];
    partially_issued_requisitions: StoreRequisition[];
    issued_requisitions?: StoreRequisition[]; // Optional until backend provides it
    user: {
        id: number;
        name: string;
        permissions: string[];
    };
    flash?: {
        success?: string;
        error?: string;
    };
}

export default function IssueManagement({
    approved_requisitions,
    partially_issued_requisitions,
    issued_requisitions = [], // Default to empty array if not provided
}: IssueManagementPageProps) {
    const [selectedRequisitions, setSelectedRequisitions] = useState<number[]>([]);
    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [isIssueDialogOpen, setIsIssueDialogOpen] = useState(false);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Tab state for pagination reset
    const [activeTab, setActiveTab] = useState("ready-to-issue");

    // Reset pagination when tab changes
    useEffect(() => {
        setCurrentPage(1);
    }, [activeTab]);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Store Keeper', href: '/store-keeper/dashboard' },
        { title: 'Item Distribution', href: '/store-requisitions/issue' },
    ];

    const handleSelectRequisition = (requisitionId: number, checked: boolean) => {
        if (checked) {
            setSelectedRequisitions(prev => [...prev, requisitionId]);
        } else {
            setSelectedRequisitions(prev => prev.filter(id => id !== requisitionId));
        }
    };

    const handleSelectAll = (requisitions: StoreRequisition[], checked: boolean) => {
        if (checked) {
            const ids = requisitions.map(req => req.id);
            setSelectedRequisitions(prev => [...new Set([...prev, ...ids])]);
        } else {
            const ids = requisitions.map(req => req.id);
            setSelectedRequisitions(prev => prev.filter(id => !ids.includes(id)));
        }
    };

    const openIssueDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setIsIssueDialogOpen(true);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const renderRequisitionTable = (requisitions: StoreRequisition[], showSelection = false, showIssueActions = true) => {
        // Apply pagination
        const totalCount = requisitions.length;
        const totalPages = Math.ceil(totalCount / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedRequisitions = requisitions.slice(startIndex, endIndex);

        if (totalCount === 0) {
            return (
                <Card>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                        <Package className="h-12 w-12 text-muted-foreground mb-4" />
                        <p className="text-muted-foreground text-center">
                            No requisitions available for issuing
                        </p>
                    </CardContent>
                </Card>
            );
        }

        return (
            <Card>
                <CardContent className="p-0">
                    {/* Desktop Table View */}
                    <div className="hidden lg:block">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    {showSelection && (
                                        <TableHead className="w-12">
                                            <Checkbox
                                                checked={requisitions.every(req => selectedRequisitions.includes(req.id))}
                                                onCheckedChange={(checked) => handleSelectAll(requisitions, checked as boolean)}
                                            />
                                        </TableHead>
                                    )}
                                    <TableHead>Requisition</TableHead>
                                    <TableHead>Requester</TableHead>
                                    <TableHead>Department</TableHead>
                                    <TableHead>Items</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Date</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paginatedRequisitions.map((requisition) => (
                                    <TableRow key={requisition.id}>
                                        {showSelection && (
                                            <TableCell>
                                                <Checkbox
                                                    checked={selectedRequisitions.includes(requisition.id)}
                                                    onCheckedChange={(checked) =>
                                                        handleSelectRequisition(requisition.id, checked as boolean)
                                                    }
                                                />
                                            </TableCell>
                                        )}
                                        <TableCell>
                                            <div>
                                                <p className="font-medium">#{requisition.id}</p>
                                                <p className="text-sm text-muted-foreground truncate max-w-[200px]">
                                                    {requisition.purpose}
                                                </p>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Users className="h-4 w-4 text-muted-foreground" />
                                                <span>{requisition.requester?.first_name} {requisition.requester?.last_name}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>{requisition.department?.name}</TableCell>
                                        <TableCell>
                                            <Badge variant="outline">
                                                {requisition.items?.length || 0} items
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <StoreRequisitionStatusBadgeWithIcon status={requisition.status} />
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">
                                                    {formatDate(requisition.approved_at || requisition.created_at)}
                                                </span>
                                            </div>
                                        </TableCell>
                                        {showIssueActions ? (
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        size="sm"
                                                        onClick={() => openIssueDialog(requisition)}
                                                    >
                                                        <Send className="h-4 w-4 mr-1" />
                                                        Issue
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        asChild
                                                    >
                                                        <Link href={`/store-requisitions/${requisition.id}`}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        ) : (
                                            <TableCell>
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    asChild
                                                >
                                                    <Link href={`/store-requisitions/${requisition.id}`}>
                                                        View
                                                    </Link>
                                                </Button>
                                            </TableCell>
                                        )}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Mobile Card View */}
                    <div className="lg:hidden">
                        {showSelection && (
                            <div className="p-4 border-b bg-muted/30">
                                <div className="flex items-center gap-2">
                                    <Checkbox
                                        checked={requisitions.every(req => selectedRequisitions.includes(req.id))}
                                        onCheckedChange={(checked) => handleSelectAll(requisitions, checked as boolean)}
                                    />
                                    <span className="text-sm font-medium">Select All</span>
                                </div>
                            </div>
                        )}
                        <div className="divide-y">
                            {paginatedRequisitions.map((requisition) => (
                                <div key={requisition.id} className="p-4 space-y-3">
                                    {/* Header Row */}
                                    <div className="flex items-start justify-between gap-3">
                                        <div className="flex items-center gap-3 min-w-0 flex-1">
                                            {showSelection && (
                                                <Checkbox
                                                    checked={selectedRequisitions.includes(requisition.id)}
                                                    onCheckedChange={(checked) =>
                                                        handleSelectRequisition(requisition.id, checked as boolean)
                                                    }
                                                />
                                            )}
                                            <div className="min-w-0 flex-1">
                                                <div className="flex items-center gap-2 mb-1">
                                                    <p className="font-bold text-primary">#{requisition.id}</p>
                                                    <StoreRequisitionStatusBadgeWithIcon status={requisition.status} />
                                                </div>
                                                <p className="text-sm text-muted-foreground line-clamp-2">
                                                    {requisition.purpose}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Details Grid */}
                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                        <div>
                                            <div className="flex items-center gap-1 text-muted-foreground mb-1">
                                                <Users className="h-3 w-3" />
                                                <span className="text-xs">Requester</span>
                                            </div>
                                            <p className="font-medium truncate">
                                                {requisition.requester?.first_name} {requisition.requester?.last_name}
                                            </p>
                                        </div>
                                        <div>
                                            <div className="text-xs text-muted-foreground mb-1">Department</div>
                                            <p className="font-medium truncate">{requisition.department?.name}</p>
                                        </div>
                                        <div>
                                            <div className="flex items-center gap-1 text-muted-foreground mb-1">
                                                <Package className="h-3 w-3" />
                                                <span className="text-xs">Items</span>
                                            </div>
                                            <Badge variant="outline" className="text-xs">
                                                {requisition.items?.length || 0} items
                                            </Badge>
                                        </div>
                                        <div>
                                            <div className="flex items-center gap-1 text-muted-foreground mb-1">
                                                <Calendar className="h-3 w-3" />
                                                <span className="text-xs">Date</span>
                                            </div>
                                            <p className="text-xs font-medium">
                                                {formatDate(requisition.approved_at || requisition.created_at)}
                                            </p>
                                        </div>
                                    </div>

                                    {/* Actions */}
                                    <div className="flex gap-2 pt-2">
                                        {showIssueActions ? (
                                            <>
                                                <Button
                                                    size="sm"
                                                    onClick={() => openIssueDialog(requisition)}
                                                    className="flex-1"
                                                >
                                                    <Send className="h-4 w-4 mr-1" />
                                                    Issue
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    asChild
                                                    className="flex-1"
                                                >
                                                    <Link href={`/store-requisitions/${requisition.id}`}>
                                                        View
                                                    </Link>
                                                </Button>
                                            </>
                                        ) : (
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                asChild
                                                className="w-full"
                                            >
                                                <Link href={`/store-requisitions/${requisition.id}`}>
                                                    View Details
                                                </Link>
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Pagination Controls - Mobile Responsive */}
                    {totalPages > 1 && (
                        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between px-3 sm:px-4 py-3 border-t bg-muted/20">
                            <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                                Showing {startIndex + 1} to {Math.min(endIndex, totalCount)} of {totalCount} requisitions
                            </div>
                            <div className="flex items-center justify-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                    disabled={currentPage === 1}
                                    className="text-xs sm:text-sm"
                                >
                                    <span className="hidden sm:inline">Previous</span>
                                    <span className="sm:hidden">Prev</span>
                                </Button>
                                <span className="text-xs sm:text-sm text-muted-foreground px-2">
                                    {currentPage} / {totalPages}
                                </span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                    disabled={currentPage === totalPages}
                                    className="text-xs sm:text-sm"
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Item Distribution" />

            <div className="flex h-full flex-1 flex-col gap-4 sm:gap-6 p-3 sm:p-4 lg:p-6">
                {/* Header - Mobile Responsive */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
                        <Button variant="outline" size="sm" className="w-fit" asChild>
                            <Link href="/store-keeper/dashboard">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                <span className="hidden sm:inline">Back to Dashboard</span>
                                <span className="sm:hidden">Back</span>
                            </Link>
                        </Button>
                        <div className="min-w-0 flex-1">
                            <h1 className="text-xl sm:text-2xl font-bold text-foreground truncate">Item Distribution</h1>
                            <p className="text-sm sm:text-base text-muted-foreground">
                                Distribute approved store requisition items to requesters
                            </p>
                        </div>
                    </div>
                </div>

                {/* Statistics Cards - Mobile Responsive */}
                <div className="grid grid-cols-2 gap-3 sm:gap-4 lg:grid-cols-4">
                    <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                        <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                            <div className="flex flex-col flex-1 min-w-0">
                                <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                    {approved_requisitions.length}
                                </div>
                                <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                    Ready to Issue
                                </div>
                            </div>
                            <div className="bg-blue-500/10 rounded-full p-2 sm:p-3 flex-shrink-0 ml-2">
                                <Clock className="text-blue-500 h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                            </div>
                        </div>
                    </Card>

                    <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                        <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                            <div className="flex flex-col flex-1 min-w-0">
                                <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                    {partially_issued_requisitions.length}
                                </div>
                                <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                    Partially Issued
                                </div>
                            </div>
                            <div className="bg-orange-500/10 rounded-full p-2 sm:p-3 flex-shrink-0 ml-2">
                                <AlertTriangle className="text-orange-500 h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                            </div>
                        </div>
                    </Card>

                    <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                        <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                            <div className="flex flex-col flex-1 min-w-0">
                                <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                    {selectedRequisitions.length}
                                </div>
                                <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                    Selected
                                </div>
                            </div>
                            <div className="bg-primary/10 rounded-full p-2 sm:p-3 flex-shrink-0 ml-2">
                                <CheckCircle className="text-primary h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                            </div>
                        </div>
                    </Card>

                    {issued_requisitions && (
                        <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
                            <div className="flex items-center justify-between h-full min-h-[80px] sm:min-h-[100px]">
                                <div className="flex flex-col flex-1 min-w-0">
                                    <div className="text-foreground text-lg sm:text-xl lg:text-2xl font-bold">
                                        {issued_requisitions.length}
                                    </div>
                                    <div className="text-muted-foreground text-xs sm:text-sm font-medium truncate">
                                        Distributed
                                    </div>
                                </div>
                                <div className="bg-green-500/10 rounded-full p-2 sm:p-3 flex-shrink-0 ml-2">
                                    <Package className="text-green-500 h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                                </div>
                            </div>
                        </Card>
                    )}
                </div>

                {/* Tabbed Content - Mobile Responsive */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
                    <TabsList className={`grid w-full ${issued_requisitions && issued_requisitions.length >= 0 ? 'grid-cols-3' : 'grid-cols-2'}`}>
                        <TabsTrigger value="ready-to-issue" className="text-xs sm:text-sm">
                            <span className="hidden sm:inline">Ready to Issue ({approved_requisitions.length})</span>
                            <span className="sm:hidden">Ready ({approved_requisitions.length})</span>
                        </TabsTrigger>
                        <TabsTrigger value="partially-issued" className="text-xs sm:text-sm">
                            <span className="hidden sm:inline">Partially Issued ({partially_issued_requisitions.length})</span>
                            <span className="sm:hidden">Partial ({partially_issued_requisitions.length})</span>
                        </TabsTrigger>
                        {issued_requisitions && (
                            <TabsTrigger value="issued" className="text-xs sm:text-sm">
                                <span className="hidden sm:inline">Distributed ({issued_requisitions.length})</span>
                                <span className="sm:hidden">Done ({issued_requisitions.length})</span>
                            </TabsTrigger>
                        )}
                    </TabsList>

                    <TabsContent value="ready-to-issue" className="space-y-4 sm:space-y-6">
                        <div>
                            <h3 className="text-foreground text-base sm:text-lg font-semibold mb-2">Ready to Distribute</h3>
                            <p className="text-xs sm:text-sm text-muted-foreground mb-4">
                                Approved requisitions ready for item distribution
                            </p>
                        </div>
                        {renderRequisitionTable(approved_requisitions, true)}
                    </TabsContent>

                    <TabsContent value="partially-issued" className="space-y-4 sm:space-y-6">
                        <div>
                            <h3 className="text-foreground text-base sm:text-lg font-semibold mb-2">Partially Issued</h3>
                            <p className="text-xs sm:text-sm text-muted-foreground mb-4">
                                Requisitions with remaining items to issue
                            </p>
                        </div>
                        {renderRequisitionTable(partially_issued_requisitions)}
                    </TabsContent>

                    {issued_requisitions && (
                        <TabsContent value="issued" className="space-y-4 sm:space-y-6">
                            <div>
                                <h3 className="text-foreground text-base sm:text-lg font-semibold mb-2">Distributed Items</h3>
                                <p className="text-xs sm:text-sm text-muted-foreground mb-4">
                                    Completed requisitions with all items distributed
                                </p>
                            </div>
                            {renderRequisitionTable(issued_requisitions, false, false)}
                        </TabsContent>
                    )}
                </Tabs>

                {/* Issue Dialog */}
                {selectedRequisition && (
                    <IssueDialog
                        requisition={selectedRequisition}
                        isOpen={isIssueDialogOpen}
                        onClose={() => {
                            setIsIssueDialogOpen(false);
                            setSelectedRequisition(null);
                        }}
                    />
                )}
            </div>
        </AppLayout>
    );
}
