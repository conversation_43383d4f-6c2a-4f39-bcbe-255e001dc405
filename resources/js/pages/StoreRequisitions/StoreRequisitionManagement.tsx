import { useState, useMemo, useEffect, useCallback } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    Plus,
    Search,
    Filter,
    Package,
    CheckCircle,
    XCircle,
    Eye
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadgeWithIcon } from '@/components/StoreRequisitionStatusBadge';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';

interface StoreRequisitionManagementPageProps {
    my_requisitions: StoreRequisition[];
    pending_approvals: StoreRequisition[];
    all_requisitions: StoreRequisition[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
        roles: string[];
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Store Requisition Management', href: '/store-requisitions' },
];

export default function StoreRequisitionManagement() {
    const { my_requisitions, pending_approvals, all_requisitions, user, flash } = usePage<StoreRequisitionManagementPageProps>().props;

    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    // Filters
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');
    const [requesterFilter, setRequesterFilter] = useState('all');

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Handle flash messages
    useEffect(() => {
        if (flash?.success) {
            window.showToast?.({
                title: 'Success',
                message: flash.success,
                type: 'success'
            });
        }
        if (flash?.error) {
            window.showToast?.({
                title: 'Error',
                message: flash.error,
                type: 'error'
            });
        }
    }, [flash]);

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, statusFilter, departmentFilter, requesterFilter]);

    const { approveRequisition, rejectRequisition, isSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
        }
    });

    // Separate approval logic for different user types
    const isStoreKeeper = user.permissions.includes('store-keep');
    const isOverseer = user.roles?.includes('Finance Manager') || user.roles?.includes('Organization Admin');
    const canViewAll = isStoreKeeper || isOverseer;
    const canApprove = isStoreKeeper || isOverseer;

    // Tab state for pagination reset
    const [activeTab, setActiveTab] = useState(canViewAll ? "all-requisitions" : "my-requisitions");

    // Reset pagination when tab changes
    useEffect(() => {
        setCurrentPage(1);
    }, [activeTab]);

    // Function to check if user can approve specific requisition (granular logic)
    const canApproveRequisition = useCallback((requisition: StoreRequisition) => {
        if (requisition.status !== 'pending_approval' || requisition.requester_user_id === user.id) {
            return false;
        }

        // Safely check if requester is a store keeper
        const requesterPermissions = requisition.requester?.permissions;
        const requesterIsStoreKeeper = Array.isArray(requesterPermissions)
            ? requesterPermissions.includes('store-keep')
            : false;

        if (requesterIsStoreKeeper) {
            return isOverseer; // Only overseers can approve store keeper requisitions
        } else {
            return isStoreKeeper; // Only store keepers can approve employee/HOD requisitions
        }
    }, [user.id, isOverseer, isStoreKeeper]);

    // Separate pending approvals into approvable and viewable
    const { approvableRequisitions, viewableRequisitions } = useMemo(() => {
        const approvable = pending_approvals.filter(req => canApproveRequisition(req));
        const viewable = pending_approvals.filter(req => !canApproveRequisition(req));
        return { approvableRequisitions: approvable, viewableRequisitions: viewable };
    }, [pending_approvals, canApproveRequisition]);

    // Get unique departments and requesters for filters
    const departments = useMemo(() => {
        const allReqs = [...my_requisitions, ...pending_approvals, ...all_requisitions];
        return allReqs
            .map(req => req.department)
            .filter(Boolean)
            .reduce((unique: typeof allReqs[0]['department'][], dept) => {
                if (!unique.find(d => d!.id === dept!.id)) {
                    unique.push(dept);
                }
                return unique;
            }, []);
    }, [my_requisitions, pending_approvals, all_requisitions]);

    const requesters = useMemo(() => {
        const allReqs = [...my_requisitions, ...pending_approvals, ...all_requisitions];
        return allReqs
            .map(req => req.requester)
            .filter(Boolean)
            .reduce((unique: typeof allReqs[0]['requester'][], requester) => {
                if (!unique.find(r => r!.id === requester!.id)) {
                    unique.push(requester);
                }
                return unique;
            }, []);
    }, [my_requisitions, pending_approvals, all_requisitions]);

    const filterRequisitions = (requisitions: StoreRequisition[]) => {
        const filtered = requisitions.filter(req => {
            const matchesSearch = !searchTerm ||
                req.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
                req.id.toString().includes(searchTerm) ||
                `${req.requester?.first_name} ${req.requester?.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());

            // Clean status filter logic - only actual statuses
            const matchesStatus = statusFilter === 'all' || req.status === statusFilter;

            const matchesDepartment = departmentFilter === 'all' || req.department?.id.toString() === departmentFilter;
            const matchesRequester = requesterFilter === 'all' || req.requester?.id.toString() === requesterFilter;

            return matchesSearch && matchesStatus && matchesDepartment && matchesRequester;
        });

        // Apply pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filtered.slice(startIndex, endIndex);
    };

    // Get total count for pagination
    const getTotalCount = (requisitions: StoreRequisition[]) => {
        return requisitions.filter(req => {
            const matchesSearch = !searchTerm ||
                req.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
                req.id.toString().includes(searchTerm) ||
                `${req.requester?.first_name} ${req.requester?.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());

            // Clean status filter logic - only actual statuses
            const matchesStatus = statusFilter === 'all' || req.status === statusFilter;

            const matchesDepartment = departmentFilter === 'all' || req.department?.id.toString() === departmentFilter;
            const matchesRequester = requesterFilter === 'all' || req.requester?.id.toString() === requesterFilter;

            return matchesSearch && matchesStatus && matchesDepartment && matchesRequester;
        }).length;
    };

    const openApprovalDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition || !dialogAction) return;

        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject') {
            rejectRequisition(selectedRequisition.id, comments || '');
        }
    };

    const renderRequisitionTable = (requisitions: StoreRequisition[], showApprovalActions = false, allowApproval = true) => {
        const filteredReqs = filterRequisitions(requisitions);
        const totalCount = getTotalCount(requisitions);
        const totalPages = Math.ceil(totalCount / itemsPerPage);

        if (totalCount === 0) {
            return (
                <Card>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                        <Package className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                            No Requisitions Found
                        </h3>
                        <p className="text-muted-foreground text-center">
                            {searchTerm || statusFilter !== 'all' || departmentFilter !== 'all' || requesterFilter !== 'all'
                                ? 'No requisitions match your current filters.'
                                : 'No requisitions available.'
                            }
                        </p>
                    </CardContent>
                </Card>
            );
        }

        return (
            <Card>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Requester</TableHead>
                            <TableHead>Department</TableHead>
                            <TableHead>Purpose</TableHead>
                            <TableHead>Items</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredReqs.map((requisition) => (
                            <TableRow key={requisition.id} className="hover:bg-muted/50">
                                <TableCell className="font-medium">
                                    #{requisition.id}
                                </TableCell>
                                <TableCell>
                                    <div className="flex flex-col">
                                        <span className="font-medium">
                                            {requisition.requester?.first_name} {requisition.requester?.last_name}
                                        </span>
                                        <span className="text-sm text-muted-foreground">
                                            {requisition.requester?.email}
                                        </span>
                                    </div>
                                </TableCell>
                                <TableCell>
                                    {requisition.department?.name || 'N/A'}
                                </TableCell>
                                <TableCell className="max-w-xs">
                                    <div className="truncate" title={requisition.purpose}>
                                        {requisition.purpose}
                                    </div>
                                </TableCell>
                                <TableCell className="text-center">
                                    {requisition.items?.length || 0}
                                </TableCell>
                                <TableCell>
                                    {requisition.requested_at 
                                        ? new Date(requisition.requested_at).toLocaleDateString()
                                        : new Date(requisition.created_at).toLocaleDateString()
                                    }
                                </TableCell>
                                <TableCell>
                                    <StoreRequisitionStatusBadgeWithIcon
                                        status={requisition.status}
                                        showIcon={false}
                                    />
                                </TableCell>
                                <TableCell className="text-right">
                                    <div className="flex gap-2 justify-end">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/store-requisitions/${requisition.id}`}>
                                                <Eye className="h-4 w-4" />
                                            </Link>
                                        </Button>
                                        {showApprovalActions && allowApproval && canApproveRequisition(requisition) && (
                                            <>
                                                <Button
                                                    onClick={() => openApprovalDialog(requisition)}
                                                    size="sm"
                                                    disabled={isSubmitting}
                                                >
                                                    <CheckCircle className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    onClick={() => openRejectionDialog(requisition)}
                                                    size="sm"
                                                    disabled={isSubmitting}
                                                >
                                                    <XCircle className="h-4 w-4" />
                                                </Button>
                                            </>
                                        )}
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-between px-4 py-3 border-t">
                        <div className="text-sm text-muted-foreground">
                            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} requisitions
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                disabled={currentPage === 1}
                            >
                                Previous
                            </Button>
                            <span className="text-sm text-muted-foreground">
                                Page {currentPage} of {totalPages}
                            </span>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                disabled={currentPage === totalPages}
                            >
                                Next
                            </Button>
                        </div>
                    </div>
                )}
            </Card>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Store Requisition Management" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-foreground">Store Requisition Management</h1>
                        <p className="text-muted-foreground">
                            Manage store inventory requisitions and approvals
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/store-requisitions/create">
                            <Plus className="mr-2 h-4 w-4" />
                            New Requisition
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                <Input
                                    placeholder="Search requisitions..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-9"
                                />
                            </div>
                            
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="draft">Draft</SelectItem>
                                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                                    <SelectItem value="approved">Approved</SelectItem>
                                    <SelectItem value="rejected">Rejected</SelectItem>
                                    <SelectItem value="issued">Issued</SelectItem>
                                    <SelectItem value="partially_issued">Partially Issued</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Departments" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Departments</SelectItem>
                                    {departments.map((department) => (
                                        <SelectItem key={department!.id} value={department!.id.toString()}>
                                            {department!.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={requesterFilter} onValueChange={setRequesterFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Requesters" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Requesters</SelectItem>
                                    {requesters.map((requester) => (
                                        <SelectItem key={requester!.id} value={requester!.id.toString()}>
                                            {requester!.first_name} {requester!.last_name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Button
                                variant="outline"
                                onClick={() => {
                                    setSearchTerm('');
                                    setStatusFilter('all');
                                    setDepartmentFilter('all');
                                    setRequesterFilter('all');
                                    setCurrentPage(1);
                                }}
                            >
                                Clear Filters
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Tabbed Content */}
             
                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
                    <TabsList className={`grid w-full mb-6 ${
                        isOverseer
                            ? 'grid-cols-4' // My Requisitions, All Requisitions, Approvable, View Only
                            : canViewAll && isStoreKeeper
                                ? 'grid-cols-3' // All, My, Pending Approvals
                                : (canViewAll || canApprove)
                                    ? 'grid-cols-2'
                                    : 'grid-cols-1'
                    }`}>
                        {canViewAll && (
                            <TabsTrigger value="all-requisitions">
                                All Requisitions ({all_requisitions.length})
                            </TabsTrigger>
                        )}
                        <TabsTrigger value="my-requisitions">
                            My Requisitions ({my_requisitions.length})
                        </TabsTrigger>
                        {isStoreKeeper && (
                            <TabsTrigger value="pending-approvals">
                                Can Approve ({pending_approvals.length})
                            </TabsTrigger>
                        )}
                        {isOverseer && (
                            <>
                                <TabsTrigger value="approvable-requisitions">
                                    Can Approve ({approvableRequisitions.length})
                                </TabsTrigger>
                                <TabsTrigger value="viewable-requisitions">
                                    View Only ({viewableRequisitions.length})
                                </TabsTrigger>
                            </>
                        )}

                    </TabsList>

                    {canViewAll && (
                        <TabsContent value="all-requisitions" className="space-y-4 mt-6">
                            <div>
                                <h3 className="text-foreground text-lg font-semibold mb-2">All Requisitions</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Complete overview of all requisitions in your organization
                                </p>
                            </div>
                            {renderRequisitionTable(all_requisitions)}
                        </TabsContent>
                    )}

                    <TabsContent value="my-requisitions" className="space-y-4 mt-6">
                        <div>
                            <h3 className="text-foreground text-lg font-semibold mb-2">My Requisitions</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                                Store requisitions you have created
                            </p>
                        </div>
                        {renderRequisitionTable(my_requisitions)}
                    </TabsContent>

                    {isStoreKeeper && (
                        <TabsContent value="pending-approvals" className="space-y-4 mt-6">
                            <div>
                                <h3 className="text-foreground text-lg font-semibold mb-2">Can Approve</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Employee/HOD requisitions that you can approve
                                </p>
                            </div>
                            {renderRequisitionTable(pending_approvals, true)}
                        </TabsContent>
                    )}

                    {/* Add new tab contents for overseers */}
                    {isOverseer && (
                        <>
                            <TabsContent value="approvable-requisitions" className="space-y-4 mt-6">
                                <div>
                                    <h3 className="text-foreground text-lg font-semibold mb-2">Store Keeper Requisitions - Can Approve</h3>
                                    <p className="text-sm text-muted-foreground mb-4">
                                        Store Keeper requisitions that you can approve as an overseer
                                    </p>
                                </div>
                                {renderRequisitionTable(approvableRequisitions, true, true)}
                            </TabsContent>

                            <TabsContent value="viewable-requisitions" className="space-y-4 mt-6">
                                <div>
                                    <h3 className="text-foreground text-lg font-semibold mb-2">Employee/HOD Requisitions - View Only</h3>
                                    <p className="text-sm text-muted-foreground mb-4">
                                        Employee and HOD requisitions that you can view but cannot approve
                                    </p>
                                </div>
                                {renderRequisitionTable(viewableRequisitions, false, false)}
                            </TabsContent>
                        </>
                    )}


                </Tabs>

                {/* Approval Dialog */}
            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={selectedRequisition}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => {
                        setIsDialogOpen(false);
                        setSelectedRequisition(null);
                        setDialogAction(null);
                    }}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isSubmitting}
                />
            )}
            </div>
        </AppLayout>
    );
}
