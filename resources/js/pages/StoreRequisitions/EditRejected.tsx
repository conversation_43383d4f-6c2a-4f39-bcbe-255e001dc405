import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { InfoIcon, ArrowLeft, LoaderCircle } from 'lucide-react';
import React from 'react';
import { StoreRequisition, InventoryItem } from '@/types/store-requisitions';
import { User } from '@/types';
import ItemsTable from '@/components/StoreRequisitions/ItemsTable';

interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface Department {
    id: number;
    name: string;
    branch_id: number;
    organization_id: number;
}

interface EditRejectedProps {
    storeRequisition: StoreRequisition & {
        items: Array<{
            id: number;
            inventory_item_id: number;
            quantity_requested: number;
            inventory_item: InventoryItem;
        }>;
        requester: User;
        department: Department;
        branch: Branch;
    };
    rejectionComments: string | null;
    inventoryItems: InventoryItem[];
    userDepartments: Department[];
    userBranches: Branch[];
}

export default function EditRejected({
    storeRequisition,
    rejectionComments,
    inventoryItems,
    userDepartments,
    userBranches
}: EditRejectedProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: 'Edit Rejected Requisition',
            href: `/store-requisitions/${storeRequisition.id}/edit-rejected`,
        },
    ];

    const { data, setData, processing, errors, setError, put } = useForm({
        purpose: storeRequisition.purpose,
        department_id: storeRequisition.department_id,
        branch_id: storeRequisition.branch_id,
        comments: '',
        items: storeRequisition.items.map(item => ({
            inventory_item_id: item.inventory_item_id,
            quantity_requested: item.quantity_requested,
        })),
    });

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!data.purpose.trim()) {
            setError('purpose', 'Purpose is required');
            return;
        }

        if (!data.comments.trim()) {
            setError('comments', 'Comments are required when resubmitting');
            return;
        }

        if (!data.items || data.items.length === 0) {
            setError('items', 'At least one item is required');
            return;
        }

        put(`/store-requisitions/${storeRequisition.id}/update-rejected`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Rejected Store Requisition #${storeRequisition.id}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">
                            Edit Rejected Store Requisition #{storeRequisition.id}
                        </h1>
                        <p className="text-muted-foreground">
                            Make necessary changes and resubmit your requisition
                        </p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href="/store-requisitions">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Store Requisitions
                        </Link>
                    </Button>
                </div>

                {rejectionComments && (
                    <Alert>
                        <InfoIcon className="h-4 w-4" />
                        <AlertTitle>Rejection Comments</AlertTitle>
                        <AlertDescription className="mt-2">
                            {rejectionComments}
                        </AlertDescription>
                    </Alert>
                )}

                {/* Edit Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Requisition Details</CardTitle>
                        <CardDescription>
                            Update the requisition details and items as needed
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={onSubmit} className="space-y-6">
                            {/* Purpose Field */}
                            <div className="space-y-2">
                                <Label htmlFor="purpose" className="text-sm font-medium">
                                    Purpose <span className="text-destructive">*</span>
                                </Label>
                                <Textarea
                                    id="purpose"
                                    placeholder="Enter the purpose of this requisition..."
                                    value={data.purpose}
                                    onChange={(e) => setData('purpose', e.target.value)}
                                    className="min-h-[80px]"
                                />
                                {errors.purpose && (
                                    <p className="text-sm text-destructive">{errors.purpose}</p>
                                )}
                            </div>

                            {/* Branch Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="branch" className="text-sm font-medium">
                                    Branch <span className="text-destructive">*</span>
                                </Label>
                                <Select
                                    value={data.branch_id?.toString()}
                                    onValueChange={(value) => setData('branch_id', parseInt(value))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a branch" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {userBranches.map((branch) => (
                                            <SelectItem key={branch.id} value={branch.id.toString()}>
                                                {branch.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.branch_id && (
                                    <p className="text-sm text-destructive">{errors.branch_id}</p>
                                )}
                            </div>

                            {/* Department Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="department" className="text-sm font-medium">
                                    Department <span className="text-destructive">*</span>
                                </Label>
                                <Select
                                    value={data.department_id?.toString()}
                                    onValueChange={(value) => setData('department_id', parseInt(value))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a department" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {userDepartments
                                            .filter(dept => !data.branch_id || dept.branch_id === data.branch_id)
                                            .map((department) => (
                                                <SelectItem key={department.id} value={department.id.toString()}>
                                                    {department.name}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>
                                {errors.department_id && (
                                    <p className="text-sm text-destructive">{errors.department_id}</p>
                                )}
                            </div>

                            <ItemsTable
                                items={data.items || []}
                                inventoryItems={inventoryItems}
                                onItemsChange={(items) => setData('items', items)}
                                error={errors.items}
                            />

                            {/* Comments for resubmission */}
                            <div className="space-y-2">
                                <Label htmlFor="comments" className="text-sm font-medium">
                                    Comments <span className="text-destructive">*</span>
                                </Label>
                                <Textarea
                                    id="comments"
                                    placeholder="Add comments about the changes made for resubmission..."
                                    value={data.comments}
                                    onChange={(e) => setData('comments', e.target.value)}
                                    className="min-h-[100px]"
                                />
                                {errors.comments && (
                                    <p className="text-sm text-destructive">{errors.comments}</p>
                                )}
                            </div>

                            {/* Submit Button */}
                            <div className="flex justify-end space-x-4">
                                <Button type="button" variant="outline" asChild>
                                    <Link href="/store-requisitions">Cancel</Link>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                    Update & Resubmit
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
