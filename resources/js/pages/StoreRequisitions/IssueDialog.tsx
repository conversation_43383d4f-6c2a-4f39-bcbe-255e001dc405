import { useState, useEffect, useCallback } from 'react';
import { router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    Send,
    AlertTriangle,
    Info
} from 'lucide-react';
import {
    StoreRequisition,
    StoreRequisitionIssueItemData,
    StockValidationResponse
} from '@/types/store-requisitions';
import {
    validateStockAvailability,
    getMaxIssuableQuantity
} from '@/utils/stock-validation';

interface IssueDialogProps {
    requisition: StoreRequisition;
    isOpen: boolean;
    onClose: () => void;
}



export function IssueDialog({ requisition, isOpen, onClose }: IssueDialogProps) {
    const [issueItems, setIssueItems] = useState<StoreRequisitionIssueItemData[]>([]);
    const [issueNotes, setIssueNotes] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [stockValidation, setStockValidation] = useState<StockValidationResponse | null>(null);

    const validateStock = useCallback(async (items: StoreRequisitionIssueItemData[]) => {
        const validation = await validateStockAvailability(requisition.id, items);
        setStockValidation(validation);
    }, [requisition.id]);

    useEffect(() => {
        if (isOpen && requisition.items) {
            // Initialize with remaining quantities for partial issues, or full quantities for new issues
            const initialItems = requisition.items.map(item => {
                const currentlyIssued = item.quantity_issued || 0;
                const remainingToIssue = item.quantity_requested - currentlyIssued;
                const availableStock = (item as { inventory_item?: { quantity_on_hand?: number } }).inventory_item?.quantity_on_hand || 0;

                // For new issues, use the minimum of requested quantity and available stock
                // For partial issues, use the minimum of remaining quantity and available stock
                const maxCanIssue = Math.min(remainingToIssue, availableStock);
                const initialQuantity = Math.max(0, maxCanIssue); // Ensure non-negative

                return {
                    id: item.id!,
                    quantity_issued: initialQuantity
                };
            });
            setIssueItems(initialItems);
            validateStock(initialItems);
        }
    }, [isOpen, requisition, validateStock]);

    const updateItemQuantity = (itemId: number, quantity: number) => {
        const requisitionItem = requisition.items?.find(item => item.id === itemId);
        if (!requisitionItem) return;

        const maxQuantity = getMaxIssuableQuantity(requisitionItem);
        const validQuantity = Math.max(0, Math.min(quantity, maxQuantity));

        const updatedItems = issueItems.map(item =>
            item.id === itemId ? { ...item, quantity_issued: validQuantity } : item
        );
        setIssueItems(updatedItems);
        validateStock(updatedItems);
    };

    const handleSubmit = () => {
        if (!stockValidation?.valid) return;

        setIsSubmitting(true);

        router.post(`/store-requisitions/${requisition.id}/issue`, {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            items: issueItems as any,
            issue_notes: issueNotes.trim() || undefined,
        }, {
            onSuccess: () => {
                onClose();
                setIssueNotes('');
            },
            onError: () => {
                // Error handling managed by Inertia
            },
            onFinish: () => {
                setIsSubmitting(false);
            }
        });
    };

    const handleClose = () => {
        if (!isSubmitting) {
            setIssueNotes('');
            setStockValidation(null);
            onClose();
        }
    };

    const getTotalItems = () => issueItems.filter(item => item.quantity_issued > 0).length;
    const hasErrors = stockValidation?.errors && stockValidation.errors.length > 0;
    const hasWarnings = stockValidation?.warnings && stockValidation.warnings.length > 0;

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
                <DialogHeader className="flex-shrink-0">
                    <DialogTitle className="flex items-center gap-2">
                        <Send className="h-5 w-5" />
                        Issue Requisition #{requisition.id}
                    </DialogTitle>
                </DialogHeader>

                <div className="flex-1 overflow-y-auto space-y-6">
                    {/* Requisition Info */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                        <div>
                            <p className="text-sm text-muted-foreground">Requester</p>
                            <p className="font-medium text-foreground/90">
                                {requisition.requester?.first_name} {requisition.requester?.last_name}
                            </p>
                        </div>
                        <div>
                            <p className="text-sm text-muted-foreground">Department</p>
                            <p className="font-medium text-foreground/90">{requisition.department?.name}</p>
                        </div>
                        <div>
                            <p className="text-sm text-muted-foreground">Status</p>
                            <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-[var(--status-approved)] rounded-full"></div>
                                <span className="font-medium text-[var(--status-approved-foreground)]">Approved</span>
                            </div>
                        </div>
                        <div className="col-span-1 md:col-span-3">
                            <p className="text-sm text-muted-foreground">Purpose</p>
                            <p className="font-medium text-foreground/90">{requisition.purpose}</p>
                        </div>
                    </div>

                    {/* Stock Validation Alerts */}
                    {hasErrors && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                <strong>Stock Issues Found:</strong>
                                <ul className="mt-2 list-disc list-inside">
                                    {stockValidation.errors.map((error, index) => (
                                        <li key={index}>{error.message}</li>
                                    ))}
                                </ul>
                            </AlertDescription>
                        </Alert>
                    )}

                    {hasWarnings && (
                        <Alert>
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                                <strong>Stock Warnings:</strong>
                                <ul className="mt-2 list-disc list-inside">
                                    {stockValidation.warnings.map((warning, index) => (
                                        <li key={index}>{warning.message}</li>
                                    ))}
                                </ul>
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Items Table */}
                    <div>
                        <h3 className="text-foreground text-lg font-semibold mb-4">Items to Issue</h3>
                        <div className="overflow-x-auto">
                            <Table className="min-w-full">
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="min-w-[200px]">Item</TableHead>
                                        <TableHead className="min-w-[100px] text-center">Requested</TableHead>
                                        <TableHead className="min-w-[100px] text-center">Previously Issued</TableHead>
                                        <TableHead className="min-w-[120px] text-center">Issue Now</TableHead>
                                        <TableHead className="min-w-[120px] text-center">Available Stock</TableHead>
                                        <TableHead className="min-w-[100px] text-center">Status</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {requisition.items?.map((item) => {
                                        const issueItem = issueItems.find(i => i.id === item.id);
                                        const issueQuantity = issueItem?.quantity_issued || 0;
                                        const inventoryItem = (item as { inventory_item?: {
                                            quantity_on_hand?: number;
                                            name?: string;
                                            sku?: string;
                                            unit_of_measure?: string;
                                        } }).inventory_item;
                                        const availableStock = inventoryItem?.quantity_on_hand || 0;
                                        const previouslyIssued = item.quantity_issued || 0;
                                        const maxCanIssue = getMaxIssuableQuantity(item);

                                        return (
                                            <TableRow key={item.id}>
                                                <TableCell className="min-w-[200px]">
                                                    <div>
                                                        <p className="font-medium text-sm text-foreground/90">{inventoryItem?.name}</p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {inventoryItem?.sku}
                                                        </p>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="text-sm">
                                                        <div className="text-foreground/90">{item.quantity_requested}</div>
                                                        <div className="text-xs text-muted-foreground">{inventoryItem?.unit_of_measure}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="text-sm">
                                                        <div className="text-foreground/90">{previouslyIssued}</div>
                                                        <div className="text-xs text-muted-foreground">{inventoryItem?.unit_of_measure}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="flex flex-col items-center gap-1">
                                                        <Input
                                                            type="number"
                                                            min="0"
                                                            max={maxCanIssue}
                                                            value={issueQuantity}
                                                            onChange={(e) => updateItemQuantity(
                                                                item.id!,
                                                                parseFloat(e.target.value) || 0
                                                            )}
                                                            className="w-20 h-8 text-center text-sm"
                                                        />
                                                        <span className="text-xs text-muted-foreground">{inventoryItem?.unit_of_measure}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="text-sm">
                                                        <div className={availableStock < issueQuantity ? 'text-destructive font-medium' : 'text-foreground/90'}>
                                                            {availableStock}
                                                        </div>
                                                        <div className="text-xs text-muted-foreground">{inventoryItem?.unit_of_measure}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <Badge variant="default" className="text-xs bg-[var(--status-approved)] text-[var(--status-approved-foreground)]">
                                                        Approved
                                                    </Badge>
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })}
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    {/* Issue Notes */}
                    <div className="space-y-2">
                        <Label htmlFor="issue_notes">Issue Notes (Optional)</Label>
                        <Textarea
                            id="issue_notes"
                            placeholder="Add any notes about this issue..."
                            value={issueNotes}
                            onChange={(e) => setIssueNotes(e.target.value)}
                            rows={3}
                            className='text-foreground'
                        />
                    </div>

                    {/* <div className="p-4 bg-muted/50 rounded-lg border">
                        <div className="space-y-4">
                            <div className="flex items-center gap-2 pb-2 border-b border-border/50">
                                <Package className="h-4 w-4 text-primary" />
                                <span className="font-semibold text-foreground">Distribution Summary</span>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Items to distribute:</span>
                                        <span className="font-bold text-lg text-primary">{issueSummary.itemsToIssue}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Total items:</span>
                                        <span className="font-medium text-foreground">{issueSummary.totalItems}</span>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Complete quantities:</span>
                                        <span className="font-medium text-green-600">{issueSummary.fullyIssuedItems}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">Partial quantities:</span>
                                        <span className="font-medium text-orange-600">{issueSummary.partiallyIssuedItems}</span>
                                    </div>
                                    {issueSummary.notIssuedItems > 0 && (
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-muted-foreground">Not distributing:</span>
                                            <span className="font-medium text-muted-foreground">{issueSummary.notIssuedItems}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="pt-2 border-t border-border/50">
                                <p className="text-xs text-muted-foreground leading-relaxed">
                                    <strong>Distribution Guide:</strong> Complete quantities = issuing full requested amount,
                                    Partial quantities = issuing less than requested (due to stock limits),
                                    Not distributing = no items being issued now (can be issued later).
                                </p>
                            </div>
                        </div>
                    </div> */}
                </div>

                <DialogFooter className="flex-shrink-0 flex gap-2 pt-4 border-t">
                    <Button
                        variant="outline"
                        onClick={handleClose}
                        disabled={isSubmitting}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        disabled={isSubmitting || hasErrors || getTotalItems() === 0}
                    >
                        {isSubmitting ? (
                            <>
                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                                Issuing...
                            </>
                        ) : (
                            <>
                                <Send className="mr-2 h-4 w-4" />
                                Issue Items ({getTotalItems()})
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
