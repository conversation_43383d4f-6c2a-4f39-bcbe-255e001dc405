
import { Head, <PERSON> } from '@inertiajs/react';
import { Arrow<PERSON>ef<PERSON>, LoaderCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useStoreRequisitionForm } from '@/hooks/use-store-requisition-form';
import { StoreRequisitionForm } from '@/components/StoreRequisitionForm';
import { StoreRequisition, InventoryItem } from '@/types/store-requisitions';
import { User } from '@/types';
import AppLayout from '@/layouts/app-layout';

// Local type definitions
interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface Department {
    id: number;
    name: string;
    branch_id: number;
    organization_id: number;
}

interface EditStoreRequisitionProps {
    store_requisition: StoreRequisition & {
        items: Array<{
            id: number;
            inventory_item_id: number;
            quantity_requested: number;
            inventory_item: InventoryItem;
        }>;
        requester: User;
        department: Department;
        branch: Branch;
    };
    inventory_items: InventoryItem[];
    branches: Branch[];
    departments: Department[];
    user_branch_id: number;
    user_department_id: number;
    user_departments: Department[];
}

function EditStoreRequisition({
    store_requisition,
    inventory_items,
    branches,
    departments,
    user_branch_id,
    user_department_id,
    user_departments,
}: EditStoreRequisitionProps) {

    const {
        data,
        setData,
        errors,
        processing,
        submit,
        submitAsDraft,
        clearErrors,
    } = useStoreRequisitionForm({
        initialData: {
            branch_id: store_requisition.branch_id,
            department_id: store_requisition.department_id,
            purpose: store_requisition.purpose,
            items: store_requisition.items?.length > 0
                ? store_requisition.items.map(item => ({
                    inventory_item_id: item.inventory_item_id,
                    quantity_requested: item.quantity_requested,
                }))
                : [{ inventory_item_id: null, quantity_requested: 1 }],
        },
    });



    const handleSubmit = () => {
        submit(`/store-requisitions/${store_requisition.id}`);
    };

    const handleSaveAsDraft = () => {
        submitAsDraft(`/store-requisitions/${store_requisition.id}`);
    };

    const canSaveAsDraft = store_requisition.status === 'draft';

    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Store Requisitions',
            href: '/store-requisitions',
        },
        {
            title: `Requisition #${store_requisition.id}`,
            href: `/store-requisitions/${store_requisition.id}`,
        },
        {
            title: 'Edit',
            href: `/store-requisitions/${store_requisition.id}/edit`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Store Requisition" />

            <div className="bg-background/90 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                        <Button variant="outline" size="sm" asChild>
                            <Link href={`/store-requisitions/${store_requisition.id}`}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Requisition
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-foreground">
                                Edit Store Requisition #{store_requisition.id}
                            </h1>
                            <p className="text-muted-foreground">
                                Status: {store_requisition.status.replace('_', ' ').toUpperCase()}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="max-w-4xl mx-auto w-full space-y-6">

                    {/* Form */}
                    <StoreRequisitionForm
                        data={data}
                        setData={setData}
                        errors={errors}
                        clearErrors={clearErrors}
                        inventoryItems={inventory_items || []}
                        branches={branches || []}
                        departments={departments || []}
                        userBranchId={user_branch_id}
                        userDepartmentId={user_department_id}
                        userDepartments={user_departments || []}
                    />

                    {/* Action Buttons */}
                    <Card className="sticky bottom-4 sm:static bg-card/95 backdrop-blur-sm border-border/50 sm:bg-card sm:backdrop-blur-none sm:border-border">
                        <CardContent className="p-4 sm:p-6">
                            <div className="flex flex-col gap-3 sm:flex-row sm:justify-end">
                                <Button
                                    type="button"
                                    onClick={handleSubmit}
                                    disabled={processing}
                                    className="w-full sm:w-auto"
                                >
                                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                    Update Requisition
                                </Button>

                                {canSaveAsDraft && (
                                    <Button
                                        type="button"
                                        variant="secondary"
                                        onClick={handleSaveAsDraft}
                                        disabled={processing}
                                        className="w-full sm:w-auto"
                                    >
                                        {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                        Save as Draft
                                    </Button>
                                )}

                                <Button
                                    variant="outline"
                                    asChild
                                    className="w-full sm:w-auto"
                                >
                                    <Link href={`/store-requisitions/${store_requisition.id}`}>
                                        Cancel
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}

export default EditStoreRequisition;
