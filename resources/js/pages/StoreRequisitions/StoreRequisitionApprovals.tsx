import React, { useState, useEffect } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    CheckCircle,
    XCircle,
    Search,
    Filter,
    ArrowLeft,
    Package
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { StoreRequisition } from '@/types/store-requisitions';
import { StoreRequisitionStatusBadgeWithIcon } from '@/components/StoreRequisitionStatusBadge';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';
import { PageProps } from '@inertiajs/core';

interface StoreRequisitionApprovalsPageProps extends PageProps {
    pending_approvals: StoreRequisition[];
    user: {
        id: number;
        name: string;
        email: string;
        permissions?: string[];
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

export default function StoreRequisitionApprovals() {
    const { pending_approvals } = usePage<StoreRequisitionApprovalsPageProps>().props;
    
    const [selectedRequisition, setSelectedRequisition] = useState<StoreRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Reset pagination when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, statusFilter, departmentFilter]);

    const { approveRequisition, rejectRequisition, isSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
        }
    });



    // Filter requisitions based on search and filters
    const allFilteredRequisitions = pending_approvals.filter(requisition => {
        const matchesSearch = searchTerm === '' ||
            requisition.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
            requisition.id.toString().includes(searchTerm) ||
            (requisition.requester?.first_name + ' ' + requisition.requester?.last_name)
                .toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || requisition.status === statusFilter;

        const matchesDepartment = departmentFilter === 'all' ||
            requisition.department?.id.toString() === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // Apply pagination
    const totalCount = allFilteredRequisitions.length;
    const totalPages = Math.ceil(totalCount / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const filteredRequisitions = allFilteredRequisitions.slice(startIndex, endIndex);

    // Get unique departments for filter
    const departments = pending_approvals
        .map(req => req.department)
        .filter(Boolean)
        .reduce((unique: typeof pending_approvals[0]['department'][], dept) => {
            if (!unique.find(d => d!.id === dept!.id)) {
                unique.push(dept);
            }
            return unique;
        }, []);

    const openApprovalDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = (requisition: StoreRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition) return;
        
        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject' && comments) {
            rejectRequisition(selectedRequisition.id, comments);
        }
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Store Requisition Approvals', href: '/store-requisitions/approvals' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Store Requisition Approvals" />

            <div className="flex h-full flex-1 flex-col gap-4 sm:gap-6 p-3 sm:p-4 lg:p-6">
                {/* Header - Mobile Responsive */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
                        <Button variant="outline" size="sm" className="w-fit" asChild>
                            <a href="/dashboard">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                <span className="hidden sm:inline">Back to Dashboard</span>
                                <span className="sm:hidden">Back</span>
                            </a>
                        </Button>
                        <div className="min-w-0 flex-1">
                            <h1 className="text-xl sm:text-2xl font-bold text-foreground truncate">
                                Store Requisition Approvals
                            </h1>
                            <p className="text-sm sm:text-base text-muted-foreground">
                                Review and approve pending store requisitions
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs sm:text-sm">
                            {filteredRequisitions.length} Pending
                        </Badge>
                    </div>
                </div>

                {/* Filters - Mobile Responsive */}
                <Card>
                    <CardHeader className="pb-3 sm:pb-4">
                        <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                            <Filter className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                            <span className="truncate">Filters</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3 sm:space-y-4">
                        <div className="grid grid-cols-1 gap-3 sm:gap-4 lg:grid-cols-3">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search by purpose, ID, or requester..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 text-sm sm:text-base"
                                />
                            </div>

                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="text-sm sm:text-base">
                                    <SelectValue placeholder="Filter by status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                                <SelectTrigger className="text-sm sm:text-base">
                                    <SelectValue placeholder="Filter by department" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Departments</SelectItem>
                                    {departments.map((dept) => (
                                        <SelectItem key={dept!.id} value={dept!.id.toString()}>
                                            {dept!.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Requisitions List - Mobile Responsive */}
                {totalCount === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-8 sm:py-12">
                            <Package className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mb-3 sm:mb-4" />
                            <h3 className="text-base sm:text-lg font-semibold text-muted-foreground mb-2">
                                No Pending Approvals
                            </h3>
                            <p className="text-sm sm:text-base text-muted-foreground text-center">
                                {searchTerm || statusFilter !== 'all' || departmentFilter !== 'all'
                                    ? 'No requisitions match your current filters.'
                                    : 'There are no store requisitions pending your approval at this time.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <Card>
                        {/* Desktop Table View */}
                        <div className="hidden lg:block">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Requisition ID</TableHead>
                                        <TableHead>Requester</TableHead>
                                        <TableHead>Department</TableHead>
                                        <TableHead>Purpose</TableHead>
                                        <TableHead>Items Count</TableHead>
                                        <TableHead>Requested Date</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredRequisitions.map((requisition) => (
                                        <TableRow key={requisition.id} className="hover:bg-muted/50">
                                            <TableCell className="font-medium">
                                                #{requisition.id}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex flex-col">
                                                    <span className="font-medium">
                                                        {requisition.requester?.first_name} {requisition.requester?.last_name}
                                                    </span>
                                                    <span className="text-sm text-muted-foreground">
                                                        {requisition.requester?.email}
                                                    </span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {requisition.department?.name || 'N/A'}
                                            </TableCell>
                                            <TableCell className="max-w-xs">
                                                <div className="truncate" title={requisition.purpose}>
                                                    {requisition.purpose}
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {requisition.items?.length || 0}
                                            </TableCell>
                                            <TableCell>
                                                {requisition.requested_at
                                                    ? new Date(requisition.requested_at).toLocaleDateString()
                                                    : 'N/A'
                                                }
                                            </TableCell>
                                            <TableCell>
                                                <StoreRequisitionStatusBadgeWithIcon
                                                    status={requisition.status}
                                                    showIcon={false}
                                                />
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex gap-2 justify-end">
                                                    <Button
                                                        onClick={() => openApprovalDialog(requisition)}
                                                        size="sm"
                                                        className="gap-2"
                                                    >
                                                        <CheckCircle className="h-4 w-4" />
                                                        Approve
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => openRejectionDialog(requisition)}
                                                        size="sm"
                                                        className="gap-2"
                                                    >
                                                        <XCircle className="h-4 w-4" />
                                                        Reject
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Mobile Card View */}
                        <div className="lg:hidden divide-y">
                            {filteredRequisitions.map((requisition) => (
                                <div key={requisition.id} className="p-4 space-y-3">
                                    {/* Header Row */}
                                    <div className="flex items-start justify-between gap-3">
                                        <div className="min-w-0 flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <p className="font-bold text-primary">#{requisition.id}</p>
                                                <StoreRequisitionStatusBadgeWithIcon
                                                    status={requisition.status}
                                                    showIcon={false}
                                                />
                                            </div>
                                            <p className="text-sm text-muted-foreground line-clamp-2">
                                                {requisition.purpose}
                                            </p>
                                        </div>
                                    </div>

                                    {/* Details Grid */}
                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                        <div>
                                            <div className="text-xs text-muted-foreground mb-1">Requester</div>
                                            <p className="font-medium truncate">
                                                {requisition.requester?.first_name} {requisition.requester?.last_name}
                                            </p>
                                            <p className="text-xs text-muted-foreground truncate">
                                                {requisition.requester?.email}
                                            </p>
                                        </div>
                                        <div>
                                            <div className="text-xs text-muted-foreground mb-1">Department</div>
                                            <p className="font-medium truncate">{requisition.department?.name || 'N/A'}</p>
                                        </div>
                                        <div>
                                            <div className="text-xs text-muted-foreground mb-1">Items Count</div>
                                            <Badge variant="outline" className="text-xs w-fit">
                                                {requisition.items?.length || 0} items
                                            </Badge>
                                        </div>
                                        <div>
                                            <div className="text-xs text-muted-foreground mb-1">Requested Date</div>
                                            <p className="text-xs font-medium">
                                                {requisition.requested_at
                                                    ? new Date(requisition.requested_at).toLocaleDateString()
                                                    : 'N/A'
                                                }
                                            </p>
                                        </div>
                                    </div>

                                    {/* Actions */}
                                    <div className="flex gap-2 pt-2">
                                        <Button
                                            onClick={() => openApprovalDialog(requisition)}
                                            size="sm"
                                            className="flex-1 gap-2"
                                        >
                                            <CheckCircle className="h-4 w-4" />
                                            Approve
                                        </Button>
                                        <Button
                                            variant="outline"
                                            onClick={() => openRejectionDialog(requisition)}
                                            size="sm"
                                            className="flex-1 gap-2"
                                        >
                                            <XCircle className="h-4 w-4" />
                                            Reject
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Pagination Controls - Mobile Responsive */}
                        {totalPages > 1 && (
                            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between px-3 sm:px-4 py-3 border-t bg-muted/20">
                                <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                                    Showing {startIndex + 1} to {Math.min(endIndex, totalCount)} of {totalCount} requisitions
                                </div>
                                <div className="flex items-center justify-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                        disabled={currentPage === 1}
                                        className="text-xs sm:text-sm"
                                    >
                                        <span className="hidden sm:inline">Previous</span>
                                        <span className="sm:hidden">Prev</span>
                                    </Button>
                                    <span className="text-xs sm:text-sm text-muted-foreground px-2">
                                        {currentPage} / {totalPages}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                        disabled={currentPage === totalPages}
                                        className="text-xs sm:text-sm"
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        )}
                    </Card>
                )}

                {/* Approval Dialog */}
            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={selectedRequisition}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => {
                        setIsDialogOpen(false);
                        setSelectedRequisition(null);
                        setDialogAction(null);
                    }}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isSubmitting}
                />
            )}
            </div>
        </AppLayout>
    );
}
