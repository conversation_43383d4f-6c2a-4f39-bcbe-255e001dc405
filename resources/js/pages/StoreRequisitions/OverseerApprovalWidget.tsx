import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Shield,
    CheckCircle,
    XCircle,
    User,
    Calendar,
    Package,
    AlertCircle
} from 'lucide-react';
import { StoreRequisition } from '@/types/store-requisitions';
import { ApprovalDialog } from './ApprovalDialog';
import { useStoreRequisitionApproval } from '@/hooks/use-store-requisition-approval';
import { OverseerOnly } from '@/components/PermissionWrapper';
import axios from 'axios';

interface PendingStoreKeeperRequisition {
    id: number;
    purpose: string;
    status: 'pending_approval' | 'approved' | 'rejected' | 'issued' | 'draft';
    requested_at: string;
    requester: {
        id: number;
        name: string;
        email: string;
        is_store_keeper: boolean;
    } | null;
    department: {
        id: number;
        name: string;
    } | null;
    items_count: number;
    items_preview: Array<{
        name: string;
        sku: string;
        quantity_requested: number;
    }>;
}

interface OverseerApprovalWidgetProps {
    /** Maximum number of requisitions to display */
    maxItems?: number;
    /** Show header with title and view all link */
    showHeader?: boolean;
    /** Custom CSS classes */
    className?: string;
    /** Initial data (optional, will fetch if not provided) */
    initialData?: PendingStoreKeeperRequisition[];
}

export function OverseerApprovalWidget({ 
    maxItems = 5, 
    showHeader = true, 
    className = '',
    initialData 
}: OverseerApprovalWidgetProps) {
    const [pendingRequisitions, setPendingRequisitions] = useState<PendingStoreKeeperRequisition[]>(initialData || []);
    const [loading, setLoading] = useState(!initialData);
    const [error, setError] = useState<string | null>(null);
    const [selectedRequisition, setSelectedRequisition] = useState<PendingStoreKeeperRequisition | null>(null);
    const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { approveRequisition, rejectRequisition, isSubmitting } = useStoreRequisitionApproval({
        onSuccess: () => {
            setIsDialogOpen(false);
            setSelectedRequisition(null);
            setDialogAction(null);
            // Refresh data after successful action
            fetchPendingRequisitions();
        }
    });

    // Fetch pending store keeper requisitions
    const fetchPendingRequisitions = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await axios.get('/overseer/pending-approvals');
            setPendingRequisitions(response.data.pending_approvals || []);
        } catch {
            // Error fetching pending requisitions - handled by UI
            setError('Failed to load pending requisitions');
        } finally {
            setLoading(false);
        }
    };

    // Fetch data on mount if no initial data provided
    useEffect(() => {
        if (!initialData) {
            fetchPendingRequisitions();
        }
    }, [initialData]);

    // Add function to check if requisition can be approved
    const canApproveRequisition = (requisition: PendingStoreKeeperRequisition) => {
        // This widget is specifically for store keeper requisitions that overseers can approve
        return requisition.requester?.is_store_keeper === true;
    };

    const openApprovalDialog = (requisition: PendingStoreKeeperRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('approve');
        setIsDialogOpen(true);
    };

    const openRejectionDialog = (requisition: PendingStoreKeeperRequisition) => {
        setSelectedRequisition(requisition);
        setDialogAction('reject');
        setIsDialogOpen(true);
    };

    const handleApprovalSubmit = (comments?: string) => {
        if (!selectedRequisition) return;
        
        if (dialogAction === 'approve') {
            approveRequisition(selectedRequisition.id, comments);
        } else if (dialogAction === 'reject' && comments) {
            rejectRequisition(selectedRequisition.id, comments);
        }
    };

    // Convert to StoreRequisition format for ApprovalDialog
    const convertToStoreRequisition = (req: PendingStoreKeeperRequisition): StoreRequisition => ({
        id: req.id,
        purpose: req.purpose,
        status: req.status,
        requested_at: req.requested_at,
        requester: req.requester ? {
            id: req.requester.id,
            first_name: req.requester.name.split(' ')[0] || '',
            last_name: req.requester.name.split(' ').slice(1).join(' ') || '',
            email: req.requester.email,
        } : undefined,
        department: req.department ? {
            id: req.department.id,
            name: req.department.name,
            branch_id: 0, // Default value since it's not provided
        } : undefined,
        items: req.items_preview.map((item, index) => ({
            id: index,
            inventory_item_id: index,
            quantity_requested: item.quantity_requested,
            quantity_issued: 0,
            inventory_item: {
                id: index,
                name: item.name,
                sku: item.sku,
                unit_of_measure: 'units',
                quantity_on_hand: 0,
                reorder_level: 0,
            }
        })),
        // Required fields with defaults
        organization_id: 0,
        branch_id: 0,
        department_id: req.department?.id || 0,
        requester_user_id: req.requester?.id || 0,
        created_at: req.requested_at,
        updated_at: req.requested_at,
    });

    return (
        <OverseerOnly fallback={null}>
            <Card className={className}>
                {showHeader && (
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-lg font-semibold flex items-center gap-2">
                            <Shield className="h-5 w-5 text-primary" />
                            Store Keeper Approvals
                        </CardTitle>
                        {pendingRequisitions.length > maxItems && (
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/store-requisitions/approvals">
                                    View All ({pendingRequisitions.length})
                                </Link>
                            </Button>
                        )}
                    </CardHeader>
                )}
                
                <CardContent className={showHeader ? '' : 'pt-6'}>
                    {loading ? (
                        <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        </div>
                    ) : error ? (
                        <div className="flex items-center justify-center py-8 text-center">
                            <div>
                                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                                <p className="text-sm text-muted-foreground">{error}</p>
                                <Button variant="outline" size="sm" onClick={fetchPendingRequisitions} className="mt-2">
                                    Retry
                                </Button>
                            </div>
                        </div>
                    ) : pendingRequisitions.length === 0 ? (
                        <div className="text-center py-8">
                            <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-muted-foreground">No store keeper requisitions pending approval</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {pendingRequisitions.slice(0, maxItems).map((requisition) => (
                                <div key={requisition.id} className="border rounded-lg p-4 space-y-3 hover:bg-muted/50 transition-colors">
                                    {/* Header */}
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">Store Requisition #{requisition.id}</span>
                                            <Badge variant="secondary" className="text-xs">
                                                {requisition.items_count} items
                                            </Badge>
                                        </div>
                                        <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                            Store Keeper Request
                                        </Badge>
                                    </div>
                                    
                                    {/* Details */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                        <div className="flex items-center gap-2">
                                            <User className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-muted-foreground">Store Keeper:</span>
                                            <span className="font-medium">{requisition.requester?.name}</span>
                                        </div>
                                        
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-muted-foreground">Requested:</span>
                                            <span className="font-medium">
                                                {requisition.requested_at 
                                                    ? new Date(requisition.requested_at).toLocaleDateString()
                                                    : 'N/A'
                                                }
                                            </span>
                                        </div>
                                    </div>

                                    {/* Purpose */}
                                    <div className="text-sm">
                                        <span className="text-muted-foreground">Purpose: </span>
                                        <span>{requisition.purpose}</span>
                                    </div>

                                    {/* Items Preview */}
                                    {requisition.items_preview && requisition.items_preview.length > 0 && (
                                        <div className="bg-muted/50 rounded-lg p-3">
                                            <div className="flex items-center gap-2 mb-2">
                                                <Package className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm font-medium">Items:</span>
                                            </div>
                                            <div className="space-y-1">
                                                {requisition.items_preview.slice(0, 2).map((item, index) => (
                                                    <div key={index} className="flex justify-between text-sm">
                                                        <span>{item.name}</span>
                                                        <span className="text-muted-foreground">
                                                            {item.quantity_requested} units
                                                        </span>
                                                    </div>
                                                ))}
                                                {requisition.items_preview.length > 2 && (
                                                    <p className="text-sm text-muted-foreground italic">
                                                        +{requisition.items_preview.length - 2} more items
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Actions */}
                                    <div className="flex justify-between items-center pt-2">
                                        <Button variant="ghost" size="sm" asChild>
                                            <Link href={`/store-requisitions/${requisition.id}`}>
                                                View Details
                                            </Link>
                                        </Button>
                                        
                                        <div className="flex gap-2">
                                            {canApproveRequisition(requisition) ? (
                                                <>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => openRejectionDialog(requisition)}
                                                        className="gap-1"
                                                    >
                                                        <XCircle className="h-4 w-4" />
                                                        Reject
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        onClick={() => openApprovalDialog(requisition)}
                                                        className="gap-1"
                                                    >
                                                        <CheckCircle className="h-4 w-4" />
                                                        Approve
                                                    </Button>
                                                </>
                                            ) : (
                                                <span className="text-sm text-muted-foreground">View Only</span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Approval Dialog */}
            {selectedRequisition && dialogAction && (
                <ApprovalDialog
                    requisition={convertToStoreRequisition(selectedRequisition)}
                    action={dialogAction}
                    isOpen={isDialogOpen}
                    onClose={() => {
                        setIsDialogOpen(false);
                        setSelectedRequisition(null);
                        setDialogAction(null);
                    }}
                    onSubmit={handleApprovalSubmit}
                    isSubmitting={isSubmitting}
                />
            )}
        </OverseerOnly>
    );
}
