// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function ForgotPassword({ status }: { status?: string }) {
    const { data, setData, post, processing, errors, wasSuccessful } = useForm({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.email'));
    };

    return (
        <AuthLayout title="Forgot Password" description="We’ll send you a link to reset your password.">
            <Head title="Forgot Password" />

            {wasSuccessful || status ? (
                <div className="bg-primary/50 text-foreground/100 rounded-md p-4 text-center">
                    {status ?? 'A password reset link has been sent to your email.'}
                </div>
            ) : (
                <form onSubmit={submit} className="space-y-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                            id="email"
                            type="email"
                            name="email"
                            autoComplete="off"
                            value={data.email}
                            autoFocus
                            required
                            disabled={processing}
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder="<EMAIL>"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="my-6 flex items-center justify-start">
                        <Button className="w-full" type="submit" disabled={processing}>
                            {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                            Email password reset link
                        </Button>
                    </div>
                </form>
            )}

            <div className="text-foreground/100 mt-4 text-center text-sm">
                Remember your password? <TextLink href={route('login')}>Return to login</TextLink>
            </div>
        </AuthLayout>
    );
}
