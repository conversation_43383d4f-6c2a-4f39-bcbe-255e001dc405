import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { getPasswordValidationMessage, getPasswordConfirmationMessage } from '@/utils/passwordValidation';

interface ResetPasswordProps {
    token: string;
    email: string;
}

type ResetPasswordForm = {
    token: string;
    email: string;
    password: string;
    password_confirmation: string;
};

export default function ResetPassword({ token, email }: ResetPasswordProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<ResetPasswordForm>>({
        token: token,
        email: email,
        password: '',
        password_confirmation: '',
    });

    const [passwordError, setPasswordError] = useState('');
    const [confirmPasswordError, setConfirmPasswordError] = useState('');

    // Real-time password validation
    const handlePasswordChange = (password: string) => {
        setData('password', password);
        if (password) {
            const message = getPasswordValidationMessage(password);
            setPasswordError(message);
        } else {
            setPasswordError('');
        }

        // Also validate confirmation if it exists
        if (data.password_confirmation) {
            const confirmMessage = getPasswordConfirmationMessage(password, data.password_confirmation);
            setConfirmPasswordError(confirmMessage);
        }
    };

    // Real-time password confirmation validation
    const handlePasswordConfirmationChange = (confirmation: string) => {
        setData('password_confirmation', confirmation);
        if (confirmation) {
            const message = getPasswordConfirmationMessage(data.password, confirmation);
            setConfirmPasswordError(message);
        } else {
            setConfirmPasswordError('');
        }
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.store'), {
            onFinish: () => {
                reset('password', 'password_confirmation');
                setPasswordError('');
                setConfirmPasswordError('');
            },
        });
    };

    return (
        <AuthLayout title="Reset password" description="Please enter your new password below">
            <Head title="Reset password" />

            <form onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                            id="email"
                            type="email"
                            name="email"
                            autoComplete="email"
                            value={data.email}
                            className="mt-1 block w-full"
                            readOnly
                            onChange={(e) => setData('email', e.target.value)}
                        />
                        <InputError message={errors.email} className="mt-2" />
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="password">Password</Label>
                        <Input
                            id="password"
                            type="password"
                            name="password"
                            autoComplete="new-password"
                            value={data.password}
                            className="mt-1 block w-full"
                            autoFocus
                            onChange={(e) => handlePasswordChange(e.target.value)}
                            placeholder="Password"
                        />
                        <InputError message={errors.password || passwordError} />

                        <div className="text-sm text-muted-foreground space-y-1">
                            <p>Password must contain:</p>
                            <ul className="list-disc list-inside space-y-0.5 ml-2">
                                <li>At least 8 characters</li>
                                <li>At least one uppercase letter (A-Z)</li>
                                <li>At least one lowercase letter (a-z)</li>
                                <li>At least one number (0-9)</li>
                                <li>At least one special character (!@#$%^&*()_+-=[]{}|;:,.{'<>'}?)</li>
                                <li>Not a commonly used password</li>
                            </ul>
                        </div>
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="password_confirmation">Confirm password</Label>
                        <Input
                            id="password_confirmation"
                            type="password"
                            name="password_confirmation"
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            className="mt-1 block w-full"
                            onChange={(e) => handlePasswordConfirmationChange(e.target.value)}
                            placeholder="Confirm password"
                        />
                        <InputError message={errors.password_confirmation || confirmPasswordError} className="mt-2" />
                    </div>

                    <Button type="submit" className="mt-4 w-full" disabled={processing}>
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                        Reset password
                    </Button>
                </div>
            </form>
        </AuthLayout>
    );
}
