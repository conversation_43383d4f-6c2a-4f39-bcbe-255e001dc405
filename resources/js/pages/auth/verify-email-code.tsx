import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import InputError from '@/components/ui/input-error';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { useForm } from '@inertiajs/react';
import { motion } from 'framer-motion';
import { LoaderCircle, Mail, RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';

type VerifyEmailCodeForm = {
    code: string;
};

interface Props {
    email: string;
    canRequestNew: boolean;
    remainingAttempts: number;
    status?: string;
}

export default function VerifyEmailCode({ email, canRequestNew, remainingAttempts, status }: Props) {
    const { data, setData, post, errors, reset } = useForm<VerifyEmailCodeForm>({
        code: '',
    });

    const { post: resendPost } = useForm({});
    const [isLoading, setIsLoading] = useState(false);
    const [resendLoading, setResendLoading] = useState(false);

    const submit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        post(route('email.verification.code.verify'), {
            onFinish: () => {
                setIsLoading(false);
            },
            onError: () => {
                setIsLoading(false);
                reset('code');
            }
        });
    };

    const resendCode = async () => {
        if (!canRequestNew) return;
        
        setResendLoading(true);
        resendPost(route('email.verification.code.resend'), {
            onFinish: () => {
                setResendLoading(false);
            },
            onError: () => {
                setResendLoading(false);
            }
        });
    };

    const inputVariants = {
        focus: { scale: 1.02, transition: { duration: 0.2 } },
        blur: { scale: 1, transition: { duration: 0.2 } },
    };

    // Auto-focus the code input
    useEffect(() => {
        const codeInput = document.getElementById('code');
        if (codeInput) {
            codeInput.focus();
        }
    }, []);

    // Format code input to only allow numbers and limit to 6 digits
    const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
        setData('code', value);
    };

    return (
        <AuthLayout title="Verify Your Email">
            <div className="w-full max-w-md mx-auto">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8"
                >
                    <div className="text-center mb-6">
                        <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
                            <Mail className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                            Verify Your Email
                        </h2>
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            We've sent a 6-digit verification code to
                        </p>
                        <p className="font-semibold text-gray-900 dark:text-white">
                            {email}
                        </p>
                    </div>

                    {status && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="mb-4 p-3 bg-green-100 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md"
                        >
                            <p className="text-green-700 dark:text-green-300 text-sm">
                                {status}
                            </p>
                        </motion.div>
                    )}

                    <form onSubmit={submit} className="space-y-6">
                        <div>
                            <Label htmlFor="code" className="text-gray-700 dark:text-gray-300">
                                Verification Code
                            </Label>
                            <motion.div
                                variants={inputVariants}
                                whileFocus="focus"
                                whileTap="focus"
                            >
                                <Input
                                    id="code"
                                    type="text"
                                    value={data.code}
                                    onChange={handleCodeChange}
                                    placeholder="Enter 6-digit code"
                                    className="mt-1 text-center text-2xl font-mono tracking-widest"
                                    maxLength={6}
                                    autoComplete="one-time-code"
                                    inputMode="numeric"
                                    pattern="[0-9]*"
                                />
                            </motion.div>
                            <InputError message={errors.code} className="mt-2" />
                        </div>

                        <Button
                            type="submit"
                            disabled={isLoading || data.code.length !== 6}
                            className="w-full"
                        >
                            {isLoading ? (
                                <>
                                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                                    Verifying...
                                </>
                            ) : (
                                'Verify Email'
                            )}
                        </Button>
                    </form>

                    <div className="mt-6 text-center space-y-3">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            Didn't receive the code?
                        </p>
                        
                        {canRequestNew ? (
                            <Button
                                type="button"
                                variant="outline"
                                onClick={resendCode}
                                disabled={resendLoading}
                                className="w-full"
                            >
                                {resendLoading ? (
                                    <>
                                        <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                                        Sending...
                                    </>
                                ) : (
                                    <>
                                        <RefreshCw className="mr-2 h-4 w-4" />
                                        Resend Code
                                    </>
                                )}
                            </Button>
                        ) : (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                <p>Too many requests. Please try again later.</p>
                                <p>Remaining attempts: {remainingAttempts}</p>
                            </div>
                        )}

                        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                            <TextLink href={route('register')} className="text-sm">
                                ← Back to Registration
                            </TextLink>
                        </div>
                    </div>
                </motion.div>
            </div>
        </AuthLayout>
    );
}
