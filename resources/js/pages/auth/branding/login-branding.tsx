"use client"

import { motion } from "framer-motion"

export default function LoginBranding() {
  return (
    <div className="text-background max-w-md px-8 z-10">
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5 }}
        className="mb-6 flex justify-center"
      >
        <svg
          width="80"
          height="80"
          viewBox="0 0 80 80"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="text-background"
        >
          <motion.path
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: "easeInOut" }}
            d="M40 8C22.36 8 8 22.36 8 40C8 57.64 22.36 72 40 72C57.64 72 72 57.64 72 40C72 22.36 57.64 8 40 8ZM40 16C53.2 16 64 26.8 64 40C64 53.2 53.2 64 40 64C26.8 64 16 53.2 16 40C16 26.8 26.8 16 40 16Z"
            fill="white"
            stroke="white"
            strokeWidth="2"
          />
          <motion.path
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 1.5, ease: "easeInOut", delay: 0.5 }}
            d="M40 24C31.2 24 24 31.2 24 40C24 48.8 31.2 56 40 56C48.8 56 56 48.8 56 40C56 31.2 48.8 24 40 24ZM40 48C35.6 48 32 44.4 32 40C32 35.6 35.6 32 40 32C44.4 32 48 35.6 48 40C48 44.4 44.4 48 40 48Z"
            fill="white"
            stroke="white"
            strokeWidth="2"
          />
        </svg>
      </motion.div>
      <motion.h1
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.5 }}
        className="text-4xl font-bold mb-4"
      >
        👋 Welcome back to Sippar
      </motion.h1>
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7, duration: 0.5 }}
        className="text-lg mb-4"
      >
        Your organization's finances — simplified, secured, and right where you left them.
      </motion.p>
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.5 }}
        className="text-base"
      >
        Log in to access your dashboards, monitor transactions, and keep everything running smoothly.
      </motion.p>
    </div>
  )
}