import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { CheckCircle, Info } from 'lucide-react';
import React, { useState } from 'react';

interface OrganizationSetupProps {
    organization: {
        id: number;
        name: string;
    };
    departmentTemplates: {
        id: number;
        name: string;
        description: string;
    }[];
    roleTemplates: {
        [key: string]: {
            id: number;
            name: string;
            description: string;
            department_template_id: number | null;
        }[];
    };
    generalRoleTemplates: {
        id: number;
        name: string;
        description: string;
    }[];
}

export default function OrganizationSetup({ organization, departmentTemplates, roleTemplates, generalRoleTemplates }: OrganizationSetupProps) {
    const { data, setData, post, processing, errors } = useForm({
        organization_id: organization.id,
        department_templates: [] as number[],
        role_templates: [] as number[],
    });

    // For debugging
    console.log('Initial form data:', {
        organization_id: organization.id,
        department_templates: [],
        role_templates: [],
    });

    const [step, setStep] = useState(1);
    const [selectedDepartments, setSelectedDepartments] = useState<number[]>([]);

    // Handle department selection
    const handleDepartmentChange = (templateId: number) => {
        const updatedDepartments = selectedDepartments.includes(templateId)
            ? selectedDepartments.filter((id) => id !== templateId)
            : [...selectedDepartments, templateId];

        setSelectedDepartments(updatedDepartments);
        setData('department_templates', updatedDepartments);
        console.log('Updated department_templates:', updatedDepartments);
    };

    // Handle role selection
    const handleRoleChange = (templateId: number) => {
        const updatedRoles = data.role_templates.includes(templateId)
            ? data.role_templates.filter((id) => id !== templateId)
            : [...data.role_templates, templateId];

        setData('role_templates', updatedRoles);
        console.log('Updated role_templates:', updatedRoles);
    };

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Ensure we have the correct data
        const formData = {
            organization_id: organization.id,
            department_templates: selectedDepartments,
            role_templates: data.role_templates,
        };

        console.log('Submitting form with data:', formData);

        // Use the formData directly instead of relying on the useForm state
        post('/organization-setup', {
            onSuccess: () => {
                console.log('Form submitted successfully');
            },
            onError: (errors: Record<string, string>) => {
                console.error('Form submission errors:', errors);
            },
        });
    };

    // Go to next step
    const nextStep = () => {
        if (step === 1 && selectedDepartments.length === 0) {
            return; // Don't proceed if no departments selected
        }
        setStep(step + 1);
    };

    // Go to previous step
    const prevStep = () => {
        setStep(step - 1);
    };

    return (
        <AppLayout>
            <Head title="Organization Setup" />
            <div className="bg-background/80 container mx-auto py-8">
                <div className="mx-auto max-w-4xl">
                    <h1 className="text-foreground mb-8 text-center text-3xl font-bold">Set Up Your Organization: {organization.name}</h1>

                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className={`flex-1 ${step >= 1 ? 'text-primary' : 'text-muted-foreground'}`}>
                                <div className="flex items-center">
                                    <div
                                        className={`mr-2 flex h-8 w-8 items-center justify-center rounded-full ${step >= 1 ? 'bg-primary text-white' : 'bg-muted'}`}
                                    >
                                        1
                                    </div>
                                    <span>Select Departments</span>
                                </div>
                            </div>
                            <div className="bg-muted mx-2 h-1 w-16"></div>
                            <div className={`flex-1 ${step >= 2 ? 'text-primary' : 'text-muted-foreground'}`}>
                                <div className="flex items-center">
                                    <div
                                        className={`mr-2 flex h-8 w-8 items-center justify-center rounded-full ${step >= 2 ? 'bg-primary text-white' : 'bg-muted'}`}
                                    >
                                        2
                                    </div>
                                    <span>Select Roles</span>
                                </div>
                            </div>
                            <div className="bg-muted mx-2 h-1 w-16"></div>
                            <div className={`flex-1 ${step >= 3 ? 'text-primary' : 'text-muted-foreground'}`}>
                                <div className="flex items-center">
                                    <div
                                        className={`mr-2 flex h-8 w-8 items-center justify-center rounded-full ${step >= 3 ? 'bg-primary text-white' : 'bg-muted'}`}
                                    >
                                        3
                                    </div>
                                    <span>Confirm</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit}>
                        {/* Step 1: Select Departments */}
                        {step === 1 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Select Departments</CardTitle>
                                    <CardDescription>Choose the departments you want to create for your organization.</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <Alert className="mb-6">
                                        <Info className="h-4 w-4" />
                                        <AlertTitle>Information</AlertTitle>
                                        <AlertDescription>
                                            You can add, edit, or remove departments later from your organization settings.
                                        </AlertDescription>
                                    </Alert>

                                    <div className="space-y-4">
                                        {departmentTemplates.map((template) => (
                                            <div key={template.id} className="flex items-start space-x-3 rounded-md border p-4">
                                                <Checkbox
                                                    id={`dept-${template.id}`}
                                                    checked={selectedDepartments.includes(template.id)}
                                                    onCheckedChange={() => handleDepartmentChange(template.id)}
                                                />
                                                <div>
                                                    <Label htmlFor={`dept-${template.id}`} className="text-base font-medium">
                                                        {template.name}
                                                    </Label>
                                                    <p className="text-muted-foreground text-sm">{template.description}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    {errors.department_templates && <p className="text-destructive mt-2">{errors.department_templates}</p>}
                                </CardContent>
                                <CardFooter className="flex justify-end">
                                    <Button type="button" variant="secondary" onClick={nextStep} disabled={selectedDepartments.length === 0}>
                                        Next
                                    </Button>
                                </CardFooter>
                            </Card>
                        )}

                        {/* Step 2: Select Roles */}
                        {step === 2 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Select Roles</CardTitle>
                                    <CardDescription>Choose the roles you want to create for your departments.</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <Alert className="mb-6">
                                        <Info className="h-4 w-4" />
                                        <AlertTitle>Information</AlertTitle>
                                        <AlertDescription>You can add, edit, or remove roles later from your organization settings.</AlertDescription>
                                    </Alert>

                                    <div className="space-y-6">
                                        {/* Department-specific roles */}
                                        {selectedDepartments.map((deptId) => {
                                            const department = departmentTemplates.find((d) => d.id === deptId);
                                            const departmentRoles = roleTemplates[deptId] || [];

                                            return (
                                                <div key={deptId} className="border-border bg-card/50 rounded-md border p-4">
                                                    <h3 className="text-foreground mb-3 text-lg font-medium">{department?.name} Roles</h3>
                                                    <div className="space-y-3">
                                                        {departmentRoles.map((role) => (
                                                            <div
                                                                key={role.id}
                                                                className="border-border bg-muted/50 hover:bg-muted/70 flex items-start space-x-3 rounded-md border p-3 transition-colors"
                                                            >
                                                                <Checkbox
                                                                    id={`role-${role.id}`}
                                                                    checked={data.role_templates.includes(role.id)}
                                                                    onCheckedChange={() => handleRoleChange(role.id)}
                                                                />
                                                                <div>
                                                                    <Label
                                                                        htmlFor={`role-${role.id}`}
                                                                        className="text-foreground text-base font-medium"
                                                                    >
                                                                        {role.name}
                                                                    </Label>
                                                                    <p className="text-muted-foreground text-sm">{role.description}</p>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            );
                                        })}

                                        {/* General roles (not tied to specific departments) */}
                                        {generalRoleTemplates.length > 0 && (
                                            <div className="border-border bg-card/50 rounded-md border p-4">
                                                <h3 className="text-foreground mb-3 text-lg font-medium">General Roles</h3>
                                                <div className="space-y-3">
                                                    {generalRoleTemplates.map((role) => (
                                                        <div
                                                            key={role.id}
                                                            className="border-border bg-muted/50 hover:bg-muted/70 flex items-start space-x-3 rounded-md border p-3 transition-colors"
                                                        >
                                                            <Checkbox
                                                                id={`role-${role.id}`}
                                                                checked={data.role_templates.includes(role.id)}
                                                                onCheckedChange={() => handleRoleChange(role.id)}
                                                            />
                                                            <div>
                                                                <Label htmlFor={`role-${role.id}`} className="text-foreground text-base font-medium">
                                                                    {role.name}
                                                                </Label>
                                                                <p className="text-muted-foreground text-sm">{role.description}</p>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {errors.role_templates && <p className="text-destructive mt-2">{errors.role_templates}</p>}
                                </CardContent>
                                <CardFooter className="flex justify-between">
                                    <Button type="button" variant="outline" onClick={prevStep}>
                                        Back
                                    </Button>
                                    <Button type="button" variant="outline" onClick={nextStep} disabled={data.role_templates.length === 0}>
                                        Next
                                    </Button>
                                </CardFooter>
                            </Card>
                        )}

                        {/* Step 3: Confirmation */}
                        {step === 3 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Confirm Your Selections</CardTitle>
                                    <CardDescription>Review your selected departments and roles before finalizing.</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-6">
                                        <div className="border-border bg-card/50 rounded-md border p-4">
                                            <h3 className="text-foreground mb-2 text-lg font-medium">Selected Departments</h3>
                                            <ul className="text-muted-foreground list-disc space-y-1 pl-5">
                                                {selectedDepartments.map((deptId) => {
                                                    const department = departmentTemplates.find((d) => d.id === deptId);
                                                    return (
                                                        <li key={deptId} className="text-foreground">
                                                            {department?.name}
                                                        </li>
                                                    );
                                                })}
                                            </ul>
                                        </div>

                                        <div className="border-border bg-card/50 rounded-md border p-4">
                                            <h3 className="text-foreground mb-2 text-lg font-medium">Selected Roles</h3>
                                            <ul className="text-muted-foreground list-disc space-y-1 pl-5">
                                                {data.role_templates.map((roleId) => {
                                                    // Find the role in either department-specific roles or general roles
                                                    let role;
                                                    for (const deptId in roleTemplates) {
                                                        role = roleTemplates[deptId]?.find((r) => r.id === roleId);
                                                        if (role) break;
                                                    }

                                                    if (!role) {
                                                        role = generalRoleTemplates.find((r) => r.id === roleId);
                                                    }

                                                    return role ? (
                                                        <li key={roleId} className="text-foreground">
                                                            {role.name}
                                                        </li>
                                                    ) : null;
                                                })}
                                            </ul>
                                        </div>

                                        <Alert>
                                            <CheckCircle className="h-4 w-4" />
                                            <AlertTitle>Ready to Complete</AlertTitle>
                                            <AlertDescription>
                                                Once you confirm, these departments and roles will be created for your organization. You can always
                                                modify them later from your organization settings.
                                            </AlertDescription>
                                        </Alert>
                                    </div>
                                </CardContent>
                                <CardFooter className="flex justify-between">
                                    <Button type="button" variant="outline" onClick={prevStep}>
                                        Back
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Processing...' : 'Complete Setup'}
                                    </Button>
                                </CardFooter>
                            </Card>
                        )}
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
