import InputError from '@/components/input-error';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';
import PasswordToggle from '@/pages/auth/passwordToggle';
import { type BreadcrumbItem } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useRef, useState } from 'react';

import HeadingSmall from '@/components/heading-small';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { getPasswordValidationMessage, getPasswordConfirmationMessage } from '@/utils/passwordValidation';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Password settings',
        href: '/settings/password',
    },
];

export default function Password() {
    const passwordInput = useRef<HTMLInputElement>(null);
    const currentPasswordInput = useRef<HTMLInputElement>(null);

    const { data, setData, errors, put, reset, processing, recentlySuccessful } = useForm({
        current_password: '',
        password: '',
        password_confirmation: '',
    });

    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);
    const [passwordError, setPasswordError] = useState('');
    const [confirmPasswordError, setConfirmPasswordError] = useState('');

    // Real-time password validation
    const handlePasswordChange = (password: string) => {
        setData('password', password);
        if (password) {
            const message = getPasswordValidationMessage(password);
            setPasswordError(message);
        } else {
            setPasswordError('');
        }

        // Also validate confirmation if it exists
        if (data.password_confirmation) {
            const confirmMessage = getPasswordConfirmationMessage(password, data.password_confirmation);
            setConfirmPasswordError(confirmMessage);
        }
    };

    // Real-time password confirmation validation
    const handlePasswordConfirmationChange = (confirmation: string) => {
        setData('password_confirmation', confirmation);
        if (confirmation) {
            const message = getPasswordConfirmationMessage(data.password, confirmation);
            setConfirmPasswordError(message);
        } else {
            setConfirmPasswordError('');
        }
    };

    const updatePassword: FormEventHandler = (e) => {
        e.preventDefault();

        put(route('password.update'), {
            preserveScroll: true,
            onSuccess: () => {
                reset();
                setPasswordError('');
                setConfirmPasswordError('');
            },
            onError: (errors) => {
                if (errors.password) {
                    reset('password', 'password_confirmation');
                    passwordInput.current?.focus();
                }

                if (errors.current_password) {
                    reset('current_password');
                    currentPasswordInput.current?.focus();
                }
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Profile settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall title="Update password" description="Ensure your account is using a long, random password to stay secure" />

                    <form onSubmit={updatePassword} className="space-y-6">
                        <div className="grid gap-2">
                            <Label htmlFor="current_password">Current password</Label>

                            <div className="relative">
                                <Input
                                    id="current_password"
                                    ref={currentPasswordInput}
                                    value={data.current_password}
                                    onChange={(e) => setData('current_password', e.target.value)}
                                    type={showCurrentPassword ? 'text' : 'password'}
                                    className="mt-1 block w-full pr-10"
                                    autoComplete="current-password"
                                    placeholder="Current password"
                                />
                                <PasswordToggle show={showCurrentPassword} toggle={() => setShowCurrentPassword(!showCurrentPassword)} />
                            </div>

                            <InputError message={errors.current_password} />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="password">New password</Label>

                            <div className="relative">
                                <Input
                                    id="password"
                                    ref={passwordInput}
                                    value={data.password}
                                    onChange={(e) => handlePasswordChange(e.target.value)}
                                    type={showNewPassword ? 'text' : 'password'}
                                    className="mt-1 block w-full pr-10"
                                    autoComplete="new-password"
                                    placeholder="New password"
                                />
                                <PasswordToggle show={showNewPassword} toggle={() => setShowNewPassword(!showNewPassword)} />
                            </div>

                            <InputError message={errors.password || passwordError} />

                            <div className="text-sm text-muted-foreground space-y-1">
                                <p>Password must contain:</p>
                                <ul className="list-disc list-inside space-y-0.5 ml-2">
                                    <li>At least 8 characters</li>
                                    <li>At least one uppercase letter (A-Z)</li>
                                    <li>At least one lowercase letter (a-z)</li>
                                    <li>At least one number (0-9)</li>
                                    <li>At least one special character (!@#$%^&*()_+-=[]{}|;:,.{'<>'}?)</li>
                                    <li>Not a commonly used password</li>
                                </ul>
                            </div>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="password_confirmation">Confirm password</Label>

                            <div className="relative">
                                <Input
                                    id="password_confirmation"
                                    value={data.password_confirmation}
                                    onChange={(e) => handlePasswordConfirmationChange(e.target.value)}
                                    type={showConfirmNewPassword ? 'text' : 'password'}
                                    className="mt-1 block w-full pr-10"
                                    autoComplete="new-password"
                                    placeholder="Confirm password"
                                />
                                <PasswordToggle show={showConfirmNewPassword} toggle={() => setShowConfirmNewPassword(!showConfirmNewPassword)} />
                            </div>

                            <InputError message={errors.password_confirmation || confirmPasswordError} />
                        </div>

                        <div className="flex items-center gap-4">
                            <Button disabled={processing}>Save password</Button>

                            <Transition
                                show={recentlySuccessful}
                                enter="transition ease-in-out"
                                enterFrom="opacity-0"
                                leave="transition ease-in-out"
                                leaveTo="opacity-0"
                            >
                                <p className="text-sm text-neutral-600">Saved</p>
                            </Transition>
                        </div>
                    </form>
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}
