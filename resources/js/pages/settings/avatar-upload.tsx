import React, { useState } from 'react';
import { type User } from '@/types';
import { router } from '@inertiajs/react';

interface AvatarUploadProps {
  user: User;
  avatar_url?: string,
  errors?: Record<string, string[]>;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({ user, avatar_url }) => {
  const [avatar, setAvatar] = useState<File | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatar(file);
      setErrorMessage(null);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!avatar) {
      setErrorMessage('Please select an image file.');
      return;
    }

    const formData = new FormData();
    formData.append('avatar', avatar);

    router.post('/settings/avatar', formData, {
      onError: (error) => {
        if (error.avatar) {
          setErrorMessage(error.avatar[0]);
        }
      },
      onSuccess: () => {
        setAvatar(null);
        setErrorMessage(null);
      },
    });
  };

  return (
    <div className="max-w-md mx-auto mt-6 bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-lg font-semibold mb-4 text-gray-800">Update Avatar</h2>

      <form onSubmit={handleSubmit} encType="multipart/form-data">
        <div className="mb-4">
          <label htmlFor="avatar" className="block text-sm font-medium text-gray-700 mb-2">
            Select Image
          </label>
          <input
            type="file"
            id="avatar"
            name="avatar"
            onChange={handleFileChange}
            accept="image/*"
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4
              file:rounded-md file:border-0
              file:text-sm file:font-semibold
              file:bg-indigo-50 file:text-indigo-700
              hover:file:bg-indigo-100"
          />
        </div>

        {errorMessage && (
          <p className="text-sm text-red-500 mb-4">{errorMessage}</p>
        )}

        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition"
        >
          Upload Avatar
        </button>
      </form>

      {user.avatar && (
        <div className="mt-6 flex flex-col items-center">
          <p className="text-sm text-gray-600 mb-2">Current Avatar:</p>
          {avatar_url && (
            <img
                src={avatar_url}
                alt="User Avatar"
                className="w-24 h-24 rounded-full object-cover border"
            />
        )}
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;
