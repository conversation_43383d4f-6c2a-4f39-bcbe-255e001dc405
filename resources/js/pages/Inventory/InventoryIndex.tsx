import React, { useState, useEffect } from 'react';
import { Head, Link, usePage, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    Package,
    Search,
    Plus,
    Eye,
    Edit,
    Trash2,
    AlertTriangle,
    Filter
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { InventoryItem } from '@/types/store-requisitions';
import { PageProps } from '@inertiajs/core';
import axios from 'axios';

interface InventoryIndexPageProps extends PageProps {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            permissions?: string[];
        };
    };
    items?: InventoryItem[];
    error?: string;
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Inventory Management', href: '/inventory' },
];

export default function InventoryIndex() {
    const { auth, items: initialItems, error: initialError } = usePage<InventoryIndexPageProps>().props;
    const [items, setItems] = useState<InventoryItem[]>(initialItems || []);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [stockFilter, setStockFilter] = useState<string>('all');
    const [error, setError] = useState<string | null>(initialError || null);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 15;

    const user = auth.user;
    const userPermissions = user.permissions || [];
    const canManageInventory = userPermissions.includes('manage-inventory') || userPermissions.includes('store-keep');
    const canViewInventory = userPermissions.includes('view-inventory') || userPermissions.includes('store-keep');

    // Update items when props change
    useEffect(() => {
        setItems(initialItems || []);
        setError(initialError || null);
    }, [initialItems, initialError]);

    // Check URL parameters for filter on mount
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const filterParam = urlParams.get('filter');
        if (filterParam === 'low-stock' || filterParam === 'out-of-stock') {
            setStockFilter(filterParam);
        }
    }, []);

    const refreshInventoryItems = async () => {
        try {
            setLoading(true);
            // Use Inertia to reload the page data
            router.reload({ only: ['items'] });
        } catch {
            // Error refreshing inventory items - handled by UI
            setError('Failed to refresh inventory items');
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (itemId: number) => {
        if (!confirm('Are you sure you want to delete this inventory item?')) {
            return;
        }

        try {
            await axios.delete(`/inventory/${itemId}`);
            // Remove item from local state immediately for better UX
            setItems(items.filter(item => item.id !== itemId));
            window.showToast?.({
                title: 'Success',
                message: 'Inventory item deleted successfully',
                type: 'success'
            });
        } catch (err: unknown) {
            // Error deleting inventory item - handled by UI
            // Refresh the data to ensure consistency
            refreshInventoryItems();
            const errorMessage = err && typeof err === 'object' && 'response' in err
                ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
                : 'Failed to delete inventory item';
            window.showToast?.({
                title: 'Error',
                message: errorMessage || 'Failed to delete inventory item',
                type: 'error'
            });
        }
    };

    // Filter and paginate items
    const allFilteredItems = Array.isArray(items) ? items.filter(item => {
        // Search filter
        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description?.toLowerCase().includes(searchTerm.toLowerCase());

        // Stock filter
        if (stockFilter === 'all') {
            return matchesSearch;
        }

        const stockStatus = getStockStatus(item);
        if (stockFilter === 'low-stock') {
            return matchesSearch && (stockStatus.status === 'low-stock' || stockStatus.status === 'out-of-stock');
        }
        if (stockFilter === 'out-of-stock') {
            return matchesSearch && stockStatus.status === 'out-of-stock';
        }
        if (stockFilter === 'in-stock') {
            return matchesSearch && stockStatus.status === 'in-stock';
        }

        return matchesSearch;
    }) : [];

    const totalItems = allFilteredItems.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const filteredItems = allFilteredItems.slice(startIndex, endIndex);

    // Reset pagination when search or filter changes
    useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, stockFilter]);

    const getStockStatus = (item: InventoryItem) => {
        const quantity = parseFloat(item.quantity_on_hand.toString());
        const reorderLevel = parseFloat(item.reorder_level.toString());

        if (quantity <= 0) {
            return { status: 'out-of-stock', label: 'Out of Stock', variant: 'destructive' as const };
        } else if (reorderLevel > 0 && quantity <= reorderLevel) {
            return { status: 'low-stock', label: 'Low Stock', variant: 'secondary' as const };
        } else {
            return { status: 'in-stock', label: 'In Stock', variant: 'default' as const };
        }
    };

    if (!canViewInventory) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Inventory Management" />
                <div className="flex h-full flex-1 flex-col items-center justify-center p-4">
                    <div className="text-center">
                        <Package className="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 className="mt-2 text-sm font-semibold text-foreground">Access Denied</h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                            You don't have permission to view inventory items.
                        </p>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Inventory Management" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-foreground">Inventory Management</h1>
                        <p className="text-muted-foreground">
                            Manage your inventory items and stock levels
                        </p>
                    </div>
                    {canManageInventory && (
                        <Button asChild>
                            <Link href="/inventory/create">
                                <Plus className="mr-2 h-4 w-4" />
                                Add Item
                            </Link>
                        </Button>
                    )}
                </div>

                {/* Search and Filters */}
                <div className="flex items-center gap-4">
                    <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                            placeholder="Search items..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-9"
                        />
                    </div>
                    <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-muted-foreground" />
                        <Select value={stockFilter} onValueChange={setStockFilter}>
                            <SelectTrigger className="w-40">
                                <SelectValue placeholder="Filter by stock" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Items</SelectItem>
                                <SelectItem value="in-stock">In Stock</SelectItem>
                                <SelectItem value="low-stock">Low Stock</SelectItem>
                                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <Badge variant="secondary" className="text-sm">
                        {filteredItems.length} items
                    </Badge>
                </div>

                {/* Content */}
                {loading ? (
                    <div className="flex h-64 items-center justify-center">
                        <div className="text-center">
                            <Package className="mx-auto h-8 w-8 animate-pulse text-muted-foreground" />
                            <p className="mt-2 text-sm text-muted-foreground">Loading inventory items...</p>
                        </div>
                    </div>
                ) : error ? (
                    <div className="flex h-64 items-center justify-center">
                        <div className="text-center">
                            <AlertTriangle className="mx-auto h-8 w-8 text-destructive" />
                            <p className="mt-2 text-sm text-destructive">{error}</p>
                            <Button variant="outline" onClick={refreshInventoryItems} className="mt-2">
                                Try Again
                            </Button>
                        </div>
                    </div>
                ) : totalItems === 0 ? (
                    <div className="flex h-64 items-center justify-center">
                        <div className="text-center">
                            <Package className="mx-auto h-8 w-8 text-muted-foreground" />
                            <h3 className="mt-2 text-sm font-semibold text-foreground">
                                {searchTerm ? 'No items found' : 'No inventory items'}
                            </h3>
                            <p className="mt-1 text-sm text-muted-foreground">
                                {searchTerm 
                                    ? 'Try adjusting your search terms'
                                    : 'Get started by adding your first inventory item'
                                }
                            </p>
                            {!searchTerm && canManageInventory && (
                                <Button asChild className="mt-4">
                                    <Link href="/inventory/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add Item
                                    </Link>
                                </Button>
                            )}
                        </div>
                    </div>
                ) : (
                    <Card>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Item</TableHead>
                                    <TableHead>SKU</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Stock</TableHead>
                                    <TableHead>Reorder Level</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredItems.map((item) => {
                                    const stockStatus = getStockStatus(item);
                                    return (
                                        <TableRow key={item.id}>
                                            <TableCell className="font-medium">
                                                {item.name}
                                            </TableCell>
                                            <TableCell className="font-mono text-sm">
                                                {item.sku}
                                            </TableCell>
                                            <TableCell className="max-w-xs">
                                                <div className="truncate" title={item.description}>
                                                    {item.description || '-'}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {item.quantity_on_hand} {item.unit_of_measure}
                                            </TableCell>
                                            <TableCell>
                                                {item.reorder_level} {item.unit_of_measure}
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant={stockStatus.variant}>
                                                    {stockStatus.label}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex gap-2 justify-end">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={`/inventory/${item.id}`}>
                                                            <Eye className="h-4 w-4" />
                                                        </Link>
                                                    </Button>
                                                    {canManageInventory && (
                                                        <>
                                                            <Button variant="outline" size="sm" asChild>
                                                                <Link href={`/inventory/${item.id}/edit`}>
                                                                    <Edit className="h-4 w-4" />
                                                                </Link>
                                                            </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleDelete(item.id)}
                                                                className="text-destructive hover:text-destructive"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>

                        {/* Pagination Controls */}
                        {totalPages > 1 && (
                            <div className="flex items-center justify-between px-4 py-3 border-t">
                                <div className="text-sm text-muted-foreground">
                                    Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} items
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                        disabled={currentPage === 1}
                                    >
                                        Previous
                                    </Button>
                                    <span className="text-sm text-muted-foreground">
                                        Page {currentPage} of {totalPages}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                        disabled={currentPage === totalPages}
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        )}
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
