import React, { useState } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
    ArrowLeft,
    Package,
    Edit,
    Building,
    Hash,
    Ruler,
    AlertTriangle,
    CheckCircle,
    Clock
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { InventoryItem, InventoryTransaction } from '@/types/store-requisitions';
import { PageProps } from '@inertiajs/core';

interface ShowInventoryPageProps extends PageProps {
    inventory_item: InventoryItem & {
        transactions: InventoryTransaction[];
    };
    user: {
        id: number;
        name: string;
        email: string;
        permissions?: string[];
    };
    [key: string]: unknown; // Index signature to satisfy PageProps constraint
}

export default function ShowInventory() {
    const { inventory_item, user } = usePage<ShowInventoryPageProps>().props;
    
    const userPermissions = user.permissions || [];
    const canManageInventory = userPermissions.includes('manage-inventory') || userPermissions.includes('store-keep');

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Inventory Management', href: '/inventory' },
        { title: inventory_item.name, href: `/inventory/${inventory_item.id}` },
    ];

    const getStockStatus = () => {
        const quantity = parseFloat(inventory_item.quantity_on_hand.toString());
        const reorderLevel = parseFloat(inventory_item.reorder_level.toString());

        if (quantity <= 0) {
            return { label: 'Out of Stock', variant: 'destructive' as const };
        } else if (reorderLevel > 0 && quantity <= reorderLevel) {
            return { label: 'Low Stock', variant: 'secondary' as const };
        } else {
            return { label: 'In Stock', variant: 'default' as const };
        }
    };

    const stockStatus = getStockStatus();

    // Pagination state for transaction history
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getTransactionTypeIcon = (type: string) => {
        switch (type) {
            case 'stock_in':
                return <CheckCircle className="h-4 w-4 text-success" />;
            case 'stock_out':
                return <AlertTriangle className="h-4 w-4 text-destructive" />;
            case 'adjustment':
                return <Edit className="h-4 w-4 text-info" />;
            default:
                return <Clock className="h-4 w-4 text-muted-foreground" />;
        }
    };

    const getTransactionTypeBadge = (type: string) => {
        switch (type) {
            case 'stock_in':
                return (
                    <Badge
                        style={{
                            backgroundColor: 'var(--status-approved)',
                            color: 'var(--status-approved-foreground)'
                        }}
                        className="font-medium"
                    >
                        Stock In
                    </Badge>
                );
            case 'stock_out':
                return <Badge variant="destructive">Stock Out</Badge>;
            case 'adjustment':
                return (
                    <Badge
                        style={{
                            backgroundColor: 'var(--status-pending)',
                            color: 'var(--status-pending-foreground)'
                        }}
                        className="font-medium"
                    >
                        Adjustment
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${inventory_item.name} - Inventory`} />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <Button variant="outline" asChild>
                        <Link href="/inventory">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Inventory
                        </Link>
                    </Button>
                    {canManageInventory && (
                        <Button asChild>
                            <Link href={`/inventory/${inventory_item.id}/edit`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Item
                            </Link>
                        </Button>
                    )}
                </div>

                {/* Item Details */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Item Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h2 className="text-2xl font-bold text-foreground">{inventory_item.name}</h2>
                                <p className="text-muted-foreground">{inventory_item.description || 'No description provided'}</p>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4">
                                <div className="flex items-center gap-2">
                                    <Hash className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm text-muted-foreground">SKU</p>
                                        <p className="font-medium">{inventory_item.sku}</p>
                                    </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                    <Ruler className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm text-muted-foreground">Unit of Measure</p>
                                        <p className="font-medium">{inventory_item.unit_of_measure}</p>
                                    </div>
                                </div>
                                
                                {inventory_item.branch && (
                                    <div className="flex items-center gap-2">
                                        <Building className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm text-muted-foreground">Branch</p>
                                            <p className="font-medium">{inventory_item.branch.name}</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Stock Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-muted-foreground">Current Stock</p>
                                    <p className="text-2xl font-bold text-foreground">
                                        {inventory_item.quantity_on_hand} {inventory_item.unit_of_measure}
                                    </p>
                                </div>
                                
                                <div>
                                    <p className="text-sm text-muted-foreground">Reorder Level</p>
                                    <p className="text-lg font-semibold text-foreground">
                                        {inventory_item.reorder_level} {inventory_item.unit_of_measure}
                                    </p>
                                </div>
                            </div>
                            
                            <div>
                                <p className="text-sm text-muted-foreground mb-2">Stock Status</p>
                                <Badge variant={stockStatus.variant} className="text-sm">
                                    {stockStatus.label}
                                </Badge>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Transaction History */}
                <Card>
                    <CardHeader>
                        <CardTitle>Transaction History</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {inventory_item.transactions && inventory_item.transactions.length > 0 ? (
                            (() => {
                                // Apply pagination to transactions
                                const totalTransactions = inventory_item.transactions.length;
                                const totalPages = Math.ceil(totalTransactions / itemsPerPage);
                                const startIndex = (currentPage - 1) * itemsPerPage;
                                const endIndex = startIndex + itemsPerPage;
                                const paginatedTransactions = inventory_item.transactions.slice(startIndex, endIndex);

                                return (
                                    <>
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Date</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Quantity Change</TableHead>
                                                    <TableHead>Balance After</TableHead>
                                                    <TableHead>User</TableHead>
                                                    <TableHead>Reference</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {paginatedTransactions.map((transaction) => (
                                                    <TableRow key={transaction.id}>
                                                        <TableCell>
                                                            {formatDate(transaction.transaction_date)}
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-2">
                                                                {getTransactionTypeIcon(transaction.transaction_type)}
                                                                {getTransactionTypeBadge(transaction.transaction_type)}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <span className={
                                                                parseFloat(transaction.quantity_change.toString()) > 0
                                                                    ? 'text-success font-medium'
                                                                    : 'text-destructive font-medium'
                                                            }>
                                                                {parseFloat(transaction.quantity_change.toString()) > 0 ? '+' : ''}
                                                                {transaction.quantity_change} {inventory_item.unit_of_measure}
                                                            </span>
                                                        </TableCell>
                                                        <TableCell>
                                                            {transaction.balance_after} {inventory_item.unit_of_measure}
                                                        </TableCell>
                                                        <TableCell>
                                                            {transaction.user?.first_name} {transaction.user?.last_name}
                                                        </TableCell>
                                                        <TableCell>
                                                            {transaction.reference_number || '-'}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>

                            {/* Pagination Controls */}
                            {totalPages > 1 && (
                                <div className="flex items-center justify-between px-4 py-3 border-t">
                                    <div className="text-sm text-muted-foreground">
                                        Showing {startIndex + 1} to {Math.min(endIndex, totalTransactions)} of {totalTransactions} transactions
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                            disabled={currentPage === 1}
                                        >
                                            Previous
                                        </Button>
                                        <span className="text-sm text-muted-foreground">
                                            Page {currentPage} of {totalPages}
                                        </span>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                            disabled={currentPage === totalPages}
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </>
                    );
                })()
                        ) : (
                            <div className="text-center py-8 text-muted-foreground">
                                <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No transaction history available</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
