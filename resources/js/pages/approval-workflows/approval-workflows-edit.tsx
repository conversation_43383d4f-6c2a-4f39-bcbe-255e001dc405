import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { PlusCircle, Trash2, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';

interface Organization {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
}

interface Department {
    id: number;
    name: string;
}

interface Role {
    id: number;
    name: string;
}

interface User {
    id: number;
    name: string;
    email: string;
    roles: string;
    display_name: string;
}

interface WorkflowStep {
    id?: number;
    step_number: number;
    role_id: string | number | null;
    approver_user_id?: number | null;
    description: string;
}

interface Workflow {
    id: number;
    name: string;
    organization_id: number;
    branch_id?: number;
    department_id?: number;
    is_default: boolean;
    description: string;
    steps: WorkflowStep[];
    organization: Organization;
}

export interface WorkflowStepFormData {
    id?: number;
    step_number: number;
    role_id: string | null;
    approver_user_id?: number | null;
    description: string;
    [key: string]: string | number | null | undefined;
}

export interface WorkflowFormData {
    name: string;
    organization_id: string;
    branch_id: string | null;
    department_id: string | null;
    is_default: boolean;
    description: string;
    steps: WorkflowStepFormData[];
    [key: string]: string | boolean | null | WorkflowStepFormData[];
}

interface FormErrors extends Record<string, string | undefined> {
    name?: string;
    organization_id?: string;
    branch_id?: string;
    department_id?: string;
    description?: string;
    [key: `steps.${number}.role_id`]: string | undefined;
    [key: `steps.${number}.approver_user_id`]: string | undefined;
    [key: `steps.${number}.description`]: string | undefined;
}

interface Props {
    workflow: Workflow;
    organizations: Organization[];
    branches: Branch[];
    departments: Department[];
    roles: Role[];
    users: User[];
}

export default function ApprovalWorkflowsEdit({ workflow, organizations = [], branches = [], departments = [], roles = [], users = [] }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: route('dashboard'),
        },
        {
            title: 'Approval Workflows',
            href: route('approval-workflows.index'),
        },
        {
            title: workflow.name,
            href: route('approval-workflows.show', { id: workflow.id }),
        },
        {
            title: 'Edit',
            href: route('approval-workflows.edit', { id: workflow.id }),
        },
    ];

    const { data, setData, put, processing, errors } = useForm<WorkflowFormData>({
        name: workflow.name,
        organization_id: workflow.organization_id.toString(),
        branch_id: workflow.branch_id?.toString() || null,
        department_id: workflow.department_id?.toString() || null,
        is_default: workflow.is_default,
        description: workflow.description || '',
        steps: workflow.steps.map((step) => ({
            id: step.id,
            step_number: step.step_number,
            role_id: step.role_id ? step.role_id.toString() : null,
            approver_user_id: step.approver_user_id || null,
            description: step.description,
        })),
    });

    const updateData = <K extends keyof WorkflowFormData>(key: K, value: WorkflowFormData[K]) => {
        setData(prev => ({ ...prev, [key]: value }));
    };

    const getStepError = (index: number, field: 'role_id' | 'approver_user_id' | 'description'): string | undefined => {
        const errorKey = `steps.${index}.${field}` as keyof FormErrors;
        return (errors as FormErrors)[errorKey];
    };

    const addStep = () => {
        updateData('steps', [
            ...data.steps,
            {
                id: undefined,
                step_number: data.steps.length + 1,
                role_id: null,
                approver_user_id: null,
                description: '',
            },
        ]);
    };

    const removeStep = (index: number) => {
        const newSteps = [...data.steps];
        newSteps.splice(index, 1);
        // Reorder remaining steps
        newSteps.forEach((step, idx) => {
            step.step_number = idx + 1;
        });
        updateData('steps', newSteps);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        const formData = { ...data };
        if (formData.branch_id === 'all') formData.branch_id = null;
        if (formData.department_id === 'all') formData.department_id = null;

        put(route('approval-workflows.update', { id: workflow.id }), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                toast({
                    title: "Success!",
                    description: "Workflow has been updated successfully",
                    variant: "default",
                    duration: 5000,
                    className: "toast-bg toast-border toast-text font-semibold shadow-lg",
                });
            },
            onError: (errors) => {
                console.error("Update failed:", errors);
                toast({
                    title: "Error",
                    description: "Failed to update workflow. Please check the form and try again.",
                    variant: "destructive",
                    duration: 3000,
                    className: "bg-destructive border-destructive text-destructive-foreground font-semibold dark:bg-destructive shadow-lg",
                });
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Workflow: ${workflow.name}`} />
            <Toaster />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground text-2xl font-bold md:text-3xl">Edit Workflow</h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <div className="space-y-6">
                        <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                            <CardHeader className="p-6">
                                <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Workflow Information</CardTitle>
                                <CardDescription className="text-sm text-gray-500 dark:text-gray-400">Update the workflow details</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Workflow Name
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => updateData('name', e.target.value)}
                                        className="border-muted-foreground/30 text-muted-foreground/90 focus:border-primary/50 focus:ring-primary/70 dark:border-muted-foreground/70 dark:bg-muted-foreground/80 dark:text-muted-foreground/100 bg-foreground/80 mt-1 block w-full rounded-md shadow-sm"
                                    />
                                    {(errors as FormErrors).name && <p className="text-sm text-red-500">{(errors as FormErrors).name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="organization_id" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Organization
                                    </Label>
                                    <Select value={data.organization_id} onValueChange={(value) => updateData('organization_id', value)}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {organizations &&
                                                organizations.map((org) => (
                                                    <SelectItem key={org.id} value={org.id.toString()}>
                                                        {org.name}
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                    {(errors as FormErrors).organization_id && <p className="text-sm text-red-500">{(errors as FormErrors).organization_id}</p>}
                                </div>

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="branch_id" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Branch (Optional)
                                        </Label>
                                        <Select
                                            value={data.branch_id || 'all'}
                                            onValueChange={value => updateData('branch_id', value === 'all' ? null : value)}
                                        >
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="All Branches" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Branches</SelectItem>
                                                {branches &&
                                                    branches.map((branch) => (
                                                        <SelectItem key={branch.id} value={branch.id.toString()}>
                                                            {branch.name}
                                                        </SelectItem>
                                                    ))}
                                            </SelectContent>
                                        </Select>
                                        {(errors as FormErrors).branch_id && <p className="text-sm text-red-500">{(errors as FormErrors).branch_id}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="department_id" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Department (Optional)
                                        </Label>
                                        <Select
                                            value={data.department_id || 'all'}
                                            onValueChange={value => updateData('department_id', value === 'all' ? null : value)}
                                        >
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="All Departments" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Departments</SelectItem>
                                                {departments &&
                                                    departments.map((dept) => (
                                                        <SelectItem key={dept.id} value={dept.id.toString()}>
                                                            {dept.name}
                                                        </SelectItem>
                                                    ))}
                                            </SelectContent>
                                        </Select>
                                        {(errors as FormErrors).department_id && <p className="text-sm text-red-500">{(errors as FormErrors).department_id}</p>}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Description (Optional)
                                    </Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => updateData('description', e.target.value)}
                                        className="focus:border-primary/50 focus:ring-primary/70 mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                    />
                                    {(errors as FormErrors).description && <p className="text-sm text-red-500">{(errors as FormErrors).description}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                            <CardHeader className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Approval Steps</CardTitle>
                                        <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                            Update the approval steps and their order
                                        </CardDescription>
                                    </div>
                                    <Button type="button" onClick={addStep} variant="outline" className="flex items-center">
                                        <PlusCircle className="mr-2 h-4 w-4" />
                                        Add Step
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                {data.steps &&
                                    data.steps.map((step, index) => (
                                        <div key={index} className="relative rounded-lg border border-gray-200 p-6 dark:border-gray-700">
                                            <div className="absolute top-4 right-4">
                                                {index > 0 && (
                                                    <Button type="button" variant="destructive" size="sm" onClick={() => removeStep(index)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                )}
                                            </div>

                                            <div className="space-y-4">
                                                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Step {step.step_number}</h4>

                                                {/* Responsive grid for form fields */}
                                                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                                    <div className="space-y-2">
                                                        <Label
                                                            htmlFor={`steps.${index}.role_id`}
                                                            className="text-sm font-medium text-gray-700 dark:text-gray-300"
                                                        >
                                                            Approver Role
                                                        </Label>
                                                        <Select
                                                            value={step.role_id?.toString() || 'pending'}
                                                            onValueChange={(value) => {
                                                                const newSteps = [...data.steps];
                                                                newSteps[index].role_id = value;
                                                                updateData('steps', newSteps);
                                                            }}
                                                        >
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select role" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {roles &&
                                                                    roles.map((role) => (
                                                                        <SelectItem key={role.id} value={role.id.toString()}>
                                                                            {role.name}
                                                                        </SelectItem>
                                                                    ))}
                                                            </SelectContent>
                                                        </Select>
                                                        {getStepError(index, 'role_id') && (
                                                            <p className="text-sm text-red-500">{getStepError(index, 'role_id')}</p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label
                                                            htmlFor={`steps.${index}.approver_user_id`}
                                                            className="text-sm font-medium text-gray-700 dark:text-gray-300"
                                                        >
                                                            Specific Approver (Optional)
                                                        </Label>
                                                        <Select
                                                            value={step.approver_user_id?.toString() || 'none'}
                                                            onValueChange={(value) => {
                                                                const newSteps = [...data.steps];
                                                                newSteps[index].approver_user_id = value !== 'none' ? parseInt(value) : null;
                                                                updateData('steps', newSteps);
                                                            }}
                                                        >
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select specific user " />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="none">No specific user (use role)</SelectItem>
                                                                {users &&
                                                                    users.map((user) => (
                                                                        <SelectItem key={user.id} value={user.id.toString()}>
                                                                            {user.display_name}
                                                                        </SelectItem>
                                                                    ))}
                                                            </SelectContent>
                                                        </Select>
                                                        <p className="text-xs text-gray-500">
                                                            If selected, this specific user will be the approver regardless of role.
                                                        </p>
                                                        {getStepError(index, 'approver_user_id') && (
                                                            <p className="text-sm text-destructive/70">{getStepError(index, 'approver_user_id')}</p>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Description field spans full width */}
                                                <div className="space-y-2">
                                                    <Label
                                                        htmlFor={`steps.${index}.description`}
                                                        className="text-sm font-medium text-gray-700 dark:text-gray-300"
                                                    >
                                                        Step Description
                                                    </Label>
                                                    <Input
                                                        id={`steps.${index}.description`}
                                                        value={step.description || ''}
                                                        onChange={(e) => {
                                                            const newSteps = [...data.steps];
                                                            newSteps[index].description = e.target.value;
                                                            updateData('steps', newSteps);
                                                        }}
                                                        className="mt-1 block w-full"
                                                        placeholder="Describe this approval step"
                                                    />
                                                    {getStepError(index, 'description') && (
                                                        <p className="text-sm text-red-500">{getStepError(index, 'description')}</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                            </CardContent>
                        </Card>

                        <div className="mt-6 flex justify-end space-x-2">
                            <Button
                                variant="outline"
                                type="button"
                                onClick={() => window.history.back()}
                                className="border border-gray-300 px-4 py-2 dark:border-gray-700"
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing} className="bg-primary px-4 py-2 text-background/100 flex items-center gap-2">
                                {processing && <Loader2 className="h-4 w-4 animate-spin" />}
                                Update Workflow
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}