import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { PlusCircle, Trash2, Loader2 } from 'lucide-react';
import { Toaster } from '@/components/ui/toaster';
import { useForm } from '@inertiajs/react';
import { toast } from '@/components/ui/use-toast';
import AppLayout from '@/layouts/app-layout';

interface Organization {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
}

interface Department {
    id: number;
    name: string;
}

interface Role {
    id: number;
    name: string;
    description?: string;
}

interface User {
    id: number;
    name: string;
    email: string;
    roles: string;
    display_name: string;
}

interface Step {
    step_number: number;
    role_id: string | null;
    approver_user_id: number | null;
    description: string;
    [key: string]: string | number | null;
}

interface WorkflowFormData {
    name: string;
    organization_id: number | string;
    branch_id: string | null;
    department_id: string | null;
    is_default: boolean;
    description: string;
    steps: Step[];
    [key: string]: string | number | boolean | null | Step[];
}

interface FormErrors extends Record<string, string | undefined> {
    name?: string;
    organization_id?: string;
    branch_id?: string;
    department_id?: string;
    description?: string;
    [key: `steps.${number}.role_id`]: string | undefined;
    [key: `steps.${number}.approver_user_id`]: string | undefined;
    [key: `steps.${number}.description`]: string | undefined;
}

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Approval Workflows',
        href: '/approval-workflows',
    },
    {
        title: 'Create',
        href: '/approval-workflows/create',
    },
];

interface Props {
    organizations: Organization[];
    branches: Branch[];
    departments: Department[];
    roles: Role[];
    users: User[];
    organizationId?: number;
}


export default function ApprovalWorkflowsCreate({
    organizations = [],
    branches = [],
    departments = [],
    roles = [],
    users = [],
    organizationId
}: Props) {
    const { data, setData, post, processing, errors } = useForm<WorkflowFormData>({
        name: '',
        organization_id: organizationId || '',
        branch_id: null,
        department_id: null,
        is_default: false,
        description: '',
        steps: [
            {
                step_number: 1,
                role_id: null,
                approver_user_id: null,
                description: '',
            },
        ],
    });

    const updateData = <K extends keyof WorkflowFormData>(key: K, value: WorkflowFormData[K]) => {
        setData(prev => ({ ...prev, [key]: value }));
    };

    const getStepError = (index: number, field: 'role_id' | 'approver_user_id' | 'description'): string | undefined => {
        const errorKey = `steps.${index}.${field}` as keyof FormErrors;
        return (errors as FormErrors)[errorKey];
    };

    const addStep = () => {
        updateData('steps', [
            ...data.steps,
            {
                step_number: data.steps.length + 1,
                role_id: null,
                approver_user_id: null,
                description: '',
            },
        ]);
    };

    const removeStep = (index: number) => {
        const newSteps = [...data.steps];
        newSteps.splice(index, 1);
        // Reorder remaining steps
        newSteps.forEach((step, idx) => {
            step.step_number = idx + 1;
        });
        updateData('steps', newSteps);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Prepare the data for submission
        const submissionData = {
            ...data,
            branch_id: data.branch_id === 'all' ? null : data.branch_id,
            department_id: data.department_id === 'all' ? null : data.department_id,
        };

        // Update the form data and submit
        setData(submissionData);

        post(route('approval-workflows.store'), {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                toast({
                    title: "Success!",
                    description: "Workflow has been created successfully",
                    variant: "default",
                    duration: 5000,
                    className: "toast-bg toast-border toast-text font-semibold shadow-lg",
                });
            },
            onError: (errors) => {
                console.error("Form submission errors:", errors);
                toast({
                    title: "Error",
                    description: "Failed to create workflow. Please check the form for errors.",
                    variant: "destructive",
                    duration: 5000,
                    className: "toast-bg toast-border toast-text font-semibold shadow-lg",
                });
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Toaster />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground text-2xl font-bold md:text-3xl">Create New Workflow</h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <div className="space-y-6">
                        <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                            <CardHeader className="p-6">
                                <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Workflow Information</CardTitle>
                                <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                    Enter the details for the new approval workflow
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Workflow Name
                                    </Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => updateData('name', e.target.value)}
                                        className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                    />
                                    {(errors as FormErrors).name && <p className="text-sm text-red-500">{(errors as FormErrors).name}</p>}
                                </div>

                                {!organizationId && (
                                    <div className="space-y-2">
                                        <Label htmlFor="organization_id" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Organization
                                        </Label>
                                        <Select
                                            value={data.organization_id ? data.organization_id.toString() : ''}
                                            onValueChange={(value) => updateData('organization_id', value)}
                                        >
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="Select organization" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {organizations.map((org) => (
                                                    <SelectItem key={org.id} value={org.id.toString()}>
                                                        {org.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {(errors as FormErrors).organization_id && <p className="text-sm text-red-500">{(errors as FormErrors).organization_id}</p>}
                                    </div>
                                )}

                                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="branch_id" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Branch (Optional)
                                        </Label>
                                        <Select
                                            value={data.branch_id || 'all'}
                                            onValueChange={value => updateData('branch_id', value === 'all' ? null : value)}
                                        >
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="All Branches" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Branches</SelectItem>
                                                {branches.map((branch) => (
                                                    <SelectItem key={branch.id} value={branch.id.toString()}>
                                                        {branch.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {(errors as FormErrors).branch_id && <p className="text-sm text-red-500">{(errors as FormErrors).branch_id}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="department_id" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Department (Optional)
                                        </Label>
                                        <Select
                                            value={data.department_id || 'all'}
                                            onValueChange={value => updateData('department_id', value === 'all' ? null : value)}
                                        >
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="All Departments" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Departments</SelectItem>
                                                {departments.map((dept) => (
                                                    <SelectItem key={dept.id} value={dept.id.toString()}>
                                                        {dept.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {(errors as FormErrors).department_id && <p className="text-sm text-red-500">{(errors as FormErrors).department_id}</p>}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Description (Optional)
                                    </Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => updateData('description', e.target.value)}
                                        className="mt-1 block w-full rounded-md border-gray-300 bg-white text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                                    />
                                    {(errors as FormErrors).description && <p className="text-sm text-red-500">{(errors as FormErrors).description}</p>}
                                </div>

                                <div className="flex items-center space-x-3">
                                    <Switch
                                        id="is_default"
                                        checked={data.is_default}
                                        onCheckedChange={(checked) => updateData('is_default', checked)}
                                    />
                                    <div className="space-y-1">
                                        <Label htmlFor="is_default" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Set as Default Workflow
                                        </Label>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            This workflow will be automatically used for requisitions in this context (department/branch/organization)
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-black">
                            <CardHeader className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Approval Steps</CardTitle>
                                        <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                                            Configure the approval steps and their order
                                        </CardDescription>
                                    </div>
                                    <Button type="button" onClick={addStep} variant="outline" className="flex items-center">
                                        <PlusCircle className="mr-2 h-4 w-4" />
                                        Add Step
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                {data.steps.map((step, index) => (
                                    <div key={index} className="relative rounded-lg border border-gray-200 p-6 dark:border-gray-700">
                                        <div className="absolute top-4 right-4">
                                            {index > 0 && (
                                                <Button type="button" variant="destructive" size="sm" onClick={() => removeStep(index)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>

                                        <div className="space-y-4">
                                            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Step {step.step_number}</h4>

                                            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                                                <div className="space-y-2">
                                                    <Label
                                                        htmlFor={`steps.${index}.approver_user_id`}
                                                        className="text-sm font-medium text-gray-700 dark:text-gray-300"
                                                    >
                                                        Specific Approver (Optional)
                                                    </Label>
                                                    <Select
                                                        value={step.approver_user_id?.toString() || 'none'}
                                                        onValueChange={value => {
                                                            const newSteps = [...data.steps];
                                                            newSteps[index].approver_user_id = value !== 'none' ? parseInt(value) : null;
                                                            updateData('steps', newSteps);
                                                        }}
                                                    >
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select specific user (optional)" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="none">No specific user (use role)</SelectItem>
                                                            {users.map(user => (
                                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                                    {user.display_name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    <p className="text-xs text-gray-500">
                                                        If selected, this specific user will be the approver regardless of role.
                                                    </p>
                                                    {getStepError(index, 'approver_user_id') && (
                                                        <p className="text-sm text-red-500">{getStepError(index, 'approver_user_id')}</p>
                                                    )}
                                                </div>

                                                {!step.approver_user_id && (
                                                    <div className="space-y-2">
                                                        <Label htmlFor={`steps.${index}.role_id`} className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                            Approver Role
                                                        </Label>
                                                        <Select
                                                            value={step.role_id || ''}
                                                            onValueChange={value => {
                                                                const newSteps = [...data.steps];
                                                                newSteps[index].role_id = value || null;
                                                                updateData('steps', newSteps);
                                                            }}
                                                        >
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select role" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {roles.map(role => (
                                                                    <SelectItem key={role.id} value={role.id.toString()}>
                                                                        {role.name}
                                                                    </SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                        {getStepError(index, 'role_id') && (
                                                            <p className="text-sm text-red-500">{getStepError(index, 'role_id')}</p>
                                                        )}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="space-y-2">
                                                <Label
                                                    htmlFor={`steps.${index}.description`}
                                                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                                                >
                                                    Step Description
                                                </Label>
                                                <Input
                                                    id={`steps.${index}.description`}
                                                    value={step.description || ''}
                                                    onChange={(e) => {
                                                        const newSteps = [...data.steps];
                                                        newSteps[index].description = e.target.value;
                                                        updateData('steps', newSteps);
                                                    }}
                                                    className="mt-1 block w-full"
                                                    placeholder="Describe this approval step"
                                                />
                                                {getStepError(index, 'description') && (
                                                    <p className="text-sm text-red-500">{getStepError(index, 'description')}</p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        <div className="mt-6 flex justify-end space-x-2">
                            <Button
                                variant="outline"
                                type="button"
                                onClick={() => window.history.back()}
                                className="border border-gray-300 px-4 py-2 dark:border-gray-700"
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing} className="bg-primary px-4 py-2 text-primary-foreground flex items-center gap-2">
                                {processing && <Loader2 className="h-4 w-4 animate-spin" />}
                                Create Workflow
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}