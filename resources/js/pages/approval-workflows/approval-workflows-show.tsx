import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, ListOrdered, Pencil, Trash2, Users } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface Organization {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
}

interface Department {
    id: number;
    name: string;
}

interface Role {
    id: number;
    name: string;
}

interface User {
    id: number;
    first_name: string;
    last_name: string;
}

interface WorkflowStep {
    id: number;
    step_number: number;
    role_id: number | null;
    description: string;
    role?: Role;
    approver?: User;
}

interface Workflow {
    id: number;
    name: string;
    description: string | null;
    is_default: boolean;
    organization: Organization;
    branch?: Branch | null;
    department?: Department | null;
    steps: WorkflowStep[];
}

interface Props {
    workflow: Workflow;
}

export default function ApprovalWorkflowsShow({ workflow }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: route('dashboard'),
        },
        {
            title: 'Approval Workflows',
            href: route('approval-workflows.index'),
        },
        {
            title: workflow.name,
            href: route('approval-workflows.show', { id: workflow.id }),
        },
    ];

    const handleDelete = () => {
        if (!confirm('Are you sure you want to delete this workflow? This action cannot be undone.')) return;
        router.delete(route('approval-workflows.destroy', { id: workflow.id }), {
            onSuccess: () => {
                toast({
                    title: 'Deleted!',
                    description: 'Workflow deleted successfully.',
                    variant: 'default',
                    className: 'toast-bg toast-border toast-text',
                });
            },
            onError: () => {
                toast({
                    title: 'Error',
                    description: 'Failed to delete workflow.',
                    variant: 'destructive',
                    className: 'toast-bg toast-border toast-text',
                });
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Workflow: ${workflow.name}`} />
            <div className=" flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-2xl font-bold text-gray-800 md:text-3xl dark:text-gray-100">
                        Workflow Details
                    </h1>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            asChild
                            className="focus:ring-primary inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-900 dark:focus:ring-offset-gray-800"
                        >
                            <Link href={route('approval-workflows.edit', { id: workflow.id })}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                            </Link>
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDelete}
                            className="inline-flex items-center rounded-md border border-transparent bg-destructive/60 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-destructive/70 focus:ring-2 focus:ring-destructive/50 focus:ring-offset-2 focus:outline-none dark:bg-red-500 dark:hover:bg-destructive/60 dark:focus:ring-offset-muted-foreground/80"
                        >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                    <Card className="rounded-lg border border-gray-200 bg-white shadow-md md:col-span-1 dark:border-gray-700 dark:bg-black">
                        <CardHeader className="p-6">
                            <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Workflow Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6 p-6">
                            <div className="flex items-center">
                                <div className="bg-primary/10 dark:bg-primary/90 flex h-10 w-10 items-center justify-center rounded-full">
                                    <ListOrdered className="text-primary/60 dark:text-primary/40 h-5 w-5" />
                                </div>
                                <div className="ml-4">
                                    <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">{workflow.name}</h2>
                                    {workflow.description && <p className="text-sm text-gray-500 dark:text-gray-400">{workflow.description}</p>}
                                </div>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</h3>
                                {workflow.is_default && (
                                    <Badge variant="default" className="mt-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                                        Default Workflow
                                    </Badge>
                                )}
                            </div>

                            <div>
                                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Organization</h3>
                                <div className="mt-1 flex items-center">
                                    <Building2 className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                                    <span className="text-gray-900 dark:text-gray-100">{workflow.organization.name}</span>
                                </div>
                            </div>

                            {workflow.branch && (
                                <div>
                                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Branch</h3>
                                    <div className="mt-1 flex items-center">
                                        <Building2 className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                                        <span className="text-gray-900 dark:text-gray-100">{workflow.branch.name}</span>
                                    </div>
                                </div>
                            )}

                            {workflow.department && (
                                <div>
                                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Department</h3>
                                    <div className="mt-1 flex items-center">
                                        <Users className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                                        <span className="text-gray-900 dark:text-gray-100">{workflow.department.name}</span>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card className="rounded-lg border border-gray-200 bg-white shadow-md md:col-span-2 dark:border-gray-700 dark:bg-black">
                        <CardHeader className="p-6">
                            <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">Approval Steps</CardTitle>
                        </CardHeader>
                        <CardContent className="p-6">
                            <div className="space-y-4">
                                {workflow.steps.map((step) => (
                                    <div
                                        key={step.id}
                                        className="flex items-start rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
                                    >
                                        <div className="bg-primary/10 text-primary/60 dark:bg-primary/90 dark:text-primary/40 flex h-8 w-8 items-center justify-center rounded-full font-semibold">
                                            {step.step_number}
                                        </div>
                                        <div className="ml-4 flex-1">
                                            <div className="flex items-center justify-between">
                                                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                                                    {step.approver
                                                        ? `${step.approver.first_name} ${step.approver.last_name}`
                                                        : step.role?.name || 'No approver assigned'
                                                    }
                                                </h4>
                                                <Badge variant="outline" className="text-muted-foreground/60 dark:text-muted-foreground/70">
                                                    {step.approver ? 'Specific User' : 'Role-based'}
                                                </Badge>
                                            </div>
                                            <div className="mt-2 space-y-1">
                                                {step.approver ? (
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        <span className="font-medium">Specific Approver:</span> This step requires approval from the assigned user.
                                                    </p>
                                                ) : step.role ? (
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        <span className="font-medium">Role-based Approval:</span> Any user with the "{step.role.name}" role can approve this step.
                                                    </p>
                                                ) : null}

                                                {step.description && (
                                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                                        <span className="font-medium">Description:</span> {step.description}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
