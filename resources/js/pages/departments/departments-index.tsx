import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Briefcase, Building2, PlusCircle, User } from 'lucide-react';

interface DepartmentsIndexProps {
    departments: {
        id: number;
        name: string;
        organization: {
            id: number;
            name: string;
        };
        branch: {
            id: number;
            name: string;
        };
        head_of_department?: {
            id: number;
            first_name: string;
            last_name: string;
        } | null;
    }[];
    isPlatformAdmin: boolean;
    organizationId?: number;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Departments',
        href: '/departments',
    },
];

export default function DepartmentsIndex({ departments, isPlatformAdmin }: DepartmentsIndexProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Departments" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/100 text-2xl font-bold">Departments</h1>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href="/departments/create">
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                New Department
                            </span>
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>All Departments</CardTitle>
                        <CardDescription>
                            {isPlatformAdmin ? 'Manage all departments across the platform' : 'Manage departments in your organization'}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {departments.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Briefcase className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No departments found</h3>
                                    <p className="text-muted-foreground text-sm">Get started by creating a new department.</p>
                                </div>
                            ) : (
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Department</TableHead>
                                            {isPlatformAdmin && <TableHead>Organization</TableHead>}
                                            <TableHead>Branch</TableHead>
                                            <TableHead>Head of Department</TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {departments.map((department) => (
                                            <TableRow key={department.id}>
                                                <TableCell>
                                                    <div className="flex items-center">
                                                        <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                                            <Briefcase className="text-primary h-4 w-4" />
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium">{department.name}</div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                {isPlatformAdmin && (
                                                    <TableCell>
                                                        <div className="flex items-center">
                                                            <div className="bg-secondary-foreground/90 flex h-6 w-6 items-center justify-center rounded-full">
                                                                <Building2 className="text-background/100 h-3 w-3" />
                                                            </div>
                                                            <span className="ml-2 text-sm">{department.organization.name}</span>
                                                        </div>
                                                    </TableCell>
                                                )}
                                                <TableCell>
                                                    <div className="text-sm">{department.branch.name}</div>
                                                </TableCell>
                                                <TableCell>
                                                    {department.head_of_department ? (
                                                        <div className="flex items-center">
                                                            <div className="bg-primary-light/10 flex h-6 w-6 items-center justify-center rounded-full">
                                                                <User className="text-primary h-3 w-3" />
                                                            </div>
                                                            <span className="ml-2 text-sm">
                                                                {department.head_of_department.first_name} {department.head_of_department.last_name}
                                                            </span>
                                                        </div>
                                                    ) : (
                                                        <Badge
                                                            variant="outline"
                                                            className="text-secondary-foreground/100 border-accent/100 bg-secondary/100 dark:border-accent/80"
                                                        >
                                                            No HOD Assigned
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex justify-end gap-2">
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/departments/${department.id}`}>View</Link>
                                                        </Button>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/departments/${department.id}/edit`}>Edit</Link>
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
