import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Building2, GitBranch, Pencil, Trash2, Users } from 'lucide-react';

interface DepartmentsShowProps {
    department: {
        id: number;
        name: string;
        organization: {
            id: number;
            name: string;
        };
        branch: {
            id: number;
            name: string;
        };
        head_of_department?: {
            id: number;
            first_name: string;
            last_name: string;
            email: string;
            avatar?: string | null;
        } | null;
        users: User[];
    };
}

export default function DepartmentsShow({ department }: DepartmentsShowProps) {
    const getInitials = useInitials();

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Departments',
            href: '/departments',
        },
        {
            title: department.name,
            href: `/departments/${department.id}`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Department: ${department.name}`} />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-2 sm:p-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-xl font-bold sm:text-2xl">Department Details</h1>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild className="flex-1 sm:flex-none">
                            <Link href={`/departments/${department.id}/edit`}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive" className="flex-1 sm:flex-none">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="md:col-span-1">
                        <CardHeader>
                            <CardTitle className="text-lg sm:text-xl">Department Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Name</h3>
                                <p className="text-lg font-semibold">{department.name}</p>
                            </div>

                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Organization</h3>
                                <div className="mt-1 flex items-center">
                                    <Building2 className="text-primary mr-2 h-4 w-4" />
                                    <Link href={`/organizations/${department.organization.id}`} className="text-primary hover:underline">
                                        {department.organization.name}
                                    </Link>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Branch</h3>
                                <div className="mt-1 flex items-center">
                                    <GitBranch className="text-primary mr-2 h-4 w-4" />
                                    <Link href={`/branches/${department.branch.id}`} className="text-primary hover:underline">
                                        {department.branch.name}
                                    </Link>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Head of Department</h3>
                                {department.head_of_department ? (
                                    <div className="mt-2 flex items-center">
                                        <Avatar className="h-8 w-8">
                                            <AvatarImage
                                                src={department.head_of_department.avatar || undefined}
                                                alt={`${department.head_of_department.first_name} ${department.head_of_department.last_name}`}
                                            />
                                            <AvatarFallback>
                                                {getInitials(`${department.head_of_department.first_name} ${department.head_of_department.last_name}`)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="ml-3">
                                            <Link
                                                href={`/users/${department.head_of_department.id}`}
                                                className="text-primary text-sm font-medium hover:underline"
                                            >
                                                {department.head_of_department.first_name} {department.head_of_department.last_name}
                                            </Link>
                                            <p className="text-muted-foreground text-xs">{department.head_of_department.email}</p>
                                        </div>
                                    </div>
                                ) : (
                                    <Badge variant="outline" className="bg-primary/100 border-accent/80 text-background/100 mt-2">
                                        No HOD Assigned
                                    </Badge>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="md:col-span-1 lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="text-lg sm:text-xl">Department Members</CardTitle>
                            <CardDescription>Users assigned to this department</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {department.users.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-6 sm:py-8">
                                    <Users className="text-muted-foreground h-10 w-10 sm:h-12 sm:w-12" />
                                    <h3 className="mt-4 text-base font-medium sm:text-lg">No users assigned</h3>
                                    <p className="text-muted-foreground text-center text-xs sm:text-sm">Assign users to this department to see them here.</p>
                                </div>
                            ) : (
                                <div className="-mx-4 -mt-2 overflow-x-auto sm:mx-0 sm:mt-0">
                                    <div className="inline-block min-w-full align-middle">
                                        <div className="overflow-hidden rounded-md border">
                                            <table className="min-w-full divide-y divide-border">
                                                <thead className="bg-muted/50">
                                                    <tr>
                                                        <th className="text-muted-foreground hidden px-4 py-3 text-left text-xs font-medium sm:table-cell sm:py-3.5">
                                                            User
                                                        </th>
                                                        <th className="text-muted-foreground px-4 py-3 text-left text-xs font-medium sm:py-3.5">
                                                            Email
                                                        </th>
                                                        <th className="text-muted-foreground hidden px-4 py-3 text-left text-xs font-medium sm:table-cell sm:py-3.5">
                                                            Role
                                                        </th>
                                                        <th className="text-muted-foreground px-4 py-3 text-right text-xs font-medium sm:py-3.5">
                                                            Actions
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="divide-y divide-border bg-card">
                                                    {department.users.map((user) => (
                                                        <tr key={user.id}>
                                                            <td className="w-full max-w-0 p-4 sm:w-auto sm:max-w-none">
                                                                <div className="flex items-center">
                                                                    <Avatar className="h-8 w-8 shrink-0">
                                                                        <AvatarImage
                                                                            src={user.avatar || undefined}
                                                                            alt={`${user.first_name} ${user.last_name}`}
                                                                        />
                                                                        <AvatarFallback>
                                                                            {getInitials(`${user.first_name} ${user.last_name}`)}
                                                                        </AvatarFallback>
                                                                    </Avatar>
                                                                    <div className="ml-3 truncate">
                                                                        <div className="truncate text-sm font-medium">
                                                                            {user.first_name} {user.last_name}
                                                                        </div>
                                                                        <div className="text-muted-foreground truncate text-xs">@{user.username}</div>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td className="hidden p-4 sm:table-cell">
                                                                <div className="text-muted-foreground truncate text-sm">{user.email}</div>
                                                            </td>
                                                            <td className="hidden p-4 sm:table-cell">
                                                                <div className="flex flex-wrap gap-1">
                                                                    {department.head_of_department && department.head_of_department.id === user.id && (
                                                                        <Badge variant="outline" className="border-primary-light/30 bg-primary-light/10 text-primary whitespace-nowrap">
                                                                            Head of Department
                                                                        </Badge>
                                                                    )}
                                                                    {user.roles &&
                                                                        user.roles.map((role, index) => (
                                                                            <Badge
                                                                                key={index}
                                                                                variant="outline"
                                                                                className="border-accent/30 bg-accent/10 text-accent whitespace-nowrap"
                                                                            >
                                                                                {role.name}
                                                                            </Badge>
                                                                        ))}
                                                                </div>
                                                            </td>
                                                            <td className="p-4 text-right">
                                                                <Button variant="outline" size="sm" asChild className="whitespace-nowrap">
                                                                    <Link href={`/users/${user.id}`}>View Profile</Link>
                                                                </Button>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
