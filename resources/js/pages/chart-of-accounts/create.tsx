import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import React, { useState } from 'react';

interface Organization {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
    organization_id: number;
}

interface ParentAccount {
    id: number;
    name: string;
    code?: string;
}

interface Props {
    organizations?: Organization[];
    organization?: Organization;
    branches: Branch[];
    parentAccounts: ParentAccount[];
    isPlatformAdmin: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Chart of Accounts', href: '/chart-of-accounts' },
    { title: 'Create', href: '/chart-of-accounts/create' },
];

export default function CreateChartOfAccount({ organizations, organization, branches, parentAccounts, isPlatformAdmin }: Props) {
    const [filteredBranches, setFilteredBranches] = useState<Branch[]>(branches);

    const { data, setData, post, processing, errors } = useForm({
        organization_id: organization?.id || '',
        branch_id: '',
        // code field removed - will be auto-generated
        name: '',
        description: '',
        spending_limit: '',
        limit_period: '',
        is_active: true,
        parent_id: '',
        account_type: '',
    });

    const handleOrganizationChange = (value: string) => {
        setData('organization_id', value);
        setData('branch_id', ''); // Reset branch when organization changes

        // Filter branches by selected organization
        if (value) {
            const orgId = parseInt(value);
            const filtered = branches.filter((branch) => branch.organization_id === orgId);
            setFilteredBranches(filtered);
        } else {
            setFilteredBranches([]);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Create a copy of the data to modify
        const formData = { ...data };

        // Convert "none" values to empty strings or null as needed by the backend
        if (formData.branch_id === 'none') formData.branch_id = '';
        if (formData.parent_id === 'none') formData.parent_id = '';
        if (formData.limit_period === 'none') formData.limit_period = '';

        // Ensure limit_period is one of the allowed values or empty
        const allowedPeriods = ['daily', 'weekly', 'monthly', 'quarterly', 'annually'];
        if (formData.limit_period && !allowedPeriods.includes(formData.limit_period)) {
            formData.limit_period = '';
        }

        // No need to include code field - it will be auto-generated on the server
        post('/chart-of-accounts', formData);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Chart of Account" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">Create Chart of Account</h1>
                        <p className="text-muted-foreground">Add a new account or sub-category to your chart of accounts</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>Account Information</CardTitle>
                            <CardDescription>Enter the details for the new chart of account</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Organization Selection (for platform admin only) */}
                            {isPlatformAdmin && (
                                <div className="space-y-2">
                                    <Label htmlFor="organization_id" className={errors.organization_id ? 'text-destructive' : ''}>
                                        Organization <span className="text-destructive">*</span>
                                    </Label>
                                    <Select value={data.organization_id.toString()} onValueChange={handleOrganizationChange}>
                                        <SelectTrigger id="organization_id" className={errors.organization_id ? 'border-destructive' : ''}>
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {organizations?.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.organization_id && <p className="text-destructive text-sm">{errors.organization_id}</p>}
                                </div>
                            )}

                            {/* Branch Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="branch_id" className={errors.branch_id ? 'text-destructive' : ''}>
                                    Branch
                                </Label>
                                <Select value={data.branch_id.toString()} onValueChange={(value) => setData('branch_id', value)}>
                                    <SelectTrigger id="branch_id" className={errors.branch_id ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select branch " />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None</SelectItem>
                                        {filteredBranches.map((branch) => (
                                            <SelectItem key={branch.id} value={branch.id.toString()}>
                                                {branch.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <p className="text-muted-foreground text-xs">
                                    Chart of accounts are primarily at the organization level. Branch is optional.
                                </p>
                                {errors.branch_id && <p className="text-destructive text-sm">{errors.branch_id}</p>}
                            </div>

                            {/* Account Type */}
                            <div className="space-y-2">
                                <Label htmlFor="account_type" className={errors.account_type ? 'text-destructive' : ''}>
                                    Account Type <span className="text-destructive">*</span>
                                </Label>
                                <Select
                                    value={data.account_type}
                                    onValueChange={(value) => {
                                        setData('account_type', value);
                                        // We no longer auto-set parent_id based on account_type
                                        // This allows users to explicitly choose the parent account
                                    }}
                                >
                                    <SelectTrigger id="account_type" className={errors.account_type ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select account type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="asset">Asset</SelectItem>
                                        <SelectItem value="liability">Liability</SelectItem>
                                        <SelectItem value="equity">Equity</SelectItem>
                                        <SelectItem value="revenue">Revenue</SelectItem>
                                        <SelectItem value="expense">Expense</SelectItem>
                                    </SelectContent>
                                </Select>
                                {errors.account_type && <p className="text-destructive text-sm">{errors.account_type}</p>}
                            </div>

                            {/* Parent Account */}
                            <div className="space-y-2">
                                <Label htmlFor="parent_id" className={errors.parent_id ? 'text-destructive' : ''}>
                                    Parent Account
                                </Label>
                                <Select value={data.parent_id.toString()} onValueChange={(value) => setData('parent_id', value)}>
                                    <SelectTrigger id="parent_id" className={errors.parent_id ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select parent account " />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None (Top-level Account)</SelectItem>
                                        {parentAccounts.map((account) => (
                                            <SelectItem key={account.id} value={account.id.toString()}>
                                                {account.code ? `${account.code} - ${account.name}` : account.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.parent_id && <p className="text-destructive text-sm">{errors.parent_id}</p>}
                            </div>

                            {/* Account Code field removed - will be auto-generated */}

                            {/* Account Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name" className={errors.name ? 'text-destructive' : ''}>
                                    Account Name <span className="text-destructive">*</span>
                                </Label>
                                <Input
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., Office Supplies"
                                    className={errors.name ? 'border-destructive' : ''}
                                />
                                {errors.name && <p className="text-destructive text-sm">{errors.name}</p>}
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description" className={errors.description ? 'text-destructive' : ''}>
                                    Description
                                </Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Enter a description for this account"
                                    className={errors.description ? 'border-destructive' : ''}
                                />
                                {errors.description && <p className="text-destructive text-sm">{errors.description}</p>}
                            </div>

                            {/* Spending Limit */}
                            <div className="space-y-2">
                                <Label htmlFor="spending_limit" className={errors.spending_limit ? 'text-destructive' : ''}>
                                    Spending Limit
                                </Label>
                                <Input
                                    id="spending_limit"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    value={data.spending_limit}
                                    onChange={(e) => setData('spending_limit', e.target.value)}
                                    placeholder="e.g., 5000.00"
                                    className={errors.spending_limit ? 'border-destructive' : ''}
                                />
                                {errors.spending_limit && <p className="text-destructive text-sm">{errors.spending_limit}</p>}
                            </div>

                            {/* Limit Period */}
                            <div className="space-y-2">
                                <Label htmlFor="limit_period" className={errors.limit_period ? 'text-destructive' : ''}>
                                    Limit Period
                                </Label>
                                <Select value={data.limit_period} onValueChange={(value) => setData('limit_period', value)}>
                                    <SelectTrigger id="limit_period" className={errors.limit_period ? 'border-destructive' : ''}>
                                        <SelectValue placeholder="Select limit period " />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None</SelectItem>
                                        <SelectItem value="daily">Daily</SelectItem>
                                        <SelectItem value="weekly">Weekly</SelectItem>
                                        <SelectItem value="monthly">Monthly</SelectItem>
                                        <SelectItem value="quarterly">Quarterly</SelectItem>
                                        <SelectItem value="annually">Annually</SelectItem>
                                    </SelectContent>
                                </Select>
                                {errors.limit_period && <p className="text-destructive text-sm">{errors.limit_period}</p>}
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center justify-between space-y-0">
                                <Label htmlFor="is_active" className={errors.is_active ? 'text-destructive' : ''}>
                                    Active
                                </Label>
                                <Switch id="is_active" checked={data.is_active} onCheckedChange={(checked) => setData('is_active', checked)} />
                                {errors.is_active && <p className="text-destructive text-sm">{errors.is_active}</p>}
                            </div>
                        </CardContent>
                        <CardFooter className="flex justify-between">
                            <Button variant="outline" type="button" onClick={() => window.history.back()}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Creating...' : 'Create Account'}
                            </Button>
                        </CardFooter>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
