import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Edit, Trash } from 'lucide-react';

interface ChartOfAccount {
    id: number;
    organization_id: number;
    branch_id: number | null;
    code: string | null;
    name: string;
    description: string | null;
    spending_limit: number | null;
    limit_period: string | null;
    is_active: boolean;
    parent_id: number | null;
    account_type: string;
    created_at: string;
    updated_at: string;
    organization?: {
        id: number;
        name: string;
    };
    branch?: {
        id: number;
        name: string;
    };
    parent?: {
        id: number;
        name: string;
        code: string | null;
    };
    children?: {
        id: number;
        name: string;
        code: string | null;
        parent_id: number;
    }[];
}

interface Props {
    chartOfAccount: ChartOfAccount;
}

export default function ShowChartOfAccount({ chartOfAccount }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Chart of Accounts', href: '/chart-of-accounts' },
        { title: chartOfAccount.name, href: `/chart-of-accounts/${chartOfAccount.id}` },
    ];

    const handleDelete = () => {
        if (confirm('Are you sure you want to delete this chart of account?')) {
            router.delete(`/chart-of-accounts/${chartOfAccount.id}`);
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getAccountTypeLabel = (type: string) => {
        switch (type) {
            case 'asset':
                return (
                    <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700">
                        Asset
                    </Badge>
                );
            case 'liability':
                return (
                    <Badge variant="outline" className="border-red-200 bg-red-50 text-red-700">
                        Liability
                    </Badge>
                );
            case 'equity':
                return (
                    <Badge variant="outline" className="border-purple-200 bg-purple-50 text-purple-700">
                        Equity
                    </Badge>
                );
            case 'revenue':
                return (
                    <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                        Revenue
                    </Badge>
                );
            case 'expense':
                return (
                    <Badge variant="outline" className="border-amber-200 bg-amber-50 text-amber-700">
                        Expense
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Chart of Account: ${chartOfAccount.name}`} />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="icon" asChild>
                            <Link href="/chart-of-accounts">
                                <ArrowLeft className="h-4 w-4" />
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">{chartOfAccount.name}</h1>
                            <p className="text-muted-foreground">{chartOfAccount.code && `Code: ${chartOfAccount.code}`}</p>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={`/chart-of-accounts/${chartOfAccount.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive" onClick={handleDelete}>
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                    </div>
                </div>

                {/* Account Details */}
                <Card>
                    <CardHeader>
                        <CardTitle>Account Details</CardTitle>
                        <CardDescription>Detailed information about this chart of account</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Account Type</p>
                                <p>{getAccountTypeLabel(chartOfAccount.account_type)}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Status</p>
                                <p>
                                    {chartOfAccount.is_active ? (
                                        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                                            Active
                                        </Badge>
                                    ) : (
                                        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
                                            Inactive
                                        </Badge>
                                    )}
                                </p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Organization</p>
                                <p>{chartOfAccount.organization?.name || 'N/A'}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Branch</p>
                                <p>{chartOfAccount.branch?.name || 'N/A'}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Parent Account</p>
                                <p>
                                    {chartOfAccount.parent ? (
                                        <Link href={`/chart-of-accounts/${chartOfAccount.parent.id}`} className="text-blue-600 hover:underline">
                                            {chartOfAccount.parent.code
                                                ? `${chartOfAccount.parent.code} - ${chartOfAccount.parent.name}`
                                                : chartOfAccount.parent.name}
                                        </Link>
                                    ) : (
                                        'None (Top-level Account)'
                                    )}
                                </p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Spending Limit</p>
                                <p>
                                    {chartOfAccount.spending_limit
                                        ? `${chartOfAccount.spending_limit} (${chartOfAccount.limit_period || 'No period set'})`
                                        : 'No limit set'}
                                </p>
                            </div>
                            <div className="space-y-1 md:col-span-2">
                                <p className="text-muted-foreground text-sm font-medium">Description</p>
                                <p>{chartOfAccount.description || 'No description provided'}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Created</p>
                                <p>{formatDate(chartOfAccount.created_at)}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-muted-foreground text-sm font-medium">Last Updated</p>
                                <p>{formatDate(chartOfAccount.updated_at)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Sub-accounts */}
                {chartOfAccount.children && chartOfAccount.children.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Sub-accounts</CardTitle>
                            <CardDescription>Accounts that have this account as their parent</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Name</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {chartOfAccount.children.map((child) => (
                                        <TableRow key={child.id}>
                                            <TableCell className="font-mono">{child.code || '-'}</TableCell>
                                            <TableCell className="font-medium">{child.name}</TableCell>
                                            <TableCell className="text-right">
                                                <Button variant="ghost" size="sm" asChild>
                                                    <Link href={`/chart-of-accounts/${child.id}`}>View</Link>
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
