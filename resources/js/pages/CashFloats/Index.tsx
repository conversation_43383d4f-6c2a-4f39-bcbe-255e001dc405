import AppLayout from '@/layouts/app-layout';
import { Link, useForm } from '@inertiajs/react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { formatCurrency } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { AlertTriangle, MoreHorizontal, PlusCircle } from 'lucide-react';

interface CashFloat {
    id: number;
    name: string;
    current_balance: number;
    alert_threshold?: number;
    status: string;
    department?: {
        id: number;
        name: string;
    };
    branch?: {
        id: number;
        name: string;
    };
    user?: {
        id: number;
        name: string;
    };
    created_at: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedData<T> {
    data: T[];
    links: PaginationLink[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Props {
    cashFloats: PaginatedData<CashFloat>;
    organization: {
        id: number;
        name: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Cash Floats', href: '/cash-floats' },
];

export default function Index({ cashFloats, organization }: Props) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedFloat, setSelectedFloat] = useState<CashFloat | null>(null);
    const [transactionType, setTransactionType] = useState<'reimbursement' | 'expense' | 'float_return' | 'disbursement' | 'float_issuance'>(
        'reimbursement',
    );

    const { data, setData, post, processing, errors, reset, clearErrors } = useForm({
        transaction_type: 'reimbursement' as 'reimbursement' | 'expense' | 'float_return' | 'disbursement' | 'float_issuance',
        total_amount: '',
        description: '',
        payment_method: '',
        reference_number: '',
        vendor_details: '',
        account_details: '',
        transaction_cost: '',
    });

    const openTransactionDialog = (float: CashFloat, type: 'reimbursement' | 'expense' | 'float_return' | 'disbursement' | 'float_issuance') => {
        setSelectedFloat(float);
        setTransactionType(type);
        setData('transaction_type', type);
        // Clear any previous errors
        clearErrors();
        // Reset form data
        reset();
        setData((prev) => ({
            ...prev,
            transaction_type: type,
        }));
        setIsDialogOpen(true);
    };

    const closeDialog = () => {
        setIsDialogOpen(false);
        setSelectedFloat(null);
        reset();
        clearErrors();
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (selectedFloat) {
            // Use router.post instead of post from useForm to handle the route properly
            post(`/cash-floats/${selectedFloat.id}/transactions`, {
                onSuccess: () => {
                    closeDialog();
                },
                onError: (errors) => {
                    console.log('Transaction errors:', errors);
                },
                preserveScroll: true,
            });
        }
    };

    const getTransactionTypeLabel = (type: string) => {
        switch (type) {
            case 'reimbursement':
                return 'Reimburse';
            case 'expense':
                return 'Record Expense';
            case 'float_return':
                return 'Return Float';
            case 'disbursement':
                return 'Disburse';
            case 'float_issuance':
                return 'Issue Float';
            default:
                return type;
        }
    };

    const getTransactionDescription = (type: string) => {
        switch (type) {
            case 'reimbursement':
                return 'Add money back to this cash float from your account.';
            case 'expense':
                return 'Record an expense paid from this cash float.';
            case 'float_return':
                return 'Return unused funds from this cash float.';
            case 'disbursement':
                return 'Disburse money from this cash float to someone.';
            case 'float_issuance':
                return 'Issue additional funds to this cash float.';
            default:
                return '';
        }
    };

    const isLowBalance = (float: CashFloat) => {
        return float.alert_threshold && float.current_balance <= float.alert_threshold;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold text-foreground/90">Cash Floats</h1>
                        <p className="mt-1 text-sm text-foreground/90">Manage cash floats for {organization.name}</p>
                    </div>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href="/cash-floats/create">
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                Create New Cash Float
                            </span>
                        </Link>
                    </Button>
                </div>

                <Card>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Department</TableHead>
                                <TableHead>Branch</TableHead>
                                <TableHead>Assigned To</TableHead>
                                <TableHead>Current Balance</TableHead>
                                <TableHead>Alert Threshold</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Created At</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {cashFloats.data.map((float) => (
                                <TableRow key={float.id}>
                                    <TableCell>
                                        <Link href={`/cash-floats/${float.id}`} className="font-medium text-blue-600 hover:text-blue-800">
                                            {float.name}
                                        </Link>
                                    </TableCell>
                                    <TableCell>{float.department?.name || '-'}</TableCell>
                                    <TableCell>{float.branch?.name || '-'}</TableCell>
                                    <TableCell>{float.user?.name || '-'}</TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <span className={isLowBalance(float) ? 'font-semibold text-red-600' : 'text-gray-text-foreground/90'}>
                                                {formatCurrency(float.current_balance)}
                                            </span>
                                            {isLowBalance(float) && <AlertTriangle className="h-4 w-4 text-red-500" />}
                                        </div>
                                    </TableCell>
                                    <TableCell>{float.alert_threshold ? formatCurrency(float.alert_threshold) : '-'}</TableCell>
                                    <TableCell>
                                        <Badge
                                            variant={
                                                float.status === 'active'
                                                    ? 'default'
                                                    : float.status === 'inactive'
                                                      ? 'secondary'
                                                      : float.status === 'reconciled'
                                                        ? 'outline'
                                                        : 'destructive'
                                            }
                                        >
                                            {float.status}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>{new Date(float.created_at).toLocaleDateString()}</TableCell>
                                    <TableCell>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="outline" size="sm">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => openTransactionDialog(float, 'float_issuance')}>
                                                    💵 Issue More Funds
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => openTransactionDialog(float, 'float_return')}>
                                                    🔄 Return Float
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                            {cashFloats.data.length === 0 && (
                                <TableRow>
                                    <TableCell colSpan={9} className="py-8 text-center text-foreground/90">
                                        No cash floats found.{' '}
                                        <Link href="/cash-floats/create" className="text-blue-600 hover:underline">
                                            Create your first cash float
                                        </Link>
                                        .
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </Card>

                {/* Transaction Dialog */}
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogContent className="sm:max-w-[500px]">
                        <DialogHeader>
                            <DialogTitle>
                                {getTransactionTypeLabel(transactionType)} - {selectedFloat?.name}
                            </DialogTitle>
                            <DialogDescription>{getTransactionDescription(transactionType)}</DialogDescription>
                            {selectedFloat && (
                                <div className="mt-2 rounded-md bg-gray-50 p-3">
                                    <span className="text-sm font-medium">Current Balance: </span>
                                    <span className={`text-sm font-semibold ${isLowBalance(selectedFloat) ? 'text-red-600' : 'text-green-600'}`}>
                                        {formatCurrency(selectedFloat.current_balance)}
                                    </span>
                                </div>
                            )}
                        </DialogHeader>

                        <form onSubmit={handleSubmit}>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <Label htmlFor="total_amount">
                                        Amount <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="total_amount"
                                        type="number"
                                        step="0.01"
                                        min="0.01"
                                        value={data.total_amount}
                                        onChange={(e) => setData('total_amount', e.target.value)}
                                        placeholder="Enter amount (e.g., 1000.00)"
                                        required
                                        className={errors.total_amount ? 'border-red-500' : ''}
                                    />
                                    {errors.total_amount && <p className="text-sm text-red-500">{errors.total_amount}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">
                                        Description <span className="text-red-500">*</span>
                                    </Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Enter description or reason for this transaction"
                                        required
                                        rows={3}
                                        className={errors.description ? 'border-red-500' : ''}
                                    />
                                    {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="payment_method">Payment Method</Label>
                                    <Select value={data.payment_method} onValueChange={(value) => setData('payment_method', value)}>
                                        <SelectTrigger className={errors.payment_method ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Select payment method" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="cash">💵 Cash</SelectItem>
                                            <SelectItem value="mpesa">📱 M-Pesa</SelectItem>
                                            <SelectItem value="bank">🏦 Bank Transfer</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.payment_method && <p className="text-sm text-red-500">{errors.payment_method}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="reference_number">Reference Number</Label>
                                    <Input
                                        id="reference_number"
                                        value={data.reference_number}
                                        onChange={(e) => setData('reference_number', e.target.value)}
                                        placeholder="Transaction ID, receipt number, etc."
                                        className={errors.reference_number ? 'border-red-500' : ''}
                                    />
                                    {errors.reference_number && <p className="text-sm text-red-500">{errors.reference_number}</p>}
                                </div>

                                {(transactionType === 'expense' || transactionType === 'disbursement') && (
                                    <div className="space-y-2">
                                        <Label htmlFor="vendor_details">Vendor/Recipient Details</Label>
                                        <Input
                                            id="vendor_details"
                                            value={data.vendor_details}
                                            onChange={(e) => setData('vendor_details', e.target.value)}
                                            placeholder="Vendor name, recipient details"
                                            className={errors.vendor_details ? 'border-red-500' : ''}
                                        />
                                        {errors.vendor_details && <p className="text-sm text-red-500">{errors.vendor_details}</p>}
                                    </div>
                                )}

                                {(transactionType === 'reimbursement' ||
                                    transactionType === 'float_return' ||
                                    transactionType === 'float_issuance') && (
                                    <div className="space-y-2">
                                        <Label htmlFor="account_details">Account Details</Label>
                                        <Input
                                            id="account_details"
                                            value={data.account_details}
                                            onChange={(e) => setData('account_details', e.target.value)}
                                            placeholder="Account or source details"
                                            className={errors.account_details ? 'border-red-500' : ''}
                                        />
                                        {errors.account_details && <p className="text-sm text-red-500">{errors.account_details}</p>}
                                    </div>
                                )}

                                <div className="space-y-2">
                                    <Label htmlFor="transaction_cost">Transaction Cost/Fee</Label>
                                    <Input
                                        id="transaction_cost"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={data.transaction_cost}
                                        onChange={(e) => setData('transaction_cost', e.target.value)}
                                        placeholder="Any additional fees (optional)"
                                        className={errors.transaction_cost ? 'border-red-500' : ''}
                                    />
                                    {errors.transaction_cost && <p className="text-sm text-red-500">{errors.transaction_cost}</p>}
                                </div>

                                {/* Show general error */}
                                {(errors as Record<string, string>).error && (
                                    <div className="rounded-md border border-red-200 bg-red-50 p-3">
                                        <p className="text-sm text-red-600">{(errors as Record<string, string>).error}</p>
                                    </div>
                                )}
                            </div>

                            <DialogFooter>
                                <Button variant="outline" type="button" onClick={closeDialog} disabled={processing}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing || !data.total_amount || !data.description}>
                                    {processing ? 'Processing...' : getTransactionTypeLabel(transactionType)}
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
