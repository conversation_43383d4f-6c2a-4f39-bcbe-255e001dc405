import { AttachmentsList } from '@/components/AttachmentsList';
import { FileUpload } from '@/components/FileUpload';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Clock, Edit, XCircle, PlusCircle, User } from 'lucide-react';
import React, { useState } from 'react';

interface TransactionItem {
    id: number;
    chart_of_account_id: number;
    description: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    reference_number: string;
}

interface User {
    id: number;
    first_name: string;
    last_name: string;
    email?: string;
}

interface Department {
    id: number;
    name: string;
}

interface Organization {
    id: number;
    name: string;
}

interface Branch {
    id: number;
    name: string;
}

interface Attachment {
    id: number;
    original_name: string;
    file_size: number;
    mime_type: string;
    description?: string;
    is_evidence: boolean;
    uploaded_at_step?: string;
    created_at: string;
    uploader: User;
}

interface ApproverDetail {
    id: number;
    name: string;
    department: string;
}

interface ApprovalHistory {
    id: number;
    action: string;
    comments: string;
    created_at: string;
    approver_id: number;
    approver_name: string;
    role_name: string;
    step_number: number;
    step_description: string;
}

interface RequisitionHistory {
    id: number;
    action: string;
    comments?: string;
    created_at: string;
    user_name: string;
    user_id: number;
    type: 'requisition';
}

interface CombinedHistoryEntry {
    id: number;
    action: string;
    comments?: string;
    created_at: string;
    user_name: string;
    step_description?: string;
    type: 'requisition' | 'approval';
}

interface Transaction {
    id: number;
    requisition_id: number | null;
    cash_float_id: number | null;
    transaction_type: 'disbursement' | 'reimbursement' | 'float_issuance' | 'float_return' | 'expense' | 'other';
    status: 'opened' | 'updated' | 'completed';
    description?: string;
    payment_method: string | null;
    account_details: string | null;
    disbursement_transaction_id: string | null;
    approvers_details: ApproverDetail[];
    total_amount: number;
    transaction_cost?: number;
    created_at: string;
    updated_at: string;
    items: TransactionItem[];
    requisition?: {
        id: number;
        requisition_number: string;
        purpose: string;
        requester: User;
        department?: Department;
        organization?: Organization;
        branch?: Branch;
    };
    cash_float?: {
        id: number;
        name: string;
        current_balance: number;
    };
    creator?: User;
    attachments: Attachment[];
}

interface CashFloat {
    id: number;
    name: string;
    current_balance: number;
}

interface ShowProps {
    transaction: Transaction;
    chartOfAccounts: Record<number, { id: number; name: string; account_type: string }>;
    requisitionHistory: RequisitionHistory[];
    approvalHistory: ApprovalHistory[];
    cashFloats: CashFloat[];
    canUpdate: boolean;
    canProcess: boolean;
    canAttachFiles: boolean;
}

interface AccountDetails {
    account_name: string;
    account_number: string;
    additional_details: string;
}
// Helper function to safely parse Account Details JSON string to JS object
const parseAccountDetails = (jsonString: string): AccountDetails | null => {
  try {
    const parsed = JSON.parse(jsonString);
    // Validate that it has the expected structure
    if (parsed && typeof parsed === 'object' && 
        'account_name' in parsed && 
        'account_number' in parsed && 
        'additional_details' in parsed) {
      return parsed as AccountDetails;
    }
    return null;
  } catch (error) {
    console.error('Failed to parse account details:', error);
    return null;
  }
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Transactions',
        href: '/transactions',
    },
    {
        title: 'Transaction Details',
        href: '#',
    },
];

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const getTransactionTypeTitle = (type: string) => {
    switch (type) {
        case 'disbursement': return 'Requisition Disbursement';
        case 'float_issuance': return 'Cash Float Issuance';
        case 'reimbursement': return 'Float Reimbursement';
        case 'expense': return 'Direct Expense';
        case 'float_return': return 'Float Return';
        default: return 'Transaction';
    }
};

const getTransactionTypeDescription = (type: string) => {
    switch (type) {
        case 'disbursement': return 'Payment for approved requisition';
        case 'float_issuance': return 'Money issued to cash float';
        case 'reimbursement': return 'Money returned to cash float';
        case 'expense': return 'Direct expense from cash float';
        case 'float_return': return 'Unused float returned to company';
        default: return 'Financial transaction';
    }
};

const getTransactionTypeLabel = (type: string) => {
    switch (type) {
        case 'disbursement': return 'Disbursement';
        case 'float_issuance': return 'Float Issuance';
        case 'reimbursement': return 'Reimbursement';
        case 'expense': return 'Expense';
        case 'float_return': return 'Float Return';
        default: return type;
    }
};

const getCombinedHistory = (requisitionHistory: RequisitionHistory[], approvalHistory: ApprovalHistory[]): CombinedHistoryEntry[] => {
    const combined: CombinedHistoryEntry[] = [
        ...requisitionHistory.map(h => ({
            id: h.id,
            action: h.action,
            comments: h.comments,
            created_at: h.created_at,
            user_name: h.user_name,
            type: 'requisition' as const
        })),
        ...approvalHistory.map(h => ({
            id: h.id,
            action: h.action,
            comments: h.comments,
            created_at: h.created_at,
            user_name: h.approver_name,
            step_description: `Step ${h.step_number}: ${h.step_description || h.role_name}`,
            type: 'approval' as const
        }))
    ];

    return combined.sort((a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );
};

const getHistoryIcon = (action: string) => {
    switch (action) {
        case 'created': return <PlusCircle className="h-3 w-3 text-blue-500" />;
        case 'approved': return <CheckCircle className="h-3 w-3 text-green-500" />;
        case 'rejected': return <XCircle className="h-3 w-3 text-red-500" />;
        case 'submitted': return <Clock className="h-3 w-3 text-amber-500" />;
        default: return <Clock className="h-3 w-3 text-gray-500" />;
    }
};

const getStatusBadgeVariant = (status: string) => {
    switch (status) {
        case 'opened':
            return 'secondary';  // Subtle background for pending
        case 'updated':
            return 'default';   // Primary color for active states
        case 'completed':
            return 'outline';   // Outlined style for completed
        default:
            return 'secondary'; // Fallback to secondary
    }
};

const getActionBadgeVariant = (action: string) => {
    switch (action) {
        case 'approved':
            return 'default';
        case 'rejected':
            return 'destructive';
        default:
            return 'outline';
    }
};

export default function Show({ transaction, chartOfAccounts, requisitionHistory, approvalHistory, cashFloats, canUpdate, canProcess, canAttachFiles }: ShowProps) {
    const [isCompleteDialogOpen, setIsCompleteDialogOpen] = useState(false);
    const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);

    // Format currency
    const formatCurrency = (amount: number) => {
        return `KSH ${new Intl.NumberFormat('en-KE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount)}`;
    };

    const { data, setData, post, processing, errors } = useForm({
        payment_method: '',
        account_details: '',
        disbursement_transaction_id: '',
        cash_float_id: 'none',
        transaction_cost: '',
        comments: '',
    });

    const handleCompleteSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(`/transactions/${transaction.id}/complete`, {
            onSuccess: () => {
                setIsCompleteDialogOpen(false);
            },
        });
    };

    const handleApproveSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(`/transactions/${transaction.id}/approve`, {
            onSuccess: () => {
                setIsApproveDialogOpen(false);
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Transaction ${transaction.id}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
                <div className="flex items-center justify-between">
                    <div className="text-foreground/100 flex items-center justify-between gap-5 w-full md:w-auto">
                        <Button variant="outline" size="icon" asChild>
                            <Link href="/transactions">
                                <ArrowLeft className="h-4 w-4" />
                            </Link>
                        </Button>
                        <h1 className="text-foregound/90 text-3xl font-bold">Transaction #{transaction.id}</h1>
                        <Badge variant={getStatusBadgeVariant(transaction.status)} className='text-foreground/90'>
                            {transaction.status === 'opened' && 'Pending'}
                            {transaction.status === 'updated' && 'Ready for Disbursement'}
                            {transaction.status === 'completed' && 'Completed'}
                            {!['opened', 'updated', 'completed'].includes(transaction.status) && transaction.status}
                        </Badge>
                    </div>
                    <div className="flex gap-2">
                        {canUpdate && (
                            <Button asChild>
                                <Link href={`/transactions/${transaction.id}/edit`}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Transaction
                                </Link>
                            </Button>
                        )}
                        {transaction.status === 'opened' && (
                            <Button onClick={() => setIsApproveDialogOpen(true)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Approve Transaction
                            </Button>
                        )}
                        {canProcess && transaction.status === 'updated' && (
                            <Button onClick={() => setIsCompleteDialogOpen(true)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Complete Disbursement
                            </Button>
                        )}
                    </div>
                </div>

                <Tabs defaultValue="details">
                    <TabsList>
                        <TabsTrigger value="details">Transaction Details</TabsTrigger>
                        {transaction.items?.length > 0 && (
                            <TabsTrigger value="items">Items</TabsTrigger>
                        )}
                        {(requisitionHistory.length > 0 || approvalHistory.length > 0) && (
                            <TabsTrigger value="workflow">Workflow History</TabsTrigger>
                        )}
                        <TabsTrigger value="attachments">Attachments</TabsTrigger>
                    </TabsList>

                    <TabsContent value="details" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>
                                    {getTransactionTypeTitle(transaction.transaction_type)}
                                </CardTitle>
                                <CardDescription>
                                    {getTransactionTypeDescription(transaction.transaction_type)}
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Basic Transaction Info */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Transaction Type</Label>
                                        <div className="mt-1 font-medium">
                                            {getTransactionTypeLabel(transaction.transaction_type)}
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Status</Label>
                                        <div className="mt-1">
                                            <Badge variant={transaction.status === 'completed' ? 'outline' : 'default'}>
                                                {transaction.status}
                                            </Badge>
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Total Amount</Label>
                                        <div className="mt-1 font-medium">
                                            {formatCurrency(transaction.total_amount)}
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Created Date</Label>
                                        <div className="mt-1 font-medium">
                                            {formatDate(transaction.created_at)}
                                        </div>
                                    </div>
                                </div>

                                <Separator />

                                {/* Requisition-specific Info  */}
                                {transaction.requisition && (
                                    <>
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div>
                                                <Label>Requisition Number</Label>
                                                <div className="mt-1 font-medium">
                                                    <Link
                                                        href={`/requisitions/${transaction.requisition.id}`}
                                                        className="text-primary hover:underline"
                                                    >
                                                        {transaction.requisition.requisition_number}
                                                    </Link>
                                                </div>
                                            </div>
                                            <div>
                                                <Label>Purpose</Label>
                                                <div className="mt-1 font-medium">
                                                    {transaction.requisition.purpose}
                                                </div>
                                            </div>
                                            <div>
                                                <Label>Requester</Label>
                                                <div className="mt-1 font-medium">
                                                    {transaction.requisition.requester.first_name} {transaction.requisition.requester.last_name}
                                                </div>
                                            </div>
                                            <div>
                                                <Label>Department</Label>
                                                <div className="mt-1 font-medium">
                                                    {transaction.requisition.department?.name || 'N/A'}
                                                </div>
                                            </div>
                                        </div>
                                        <Separator />
                                    </>
                                )}

                                {/* Cash Float Info  */}
                                {transaction.cash_float && (
                                    <>
                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div>
                                                <Label>Cash Float</Label>
                                                <div className="mt-1 font-medium">
                                                    <Link
                                                        href={`/cash-floats/${transaction.cash_float.id}`}
                                                        className="text-primary hover:underline"
                                                    >
                                                        {transaction.cash_float.name}
                                                    </Link>
                                                </div>
                                            </div>
                                            <div>
                                                <Label>Float Balance</Label>
                                                <div className="mt-1 font-medium">
                                                    {formatCurrency(transaction.cash_float.current_balance)}
                                                </div>
                                            </div>
                                        </div>
                                        <Separator />
                                    </>
                                )}

                                {/* Transaction Description for non-requisition transactions */}
                                {!transaction.requisition && transaction.description && (
                                    <>
                                        <div>
                                            <Label>Description</Label>
                                            <div className="mt-1 rounded-md border p-3">
                                                <pre className="text-sm whitespace-pre-wrap break-words">
                                                    {transaction.description}
                                                </pre>
                                            </div>
                                        </div>
                                        <Separator />
                                    </>
                                )}

                                {/* Payment Details */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label>Payment Method</Label>
                                        <div className="mt-1 font-medium">
                                            {transaction.payment_method || 'Not specified'}
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Transaction Cost</Label>
                                        <div className="mt-1 font-medium">
                                            {transaction.transaction_cost ? formatCurrency(transaction.transaction_cost) : 'None'}
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Disbursement ID</Label>
                                        <div className="mt-1 font-medium">
                                            {transaction.disbursement_transaction_id || 'Not processed yet'}
                                        </div>
                                    </div>
                                    <div>
                                        <Label>Created By</Label>
                                        <div className="mt-1 font-medium">
                                            {transaction.creator ?
                                                `${transaction.creator.first_name} ${transaction.creator.last_name}` :
                                                'System'
                                            }
                                        </div>
                                    </div>
                                </div>

                                {/* Account Details */}
                                {transaction.account_details && (() => {
                                    const accountDetails = parseAccountDetails(transaction.account_details);
                                    return accountDetails ? (
                                        <>
                                            <Separator />
                                            <div>
                                                <Label className='text-2xl'>M-Pesa Account Details</Label>
                                                <div className="mt-6 p-8 rounded-md border p-3">
                                                    
                                                    <div className="space-y-3">
                                                        <div className="flex flex-col p-2">
                                                            <Label>Account Name</Label>
                                                            <span className="mt-3 text-lg text-foreground/100">{accountDetails.account_name}</span>
                                                        </div>
                                                        
                                                        <div className="flex flex-col p-2">
                                                            <Label>Account Number</Label>
                                                            <span className="mt-3 text-lg font-mono text-foreground/100">{accountDetails.account_number}</span>
                                                        </div>
                                                        
                                                        <div className="flex flex-col p-2">
                                                            <Label>Additional Details</Label>
                                                            <span className="mt-3 text-base text-foreground/100">{accountDetails.additional_details}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    ) : (
                                        // Fallback: display as plain text if JSON parsing fails
                                        <>
                                            <Separator />
                                            <div>
                                                <Label>Account Details</Label>
                                                <div className="mt-1 rounded-md border p-3">
                                                    <pre className="text-sm whitespace-pre-wrap break-words">
                                                        {transaction.account_details}
                                                    </pre>
                                                </div>
                                            </div>
                                        </>
                                    );
                                })()}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="items">
                        <Card>
                            <CardHeader>
                                <CardTitle>Transaction Items</CardTitle>
                                <CardDescription>Items included in this transaction</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Description</TableHead>
                                            <TableHead>Category</TableHead>
                                            <TableHead className="text-right">Quantity</TableHead>
                                            <TableHead className="text-right">Unit Price</TableHead>
                                            <TableHead className="text-right">Total</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {transaction.items.map((item) => (
                                            <TableRow key={item.id}>
                                                <TableCell className="font-medium">{item.description}</TableCell>
                                                <TableCell>{chartOfAccounts[item.chart_of_account_id]?.name || 'Unknown Category'}</TableCell>
                                                <TableCell className="text-right">{item.quantity}</TableCell>
                                                <TableCell className="text-right">{formatCurrency(item.unit_price)}</TableCell>
                                                <TableCell className="text-right">{formatCurrency(item.total_price)}</TableCell>
                                            </TableRow>
                                        ))}
                                        <TableRow>
                                            <TableCell colSpan={4} className="text-accent/80 text-left font-bold">
                                                Total
                                            </TableCell>
                                            <TableCell className="text-left font-bold">{formatCurrency(transaction.total_amount)}</TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    </TabsContent>



                    <TabsContent value="workflow">
                        <Card>
                            <CardHeader>
                                <CardTitle>Workflow History</CardTitle>
                                <CardDescription>
                                    Complete history from creation to completion
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-8">
                                    {(() => {
                                        const combinedHistory = getCombinedHistory(requisitionHistory, approvalHistory);
                                        return combinedHistory.length > 0 ? (
                                            combinedHistory.map((entry, index) => (
                                                <div key={`${entry.type}-${entry.id}`} className="relative pl-8">
                                                    {/* Timeline line */}
                                                    {index < combinedHistory.length - 1 && (
                                                        <div className="bg-border absolute top-6 left-3 h-full w-px" />
                                                    )}

                                                    {/* Timeline dot with appropriate icon */}
                                                    <div className="bg-background absolute top-1 left-0 flex h-6 w-6 items-center justify-center rounded-full border">
                                                        {getHistoryIcon(entry.action)}
                                                    </div>

                                                    {/* History content */}
                                                    <div className="flex flex-col space-y-1">
                                                        <div className="flex items-center gap-2">
                                                            <span className="font-medium">{entry.user_name}</span>
                                                            <Badge variant={getActionBadgeVariant(entry.action)} className='text-foreground/90'>
                                                                {entry.action}
                                                            </Badge>
                                                            <span className="text-muted-foreground text-sm">
                                                                {formatDate(entry.created_at)}
                                                            </span>
                                                        </div>

                                                        {entry.step_description && (
                                                            <div className="text-muted-foreground text-sm">
                                                                {entry.step_description}
                                                            </div>
                                                        )}

                                                        {entry.comments && (
                                                            <div className="bg-muted mt-1 rounded-md p-2 text-sm">
                                                                {entry.comments}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-muted-foreground text-center">No workflow history available</div>
                                        );
                                    })()}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="attachments">
                        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                            {/* Existing Attachments */}
                            <AttachmentsList
                                attachments={transaction.attachments || []}
                                canDelete={canAttachFiles}
                                className="w-full"
                            />

                            {/* File Upload */}
                            {canAttachFiles && (
                                <FileUpload
                                    entityType="transaction"
                                    entityId={transaction.id}
                                    uploadedAtStep={transaction.status}
                                    className="w-full"
                                />
                            )}
                        </div>
                    </TabsContent>
                </Tabs>

                {/* Approve Transaction Dialog */}
                <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Approve Transaction</DialogTitle>
                            <DialogDescription>Provide payment details to approve and complete this transaction.</DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleApproveSubmit}>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <Label htmlFor="payment_method">Payment Method</Label>
                                    <Input
                                        id="payment_method"
                                        value={data.payment_method}
                                        onChange={(e) => setData('payment_method', e.target.value)}
                                        placeholder="e.g., M-Pesa, Bank Transfer"
                                    />
                                    {errors.payment_method && <p className="text-sm text-destructive">{errors.payment_method}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="account_details">Account Details</Label>
                                    <Input
                                        id="account_details"
                                        value={data.account_details}
                                        onChange={(e) => setData('account_details', e.target.value)}
                                        placeholder="e.g., Account Number, Phone Number"
                                    />
                                    {errors.account_details && <p className="text-sm text-destructive">{errors.account_details}</p>}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="disbursement_transaction_id">Disbursement Transaction ID </Label>
                                    <Input
                                        id="disbursement_transaction_id"
                                        value={data.disbursement_transaction_id}
                                        onChange={(e) => setData('disbursement_transaction_id', e.target.value)}
                                        placeholder="Enter transaction ID from payment system"
                                    />
                                    {errors.disbursement_transaction_id && (
                                        <p className="text-sm text-destructive">{errors.disbursement_transaction_id}</p>
                                    )}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button variant="outline" type="button" onClick={() => setIsApproveDialogOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Approve Transaction
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>

                {/* Complete Disbursement Dialog */}
                <Dialog open={isCompleteDialogOpen} onOpenChange={setIsCompleteDialogOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Complete Disbursement</DialogTitle>
                            <DialogDescription>Enter the disbursement transaction ID to mark this transaction as completed.</DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleCompleteSubmit}>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <Label htmlFor="disbursement_transaction_id">Disbursement Transaction ID *</Label>
                                    <Input
                                        id="disbursement_transaction_id"
                                        value={data.disbursement_transaction_id}
                                        onChange={(e) => setData('disbursement_transaction_id', e.target.value)}
                                        placeholder="Enter transaction ID"
                                        required
                                    />
                                    {errors.disbursement_transaction_id && (
                                        <p className="text-sm text-destructive">{errors.disbursement_transaction_id}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="cash_float_id">Cash Float (Optional)</Label>
                                    <Select value={data.cash_float_id} onValueChange={(value) => setData('cash_float_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a cash float" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            {cashFloats.map((cashFloat) => (
                                                <SelectItem key={cashFloat.id} value={cashFloat.id.toString()}>
                                                    {cashFloat.name} (Balance: {formatCurrency(cashFloat.current_balance)})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.cash_float_id && <p className="text-sm text-destructive">{errors.cash_float_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="transaction_cost">Transaction Cost (Optional)</Label>
                                    <Input
                                        id="transaction_cost"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        value={data.transaction_cost}
                                        onChange={(e) => setData('transaction_cost', e.target.value)}
                                        placeholder="Enter processing fees or transaction costs"
                                    />
                                    {errors.transaction_cost && <p className="text-sm text-destructive">{errors.transaction_cost}</p>}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button variant="outline" type="button" onClick={() => setIsCompleteDialogOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Complete Transaction
                                </Button>
                            </DialogFooter>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
