import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pagination } from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowUpDown, CheckCircle, Eye, Search } from 'lucide-react';
import { useState } from 'react';

interface Transaction {
    id: number;
    requisition_id: number;
    status: string;
    payment_method?: string;
    account_details?: string;
    total_amount: number;
    created_at: string;
    disbursement_transaction_id: string | null;
    requisition?: {
        requisition_number: string;
        purpose?: string;
    } | null;
    creator?: {
        id: number;
        first_name: string;
        last_name: string;
        email: string;
    };
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface IndexProps {
    transactions: {
        data: Transaction[];
        links: PaginationLink[];
        total: number;
        current_page: number;
        last_page: number;
    };
    filters: {
        search: string;
        status: string;
        sort: string;
        direction: string;
        date_from: string | null;
        date_to: string | null;
    };
    canManageTransactions: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Transactions',
        href: '/transactions',
    },
];

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
    });
};

const getStatusBadgeVariant = (status: string) => {
    switch (status) {
        case 'opened':
            return 'secondary';
        case 'updated':
            return 'outline';
        case 'completed':
            return 'default';
        default:
            return 'outline';
    }
};

export default function Index({ transactions, filters, canManageTransactions }: IndexProps) {
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');
    const [dateFrom, setDateFrom] = useState<Date | undefined>(filters.date_from ? new Date(filters.date_from) : undefined);
    const [dateTo, setDateTo] = useState<Date | undefined>(filters.date_to ? new Date(filters.date_to) : undefined);

    // Format currency
    const formatCurrency = (amount: number) => {
        return `KSH ${new Intl.NumberFormat('en-KE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount)}`;
    };

    const handleSearch = () => {
        router.get(
            '/transactions',
            {
                search: searchQuery,
                status: statusFilter === 'all' ? '' : statusFilter,
                sort: filters.sort,
                direction: filters.direction,
                date_from: dateFrom ? dateFrom.toISOString().split('T')[0] : null,
                date_to: dateTo ? dateTo.toISOString().split('T')[0] : null,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSort = (column: string) => {
        const direction = filters.sort === column && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            '/transactions',
            {
                search: searchQuery,
                status: statusFilter === 'all' ? '' : statusFilter,
                sort: column,
                direction: direction,
                date_from: dateFrom ? dateFrom.toISOString().split('T')[0] : null,
                date_to: dateTo ? dateTo.toISOString().split('T')[0] : null,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleReset = () => {
        setSearchQuery('');
        setStatusFilter('all');
        setDateFrom(undefined);
        setDateTo(undefined);
        router.get(
            '/transactions',
            {},
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Transactions" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
                <div className="flex items-center justify-between w-full">
                    <h1 className="text-3xl text-foreground/90 font-bold">Transactions</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Transaction Management</CardTitle>
                        <CardDescription>View and manage all financial transactions</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Filters */}
                            <div className="flex flex-col gap-4 md:flex-row">
                                <div className="flex-1 space-y-2">
                                    <Label htmlFor="search">Search</Label>
                                    <div className="flex w-full max-w-sm items-center space-x-2">
                                        <Input
                                            id="search"
                                            placeholder="Search by ID or requisition number"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                        />
                                        <Button type="button" size="icon" onClick={handleSearch}>
                                            <Search className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                <div className="w-full max-w-[180px] space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                                        <SelectTrigger id="status">
                                            <SelectValue placeholder="All statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All statuses</SelectItem>
                                            <SelectItem value="opened">Opened</SelectItem>
                                            <SelectItem value="updated">Ready for Disbursement</SelectItem>
                                            <SelectItem value="completed">Completed</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="w-full max-w-[180px] space-y-2">
                                    <Label htmlFor="date-from">From Date</Label>
                                    <DatePicker id="date-from" selected={dateFrom} onSelect={setDateFrom} placeholder="Select start date" />
                                </div>

                                <div className="w-full max-w-[180px] space-y-2">
                                    <Label htmlFor="date-to">To Date</Label>
                                    <DatePicker id="date-to" selected={dateTo} onSelect={setDateTo} placeholder="Select end date" />
                                </div>

                                <div className="flex items-end space-x-2">
                                    <Button onClick={handleSearch}>Apply Filters</Button>
                                    <Button variant="outline" onClick={handleReset}>
                                        Reset
                                    </Button>
                                </div>
                            </div>

                            {/* Transactions Table */}
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-[100px]">
                                                <Button variant="ghost" className="p-0 hover:bg-transparent" onClick={() => handleSort('id')}>
                                                    ID
                                                    <ArrowUpDown className="ml-2 h-4 w-4" />
                                                </Button>
                                            </TableHead>
                                            <TableHead>Requisition</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>
                                                <Button
                                                    variant="ghost"
                                                    className="p-0 hover:bg-transparent"
                                                    onClick={() => handleSort('total_amount')}
                                                >
                                                    Amount
                                                    <ArrowUpDown className="ml-2 h-4 w-4" />
                                                </Button>
                                            </TableHead>
                                            <TableHead>
                                                <Button variant="ghost" className="p-0 hover:bg-transparent" onClick={() => handleSort('created_at')}>
                                                    Date
                                                    <ArrowUpDown className="ml-2 h-4 w-4" />
                                                </Button>
                                            </TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {transactions.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="h-24 text-center">
                                                    No transactions found.
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            transactions.data.map((transaction) => (
                                                <TableRow key={transaction.id}>
                                                    <TableCell className="font-medium">TRX-{transaction.id}</TableCell>
                                                    <TableCell>
                                                        <div className="flex flex-col">
                                                            <span>{transaction.requisition?.requisition_number || 'N/A'}</span>
                                                            <span className="text-muted-foreground text-xs">
                                                                {transaction.requisition?.purpose || 'No purpose specified'}
                                                            </span>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={getStatusBadgeVariant(transaction.status)}>
                                                            {transaction.status === 'opened' && 'Pending'}
                                                            {transaction.status === 'updated' && 'Ready for Disbursement'}
                                                            {transaction.status === 'completed' && 'Completed'}
                                                            {!['opened', 'updated', 'completed'].includes(transaction.status) && transaction.status}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>{formatCurrency(transaction.total_amount)}</TableCell>
                                                    <TableCell>{formatDate(transaction.created_at)}</TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end space-x-2">
                                                            <Button variant="ghost" size="icon" asChild>
                                                                <Link href={`/transactions/${transaction.id}`}>
                                                                    <Eye className="h-4 w-4" />
                                                                </Link>
                                                            </Button>

                                                            {canManageTransactions && transaction.status === 'updated' && (
                                                                <Button variant="ghost" size="icon" asChild>
                                                                    <Link href={`/transactions/${transaction.id}`}>
                                                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                                                    </Link>
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {transactions.last_page > 1 && (
                                <Pagination>
                                    <div className="flex items-center justify-between">
                                        <span className="text-muted-foreground text-sm">
                                            Showing {transactions.data.length} of {transactions.total} transactions
                                        </span>
                                        <div className="flex items-center space-x-2">
                                            {transactions.links.map((link, i) => (
                                                <Button
                                                    key={i}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    disabled={!link.url}
                                                    onClick={() => link.url && router.visit(link.url)}
                                                    className="px-4 py-2"
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                </Pagination>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
