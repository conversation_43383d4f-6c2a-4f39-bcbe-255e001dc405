import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { CheckCircle, AlertCircle, ArrowLeft, FileText } from 'lucide-react';
import { type TemplatePreviewData } from '@/types/WorkflowTemplate';

interface Props {
    template: TemplatePreviewData['template'];
    previewData: TemplatePreviewData['previewData'];
    validation: TemplatePreviewData['validation'];
    context: TemplatePreviewData['context'];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Approval Workflows',
        href: '/approval-workflows',
    },
    {
        title: 'Templates',
        href: '/workflow-templates',
    },
    {
        title: 'Preview',
        href: '#',
    },
];

export default function WorkflowTemplatePreview({
    template,
    previewData,
    validation,
    context
}: Props) {
    const { data, setData, post, processing, errors } = useForm({
        custom_name: previewData.name || '',
        is_default: previewData.is_default || false,
        ...context
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('workflow-templates.create-from-template', template.id));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Preview: ${template.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-background/100 text-foreground/100 text-2xl font-bold md:text-3xl">
                            Preview Template
                        </h1>
                        <p className="text-muted-foreground mt-2">
                            Review and customize the workflow before creating it.
                        </p>
                    </div>
                    <Button variant="outline" asChild>
                        <a href={route('workflow-templates.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Templates
                        </a>
                    </Button>
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Template Information */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                                    <FileText className="h-5 w-5 text-primary" />
                                </div>
                                <div>
                                    <CardTitle>{template.name}</CardTitle>
                                    <CardDescription>
                                        <Badge variant="secondary">{template.category}</Badge>
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {template.description && (
                                <p className="text-sm text-muted-foreground mb-4">
                                    {template.description}
                                </p>
                            )}

                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-medium mb-2">Workflow Steps</h4>
                                    <div className="space-y-2">
                                        {previewData.steps.map((step, index) => (
                                            <div key={index} className="flex items-center gap-3 p-3 rounded-lg border bg-muted/30">
                                                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                                                    {step.step_number}
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-medium text-sm">
                                                        {step.description || `Step ${step.step_number}`}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {step.role_id ? `Role ID: ${step.role_id}` :
                                                         step.approver_user_id ? `User ID: ${step.approver_user_id}` :
                                                         'No approver assigned'}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <div className="flex items-center gap-2">
                                    {validation.valid ? (
                                        <>
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            <span className="text-sm text-green-600">Template is valid</span>
                                        </>
                                    ) : (
                                        <>
                                            <AlertCircle className="h-4 w-4 text-red-600" />
                                            <span className="text-sm text-red-600">Template has validation errors</span>
                                        </>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Customization Form */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Customize Workflow</CardTitle>
                            <CardDescription>
                                Adjust the workflow settings before creating it.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <Label htmlFor="custom_name">Workflow Name</Label>
                                    <Input
                                        id="custom_name"
                                        value={data.custom_name}
                                        onChange={(e) => setData('custom_name', e.target.value)}
                                        placeholder="Enter a custom name for this workflow"
                                    />
                                    {errors.custom_name && (
                                        <p className="text-sm text-red-600 mt-1">{errors.custom_name}</p>
                                    )}
                                </div>

                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="is_default"
                                        checked={data.is_default}
                                        onChange={(e) => setData('is_default', e.target.checked)}
                                        className="rounded border-gray-300"
                                    />
                                    <Label htmlFor="is_default">Set as default workflow</Label>
                                </div>

                                {!validation.valid && (
                                    <div className="p-4 rounded-lg border border-red-200 bg-red-50">
                                        <h4 className="font-medium text-red-800 mb-2">Validation Errors</h4>
                                        <ul className="text-sm text-red-700 space-y-1">
                                            {Object.entries(validation.errors).map(([field, messages]) => (
                                                <li key={field}>
                                                    <strong>{field}:</strong> {Array.isArray(messages) ? messages.join(', ') : messages}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}

                                <div className="flex gap-3 pt-4">
                                    <Button
                                        type="submit"
                                        disabled={processing || !validation.valid}
                                        className="flex-1"
                                    >
                                        {processing ? 'Creating...' : 'Create Workflow'}
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        asChild
                                    >
                                        <a href={route('workflow-templates.index')}>
                                            Cancel
                                        </a>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
