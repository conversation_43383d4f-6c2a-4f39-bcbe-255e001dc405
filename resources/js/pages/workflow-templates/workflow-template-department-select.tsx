import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, FileText, Building2 } from 'lucide-react';
import { type WorkflowTemplate } from '@/types/WorkflowTemplate';

interface Department {
    id: number;
    name: string;
}

interface Props {
    template: WorkflowTemplate;
    departments: Department[];
    context: {
        organization_id: number;
        branch_id?: number;
        custom_name?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Approval Workflows',
        href: '/approval-workflows',
    },
    {
        title: 'Templates',
        href: '/workflow-templates',
    },
    {
        title: 'Select Department',
        href: '#',
    },
];

export default function WorkflowTemplateDepartmentSelect({
    template,
    departments,
    context
}: Props) {
    const { data, setData, get, processing } = useForm({
        department_id: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (data.department_id) {
            get(route('workflow-templates.preview', {
                template: template.id,
                organization_id: context.organization_id,
                department_id: data.department_id,
                ...(context.branch_id && { branch_id: context.branch_id }),
                ...(context.custom_name && { custom_name: context.custom_name }),
            }));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Select Department - ${template.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-background/100 text-foreground/100 text-2xl font-bold md:text-3xl">
                            Select Department
                        </h1>
                        <p className="text-muted-foreground mt-2">
                            This template requires department-specific roles. Please select your department.
                        </p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('workflow-templates.index', { organization_id: context.organization_id })}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Templates
                        </Link>
                    </Button>
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Template Information */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                                    <FileText className="h-5 w-5 text-primary" />
                                </div>
                                <div>
                                    <CardTitle>{template.name}</CardTitle>
                                    <CardDescription>
                                        <Badge variant="secondary">{template.category}</Badge>
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {template.description && (
                                <p className="text-sm text-muted-foreground mb-4">
                                    {template.description}
                                </p>
                            )}

                            <div className="space-y-4">
                                <div>
                                    <h4 className="font-medium mb-2">Why Department Selection is Required</h4>
                                    <div className="space-y-2">
                                        {template.template_data.steps.map((step, index) => {
                                            const requiresDept = step.description?.includes('Department Head') ||
                                                               step.description?.includes('HOD') ||
                                                               step.description?.includes('Direct Supervisor');

                                            if (!requiresDept) return null;

                                            return (
                                                <div key={index} className="flex items-center gap-3 p-3 rounded-lg border bg-yellow-50 border-yellow-200">
                                                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-yellow-500 text-white text-xs font-medium">
                                                        {step.step_number}
                                                    </div>
                                                    <div className="flex-1">
                                                        <p className="font-medium text-sm text-yellow-800">
                                                            {step.description}
                                                        </p>
                                                        <p className="text-xs text-yellow-700">
                                                            Requires department-specific role assignment
                                                        </p>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Department Selection */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                                    <Building2 className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <CardTitle>Select Your Department</CardTitle>
                                    <CardDescription>
                                        Choose the department for role assignment
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <Label className="text-base font-medium">Available Departments</Label>
                                    <div className="mt-3 space-y-2">
                                        {departments.map((department) => (
                                            <div key={department.id} className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors">
                                                <input
                                                    type="radio"
                                                    id={`dept-${department.id}`}
                                                    name="department_id"
                                                    value={department.id.toString()}
                                                    checked={data.department_id === department.id.toString()}
                                                    onChange={(e) => setData('department_id', e.target.value)}
                                                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                                />
                                                <Label
                                                    htmlFor={`dept-${department.id}`}
                                                    className="flex-1 cursor-pointer font-medium"
                                                >
                                                    {department.name}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {departments.length === 0 && (
                                    <div className="text-center py-8">
                                        <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <p className="text-sm text-muted-foreground">
                                            No departments available in this organization.
                                        </p>
                                    </div>
                                )}

                                <div className="flex gap-3 pt-4">
                                    <Button
                                        type="submit"
                                        disabled={processing || !data.department_id || departments.length === 0}
                                        className="flex-1"
                                    >
                                        {processing ? 'Loading...' : 'Continue to Preview'}
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        asChild
                                    >
                                        <Link href={route('workflow-templates.index', { organization_id: context.organization_id })}>
                                            Cancel
                                        </Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
