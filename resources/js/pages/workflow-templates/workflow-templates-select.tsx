import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { FileText, PlusCircle, Settings, ArrowRight } from 'lucide-react';
import { type TemplatesByCategory, type WorkflowTemplate } from '@/types/WorkflowTemplate';

interface Props {
    templatesByCategory: TemplatesByCategory;
    selectedOrganizationId?: number;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Approval Workflows',
        href: '/approval-workflows',
    },
    {
        title: 'Create from Template',
        href: '/workflow-templates',
    },
];

// Helper function to check if template requires department
function templateRequiresDepartment(template: WorkflowTemplate): boolean {
    if (!template.template_data?.steps) return false;

    return template.template_data.steps.some((step) => {
        const description = step.description || '';
        return description.includes('Department Head') ||
               description.includes('HOD') ||
               description.includes('Direct Supervisor');
    });
}

export default function WorkflowTemplatesSelect({
    templatesByCategory,
    selectedOrganizationId
}: Props) {
    const hasTemplates = Object.keys(templatesByCategory).length > 0;

    // Get department_id from URL if available
    const urlParams = new URLSearchParams(window.location.search);
    const departmentId = urlParams.get('department_id');

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Workflow from Template" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-background/100 text-foreground/100 text-2xl font-bold md:text-3xl">
                            Create Workflow
                        </h1>
                        <p className="text-muted-foreground mt-2">
                            Choose a template to quickly create a workflow, or create a custom workflow from scratch.
                        </p>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Template Option */}
                    <Card className="relative overflow-hidden">
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                                    <FileText className="h-5 w-5 text-primary" />
                                </div>
                                <div>
                                    <CardTitle>Use Template</CardTitle>
                                    <CardDescription>
                                        Start with a pre-configured workflow template
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground mb-4">
                                Choose from our collection of proven workflow templates designed for common business processes.
                                Templates can be customized to fit your organization's needs.
                            </p>
                            {hasTemplates ? (
                                <div className="space-y-4">
                                    {Object.entries(templatesByCategory).map(([category, templates]) => (
                                        <div key={category} className="space-y-2">
                                            <h4 className="font-medium text-sm">{category}</h4>
                                            <div className="grid gap-2">
                                                {templates.slice(0, 3).map((template) => (
                                                    <button
                                                        key={template.id}
                                                        onClick={() => {
                                                            router.visit(route('workflow-templates.preview', {
                                                                template: template.id,
                                                                organization_id: selectedOrganizationId,
                                                                ...(departmentId && { department_id: departmentId })
                                                            }));
                                                        }}
                                                        className="w-full flex items-center justify-between p-3 rounded-lg border bg-muted/30 hover:bg-primary/5 hover:border-primary/50 transition-colors cursor-pointer text-left"
                                                    >
                                                        <div className="flex-1">
                                                            <div className="flex items-center gap-2">
                                                                <p className="font-medium text-sm">{template.name}</p>
                                                                {templateRequiresDepartment(template) && (
                                                                    <Badge variant="outline" className="text-xs">
                                                                        Dept Required
                                                                    </Badge>
                                                                )}
                                                            </div>
                                                            {template.description && (
                                                                <p className="text-xs text-muted-foreground">
                                                                    {template.description}
                                                                </p>
                                                            )}
                                                        </div>
                                                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                                                    </button>
                                                ))}
                                            </div>
                                            {templates.length > 3 && (
                                                <p className="text-xs text-muted-foreground">
                                                    +{templates.length - 3} more templates in this category
                                                </p>
                                            )}
                                        </div>
                                    ))}
                                    <Button className="w-full" asChild>
                                        <Link href={route('workflow-templates.index', {
                                            ...(selectedOrganizationId && { organization_id: selectedOrganizationId }),
                                            ...(departmentId && { department_id: departmentId })
                                        })}>
                                            Browse All Templates
                                        </Link>
                                    </Button>
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-sm text-muted-foreground">
                                        No templates available yet.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Custom Option */}
                    <Card className="relative overflow-hidden">
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-secondary/10">
                                    <Settings className="h-5 w-5 text-secondary-foreground" />
                                </div>
                                <div>
                                    <CardTitle>Create Custom</CardTitle>
                                    <CardDescription>
                                        Build a workflow from scratch
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground mb-6">
                                Create a completely custom workflow tailored to your specific requirements.
                                Define your own approval steps, roles, and conditions.
                            </p>
                            <Button className="w-full" variant="outline" asChild>
                                <Link href={route('approval-workflows.create', selectedOrganizationId ? { organization_id: selectedOrganizationId } : {})}>
                                    <PlusCircle className="h-4 w-4 mr-2" />
                                    Create Custom Workflow
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Stats */}
                {hasTemplates && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Available Templates</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {Object.entries(templatesByCategory).map(([category, templates]) => (
                                    <Badge key={category} variant="secondary">
                                        {category} ({templates.length})
                                    </Badge>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
