import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Head, Link } from '@inertiajs/react';
import { Eye, Filter } from 'lucide-react';

interface User {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
}

interface Department {
    id: number;
    name: string;
}

interface ApprovalStep {
    id: number;
    step_number: number;
    role_id: number;
    approver_user_id: number | null;
    description: string;
    role?: {
        id: number;
        name: string;
    };
    approver?: {
        id: number;
        first_name: string;
        last_name: string;
    };
}

interface Approval {
    id: number;
    requisition_id: number;
    approval_workflow_step_id: number;
    approver_user_id: number;
    action: string;
    comments: string;
    created_at: string;
    approver?: User;
    step?: ApprovalStep;
}

interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    total_amount: number;
    status: string;
    created_at: string;
    requester: User;
    department: Department;
    current_approval_step_id: number;
    currentApprovalStep?: ApprovalStep;
    approvals?: Approval[];
}

interface ChartOfAccount {
    id: number;
    name: string;
    account_type: string;
}

interface Props {
    requisitions: {
        data: Requisition[];
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        meta: {
            current_page: number;
            from: number;
            last_page: number;
            links: {
                url: string | null;
                label: string;
                active: boolean;
            }[];
            path: string;
            per_page: number;
            to: number;
            total: number;
        };
    };
    chartOfAccounts: Record<number, ChartOfAccount>;
    filters: {
        search?: string;
        sort?: string;
        direction?: string;
        status?: string;
    };
}

export default function MyApprovalHistory({ requisitions, filters }: Props) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending_approval':
                return <Badge className="bg-accent/50">Pending Approval</Badge>;
            case 'approved':
                return <Badge className="bg-green-500">Approved</Badge>;
            case 'rejected':
                return <Badge className="bg-red-500">Rejected</Badge>;
            case 'returned_for_revision':
                return <Badge className="bg-orange-500">Returned for Revision</Badge>;
            default:
                return <Badge className="bg-gray-500">{status}</Badge>;
        }
    };

    const getUserApprovalStatus = (requisition: Requisition) => {
        if (!requisition.approvals || requisition.approvals.length === 0) {
            return <Badge className="bg-blue-500">Pending Your Action</Badge>;
        }

        // Find the most recent approval by this user
        const userApprovals = requisition.approvals.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        const latestApproval = userApprovals[0];

        switch (latestApproval.action) {
            case 'approved':
                return <Badge className="bg-green-500">You Approved</Badge>;
            case 'rejected':
                return <Badge className="bg-red-500">You Rejected</Badge>;
            case 'returned_for_revision':
                return <Badge className="bg-orange-500">You Returned</Badge>;
            default:
                return <Badge className="bg-gray-500">{latestApproval.action}</Badge>;
        }
    };

    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Approval History" />

            <div className="container mx-auto py-8">
                <div className="mb-6 flex items-center justify-between">
                    <div>
                        <h1 className="text-foreground/100 text-background/100 text-2xl font-bold">My Approval History</h1>
                        <div className="mt-2">
                            <Link href={route('requisitions.my-approvals')} className="text-sm text-blue-600 hover:underline">
                                View requisitions awaiting my approval
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-center space-x-2">
                        <form method="GET" className="flex items-center space-x-2">
                            <Input type="text" name="search" placeholder="Search requisitions..." defaultValue={filters.search} className="w-64" />

                            <Select name="status" defaultValue={filters.status || 'all'}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                                    <SelectItem value="approved">Approved</SelectItem>
                                    <SelectItem value="rejected">Rejected</SelectItem>
                                    <SelectItem value="returned_for_revision">Returned for Revision</SelectItem>
                                </SelectContent>
                            </Select>

                            <Button type="submit" size="sm">
                                <Filter className="mr-2 h-4 w-4" />
                                Filter
                            </Button>
                        </form>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Requisitions You've Been Involved With</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {!requisitions.data ? (
                            <div className="py-8 text-center">
                                <p className="text-gray-500">Loading requisition data...</p>
                            </div>
                        ) : requisitions.data.length === 0 ? (
                            <div className="py-8 text-center">
                                <p className="text-gray-500">You haven't been involved in any requisition approvals yet.</p>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <table className="w-full border-collapse">
                                    <thead>
                                        <tr className="bg-gray-100 dark:bg-gray-800">
                                            <th className="px-4 py-2 text-left">Requisition #</th>
                                            <th className="px-4 py-2 text-left">Date</th>
                                            <th className="px-4 py-2 text-left">Requester</th>
                                            <th className="px-4 py-2 text-left">Department</th>
                                            <th className="px-4 py-2 text-left">Purpose</th>
                                            <th className="px-4 py-2 text-right">Amount</th>
                                            <th className="px-4 py-2 text-center">Status</th>
                                            <th className="px-4 py-2 text-center">Your Action</th>
                                            <th className="px-4 py-2 text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {requisitions.data.map((requisition) => (
                                            <tr key={requisition.id} className="border-t border-gray-200 dark:border-gray-700">
                                                <td className="px-4 py-2">{requisition.requisition_number}</td>
                                                <td className="px-4 py-2">{formatDate(requisition.created_at)}</td>
                                                <td className="px-4 py-2">
                                                    {requisition.requester.first_name} {requisition.requester.last_name}
                                                </td>
                                                <td className="px-4 py-2">{requisition.department.name}</td>
                                                <td className="px-4 py-2">{requisition.purpose}</td>
                                                <td className="px-4 py-2 text-right">{formatCurrency(requisition.total_amount)}</td>
                                                <td className="px-4 py-2 text-center">{getStatusBadge(requisition.status)}</td>
                                                <td className="px-4 py-2 text-center">{getUserApprovalStatus(requisition)}</td>
                                                <td className="px-4 py-2 text-center">
                                                    <Link
                                                        href={route('requisitions.approvals', {
                                                            requisition: requisition.id,
                                                            department: requisition.department,
                                                        })}
                                                    >
                                                        <Button size="sm" variant="outline">
                                                            <Eye className="mr-1 h-4 w-4" />
                                                            View
                                                        </Button>
                                                    </Link>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}

                        {/* Pagination */}
                        {requisitions.meta && requisitions.meta.last_page > 1 && (
                            <div className="mt-6 flex items-center justify-between">
                                <div className="text-sm text-gray-500">
                                    Showing {requisitions.meta.from} to {requisitions.meta.to} of {requisitions.meta.total} requisitions
                                </div>
                                <div className="flex space-x-1">
                                    {requisitions.meta.links &&
                                        requisitions.meta.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <span
                                                        key={i}
                                                        className="rounded border border-gray-300 px-3 py-1 text-gray-400"
                                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                                    />
                                                );
                                            }

                                            return (
                                                <Link
                                                    key={i}
                                                    href={link.url}
                                                    className={`rounded border border-gray-300 px-3 py-1 ${
                                                        link.active ? 'bg-blue-500 text-white' : 'text-gray-700'
                                                    }`}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            );
                                        })}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
