import { useState, use<PERSON><PERSON><PERSON>, FormEventHandler } from 'react';
import { Head } from '@inertiajs/react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { PageProps } from '@inertiajs/core';
import { router, usePage } from '@inertiajs/react';
import { Plus, Trash, LoaderCircle } from "lucide-react";
// Removed Sonner toast - using global ToastContainer instead
import AppLayout from '@/layouts/app-layout';

interface RequisitionItem {
    chart_of_account_id: number | null;
    description: string;
    quantity: number;
    unit_price: number;
}

type ChartOfAccount = {
    id: number;
    code: string;
    name: string;
    account_type: string;
    parent_id: number | null;
    organization_id: number;
};

type Department = {
    id: number;
    name: string;
};

interface CreateRequisitionPageProps extends PageProps {
    requisition_form_uuid: string;
    list_of_chart_of_accounts: ChartOfAccount[];
    department_id: number | null;
    department_name: string | null;
    user_departments: Department[];
}

const CreateRequisition = () => {
    const {
        requisition_form_uuid,
        list_of_chart_of_accounts,
        department_id,
        department_name,
        user_departments = [],
    } = usePage<CreateRequisitionPageProps>().props;

    // Initialize chart of accounts on component mount
    useEffect(() => {
        // Chart of accounts loaded
    }, [list_of_chart_of_accounts]);

    // Function to handle department change
    const handleDepartmentChange = (departmentId: string) => {
        // Use Inertia router to navigate to the same page with a different department parameter
        router.get('/requisitions/create', { department: departmentId });
    };

    const [purpose, setPurpose] = useState('');
    const [notes, setNotes] = useState('');
    const [items, setItems] = useState<RequisitionItem[]>([{ chart_of_account_id: null, description: '', quantity: 1, unit_price: 0 }]);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [processing, setProcessing] = useState(false);

    const handleItemChange = (index: number, field: keyof RequisitionItem, value: string) => {
        const updated = [...items];
        updated[index] = {
            ...updated[index],
            [field]: field === 'quantity' || field === 'unit_price' ? Number(value) : value,
        };
        setItems(updated);
    };

    const addItem = () => {
        setItems([...items, { chart_of_account_id: null, description: '', quantity: 1, unit_price: 0 }]);
    };

  const removeItem = (index: number) => {
    if (items.length === 1) {
      window.showToast?.({
        title: 'Validation Error',
        message: 'At least one item required',
        type: 'error'
      });
      return;
    }

        const updated = [...items];
        updated.splice(index, 1);
        setItems(updated);
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!purpose.trim()) {
            newErrors.purpose = 'Narration is required';
        }

        items.forEach((item, index) => {
            if (!item.chart_of_account_id) {
                newErrors[`items.${index}.chart_of_account_id`] = 'Chart of account is required';
            }
            if (!item.description.trim()) {
                newErrors[`items.${index}.description`] = 'Description is required';
            }
            if (item.quantity <= 0) {
                newErrors[`items.${index}.quantity`] = 'Quantity must be greater than zero';
            }
            if (item.unit_price < 0) {
                newErrors[`items.${index}.unit_price`] = 'Price cannot be negative';
            }
        });

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit: FormEventHandler = async (e) => {
        e.preventDefault();

    if (!validateForm()) {
      window.showToast?.({
        title: 'Validation Error',
        message: 'Please fix form errors',
        type: 'error'
      });
      return;
    }

        setProcessing(true);

    if (!requisition_form_uuid) {
      window.showToast?.({
        title: 'Error',
        message: 'Form ID missing',
        type: 'error'
      });
      setProcessing(false);
      return;
    }

        // Prepare the data to send
        const formData = {
            requisition_form_uuid,
            purpose,
            notes,
            department_id: department_id, // Include the department_id
            requisition_items: items.map((item) => ({
                chart_of_account_id: item.chart_of_account_id,
                description: item.description,
                quantity: item.quantity,
                unit_price: item.unit_price,
            })),
        };

    // Use Inertia's router.post for submission
    router.post('/requisitions', formData, {
      onSuccess: () => {
        // Notification system will handle the success toast
        // No immediate toast needed here
      },
      onError: (errors) => {
        setErrors(errors);
        window.showToast?.({
          title: 'Error',
          message: 'Submission failed. Check form errors.',
          type: 'error'
        });
      },
      onFinish: () => {
        setProcessing(false);
      },
      preserveState: true,
      preserveScroll: true,
      forceFormData: true
    });
  };

    const calculateTotal = () => {
        return items.reduce((sum, item) => sum + item.quantity * (item.unit_price || 0), 0).toFixed(2);
    };

  const breadcrumbs = [
    {
      title: 'Dashboard',
      href: '/dashboard',
    },
    {
      title: 'Requisitions',
      href: '/requisitions/history',
    },
    {
      title: 'Create Requisition',
      href: '/requisitions/create',
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Requisition" />
      {/* Using global ToastContainer instead of individual Toaster */}
      <Card className="w-full max-w-4xl mx-auto mb-4">
        <CardHeader>
          <CardTitle className="text-2xl">Department Information</CardTitle>
          <CardDescription>
            This requisition will be created for the following department
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Department:</p>
              <p className="text-lg font-medium">{department_name || 'Not selected'}</p>
            </div>

                            {user_departments.length > 1 && (
                                <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                    <Badge variant="secondary" className="w-fit">
                                        {user_departments.length} departments available
                                    </Badge>
                                    <Select value={department_id?.toString() || ''} onValueChange={handleDepartmentChange}>
                                        <SelectTrigger className="w-full sm:w-[200px]">
                                            <SelectValue placeholder="Switch department" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {user_departments.map((dept) => (
                                                <SelectItem key={dept.id} value={dept.id.toString()}>
                                                    {dept.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                <Card className="mx-auto w-full max-w-4xl">
                    <CardHeader>
                        <CardTitle className="text-2xl">Requisition Form</CardTitle>
                        <CardDescription>
                            Complete the form below to submit a new requisition request
                        </CardDescription>
                    </CardHeader>

                    <form onSubmit={handleSubmit}>
                        <CardContent className="space-y-6 pt-6">
                            <div className="space-y-2">
                                <Label htmlFor="purpose" className="text-base">
                                    Purpose <span className="text-destructive">*</span>
                                </Label>
                                <Input
                                    id="purpose"
                                    value={purpose}
                                    onChange={(e) => setPurpose(e.target.value)}
                                    placeholder="Briefly describe the purpose of this requisition"
                                    className={errors.purpose ? 'border-red-500' : ''}
                                    autoFocus
                                />
                                {errors.purpose && <p className="text-sm text-destructive">{errors.purpose}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes" className="text-base">
                                    Additional Notes
                                </Label>
                                <Textarea
                                    id="notes"
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Any additional information or context"
                                    rows={3}
                                />
                            </div>

                            <div className="pt-4">
                                <h3 className="mb-4 text-lg font-medium">Items</h3>
                                <div className="space-y-4">
                                    {items.map((item, index) => (
                                        <Card key={index} className="overflow-hidden border">
                                            <CardContent className="p-4">
                                                <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
                                                    <div className="space-y-2">
                                                        <Label htmlFor={`account-${index}`} className="text-sm">
                                                            Chart of Account <span className="text-destructive">*</span>
                                                        </Label>
                                                        <Select
                                                            value={item.chart_of_account_id ? item.chart_of_account_id.toString() : ''}
                                                            onValueChange={(value: string) => handleItemChange(index, 'chart_of_account_id', value)}
                                                        >
                                                            <SelectTrigger
                                                                id={`account-${index}`}
                                                                className={errors[`items.${index}.chart_of_account_id`] ? 'border-red-500' : ''}
                                                            >
                                                                <SelectValue placeholder="Select account" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {list_of_chart_of_accounts.map((coa) => (
                                                                    <SelectItem key={coa.id} value={coa.id.toString()}>
                                                                        {coa.name}
                                                                    </SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                        {errors[`items.${index}.chart_of_account_id`] && (
                                                            <p className="text-xs text-destructive">{errors[`items.${index}.chart_of_account_id`]}</p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor={`description-${index}`} className="text-sm">
                                                            Description <span className="text-destructive">*</span>
                                                        </Label>
                                                        <Input
                                                            id={`description-${index}`}
                                                            value={item.description}
                                                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                                                            placeholder="Item description"
                                                            className={errors[`items.${index}.description`] ? 'border-red-500' : ''}
                                                        />
                                                        {errors[`items.${index}.description`] && (
                                                            <p className="text-xs text-destructive">{errors[`items.${index}.description`]}</p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor={`quantity-${index}`} className="text-sm">
                                                            Quantity <span className="text-destructive">*</span>
                                                        </Label>
                                                        <Input
                                                            id={`quantity-${index}`}
                                                            type="number"
                                                            min="1"
                                                            value={item.quantity}
                                                            onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                                                            className={errors[`items.${index}.quantity`] ? 'border-red-500' : ''}
                                                        />
                                                        {errors[`items.${index}.quantity`] && (
                                                            <p className="text-xs text-destructive">{errors[`items.${index}.quantity`]}</p>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        <Label htmlFor={`price-${index}`} className="text-sm">
                                                            Unit Price <span className="text-destructive">*</span>
                                                        </Label>
                                                        <div className="flex items-center">
                                                            <Input
                                                                id={`price-${index}`}
                                                                type="number"
                                                                min="0"
                                                                step="0.01"
                                                                value={item.unit_price || ''}
                                                                placeholder="0"
                                                                onChange={(e) => handleItemChange(index, 'unit_price', e.target.value)}
                                                                className={errors[`items.${index}.unit_price`] ? 'border-red-500' : ''}
                                                            />
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => removeItem(index)}
                                                                className="ml-2 flex-shrink-0 text-destructive hover:bg-destructive/10 hover:text-destructive"
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                        {errors[`items.${index}.unit_price`] && (
                                                            <p className="text-xs text-destructive">{errors[`items.${index}.unit_price`]}</p>
                                                        )}
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}

                                    <Button type="button" onClick={addItem} variant="outline" className="mt-2 w-full">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add Another Item
                                    </Button>
                                </div>
                            </div>

                            <div className="flex justify-end pt-4">
                                <div className="bg-muted rounded-md p-4">
                                    <p className="font-medium">Total: KSH. {calculateTotal()}</p>
                                </div>
                            </div>
                        </CardContent>

                        <CardFooter className="flex justify-between">
                            <Button type="button" variant="outline" disabled={processing} onClick={() => router.visit('/requisitions/history')}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                Submit Requisition
                            </Button>
                        </CardFooter>
                    </form>
                </Card>
        </AppLayout>
    );
};

export default CreateRequisition;
