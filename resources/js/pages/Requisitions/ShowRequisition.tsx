import { AttachmentsList } from '@/components/AttachmentsList';
import { FileUpload } from '@/components/FileUpload';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Link } from '@inertiajs/react';
import { ArrowLeft, Edit, Home, PlusCircle } from 'lucide-react';
import AppLayout from "@/layouts/app-layout";

interface RequisitionItem {
    id: number;
    description: string;
    quantity: number;
    unit_price: number;
    chart_of_account_id: number;
    total_price: number;
}

interface Transaction {
    id: number;
    requisition_id: number;
    status: string;
    account_details: string | null;
    disbursement_transaction_id: string | null;
    total_amount: number;
    created_at: string;
}

// TransactionItem interface removed as it's not used

interface Attachment {
    id: number;
    original_name: string;
    file_size: number;
    mime_type: string;
    description?: string;
    is_evidence: boolean;
    uploaded_at_step?: string;
    created_at: string;
    uploader: {
        id: number;
        first_name: string;
        last_name: string;
    };
}

interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    notes: string | null;
    total_amount: number;
    items?: RequisitionItem[];
    transactions?: Transaction[];
    attachments?: Attachment[];
    status: string;
    requester_user_id: number;
}

interface AttachmentUIState {
    show_upload: boolean;
    redirect_to_transaction: boolean;
    transaction_id: number | null;
}

interface ShowRequisitionProps {
    requisition: Requisition;
    canEdit: boolean;
    canAttachFiles: boolean;
    attachmentUIState: AttachmentUIState;
}

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Requisitions',
        href: '/requisitions/history',
    },
    {
        title: 'Requisition Details',
        href: '#',
    },
];

const ShowRequisition = ({ requisition, canEdit, canAttachFiles, attachmentUIState }: ShowRequisitionProps) => {
    // Helper function to determine the chart of account type from the ID
    // TODO replace with passed in page props for the charts of accounts
    const getChartOfAccountType = (id: number) => {
        switch (id) {
            case 2:
                return 'Consumable';
            case 3:
                return 'Equipment';
            case 4:
                return 'Service';
            default:
                return 'General';
        }
    };

    // Helper function to render status badge
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending_approval':
                return <Badge className="dark:text-secondary-foreground/100 bg-accent/50 dark:bg-accent/60">Pending Approval</Badge>;
            case 'approved':
                return <Badge className="bg-primary-500">Approved</Badge>;
            case 'rejected':
                return <Badge className="dark:text-secondary-foreground/100 bg-red-500 dark:bg-red-600">Rejected</Badge>;
            case 'returned_for_revision':
                return <Badge className="dark:text-secondary-foreground/100 bg-orange-500 dark:bg-orange-600">Returned for Revision</Badge>;
            default:
                return <Badge className="dark:text-secondary-foreground/100 bg-gray-500 dark:bg-gray-600">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs} className="dark:bg-card-foreground/80">
            <div className="container mx-auto px-4 py-8">
                {/* Move navigation buttons to the top */}
                <div className="mb-6 flex flex-row gap-2 items-center justify-between">
                    <Button variant="outline" asChild className="order-1">
                        <Link href="/dashboard">
                            <Home className="mr-2 h-4 w-4" />
                            <span className="hidden sm:inline">Dashboard</span>
                        </Link>
                    </Button>
                    <div className="flex-1 flex justify-end order-3 sm:order-2">
                        <Button asChild className="w-full max-w-xs justify-center sm:w-auto">
                            <Link href="/requisitions/create">
                                <span className="sm:hidden"><PlusCircle className="h-5 w-5" /></span>
                                <span className="hidden sm:inline-flex"><PlusCircle className="mr-2 h-4 w-4" />Create New Requisition</span>
                            </Link>
                        </Button>
                    </div>
                </div>
                <Card className="mx-auto w-full max-w-4xl">
                    <CardHeader className="bg-primary/5">
                        <div className="flex items-start justify-between">
                            <div>
                                <CardTitle className="text-2xl">Requisition Details</CardTitle>
                                <CardDescription>Requisition number: {requisition.requisition_number}</CardDescription>
                            </div>
                            <div>{getStatusBadge(requisition.status)}</div>
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-6">
                        <div className="space-y-2">
                            <Label className="text-base">Purpose</Label>
                            <p className="text-foreground/100">{requisition.purpose}</p>
                        </div>
                        <div className="space-y-2">
                            <Label className="text-base">Additional Notes</Label>
                            {requisition.notes ? (
                                <p className="text-foreground/100">{requisition.notes}</p>
                            ) : (
                                <p className="text-gray-500 italic">No additional notes.</p>
                            )}
                        </div>

                        <div className="pt-4">
                            {requisition.status === 'approved' && requisition.transactions && requisition.transactions.length > 0 ? (
                                <>
                                    <div className="mb-4 flex items-center justify-between">
                                        <h3 className="text-lg font-medium">Disbursement Status</h3>
                                        <Button variant="outline" asChild>
                                            <Link href={`/disbursement/${requisition.transactions[0].id}`}>View Disbursement Details</Link>
                                        </Button>
                                    </div>

                                    <div className="mb-6 rounded-md border border-blue-200 bg-blue-50 p-4">
                                        <h4 className="mb-2 font-medium text-blue-800">Disbursement Information</h4>
                                        <p className="text-blue-700">
                                            This requisition has been approved and moved to the disbursement process. Current status:{' '}
                                            <Badge
                                                className={
                                                    requisition.transactions[0].status === 'opened'
                                                        ? 'bg-accent/50'
                                                        : requisition.transactions[0].status === 'updated'
                                                            ? 'bg-blue-500'
                                                            : 'bg-green-500'
                                                }
                                            >
                                                {requisition.transactions[0].status.charAt(0).toUpperCase() + requisition.transactions[0].status.slice(1)}
                                            </Badge>
                                        </p>
                                    </div>
                                </>
                            ) : (
                                <h3 className="mb-4 text-lg font-medium">Items</h3>
                            )}

                            {/* Excel-like table for requisition items */}
                            <div className="overflow-x-auto">
                                <table className="w-full border-collapse bg-background/100 shadow-sm">
                                    <thead>
                                        <tr className="bg-muted-foreground/40">
                                            <th className="border px-4 py-2 text-left text-foreground/100">Type</th>
                                            <th className="border px-4 py-2 text-left text-foreground/100">Description</th>
                                            <th className="border px-4 py-2 text-right text-foreground/100">Quantity</th>
                                            <th className="border px-4 py-2 text-right text-foreground/100">Unit Price</th>
                                            <th className="border px-4 py-2 text-right text-foreground/100">Total Price</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {requisition.items &&
                                            requisition.items.map((item, index) => (
                                                <tr key={index} className={index % 2 === 0 ? 'bg-background/70' : 'bg-background/90'}>
                                                    <td className="border px-4 py-2 text-foreground/90">{getChartOfAccountType(item.chart_of_account_id)}</td>
                                                    <td className="border px-4 py-2 text-foreground/90">{item.description}</td>
                                                    <td className="border px-4 py-2 text-right text-foreground/90">{item.quantity}</td>
                                                    <td className="border px-4 py-2 text-right text-foreground/90">${item.unit_price}</td>
                                                    <td className="border px-4 py-2 text-right text-foreground/90">${item.total_price}</td>
                                                </tr>
                                            ))}
                                        {!requisition.items && (
                                            <tr>
                                                <td colSpan={5} className="border px-4 py-2 text-center text-gray-500">
                                                    Items have been moved to the disbursement process
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                    <tfoot>
                                        <tr className="bg-primary/10 font-medium">
                                            <td colSpan={4} className="border px-4 py-2 text-right text-gray-900">
                                                Total:
                                            </td>
                                            <td className="border px-4 py-2 text-right font-bold text-gray-900">${requisition.total_amount}</td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        {/* Attachments Section */}
                        <div className="border-t pt-6">
                            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                {/* Existing Attachments */}
                                <AttachmentsList attachments={requisition.attachments || []} canDelete={canAttachFiles} className="w-full" />

                                {/* File Upload or Redirect to Transaction */}
                                {canAttachFiles && attachmentUIState.show_upload && (
                                    <FileUpload
                                        entityType="requisition"
                                        entityId={requisition.id}
                                        uploadedAtStep={requisition.status}
                                        className="w-full"
                                    />
                                )}

                                {/* Redirect to Disbursement Button */}
                                {canAttachFiles && attachmentUIState.redirect_to_transaction && (
                                    <Card className="w-full">
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <PlusCircle className="h-5 w-5" />
                                                Attach Evidence & Manage Disbursement
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <p className="text-gray-600">
                                                This requisition has been approved and converted to a transaction. Please go to the disbursement area to
                                                provide bank details and attach evidence.
                                            </p>
                                            <Button asChild className="w-full">
                                                <Link href={`/disbursement/${attachmentUIState.transaction_id}`}>Go to Disbursement Area</Link>
                                            </Button>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        </div>
                    </CardContent>
                    <CardFooter className="bg-muted/50 flex justify-between border-t p-4">
                        <div className="flex gap-2">
                            <Button variant="ghost" asChild>
                                <Link href="/requisitions/history">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to History
                                </Link>
                            </Button>
                        </div>
                        <div className="flex gap-2">
                            {canEdit && requisition.status === 'rejected' && (
                                <Button variant="secondary" asChild>
                                    <Link href={`/requisitions/${requisition.id}/edit-rejected`}>
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit Rejected Requisition
                                    </Link>
                                </Button>
                            )}
                        </div>
                    </CardFooter>
                </Card>
            </div>
        </AppLayout>
    );
};

export default ShowRequisition;
