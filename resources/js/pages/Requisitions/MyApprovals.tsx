import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { MobileTable, MobileTableHeader, MobileTableBody, MobileTableRow, MobileTableHead, MobileTableCell } from '@/components/ui/mobile-table';
import AppLayout from '@/layouts/app-layout';
import { useMobileTable } from '@/hooks/use-mobile-table';
import { formatCurrency } from '@/lib/utils';
import { Head, Link } from '@inertiajs/react';
import { Eye, Search } from 'lucide-react';

interface User {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
}

interface Department {
    id: number;
    name: string;
}

interface ApprovalStep {
    id: number;
    step_number: number;
    role_id: number;
    approver_user_id: number | null;
    description: string;
    role?: {
        id: number;
        name: string;
    };
    approver?: {
        id: number;
        first_name: string;
        last_name: string;
    };
}

interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    total_amount: number;
    status: string;
    created_at: string;
    requester: User;
    department: Department;
    current_approval_step_id: number;
    currentApprovalStep?: ApprovalStep;
}

interface ChartOfAccount {
    id: number;
    name: string;
    account_type: string;
}

interface Props {
    requisitions: {
        data: Requisition[];
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        meta: {
            current_page: number;
            from: number;
            last_page: number;
            links: {
                url: string | null;
                label: string;
                active: boolean;
            }[];
            path: string;
            per_page: number;
            to: number;
            total: number;
        };
    };
    chartOfAccounts: Record<number, ChartOfAccount>;
    filters: {
        search?: string;
        sort?: string;
        direction?: string;
    };
}

const breadcrumbs = [
    { title: 'Home', href: route('dashboard') },
    { title: 'My Approval Queue', href: route('requisitions.my-approvals') }
];

export default function MyApprovals({ requisitions, filters }: Props) {
    const { shouldShowItem, hasMoreItems, toggleShowAll, showAll } = useMobileTable({ maxVisibleItems: 5 });

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending_approval':
                return <Badge className="bg-accent/70">Pending Approval</Badge>;
            case 'approved':
                return <Badge className="bg-primary/70">Approved</Badge>;
            case 'rejected':
                return <Badge className="bg-destructive/80">Rejected</Badge>;
            case 'returned_for_revision':
                return <Badge className="bg-orange/70">Returned for Revision</Badge>;
            default:
                return <Badge className="bg-muted-foreground">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Approval Queue" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-foreground/100 text-2xl font-bold">
                            My Approval Queue
                        </h1>
                        <div className="mt-2">
                            <Link href={route('requisitions.my-approval-history')} className="text-sm text-primary/70 hover:underline">
                                View all requisitions I've been involved with
                            </Link>
                        </div>
                    </div>
                    <div className="flex w-full justify-center sm:w-auto sm:justify-end mt-4 sm:mt-0">
                        <form method="GET" className="flex w-full max-w-xs items-center space-x-2 sm:w-auto">
                            <Input type="text" name="search" placeholder="Search requisitions..." defaultValue={filters.search} className="w-full sm:w-64" />
                            <Button type="submit" size="sm">
                                <Search className="mr-2 h-4 w-4" />
                                <span className="hidden sm:inline">Search</span>
                            </Button>
                        </form>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Requisitions Awaiting Your Approval</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {!requisitions.data ? (
                            <div className="py-8 text-center">
                                <p className="text-muted-foreground">Loading requisition data...</p>
                            </div>
                        ) : requisitions.data.length === 0 ? (
                            <div className="py-8 text-center">
                                <p className="text-muted-foreground text-lg font-medium">No requisitions are currently awaiting your approval.</p>
                            </div>
                        ) : (
                            <>
                                <MobileTable>
                                    <MobileTableHeader>
                                        <tr className="bg-muted-foreground/90">
                                            <MobileTableHead>Requisition #</MobileTableHead>
                                            <MobileTableHead>Requester</MobileTableHead>
                                            <MobileTableHead>Department</MobileTableHead>
                                            <MobileTableHead>Purpose</MobileTableHead>
                                            <MobileTableHead className="text-right">Amount</MobileTableHead>
                                            <MobileTableHead className="text-center">Status</MobileTableHead>
                                            <MobileTableHead className="text-center">Current Step</MobileTableHead>
                                            <MobileTableHead className="text-center">Actions</MobileTableHead>
                                        </tr>
                                    </MobileTableHeader>
                                    <MobileTableBody>
                                        {requisitions.data.filter((_, idx) => shouldShowItem(idx)).map((requisition) => (
                                            <MobileTableRow
                                                key={requisition.id}
                                                primaryContent={
                                                    <>
                                                        <div className="flex items-center justify-between">
                                                            <span className="font-medium">{requisition.requisition_number}</span>
                                                            {getStatusBadge(requisition.status)}
                                                        </div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {requisition.purpose}
                                                        </div>
                                                        <div className="flex justify-between mt-2">
                                                            <span className="text-xs">{requisition.requester.first_name} {requisition.requester.last_name}</span>
                                                            <span className="text-xs">{formatCurrency(requisition.total_amount)}</span>
                                                        </div>
                                                    </>
                                                }
                                                expandedContent={
                                                    <div className="space-y-2">
                                                        <MobileTableCell label="Department">{requisition.department?.name || 'N/A'}</MobileTableCell>
                                                        <MobileTableCell label="Current Step">
                                                            {requisition.currentApprovalStep ? (
                                                                <span>
                                                                    Step {requisition.currentApprovalStep.step_number}:&nbsp;
                                                                    {requisition.currentApprovalStep.description ||
                                                                        (requisition.currentApprovalStep.role
                                                                            ? requisition.currentApprovalStep.role.name
                                                                            : 'Unknown')}
                                                                </span>
                                                            ) : (
                                                                <span>N/A</span>
                                                            )}
                                                        </MobileTableCell>
                                                        <MobileTableCell label="Actions">
                                                            <Link
                                                                href={route('requisitions.approvals', {
                                                                    requisition: requisition.id,
                                                                    department: requisition.department?.id,
                                                                })}
                                                            >
                                                                <Button size="sm" variant="outline">
                                                                    <Eye className="mr-1 h-4 w-4" />
                                                                    View
                                                                </Button>
                                                            </Link>
                                                        </MobileTableCell>
                                                    </div>
                                                }
                                            >
                                                <MobileTableCell>{requisition.requisition_number}</MobileTableCell>
                                                <MobileTableCell>{requisition.requester.first_name} {requisition.requester.last_name}</MobileTableCell>
                                                <MobileTableCell>{requisition.department?.name || 'N/A'}</MobileTableCell>
                                                <MobileTableCell>{requisition.purpose}</MobileTableCell>
                                                <MobileTableCell className="text-right">{formatCurrency(requisition.total_amount)}</MobileTableCell>
                                                <MobileTableCell className="text-center">{getStatusBadge(requisition.status)}</MobileTableCell>
                                                <MobileTableCell className="text-center">
                                                    {requisition.currentApprovalStep ? (
                                                        <span>
                                                            Step {requisition.currentApprovalStep.step_number}:&nbsp;
                                                            {requisition.currentApprovalStep.description ||
                                                                (requisition.currentApprovalStep.role
                                                                    ? requisition.currentApprovalStep.role.name
                                                                    : 'Unknown')}
                                                        </span>
                                                    ) : (
                                                        <span>N/A</span>
                                                    )}
                                                </MobileTableCell>
                                                <MobileTableCell className="text-center">
                                                    <Link
                                                        href={route('requisitions.approvals', {
                                                            requisition: requisition.id,
                                                            department: requisition.department?.id,
                                                        })}
                                                    >
                                                        <Button size="sm" variant="outline">
                                                            <Eye className="mr-1 h-4 w-4" />
                                                            View
                                                        </Button>
                                                    </Link>
                                                </MobileTableCell>
                                            </MobileTableRow>
                                        ))}
                                    </MobileTableBody>
                                </MobileTable>
                                {/* Show more/less button for mobile */}
                                {hasMoreItems(requisitions.data.length) && (
                                    <div className="mt-4 flex justify-center md:hidden">
                                        <Button variant="outline" onClick={toggleShowAll} className="w-full max-w-xs">
                                            {showAll ? 'Show less' : `Show ${requisitions.data.length - 5} more`}
                                        </Button>
                                    </div>
                                )}
                            </>
                        )}

                        {/* Pagination */}
                        {requisitions.meta && requisitions.meta.last_page > 1 && (
                            <div className="mt-6 flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                                <div className="text-sm text-muted-foreground">
                                    Showing {requisitions.meta.from} to {requisitions.meta.to} of {requisitions.meta.total} requisitions
                                </div>
                                <div className="flex flex-wrap gap-1">
                                    {requisitions.meta.links &&
                                        requisitions.meta.links.map((link, i) => {
                                            if (link.url === null) {
                                                return (
                                                    <span
                                                        key={i}
                                                        className="rounded border border-muted-foreground/90 px-3 py-1 text-muted-foreground"
                                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                                    />
                                                );
                                            }

                                            return (
                                                <Link
                                                    key={i}
                                                    href={link.url}
                                                    className={`rounded border border-muted-foreground/90 px-3 py-1 ${
                                                        link.active ? 'bg-primary/70 text-white' : 'text-muted-foreground'
                                                    }`}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            );
                                        })}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
