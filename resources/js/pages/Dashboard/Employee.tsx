import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, usePage } from '@inertiajs/react';
import DashboardSwitcher from '@/components/DashboardSwitcher';
import 'chart.js/auto';
import { CheckCircle, Clock, FileText, Package, PlusCircle, User } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';


interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    total_amount: number;
    status: string;
    created_at: string;
}

interface EmployeeDashboardProps {
    requisitions: Requisition[];
    pendingApprovals?: Requisition[];
    requisitionStats: {
        total: number;
        pending: number;
        approved: number;
        rejected: number;
        pendingApprovals: number;
    };
    organization: {
        id: number;
        name: string;
    };
    department: {
        id: number;
        name: string;
    } | null;

    userRoles: Array<{
        role: string;
        organization: string;
        branch: string;
        department: string;
    }>;
    frequentCharts: Array<{
        id: number;
        name: string;
        count: number;
    }>;
    spendingTrends: Array<{
        period: string;
        amount: number;
    }>;
    userDepartments: Array<{
        id: number;
        name: string;
    }>;
    user: {
        id: number;
        name: string;
        email: string;
        permissions: string[];
    };
    availableContexts?: Array<{
        type: 'role' | 'department';
        key: string;
        label: string;
        organization: { id: number; name: string } | null;
        department: { id: number; name: string } | null;
        priority: number;
        role_id?: number;
        department_id?: number;
    }>;
    flash?: {
        success?: string;
        error?: string;
    };
}

export default function EmployeeDashboard({
    requisitions = [],
    requisitionStats = { total: 0, pending: 0, approved: 0, rejected: 0, pendingApprovals: 0 },
    organization = { id: 0, name: '' },
    department = null,
    userRoles = [],

    userDepartments = [],
    user = { id: 0, name: '', email: '', permissions: [] },
    availableContexts = []
}: EmployeeDashboardProps) {
    const { flash } = usePage().props as { flash?: { success?: string; error?: string } };

    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
    ];

    // Handle flash messages
    useEffect(() => {
        if (flash?.success) {
            window.showToast?.({
                title: 'Success',
                message: flash.success,
                type: 'success'
            });
        }
        if (flash?.error) {
            window.showToast?.({
                title: 'Error',
                message: flash.error,
                type: 'error'
            });
        }
    }, [flash]);

    const initializeSelectedDepartment = useCallback(() => {

        const storedDepartment = localStorage.getItem('selectedDepartment');


        if (storedDepartment && userDepartments.some(dept => dept.id.toString() === storedDepartment)) {
            return storedDepartment;
        }


        if (department?.id) {
            return department.id.toString();
        }


        if (userDepartments.length > 0) {
            return userDepartments[0].id.toString();
        }

        return '';
    }, [department, userDepartments]);

    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [activeDepartmentName, setActiveDepartmentName] = useState('');


    useEffect(() => {
        const initialDepartment = initializeSelectedDepartment();
        setSelectedDepartment(initialDepartment);


        if (initialDepartment && userDepartments) {
            const deptObj = userDepartments.find(dept => dept.id.toString() === initialDepartment);
            if (deptObj) {
                setActiveDepartmentName(deptObj.name);
            }
        }
    }, [initializeSelectedDepartment, userDepartments]);

    const handleDepartmentChange = (departmentId: string) => {

        setSelectedDepartment(departmentId);


        localStorage.setItem('selectedDepartment', departmentId);


        if (userDepartments) {
            const deptObj = userDepartments.find(dept => dept.id.toString() === departmentId);
            if (deptObj) {
                setActiveDepartmentName(deptObj.name);
            }
        }

        // Navigate to dashboard with the selected department
        router.get(
            '/dashboard',
            { department: departmentId },
            {
                preserveState: true,
                preserveScroll: true,
                only: ['requisitions', 'requisitionStats', 'budgetData', 'frequentCharts', 'spendingTrends'],
            },
        );
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending_approval':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-accent/200 dark:border-accent/60 dark:bg-accent/90/20 text-accent/80"
                    >
                        Pending
                    </Badge>
                );
            case 'approved':
                return (
                    <Badge variant="outline" className="bg-primary/100 text-primary-800">
                        Approved
                    </Badge>
                );
            case 'rejected':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-red-100 text-red-800 dark:border-red-600 dark:bg-red-900/20"
                    >
                        Rejected
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };




    const getCurrentRole = () => {
        if (!userRoles || userRoles.length === 0) return null;


        if (activeDepartmentName) {
            const matchingRole = userRoles.find(role =>
                role.department.toLowerCase() === activeDepartmentName.toLowerCase()
            );
            if (matchingRole) return matchingRole;
        }


        return userRoles[0];
    };

    const currentRole = getCurrentRole();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Employee Dashboard" />
            <div className="bg-background/90 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-2xl font-bold">Employee Dashboard</h1>
                    <Button asChild>
                        <Link href={'/requisitions'}>
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Create Requisition
                        </Link>
                    </Button>
                </div>

                {availableContexts && availableContexts.length > 1 && (
                    <div className="w-full max-w-sm">
                        <DashboardSwitcher availableContexts={availableContexts} />
                    </div>
                )}

                {/* Organization Info Card */}
                <Card>
                    <CardHeader>
                        <CardTitle>Your Information</CardTitle>
                        <CardDescription>Your organization and department details</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div>
                                <p className="text-muted-foreground text-sm font-medium">Organization</p>
                                <p className="text-base font-semibold">{organization?.name || 'Not assigned'}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground text-sm font-medium">Branch</p>
                                <p className="text-base font-semibold">{currentRole?.branch || 'Not assigned'}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground text-sm font-medium">Department</p>
                                <p className="text-base font-semibold">
                                    {activeDepartmentName || department?.name || currentRole?.department || 'Not assigned'}
                                    {userDepartments.length > 1 && (
                                        <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700">
                                            {userDepartments.length} departments
                                        </Badge>
                                    )}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {userDepartments.length > 1 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Switch Department</CardTitle>
                            <CardDescription>Select the department to view data for</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Select value={selectedDepartment} onValueChange={handleDepartmentChange}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select department" />
                                </SelectTrigger>
                                <SelectContent>
                                    {userDepartments.map((dept) => (
                                        <SelectItem key={dept.id} value={dept.id.toString()}>
                                            {dept.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </CardContent>
                    </Card>
                )}

                <Card>
                    <CardHeader>
                        <CardTitle>Recent Requisitions</CardTitle>
                        <CardDescription>Your recently submitted requisition requests</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {!requisitions || requisitions.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-8 text-center">
                                <FileText className="text-muted-foreground mb-4 h-12 w-12" />
                                <h3 className="text-lg font-medium">No requisitions yet</h3>
                                <p className="text-muted-foreground text-sm">Create your first requisition to see it here.</p>
                                <Button className="mt-4" asChild>
                                    <Link href={`/requisitions/create${selectedDepartment ? `?department=${selectedDepartment}` : ''}`}>
                                        <PlusCircle className="mr-2 h-4 w-4" />
                                        Create Requisition
                                    </Link>
                                </Button>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="px-2 py-3 text-left">Requisition #</th>
                                            <th className="px-2 py-3 text-left">Date</th>
                                            <th className="px-2 py-3 text-left">Purpose</th>
                                            <th className="px-2 py-3 text-right">Amount</th>
                                            <th className="px-2 py-3 text-center">Status</th>
                                            <th className="px-2 py-3 text-right">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {requisitions.map((requisition) => (
                                            <tr key={requisition.id} className="border-b">
                                                <td className="px-2 py-3">{requisition.requisition_number}</td>
                                                <td className="px-2 py-3">{new Date(requisition.created_at).toLocaleDateString()}</td>
                                                <td className="px-2 py-3">{requisition.purpose}</td>
                                                <td className="px-2 py-3 text-right">${Number(requisition.total_amount).toFixed(2)}</td>
                                                <td className="px-2 py-3 text-center">{getStatusBadge(requisition.status)}</td>
                                                <td className="px-2 py-3 text-right">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={`/requisitions/${requisition.id}`}>View</Link>
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common tasks you can perform</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href={`/requisitions/create${selectedDepartment ? `?department=${selectedDepartment}` : ''}`}>
                                    <PlusCircle className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">Create Requisition</span>
                                </Link>
                            </Button>
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href="/settings/profile">
                                    <User className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">View Profile</span>
                                </Link>
                            </Button>
                            <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                <Link href={`/requisitions/history${selectedDepartment ? `?department=${selectedDepartment}` : ''}`}>
                                    <FileText className="h-5 w-5 sm:h-6 sm:w-6" />
                                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">View All Requisitions</span>
                                </Link>
                            </Button>
                            {/* Approval Actions - Only for users with approver permissions */}
                            {user.permissions.includes('approver') && (
                                <>
                                    <Button
                                        variant="outline"
                                        className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3 hover:bg-accent/100 text-accent/200 border-accent/20 bg-accent/70"
                                        asChild
                                    >
                                        <Link href="/requisitions/my-approvals">
                                            <Clock className="h-5 w-5 sm:h-6 sm:w-6" />
                                            <span className="text-xs sm:text-sm font-medium text-center leading-tight">My Approval Queue</span>
                                            {requisitionStats.pendingApprovals > 0 && (
                                                <Badge className="bg-accent/20 text-accent/80 mt-1 text-xs">{requisitionStats.pendingApprovals}</Badge>
                                            )}
                                        </Link>
                                    </Button>

                                    <Button
                                        variant="outline"
                                        className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3"
                                        asChild
                                    >
                                        <Link href="/requisitions/my-approval-history">
                                            <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6" />
                                            <span className="text-xs sm:text-sm font-medium text-center leading-tight">My Approval History</span>
                                        </Link>
                                    </Button>
                                </>
                            )}

                            {/* Store Requisition Actions - Limited to regular employees */}
                            {user.permissions.includes('create-store-requisition') && !user.permissions.includes('store-keep') && (
                                <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                    <Link href="/store-requisitions/create">
                                        <Package className="h-5 w-5 sm:h-6 sm:w-6" />
                                        <span className="text-xs sm:text-sm font-medium text-center leading-tight">Create Store Requisition</span>
                                    </Link>
                                </Button>
                            )}

                            {user.permissions.includes('view-store-requisitions') && !user.permissions.includes('store-keep') && (
                                <Button variant="outline" className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 p-2 sm:p-3" asChild>
                                    <Link href="/store-requisitions">
                                        <Package className="h-5 w-5 sm:h-6 sm:w-6" />
                                        <span className="text-xs sm:text-sm font-medium text-center leading-tight">My Store Requisitions</span>
                                    </Link>
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
