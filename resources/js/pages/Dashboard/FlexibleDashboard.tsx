import React, { useState, useEffect } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import DashboardSwitcher from '@/components/DashboardSwitcher';
import {
  PlusCircle,
  ClipboardList,
  Users,
  Building,
  DollarSign,
  CheckCircle,
  Clock,
  FileText,
  TrendingUp,
  AlertCircle,
  UserCheck,
  FileSpreadsheet,
  User,
  Settings,
  BarChart3,
  Shield,
  XCircle,
  AlertTriangle,
  Wallet,
  Receipt,
  BookOpen,
  UserCog,
  ListOrdered,
  GitBranch,
  Briefcase
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

interface Role {
  id: number;
  name: string;
  description?: string;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
}

interface DashboardContext {
  type: 'role' | 'department';
  key: string;
  label: string;
  organization: Organization | null;
  department: Department | null;
  priority: number;
  role_id?: number;
  department_id?: number;
}

interface User {
  id: number;
  name: string;
  email: string;
  permissions: string[];
}

interface DashboardAction {
  label: string;
  href: string;
  icon: string;
  variant: 'default' | 'outline' | 'secondary';
  type?: string;
}

interface DashboardWidget {
  title: string;
  count: number;
  href: string;
  icon: string;
}

interface CashFloat {
  id: number;
  name: string;
  current_balance: number;
  initial_amount: number;
  status: string;
  department?: {
    id: number;
    name: string;
  };
  branch?: {
    id: number;
    name: string;
  };
  user?: {
    id: number;
    name: string;
  };
}

interface Transaction {
  id: number;
  total_amount?: number;
  transaction_type?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  status?: string;
  creator?: User;
  requisition?: {
    requester?: User;
    department?: Department;
    purpose?: string;
  };
  cashFloat?: {
    department?: Department;
    branch?: {
      id: number;
      name: string;
    };
    user?: User;
  };
}

interface FloatStatus {
  id: number;
  department?: string;
  allocated?: number;
  used?: number;
  remaining?: number;
  alert_threshold?: number;
  is_low?: boolean;
  utilization_percentage?: number;
  remaining_percentage_safe?: number;
  last_transaction_at?: string;
  monthly_transactions?: number;
  monthly_total?: number;
}

interface PendingApproval {
  id: number;
  purpose: string;
  total_amount: number;
  status: string;
  created_at: string;
  requester?: {
    first_name?: string;
    last_name?: string;
  };
  department?: {
    id: number;
    name: string;
  };
}

interface StatsData {
  total?: number;
  pending?: number;
  approved?: number;
  rejected?: number;
  active?: number;
  totalFloats?: number;
  activeFloats?: number;
  pendingTransactions?: number;
  approvedRequisitions?: number;
  rejectedRequisitions?: number;
  totalFloat?: number;
  lowFloatDepartments?: number;
  totalRemaining?: number;
  pendingApprovals?: number;
}

interface RecentItem {
  id: number;
  [key: string]: unknown;
}



interface CashFloatManagement {
  showCreateButton: boolean;
  stats: {
    totalFloat: number;
    lowFloatDepartments: number;
  };
}

interface DashboardData {
  stats: Record<string, StatsData>;
  actions: DashboardAction[];
  recentItems: Record<string, RecentItem[]>;
  widgets: Record<string, DashboardWidget>;
  pendingTransactions?: Transaction[];
  recentTransactions?: Transaction[];
  floatStatus?: FloatStatus[];
  pendingApprovals?: PendingApproval[];
  canManageCashFloats?: boolean;
  cashFloatManagement?: CashFloatManagement;
}

interface FlexibleDashboardProps {
  role: Role;
  permissions: string[];
  organization: Organization | null;
  department: Department | null;
  availableContexts: DashboardContext[];
  dashboardData: DashboardData;
  cashFloats: CashFloat[];
  user: User;
}

interface UserDetail {
  id: number;
  name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  avatar?: string;
  permissions?: string[];
}

interface UserDetail extends RecentItem {
  email?: string;
  avatar?: string;
  first_name?: string;
  last_name?: string;
}

const iconMap = {
  PlusCircle,
  ClipboardList,
  Users,
  Building,
  DollarSign,
  CheckCircle,
  Clock,
  FileText,
  TrendingUp,
  AlertCircle,
  UserCheck,
  FileSpreadsheet,
  User,
  Settings,
  BarChart3,
  Shield,
  XCircle,
  AlertTriangle,
  Wallet,
  Receipt,
  BookOpen,
  UserCog,
  ListOrdered,
  GitBranch,
  Briefcase
};

const getIcon = (iconName: string) => {
  const IconComponent = iconMap[iconName as keyof typeof iconMap];
  return IconComponent ? <IconComponent className="h-4 w-4" /> : <FileText className="h-4 w-4" />;
};

export default function FlexibleDashboard({
  role,
  department,
  availableContexts,
  dashboardData,
  cashFloats
}: FlexibleDashboardProps) {
  const { flash } = usePage().props as { flash?: { success?: string; error?: string } };

  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [selectedCashFloat, setSelectedCashFloat] = useState<string>('');

  // Handle flash messages
  useEffect(() => {
    if (flash?.success) {
      window.showToast?.({
        title: 'Success',
        message: flash.success,
        type: 'success'
      });
    }
    if (flash?.error) {
      window.showToast?.({
        title: 'Error',
        message: flash.error,
        type: 'error'
      });
    }
  }, [flash]);

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Dashboard',
      href: '/dashboard',
    },
    {
      title: role.name,
      href: '/dashboard',
    },
  ];

  // Helper functions for Finance Manager components
  const getFullName = (user?: User | UserDetail) => {
    if (!user) return 'Unknown User';
    if ('name' in user && user.name) return user.name;
    const userDetail = user as UserDetail;
    return `${userDetail.first_name || ''} ${userDetail.last_name || ''}`.trim() || 'Unknown User';
  };

  const getDepartmentName = (department?: Department) => {
    return department?.name || 'Unknown Department';
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderStatsCards = () => {
    const stats = dashboardData.stats;
    
    return (
      <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
        {/* Requisition stats */}
        {stats.requisitions && (
          <Link href="/requisitions/history" className="block h-full">
            <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
              <div className="flex items-center justify-between h-full">
                <div className="flex flex-col">
                  <div className="text-foreground text-lg sm:text-xl font-bold md:text-2xl">{stats.requisitions.total}</div>
                  <div className="text-muted-foreground text-xs font-medium sm:text-sm">Requisitions</div>
                </div>
                <div className="bg-primary/10 rounded-full p-2 flex-shrink-0">
                  <ClipboardList className="text-primary h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                </div>
              </div>
            </Card>
          </Link>
        )}

        {/* Pending Approvals stat card */}
        {stats.requisitions && stats.requisitions.pendingApprovals !== undefined && (
          <Link href="/requisitions/my-approvals" className="block h-full">
            <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
              <div className="flex items-center justify-between h-full">
                <div className="flex flex-col">
                  <div className="text-foreground text-lg sm:text-xl font-bold md:text-2xl">{stats.requisitions.pendingApprovals}</div>
                  <div className="text-muted-foreground text-xs font-medium sm:text-sm">Pending Approvals</div>
                </div>
                <div className="bg-warning/10 rounded-full p-2 flex-shrink-0">
                  <Clock className="text-warning h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                </div>
              </div>
            </Card>
          </Link>
        )}

        {/* Total Float stat card - Always show if finances data exists */}
        {stats.finances && (
          <Link href="/cash-floats" className="block h-full">
            <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
              <div className="flex items-center justify-between h-full">
                <div className="flex flex-col">
                  <div className="text-foreground text-lg sm:text-xl font-bold md:text-2xl">${(stats.finances.totalFloat || 0).toLocaleString()}</div>
                  <div className="text-muted-foreground text-xs font-medium sm:text-sm">Total Float</div>
                </div>
                <div className="bg-info/10 rounded-full p-2 flex-shrink-0">
                  <Wallet className="text-info h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                </div>
              </div>
            </Card>
          </Link>
        )}

        {/* Low Float Alerts stat card - Always show if finances data exists */}
        {stats.finances && (
          <Link href="/cash-floats" className="block h-full">
            <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
              <div className="flex items-center justify-between h-full">
                <div className="flex flex-col">
                  <div className="text-foreground text-lg sm:text-xl font-bold md:text-2xl">{stats.finances.lowFloatDepartments || 0}</div>
                  <div className="text-muted-foreground text-xs font-medium sm:text-sm">Low Float Alerts</div>
                </div>
                <div className="bg-warning/10 rounded-full p-2 flex-shrink-0">
                  <AlertTriangle className="text-warning h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                </div>
              </div>
            </Card>
          </Link>
        )}

        {/* User stats */}
        {stats.users && (
          <Link href="/users" className="block h-full">
            <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
              <div className="flex items-center justify-between h-full">
                <div className="flex flex-col">
                  <div className="text-foreground text-lg sm:text-xl font-bold md:text-2xl">{stats.users.total}</div>
                  <div className="text-muted-foreground text-xs font-medium sm:text-sm">Users</div>
                </div>
                <div className="bg-primary/10 rounded-full p-2 flex-shrink-0">
                  <Users className="text-primary h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                </div>
              </div>
            </Card>
          </Link>
        )}

        {/* Department stats */}
        {stats.departments && (
          <Link href="/departments" className="block h-full">
            <Card className="p-3 sm:p-4 shadow-md transition-shadow hover:shadow-lg h-full hover:bg-muted/30">
              <div className="flex items-center justify-between h-full">
                <div className="flex flex-col">
                  <div className="text-foreground text-lg sm:text-xl font-bold md:text-2xl">{stats.departments.total}</div>
                  <div className="text-muted-foreground text-xs font-medium sm:text-sm">Departments</div>
                </div>
                <div className="bg-primary/10 rounded-full p-2 flex-shrink-0">
                  <Building className="text-primary h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                </div>
              </div>
            </Card>
          </Link>
        )}
      </div>
    );
  };

  const renderPendingTransactions = () => (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle>Pending Transactions</CardTitle>
        <CardDescription>Transactions awaiting processing</CardDescription>
      </CardHeader>
      <CardContent>
        {dashboardData.pendingTransactions && dashboardData.pendingTransactions.length > 0 ? (
          <div className="space-y-3">
            {dashboardData.pendingTransactions.map((transaction) => {
              const requester = transaction.requisition?.requester || transaction.creator;
              const department = transaction.requisition?.department ||
                transaction.cashFloat?.department ||
                (transaction.cashFloat?.branch ? { name: transaction.cashFloat.branch.name } : null) ||
                (transaction.cashFloat?.user ? { name: transaction.cashFloat.user.name } : null);

              return (
                <div key={transaction.id} className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-x-3 gap-y-1 border-b pb-2 last:border-b-0">
                  <div className="flex-grow min-w-0">
                    <p className="font-medium text-sm truncate" title={transaction.transaction_type}>
                      {transaction.transaction_type?.replace('_', ' ').toUpperCase() || 'Transaction'}
                      {transaction.requisition && ` - ${transaction.requisition.purpose || 'No purpose'}`}
                    </p>
                    <div className="flex flex-wrap items-center gap-x-1.5 gap-y-0 text-xs text-muted-foreground">
                      <span>{getFullName(requester)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap">{getDepartmentName(department as Department)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap font-semibold">${transaction.total_amount?.toLocaleString() || 0}</span>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-1.5 xs:mt-0 flex-shrink-0 self-start xs:self-center">
                    <Badge
                      variant={transaction.status === 'opened' ? 'default' : 'outline'}
                      className="text-xs px-1.5 py-0.5"
                    >
                      {transaction.status || 'pending'}
                    </Badge>
                    <Button size="sm" variant="outline" asChild className="px-2 py-1 text-xs">
                      <Link href={`/transactions/${transaction.id}`}>View</Link>
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <CheckCircle className="h-10 w-10 text-muted-foreground" />
            <h3 className="mt-3 text-md font-medium">No pending transactions</h3>
            <p className="text-xs text-muted-foreground">All transactions have been processed</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" asChild className="w-full text-sm py-2">
          <Link href="/transactions">
            View All Transactions
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const renderFloatStatus = () => (
    <Card>
      <CardHeader>
        <CardTitle>Float Status</CardTitle>
        <CardDescription className="text-xs sm:text-sm">Assigned float levels</CardDescription>
      </CardHeader>
      <CardContent>
        {dashboardData.floatStatus && dashboardData.floatStatus.length > 0 ? (
          <div className="space-y-3">
            {dashboardData.floatStatus.map((float) => (
              <div key={float.id}>
                <div className="flex flex-row items-center justify-between gap-2">
                  <p className="font-medium text-sm truncate" title={float.department}>{float.department || 'Unassigned Float'}</p>
                  <Badge variant={float.is_low ? 'destructive' : 'outline'} className=" text-foreground/90 c text-xs px-1.5 py-0.5 whitespace-nowrap">
                    ${float.remaining?.toLocaleString() || 0}
                  </Badge>
                </div>
                <Progress
                  value={float.remaining_percentage_safe ?? 0}
                  className={`mt-1.5 h-1.5 sm:h-2 ${float.is_low ? 'bg-red-100 dark:bg-red-900/30' : ''}`}
                  aria-label={`Float progress for ${float.department}`}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Allocated: ${float.allocated?.toLocaleString() || 0}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Wallet className="h-10 w-10 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mt-2">No float data available</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full text-sm py-2" asChild>
          <Link href="/cash-floats">Manage Floats</Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const handleExportReport = () => {
    if (!selectedCashFloat) return;

    // Get current date and 30 days ago for default date range
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Open export URL in new tab
    const exportUrl = `/cash-floats/${selectedCashFloat}/export-pdf?start_date=${startDate}&end_date=${endDate}`;
    window.open(exportUrl, '_blank');

    setIsExportDialogOpen(false);
    setSelectedCashFloat('');
  };

  const renderRecentTransactions = () => (
    <Card>
      <CardHeader>
        <CardTitle>Recent Transactions</CardTitle>
        <CardDescription>Latest petty cash activities</CardDescription>
      </CardHeader>
      <CardContent>
        {dashboardData.recentTransactions && dashboardData.recentTransactions.length > 0 ? (
          <div className="space-y-3">
            {dashboardData.recentTransactions.map((transaction) => {
              const requester = transaction.requisition?.requester || transaction.creator;
              const department = transaction.cashFloat?.department ||
                (transaction.cashFloat?.branch ? { name: transaction.cashFloat.branch.name } : null) ||
                (transaction.cashFloat?.user ? { name: transaction.cashFloat.user.name } : null);

              return (
                <div key={transaction.id} className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-x-3 gap-y-1 border-b pb-2 last:border-b-0">
                  <div className="flex-grow min-w-0">
                    <p className="font-medium text-sm truncate" title={transaction.transaction_type}>{transaction.transaction_type || 'Transaction'}</p>
                    <div className="flex flex-wrap items-center gap-x-1.5 gap-y-0 text-xs text-muted-foreground">
                      <span>{getFullName(requester)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap">{getDepartmentName(department as Department)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap font-semibold">${transaction.total_amount?.toLocaleString() || 0}</span>
                    </div>
                  </div>
                  <Badge
                    variant={transaction.status === 'completed' ? 'default' : (transaction.status === 'opened' ? 'default' : 'outline')}
                    className="mt-1.5 xs:mt-0 flex-shrink-0 text-xs px-1.5 py-0.5 self-start xs:self-center whitespace-nowrap"
                  >
                    {transaction.status || 'pending'}
                  </Badge>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Receipt className="h-10 w-10 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mt-2">No recent transactions</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full text-sm py-2" asChild>
          <Link href="/transactions">
            View All Transactions
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const renderRecentUsers = () => (
    <Card className="shadow-md transition-shadow hover:shadow-lg">
      <CardHeader>
        <CardTitle className="text-foreground flex items-center gap-2">
          <Users className="text-primary h-5 w-5" />
          Recent Users
        </CardTitle>
        <CardDescription className="text-xs sm:text-sm">Recently added users to your organization</CardDescription>
      </CardHeader>
      <CardContent>
        {dashboardData.recentItems?.users && dashboardData.recentItems.users.length > 0 ? (
          <div className="space-y-4">
            {dashboardData.recentItems.users.map((user) => (
              <div
                key={user.id}
                className="bg-muted/30 hover:bg-muted/50 flex items-center gap-4 rounded-lg p-3 transition-colors"
              >
                <Avatar className="border-primary/20 h-10 w-10 border-2">
                  <AvatarImage src={(user as UserDetail).avatar} alt={getFullName(user as UserDetail)} />
                  <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                    {getInitials(getFullName(user as UserDetail))}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1">
                  <p className="text-foreground font-medium text-sm">
                    {getFullName(user as UserDetail)}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {(user as UserDetail).email || 'No email'}
                  </p>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/users/${user.id}`}>View</Link>
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Users className="h-10 w-10 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mt-2">No recent users</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full text-sm py-2" asChild>
          <Link href="/users">
            View All Users
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const renderCashFloatManagement = () => {
    if (!dashboardData.cashFloatManagement) {
      return null;
    }

    const { stats, showCreateButton } = dashboardData.cashFloatManagement;

    return (
      <Card>
        <CardHeader>
          <CardTitle>Cash Float Management</CardTitle>
          <CardDescription className="text-xs sm:text-sm">Manage cash floats and view their status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
            <div className="text-sm">
              <h3 className="text-md sm:text-lg font-semibold">Total Float: ${stats.totalFloat?.toLocaleString()}</h3>
              <p className="text-xs sm:text-sm text-muted-foreground">Low Float Alerts: {stats.lowFloatDepartments}</p>
            </div>
            {showCreateButton && (
              <Button asChild className="w-full sm:w-auto text-sm py-2 px-3 mt-1 sm:mt-0">
                <Link href="/cash-floats/create">Create New Cash Float</Link>
              </Button>
            )}
          </div>

          {dashboardData.floatStatus && dashboardData.floatStatus.length > 0 ? (
            <div className="space-y-3">
              {dashboardData.floatStatus.map((float) => (
                <div key={float.id} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 p-3 sm:p-4 border rounded-lg hover:bg-muted/30 transition-colors">
                  <div className="flex-grow min-w-0 w-full sm:w-auto">
                    <h4 className="font-medium text-sm sm:text-base truncate" title={float.department}>{float.department || 'Unassigned Float'}</h4>
                    <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 gap-x-3 gap-y-2 mt-2 text-xs sm:text-sm">
                      <div>
                        <span className="text-muted-foreground">Allocated:</span>
                        <p className="font-medium">${float.allocated?.toLocaleString() || 0}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Used:</span>
                        <p className="font-medium">${float.used?.toLocaleString() || 0}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Remaining:</span>
                        <p className="font-medium text-success">${float.remaining?.toLocaleString() || 0}</p>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-muted-foreground flex flex-wrap gap-x-3 gap-y-1">
                      {float.is_low && <span className="text-destructive font-semibold">LOW BALANCE</span>}
                      {float.alert_threshold && <span>Threshold: ${float.alert_threshold.toLocaleString()}</span>}
                      {float.utilization_percentage !== undefined && <span>Utilized: {float.utilization_percentage}%</span>}
                    </div>
                  </div>
                  <Button size="sm" variant="outline" asChild className="w-full sm:w-auto flex-shrink-0 text-xs sm:text-sm">
                    <Link href={`/cash-floats/${float.id}`}>View Details</Link>
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <Wallet className="h-10 w-10 text-muted-foreground" />
              <p className="text-xs text-muted-foreground mt-2">No cash floats configured</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderActionButtons = () => {
    const buttons = [];

    // Add Create Requisition button
    buttons.push(
      <Button key="create-requisition" variant="default" size="sm" className="text-xs sm:text-sm" asChild>
        <Link href={`/requisitions/create${department ? `?department=${department.id}` : ''}`}>
          <PlusCircle className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
          <span className="hidden xs:inline">Create </span>Requisition
        </Link>
      </Button>
    );

    // Add Pending Approvals button if there are pending approvals
    if (dashboardData.pendingApprovals && dashboardData.pendingApprovals.length > 0) {
      buttons.push(
        <Button key="pending-approvals" variant="outline" size="sm" className="text-xs sm:text-sm" asChild>
          <Link href="/requisitions/approvals">
            <Clock className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden xs:inline">Pending </span>Approvals
            <Badge variant="secondary" className="ml-1 sm:ml-2 text-xs">
              {dashboardData.pendingApprovals.length}
            </Badge>
          </Link>
        </Button>
      );
    }

    return buttons;
  };



  const renderQuickActions = () => {
    const actions: Array<{
      icon: React.ReactElement;
      label: string;
      href: string;
      description: string;
      isExport?: boolean;
      action?: DashboardAction;
    }> = [];

    // Add actions from dashboardData first
    dashboardData.actions.forEach((action) => {
      // Skip pending approvals as they have their own section
      if (action.label === 'Pending Approvals') {
        return;
      }

      if (action.label.toLowerCase().includes('cash float') ||
          action.label.toLowerCase().includes('manage cash floats') ||
          action.href.includes('/cash-floats')) {
        return;
      }

      // Add export and other actions to quick actions
      if (action.type === 'export') {
        actions.push({
          icon: getIcon('FileSpreadsheet'),
          label: action.label,
          href: '#',
          description: 'Export cash float reports',
          isExport: true,
          action: action
        });
      } else {
        actions.push({
          icon: getIcon(action.icon),
          label: action.label,
          href: action.href,
          description: action.label
        });
      }
    });

    return (
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common management tasks for organization oversight</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3 sm:gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
            {actions.map((action, index) => (
              action.isExport ? (
                // Export dialog
                <Dialog key={`export-${index}`} open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 text-center p-2 sm:p-3"
                    >
                      {action.icon}
                      <span className="text-xs sm:text-sm font-medium leading-tight">{action.label}</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Export Cash Float Report</DialogTitle>
                      <DialogDescription>
                        Select a cash float to export its transaction report for the last 30 days.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm text-foreground font-medium">Select Cash Float</label>
                        <Select value={selectedCashFloat} onValueChange={setSelectedCashFloat}>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose a cash float..." />
                          </SelectTrigger>
                          <SelectContent>
                            {cashFloats.map((cashFloat) => (
                              <SelectItem key={cashFloat.id} value={cashFloat.id.toString()}>
                                {cashFloat.name} - ${cashFloat.current_balance.toLocaleString()} remaining
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button
                          onClick={handleExportReport}
                          disabled={!selectedCashFloat}
                        >
                          Export Report
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              ) : (
                <Button
                  key={action.href}
                  variant="outline"
                  className="flex h-20 sm:h-24 flex-col items-center justify-center space-y-1 sm:space-y-2 text-center p-2 sm:p-3"
                  asChild
                >
                  <Link href={action.href}>
                    {action.icon}
                    <span className="text-xs sm:text-sm font-medium leading-tight">{action.label}</span>
                  </Link>
                </Button>
              )
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };
  
  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${role.name} Dashboard`} />

      <div className="flex h-full flex-1 flex-col gap-4 p-3 sm:p-4 md:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-foreground text-xl sm:text-2xl font-bold truncate">{role.name} Dashboard</h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Manage your tasks and access role-specific features
            </p>
          </div>

          <div className="flex flex-wrap gap-2 w-full sm:w-auto">
            {renderActionButtons()}
          </div>
        </div>

        {/* Dashboard Switcher */}
        {availableContexts && availableContexts.length > 1 && (
          <div className="w-full max-w-sm sm:max-w-md">
            <DashboardSwitcher availableContexts={availableContexts} />
          </div>
        )}

        {/* Stats Cards */}
        {renderStatsCards()}

        {/* Finance Manager Components */}
        <div className="grid gap-4 grid-cols-1 lg:grid-cols-3">
          {renderPendingTransactions()}
          {renderFloatStatus()}
        </div>

        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          {renderRecentTransactions()}
          {renderRecentUsers()}
        </div>

        {/* Cash Float Management */}
        {renderCashFloatManagement()}

        {renderQuickActions()}
      </div>
    </AppLayout>
  );
}