import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { AlertCircle, Briefcase, CheckCircle, ClipboardList, Clock, DollarSign, FileText, PlusCircle, UserCircle, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import DashboardSwitcher from '@/components/DashboardSwitcher';

interface Department {
    id: number;
    name: string;
    branch_id: number;
    organization_id: number;
    hod_user_id: number;
    branch: {
        id: number;
        name: string;
    };
}

interface Organization {
    id: number;
    name: string;
    contact_email: string;
}

interface User {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    status: string;
    avatar?: string;
}

interface Requisition {
    id: number;
    requisition_number: string;
    purpose: string;
    total_amount: number;
    status: string;
    created_at: string;
    requester_user_id: number;
    requester?: {
        id: number;
        first_name: string;
        last_name: string;
        email: string;
    };
}

interface DashboardHODProps {
  stats: {
    departmentMembers: number;
    pendingRequisitions: number;
    approvedRequisitions?: number;
    rejectedRequisitions?: number;
    totalBudget?: number;
    spentBudget?: number;
    hodRequisitions?: number;
  };
  department: Department;
  departments?: Department[];
  departmentMembers: User[];
  pendingRequisitions: Requisition[];
  hodRequisitions: Requisition[];
  organization: Organization;
  user: User;
  availableContexts?: Array<{
    type: 'role' | 'department';
    key: string;
    label: string;
    organization: { id: number; name: string } | null;
    department: { id: number; name: string } | null;
    priority: number;
    role_id?: number;
    department_id?: number;
  }>;
}

export default function DashboardHOD({
  stats,
  department,
  departments = [],
  departmentMembers,
  pendingRequisitions,
  hodRequisitions = [],
  organization,
  user,
  availableContexts = []
}: DashboardHODProps) {
    const { flash } = usePage().props as { flash?: { success?: string; error?: string } };

    // For multi-department support
    const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>(department.id.toString());

    // Handle flash messages
    useEffect(() => {
        if (flash?.success) {
            window.showToast?.({
                title: 'Success',
                message: flash.success,
                type: 'success'
            });
        }
        if (flash?.error) {
            window.showToast?.({
                title: 'Error',
                message: flash.error,
                type: 'error'
            });
        }
    }, [flash]);

    // Initialize from localStorage if available
    useEffect(() => {
        const storedDepartment = localStorage.getItem('hodSelectedDepartment');
        if (storedDepartment && departments.some((dept) => dept.id.toString() === storedDepartment)) {
            setSelectedDepartmentId(storedDepartment);
        }
    }, [departments]);

    // Handle department change
    const handleDepartmentChange = (departmentId: string) => {
        setSelectedDepartmentId(departmentId);
        localStorage.setItem('hodSelectedDepartment', departmentId);

        // Use Inertia router for smoother navigation
        router.get(`/dashboard/department/${departmentId}`);
    };

    // Format currency
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
        }).format(amount);
    };

    // Get status badge
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending_approval':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-accent/100 dark:border-accent/60 dark:bg-accent/90/20 text-accent/80"
                    >
                        Pending
                    </Badge>
                );
            case 'approved':
                return (
                    <Badge variant="outline" className="bg-primary/100 text-primary-800">
                        Approved
                    </Badge>
                );
            case 'rejected':
                return (
                    <Badge
                        variant="outline"
                        className="dark:text-secondary-foreground/100 bg-red-100 text-red-800 dark:border-red-600 dark:bg-red-900/20"
                    >
                        Rejected
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    // Define breadcrumbs
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Department',
            href: `/departments/${department.id}`,
        },
        {
            title: department.name,
            href: `/departments/${department.id}`,
        },
    ];
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${department.name} Dashboard`} />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Dashboard Switcher */}
          {availableContexts && availableContexts.length > 1 && (
            <div className="w-full max-w-sm mb-8">
              <DashboardSwitcher availableContexts={availableContexts} />
            </div>
          )}

          {/* Department Overview */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-foreground/100 text-2xl font-bold tracking-tight">{department.name} Dashboard</h1>
              <p className="text-muted-foreground">
                Manage your department and review pending requisitions
              </p>
            </div>

                        <div className="flex items-center gap-4">
                            {/* Multi-department selector - only show if user is HOD for multiple departments */}
                            {departments.length > 1 && (
                                <Select value={selectedDepartmentId} onValueChange={handleDepartmentChange}>
                                    <SelectTrigger className="w-[240px]">
                                        <SelectValue placeholder="Select department" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {departments.map((dept) => (
                                            <SelectItem key={dept.id} value={dept.id.toString()}>
                                                {dept.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            )}

                            {/* Create Requisition Button */}
                            <Button asChild>
                                <Link href={`/requisitions/create?department=${department.id}`}>
                                    <PlusCircle className="mr-2 h-4 w-4" />
                                    Create Requisition
                                </Link>
                            </Button>
                        </div>
                    </div>

                    {/* Stats Cards */}
                    <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Department Members</CardTitle>
                                <Users className="text-muted-foreground h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-background/100 text-foreground/100 text-2xl font-bold">{stats.departmentMembers}</div>
                                <p className="text-muted-foreground text-xs">Active members in your department</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
                                <AlertCircle className="text-accent/50 h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-background/100 text-foreground/100 text-2xl font-bold">{stats.pendingRequisitions}</div>
                                <p className="text-muted-foreground text-xs">Requisitions awaiting your approval</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Your Requisitions</CardTitle>
                                <UserCircle className="h-4 w-4 text-blue-500" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-background/100 text-foreground/100 text-2xl font-bold">{stats.hodRequisitions || 0}</div>
                                <p className="text-muted-foreground text-xs">Requisitions you've created</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Organization</CardTitle>
                                <Briefcase className="text-muted-foreground h-4 w-4" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-background/100 text-foreground/100 text-2xl font-bold">{organization.name}</div>
                                <p className="text-muted-foreground text-xs">Parent organization</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content - Tabs for different requisition types */}
                    <Tabs defaultValue="pending" className="mb-8">
                        <TabsList className="mb-4 grid w-full grid-cols-2">
                            <TabsTrigger value="pending">
                                <AlertCircle className="mr-2 h-4 w-4" />
                                Pending Approvals
                            </TabsTrigger>
                            <TabsTrigger value="personal">
                                <UserCircle className="mr-2 h-4 w-4" />
                                My Requisitions
                            </TabsTrigger>
                        </TabsList>

                        {/* Pending Approvals Tab */}
                        <TabsContent value="pending">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Pending Approvals</CardTitle>
                                    <CardDescription>Requisitions from department members awaiting your approval</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {pendingRequisitions.length === 0 ? (
                                        <div className="flex flex-col items-center justify-center py-8 text-center">
                                            <CheckCircle className="text-muted-foreground mb-4 h-12 w-12" />
                                            <h3 className="text-lg font-medium">No pending requisitions</h3>
                                            <p className="text-muted-foreground text-sm">All requisitions have been processed.</p>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {pendingRequisitions.map((requisition) => (
                                                <div key={requisition.id} className="flex items-center justify-between border-b pb-4">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="bg-primary/10 rounded-full p-2">
                                                            <FileText className="text-primary h-5 w-5" />
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium">{requisition.purpose}</p>
                                                            <div className="text-muted-foreground flex items-center text-xs">
                                                                <Clock className="mr-1 h-3 w-3" />
                                                                <span>{new Date(requisition.created_at).toLocaleDateString()}</span>
                                                                {requisition.requester && (
                                                                    <span className="ml-2">
                                                                        by {requisition.requester.first_name} {requisition.requester.last_name}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <div className="text-sm font-medium">
                                                            <DollarSign className="mr-1 inline h-3 w-3" />
                                                            {formatCurrency(requisition.total_amount)}
                                                        </div>
                                                        {getStatusBadge(requisition.status)}
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link
                                                                href={`/requisitions/approvals?department=${department.id}&requisition=${requisition.id}`}
                                                            >
                                                                Review
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                                <CardFooter>
                                    <Button variant="outline" className="w-full" asChild>
                                        <Link href={`/requisitions/approvals?department=${department.id}&status=pending_approval`}>
                                            View All Pending Approvals
                                        </Link>
                                    </Button>
                                </CardFooter>
                            </Card>
                        </TabsContent>

                        {/* My Requisitions Tab */}
                        <TabsContent value="personal">
                            <Card>
                                <CardHeader>
                                    <CardTitle>My Requisitions</CardTitle>
                                    <CardDescription>Requisitions you've created as {department.name} HOD</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {hodRequisitions.length === 0 ? (
                                        <div className="flex flex-col items-center justify-center py-8 text-center">
                                            <ClipboardList className="text-muted-foreground mb-4 h-12 w-12" />
                                            <h3 className="text-lg font-medium">No requisitions yet</h3>
                                            <p className="text-muted-foreground text-sm">Create your first requisition to see it here.</p>
                                            <Button className="mt-4" asChild>
                                                <Link href={`/requisitions/create?department=${department.id}`}>
                                                    <PlusCircle className="mr-2 h-4 w-4" />
                                                    Create Requisition
                                                </Link>
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {hodRequisitions.map((requisition) => (
                                                <div key={requisition.id} className="flex items-center justify-between border-b pb-4">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="rounded-full bg-blue-50 p-2">
                                                            <FileText className="h-5 w-5 text-blue-500" />
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium">{requisition.purpose}</p>
                                                            <div className="text-muted-foreground flex items-center text-xs">
                                                                <Clock className="mr-1 h-3 w-3" />
                                                                <span>{new Date(requisition.created_at).toLocaleDateString()}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <div className="text-sm font-medium">
                                                            <DollarSign className="mr-1 inline h-3 w-3" />
                                                            {formatCurrency(requisition.total_amount)}
                                                        </div>
                                                        {getStatusBadge(requisition.status)}
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/requisitions/${requisition.id}`}>View</Link>
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                                <CardFooter>
                                    <Button variant="outline" className="w-full" asChild>
                                        <Link href={`/requisitions/history?department=${department.id}&user=${user.id}`}>
                                            View All My Requisitions
                                        </Link>
                                    </Button>
                                </CardFooter>
                            </Card>
                        </TabsContent>
                    </Tabs>

                    {/* Department Members */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Department Members</CardTitle>
                            <CardDescription>Staff in your department</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {departmentMembers.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8 text-center">
                                    <Users className="text-muted-foreground mb-4 h-12 w-12" />
                                    <h3 className="text-lg font-medium">No department members</h3>
                                    <p className="text-muted-foreground text-sm">Add members to your department to see them here.</p>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {departmentMembers.slice(0, 5).map((member) => (
                                        <div key={member.id} className="flex items-center justify-between border-b pb-4">
                                            <div className="flex items-center space-x-4">
                                                <Avatar>
                                                    <AvatarImage src={member.avatar} />
                                                    <AvatarFallback>
                                                        {member.first_name.charAt(0)}
                                                        {member.last_name.charAt(0)}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div>
                                                    <p className="text-sm font-medium">
                                                        {member.first_name} {member.last_name}
                                                    </p>
                                                    <p className="text-muted-foreground text-xs">{member.email}</p>
                                                </div>
                                            </div>
                                            <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>{member.status}</Badge>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                        <CardFooter>
                            <Button variant="outline" className="w-full" asChild>
                                <Link href="/users">View All Members</Link>
                            </Button>
                        </CardFooter>
                    </Card>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common tasks for department management</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                                <Button variant="outline" className="flex h-24 flex-col items-center justify-center space-y-2" asChild>
                                    <Link href={`/requisitions/approvals?department=${department.id}&status=pending_approval`}>
                                        <AlertCircle className="h-6 w-6" />
                                        <span>Pending Approvals</span>
                                    </Link>
                                </Button>
                                <Button variant="outline" className="flex h-24 flex-col items-center justify-center space-y-2" asChild>
                                    <Link href={`/requisitions/history?department=${department.id}&user=${user.id}`}>
                                        <UserCircle className="h-6 w-6" />
                                        <span>My Requisitions</span>
                                    </Link>
                                </Button>
                                <Button variant="outline" className="flex h-24 flex-col items-center justify-center space-y-2" asChild>
                                    <Link href="/users">
                                        <Users className="h-6 w-6" />
                                        <span>Manage Members</span>
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}