import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { Head, Link } from '@inertiajs/react';
import { AlertCircle } from 'lucide-react';

interface ErrorProps {
    message: string;
    user: {
        id: number;
        first_name: string;
        last_name: string;
        email: string;
    };
}

export default function Error({ message }: ErrorProps) {
    return (
        <AppLayout>
            <Head title="Dashboard Error" />
            <div className=" flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-muted-foreground/80 dark:text-muted-foreground/100 text-background/100 text-foreground/100 text-2xl font-bold md:text-3xl">
                        Dashboard Error
                    </h1>
                </div>

                <div className="grid gap-6">
                    <Alert variant="destructive" className="border-red-500 bg-red-50 dark:bg-red-900/20">
                        <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                        <AlertTitle className="font-medium text-red-600 dark:text-red-400">Access Error</AlertTitle>
                        <AlertDescription className="text-red-600 dark:text-red-400">{message}</AlertDescription>
                    </Alert>

                    <div className="flex flex-col gap-4 rounded-lg bg-white p-6 shadow dark:bg-gray-800">
                        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">What can you do?</h2>
                        <ul className="list-inside list-disc space-y-2 text-gray-600 dark:text-gray-300">
                            <li>Contact your administrator to assign the correct role</li>
                            <li>Try logging out and logging back in</li>
                            <li>Return to the homepage and try accessing a different section</li>
                        </ul>
                        <div className="mt-4 flex gap-4">
                            <Button asChild>
                                <Link href="/dashboard">Return to Dashboard</Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/">Go to Homepage</Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
