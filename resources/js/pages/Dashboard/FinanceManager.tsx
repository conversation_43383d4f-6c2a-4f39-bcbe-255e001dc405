import { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import DashboardSwitcher from '@/components/DashboardSwitcher';
import {
  PlusCircle,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  FileSpreadsheet,
  Wallet,
  Receipt,
  BookOpen,
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import FloatIssuance from '@/components/FloatIssuance';


interface CashFloat {
  id: number;
  name: string;
  current_balance: number;
  department?: {
    id: number;
    name: string;
  };
  branch?: {
    id: number;
    name: string;
  };
  user?: {
    id: number;
    name: string;
  };
}

interface User {
  id: number;
  first_name: string;
  last_name: string;
  name?: string;
  email: string;
}

interface Department {
  id: number;
  name: string;
}

interface Transaction {
  id: number;
  total_amount?: number;
  transaction_type?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  status?: string;
  creator?: User;
  requisition?: {
    requester?: User;
    department?: Department;
    purpose?: string;
  };
  cashFloat?: {
    department?: Department;
    branch?: {
      id: number;
      name: string;
    };
    user?: User;
  };
}

interface FloatStatus {
  id: number;
  department?: string;
  allocated?: number;
  used?: number;
  remaining?: number;
  alert_threshold?: number;
  is_low?: boolean;
  utilization_percentage?: number;
  remaining_percentage_safe?: number;
  last_transaction_at?: string;
  monthly_transactions?: number;
  monthly_total?: number;
}

interface Stats {
  pendingTransactions: number;
  approvedRequisitions: number;
  rejectedRequisitions: number;
  totalFloat: number;
  lowFloatDepartments: number;
  totalRemaining?: number;
  totalUsed?: number;
  lowFloatAlerts?: Array<{ department?: string; remaining?: number; threshold?: number }>;
  utilizationRate?: number;
}

interface Organization {
  id: number;
  name: string;
}

interface FinanceManagerDashboardProps {
  stats: Stats;
  pendingTransactions: Transaction[];
  recentTransactions: Transaction[];
  floatStatus: FloatStatus[];
  cashFloats: CashFloat[];
  organization: Organization;
  user: User;
  availableContexts?: Array<{
    type: 'role' | 'department';
    key: string;
    label: string;
    organization: { id: number; name: string } | null;
    department: { id: number; name: string } | null;
    priority: number;
    role_id?: number;
    department_id?: number;
  }>;
}


const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
];


export default function FinanceManagerDashboard({
  stats,
  pendingTransactions = [],
  recentTransactions = [],
  floatStatus = [],
  cashFloats = [],
  organization,
  availableContexts

}: FinanceManagerDashboardProps) {
  const [isFloatIssuanceOpen, setIsFloatIssuanceOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [selectedCashFloat, setSelectedCashFloat] = useState<string>('');


  const getFullName = (user?: User) => {
    if (!user) return 'Unknown User';
    if (user.name) return user.name;
    return `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown User';
  };

  const getDepartmentName = (department?: Department) => {
    return department?.name || 'Unknown Department';
  };

  const handleExportReport = () => {
    if (!selectedCashFloat) return;

    // Get current date and 30 days ago for default date range
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Open export URL in new tab
    const exportUrl = `/cash-floats/${selectedCashFloat}/export-pdf?start_date=${startDate}&end_date=${endDate}`;
    window.open(exportUrl, '_blank');

    setIsExportDialogOpen(false);
    setSelectedCashFloat('');
  };

  const renderStatsCards = () => (

    <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-5">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">Pending Transactions</CardTitle>
          <Clock className="h-4 w-4 text-amber-500" />
        </CardHeader>
        <CardContent className="pt-1 sm:pt-0">
          <div className="text-xl sm:text-2xl font-bold">{stats.pendingTransactions}</div>
          <p className="text-[10px] sm:text-xs text-muted-foreground leading-tight">
            Transactions awaiting processing
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">Approved</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent className="pt-1 sm:pt-0">
          <div className="text-xl sm:text-2xl font-bold">{stats.approvedRequisitions}</div>
          <p className="text-[10px] sm:text-xs text-muted-foreground leading-tight">
            Approved this month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">Rejected</CardTitle>
          <XCircle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent className="pt-1 sm:pt-0">
          <div className="text-xl sm:text-2xl font-bold">{stats.rejectedRequisitions}</div>
          <p className="text-[10px] sm:text-xs text-muted-foreground leading-tight">
            Rejected this month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">Total Float</CardTitle>
          <Wallet className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent className="pt-1 sm:pt-0">
          <div className="text-xl sm:text-2xl font-bold">KSH {stats.totalRemaining?.toLocaleString()}</div>
          <p className="text-[10px] sm:text-xs text-muted-foreground leading-tight">
            Current petty cash float
          </p>
        </CardContent>
      </Card>

      <Card className="col-span-2 md:col-span-1">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1.5 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium">Low Float Alerts</CardTitle>
          <AlertTriangle className="h-4 w-4 text-amber-500" />
        </CardHeader>
        <CardContent className="pt-1 sm:pt-0">
          <div className="text-xl sm:text-2xl font-bold">{stats.lowFloatDepartments}</div>
          <p className="text-[10px] sm:text-xs text-muted-foreground leading-tight">
            Floats below minimum
          </p>
        </CardContent>
      </Card>
    </div>
  );

  const renderPendingTransactions = () => (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle>Pending Transactions</CardTitle>
        <CardDescription className="text-xs sm:text-sm">Transactions awaiting processing</CardDescription>
      </CardHeader>
      <CardContent>
        {pendingTransactions.length > 0 ? (
          <div className="space-y-3">
            {pendingTransactions.map((transaction) => {
              const requester = transaction.requisition?.requester || transaction.creator;
              const department = transaction.requisition?.department ||
                transaction.cashFloat?.department ||
                (transaction.cashFloat?.branch ? { name: transaction.cashFloat.branch.name } : null) ||
                (transaction.cashFloat?.user ? { name: transaction.cashFloat.user.name } : null);

              return (
                <div key={transaction.id} className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-x-3 gap-y-1 border-b pb-2 last:border-b-0">
                  <div className="flex-grow min-w-0">
                    <p className="font-medium text-sm truncate" title={transaction.transaction_type}>
                      {transaction.transaction_type?.replace('_', ' ').toUpperCase() || 'Transaction'}
                      {transaction.requisition && ` - ${transaction.requisition.purpose || 'No purpose'}`}
                    </p>
                    <div className="flex flex-wrap items-center gap-x-1.5 gap-y-0 text-xs text-muted-foreground">
                      <span>{getFullName(requester)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap">{getDepartmentName(department as Department)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap font-semibold">KSH {transaction.total_amount?.toLocaleString() || 0}</span>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-1.5 xs:mt-0 flex-shrink-0 self-start xs:self-center">
                    <Badge
                      variant={transaction.status === 'opened' ? 'default' : 'outline'}
                      className="text-xs px-1.5 py-0.5"
                    >
                      {transaction.status || 'pending'}
                    </Badge>
                    <Button size="sm" variant="outline" asChild className="px-2 py-1 text-xs">
                      <Link href={`/transactions/${transaction.id}`}>View</Link>
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <CheckCircle className="h-10 w-10 text-muted-foreground" />
            <h3 className="mt-3 text-md font-medium">No pending transactions</h3>
            <p className="text-xs text-muted-foreground">All transactions have been processed</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" asChild className="w-full text-sm py-2">
          <Link href="/transactions">
            View All Transactions
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const renderFloatStatus = () => (
    <Card>
      <CardHeader>
        <CardTitle>Float Status</CardTitle>
        <CardDescription className="text-xs sm:text-sm">Assigned float levels</CardDescription>
      </CardHeader>
      <CardContent>
        {floatStatus.length > 0 ? (
          <div className="space-y-3">
            {floatStatus.map((float) => (
              <div key={float.id}>
                <div className="flex flex-row items-center justify-between gap-2">
                  <p className="font-medium text-sm truncate" title={float.department}>{float.department || 'Unassigned Float'}</p>
                  <Badge variant={float.is_low ? 'destructive' : 'outline'} className=" text-xs px-1.5 py-0.5 whitespace-nowrap text-foreground/90">
                    KSH {float.remaining?.toLocaleString() || 0}
                  </Badge>
                </div>
                <Progress
                  value={float.remaining_percentage_safe ?? 0}
                  className={`mt-1.5 h-1.5 sm:h-2 ${float.is_low ? 'bg-red-100 dark:bg-red-900/30' : ''}`}
                  aria-label={`Float progress for ${float.department}`}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Allocated: KSH {float.allocated?.toLocaleString() || 0}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Wallet className="h-10 w-10 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mt-2">No float data available</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full text-sm py-2" asChild>
          <Link href="/cash-floats">Manage Floats</Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const renderRecentTransactions = () => (
    <Card>
      <CardHeader>
        <CardTitle>Recent Transactions</CardTitle>
        <CardDescription className="text-xs sm:text-sm">Latest petty cash activities</CardDescription>
      </CardHeader>
      <CardContent>
        {recentTransactions.length > 0 ? (
          <div className="space-y-3">
            {recentTransactions.map((transaction) => {
              const requester = transaction.requisition?.requester || transaction.creator;
              const department = transaction.cashFloat?.department ||
                (transaction.cashFloat?.branch ? { name: transaction.cashFloat.branch.name } : null) ||
                (transaction.cashFloat?.user ? { name: transaction.cashFloat.user.name } : null);

              return (
                <div key={transaction.id} className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-x-3 gap-y-1 border-b pb-2 last:border-b-0">
                  <div className="flex-grow min-w-0">
                    <p className="font-medium text-sm truncate" title={transaction.transaction_type}>{transaction.transaction_type || 'Transaction'}</p>
                    <div className="flex flex-wrap items-center gap-x-1.5 gap-y-0 text-xs text-muted-foreground">
                      <span>{getFullName(requester)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap">{getDepartmentName(department as Department)}</span>
                      <span className="hidden xxs:inline">•</span>
                      <span className="whitespace-nowrap font-semibold">KSH {transaction.total_amount?.toLocaleString() || 0}</span>
                    </div>
                  </div>
                  <Badge
                    variant={transaction.status === 'completed' ? 'default' : (transaction.status === 'opened' ? 'default' : 'outline')}
                    className="mt-1.5 xs:mt-0 flex-shrink-0 text-xs px-1.5 py-0.5 self-start xs:self-center whitespace-nowrap"
                  >
                    {transaction.status || 'pending'}
                  </Badge>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Receipt className="h-10 w-10 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mt-2">No recent transactions</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full text-sm py-2" asChild>
          <Link href="/transactions">
            View All Transactions
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );

  const renderQuickActions = () => (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription className="text-xs sm:text-sm">Common finance tasks</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 xs:grid-cols-3 gap-2 sm:gap-3">
          <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center space-y-1 text-center p-1.5 sm:p-2" asChild>
            <Link href="/cash-floats">
              <Wallet className="h-5 w-5 mb-0.5" />
              <span className="text-[11px] sm:text-xs leading-tight">Manage Floats</span>
            </Link>
          </Button>
          <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center space-y-1 text-center p-1.5 sm:p-2" asChild>
            <Link href="/transactions">
              <Receipt className="h-5 w-5 mb-0.5" />
              <span className="text-[11px] sm:text-xs leading-tight">Transactions</span>
            </Link>
          </Button>
          <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center space-y-1 text-center p-1.5 sm:p-2" asChild>
            <Link href="/chart-of-accounts">
              <BookOpen className="h-5 w-5 mb-0.5" />
              <span className="text-[11px] sm:text-xs leading-tight">Chart of Accounts</span>
            </Link>
          </Button>
          <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="h-auto py-3 flex flex-col items-center justify-center space-y-1 text-center p-1.5 sm:p-2"
              >
                <FileSpreadsheet className="h-5 w-5 mb-0.5" />
                <span className="text-[11px] sm:text-xs leading-tight">Export Report</span>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Export Cash Float Report</DialogTitle>
                <DialogDescription>
                  Select a cash float to export its transaction report for the last 30 days.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm text-foreground font-medium">Select Cash Float</label>
                  <Select value={selectedCashFloat} onValueChange={setSelectedCashFloat}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a cash float..." />
                    </SelectTrigger>
                    <SelectContent>
                      {cashFloats.map((cashFloat) => (
                        <SelectItem key={cashFloat.id} value={cashFloat.id.toString()}>
                          {cashFloat.name} - KSH {cashFloat.current_balance.toLocaleString()} remaining
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleExportReport}
                    disabled={!selectedCashFloat}
                  >
                    Export Report
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );

  const renderCashFloatManagement = () => (
    <Card>
      <CardHeader>
        <CardTitle>Cash Float Management</CardTitle>
        <CardDescription className="text-xs sm:text-sm">Manage cash floats and view their status</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
          <div className="text-sm">
            <h3 className="text-md sm:text-lg font-semibold">Total Float: KSH {stats.totalFloat?.toLocaleString()}</h3>
            <p className="text-xs sm:text-sm text-muted-foreground">Low Float Alerts: {stats.lowFloatDepartments}</p>
          </div>
          <Button asChild className="w-full sm:w-auto text-sm py-2 px-3 mt-1 sm:mt-0">
            <Link href="/cash-floats/create">Create New Cash Float</Link>
          </Button>
        </div>

        {floatStatus.length > 0 ? (
          <div className="space-y-3">
            {floatStatus.map((float) => (
              <div key={float.id} className="flex flex-col xs:flex-row justify-between items-start xs:items-center gap-x-3 gap-y-1.5 p-2.5 sm:p-3 border rounded-lg">
                <div className="flex-grow min-w-0">
                  <h4 className="font-medium text-sm truncate" title={float.department}>{float.department || 'Unassigned Float'}</h4>
                  <div className="grid grid-cols-1 xxs:grid-cols-2 sm:grid-cols-3 gap-x-2 sm:gap-x-3 gap-y-1 mt-1 text-xs">
                    <div>
                      <span className="text-muted-foreground">Allocated:</span>
                      <p className="font-medium">KSH {float.allocated?.toLocaleString() || 0}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Used:</span>
                      <p className="font-medium">KSH {float.used?.toLocaleString() || 0}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Remaining:</span>
                      <p className="font-medium text-green-600 dark:text-green-500">KSH {float.remaining?.toLocaleString() || 0}</p>
                    </div>
                  </div>
                  <div className="mt-1.5 text-[11px] sm:text-xs text-muted-foreground flex flex-wrap gap-x-2 gap-y-0.5">
                    {float.is_low && <span className="text-red-500 font-semibold">LOW BALANCE</span>}
                    {float.alert_threshold && <span>Threshold: KSH {float.alert_threshold.toLocaleString()}</span>}
                    {float.utilization_percentage !== undefined && <span>Utilized: {float.utilization_percentage}%</span>}
                  </div>
                </div>
                <Button size="sm" variant="outline" asChild className="w-full xs:w-auto mt-2 xs:mt-0 flex-shrink-0 self-stretch xs:self-center px-2 py-1 text-xs">
                  <Link href={`/cash-floats/${float.id}`}>View Details</Link>
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Wallet className="h-10 w-10 text-muted-foreground" />
            <p className="text-xs text-muted-foreground mt-2">No cash floats configured</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Finance Manager Dashboard" />
      <div className="flex h-full flex-1 flex-col gap-3 sm:gap-4 p-2.5 sm:p-4 md:p-6 lg:p-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-lg text-accent/90 sm:text-xl font-bold">Finance Dashboard</h1>
            <p className="text-xs sm:text-sm text-muted-foreground">
              {organization.name}
            </p>
          </div>
          <div className="flex flex-wrap gap-2 w-full sm:w-auto">
            <Button asChild className="flex-1 sm:flex-none">
              <Link href="/requisitions/create">
                <PlusCircle className="mr-2 h-4 w-4" />
                <span className="hidden xs:inline">Create </span>Requisition
              </Link>
            </Button>
            <Button variant="outline" asChild className="flex-1 sm:flex-none">
              <Link href="/cash-floats/create">
                <Wallet className="mr-2 h-4 w-4" />
                <span className="hidden xs:inline">Create </span>Cash Float
              </Link>
            </Button>
          </div>
        </div>
        {/* Dashboard Switcher */}
        {availableContexts && availableContexts.length > 1 && (
          <div className="w-full max-w-sm">
            <DashboardSwitcher availableContexts={availableContexts} />
          </div>
        )}
        {renderStatsCards()}

        <div className="grid gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-3">
          {renderPendingTransactions()}
          {renderFloatStatus()}
        </div>

        <div className="grid gap-3 sm:gap-4 md:grid-cols-2">
          {renderRecentTransactions()}
          {renderQuickActions()}
        </div>

        {renderCashFloatManagement()}
      </div>

      <FloatIssuance
        isOpen={isFloatIssuanceOpen}
        onClose={() => setIsFloatIssuanceOpen(false)}
        availableFloats={cashFloats}
      />
    </AppLayout>
  );
}