import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAppearance } from '@/hooks/use-appearance'; // Import from the correct path
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { motion } from 'framer-motion';
import { Briefcase, Building2, Moon, Sun, UserCog, Users } from 'lucide-react';

interface PlatformAdminProps {
    stats: {
        organizations: number;
        users: number;
        roles: number;
        departments: number;
    };
    recentUsers: User[];
    recentOrganizations: {
        id: number;
        name: string;
        contact_email: string;
        status: string;
        created_at: string;
    }[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } },
};

const buttonHover = {
    hover: {
        scale: 1.05,
        transition: { duration: 0.3, ease: 'easeOut' },
    },
};

// const cardHover = {
//     hover: {
//         scale: 1.02,
//         boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",
//         transition: { duration: 0.3, ease: "easeOut" },
//     },
// };

const platformCardHover = {
    hover: {
        background: 'linear-gradient(to right, #059669, #047857)',
        color: '#ffffff',
        transition: {
            background: { duration: 0.3, ease: 'easeOut' },
            color: { duration: 0.3, ease: 'easeOut', delay: 0.1 },
        },
    },
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
        },
    },
};

export default function DashboardPlatformAdmin({ stats, recentUsers, recentOrganizations }: PlatformAdminProps) {
    const getInitials = useInitials();
    const { appearance, updateAppearance } = useAppearance();

    const toggleTheme = () => {
        const newAppearance = appearance === 'dark' ? 'light' : 'dark';
        updateAppearance(newAppearance);
    };

    const isDarkMode = appearance === 'dark' || (appearance === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    return (
        <AppLayout
            breadcrumbs={breadcrumbs}
            className={isDarkMode ? 'bg-background text-foreground' : 'from-primary/50 to-muted-foreground/50 bg-gradient-to-r'}
        >
            <Head title="Platform Admin Dashboard" />
            <div
                className={
                    isDarkMode
                        ? 'bg-background min-h-screen py-12'
                        : 'from-muted-foreground/50 to-muted-foreground/20 min-h-screen bg-gradient-to-r py-12'
                }
            >
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <motion.div className="mb-10 flex items-center justify-between" variants={fadeInUp} initial="hidden" animate="visible">
                        <h1 className={isDarkMode ? 'text-foreground text-4xl font-extrabold' : 'text-muted-foreground/90 text-4xl font-extrabold'}>
                            Platform Admin Dashboard
                        </h1>
                        <div className="flex gap-4">
                            <motion.div variants={buttonHover} whileHover="hover">
                                <Button
                                    asChild
                                    className={
                                        isDarkMode
                                            ? 'from-primary/80 to-primary/90 hover:from-primary/90 hover:to-primary rounded-xl bg-gradient-to-r px-6 py-3 text-white shadow-md'
                                            : 'from-primary/60 to-primary/70 hover:from-primary/70 hover:to-primary/80 rounded-xl bg-gradient-to-r px-6 py-3 text-white shadow-md'
                                    }
                                >
                                    <Link href="/organizations/create">
                                        <span className="sm:hidden">+ Organization</span>
                                        <span className="hidden sm:inline">New Organization</span>
                                    </Link>
                                </Button>
                            </motion.div>
                            <motion.div variants={buttonHover} whileHover="hover">
                                <Button
                                    asChild
                                    className={
                                        isDarkMode
                                            ? 'from-primary/80 to-primary/90 hover:from-primary/90 hover:to-primary rounded-xl bg-gradient-to-r px-6 py-3 text-white shadow-md'
                                            : 'from-primary/60 to-primary/70 hover:from-primary/70 hover:to-primary/80 rounded-xl bg-gradient-to-r px-6 py-3 text-white shadow-md'
                                    }
                                >
                                    <Link href="/users/create">
                                        <span className="sm:hidden">+ User</span>
                                        <span className="hidden sm:inline">New User</span>
                                    </Link>
                                </Button>
                            </motion.div>
                            <Button
                                variant="outline"
                                onClick={toggleTheme}
                                className={
                                    isDarkMode
                                        ? 'hover:bg-muted-foreground/80 border-white text-white'
                                        : 'border-muted-foreground/90 text-muted-foreground/90 hover:bg-muted-foreground/20'
                                }
                            >
                                {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                            </Button>
                        </div>
                    </motion.div>

                    {/* Stats Cards */}
                    <motion.div className="mb-10 grid gap-6 md:grid-cols-4" variants={staggerContainer} initial="hidden" animate="visible">
                        {[
                            { title: 'Organizations', icon: <Building2 className="text-primary/40 h-8 w-8" />, value: stats.organizations },
                            { title: 'Users', icon: <Users className="text-primary/40 h-8 w-8" />, value: stats.users },
                            { title: 'Roles', icon: <UserCog className="text-primary/40 h-8 w-8" />, value: stats.roles },
                            { title: 'Departments', icon: <Briefcase className="text-primary/40 h-8 w-8" />, value: stats.departments },
                        ].map((stat, index) => (
                            <motion.div key={index} variants={fadeInUp} whileHover="hover">
                                <Card
                                    className={
                                        isDarkMode
                                            ? 'bg-background overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl'
                                            : 'from-muted-foreground/5 overflow-hidden rounded-2xl bg-gradient-to-r to-white shadow-lg transition-all duration-300 hover:shadow-xl'
                                    }
                                >
                                    <CardHeader
                                        className={
                                            isDarkMode
                                                ? 'bg-muted flex flex-row items-center justify-between p-6'
                                                : 'bg-primary/5 flex flex-row items-center justify-between p-6'
                                        }
                                    >
                                        <CardTitle
                                            className={
                                                isDarkMode
                                                    ? 'text-foreground text-lg font-semibold'
                                                    : 'text-muted-foreground/90 text-lg font-semibold'
                                            }
                                        >
                                            {stat.title}
                                        </CardTitle>
                                        {stat.icon}
                                    </CardHeader>
                                    <CardContent className="p-6">
                                        <div className={isDarkMode ? 'text-primary/30 text-4xl font-bold' : 'text-primary/70 text-4xl font-bold'}>
                                            {stat.value}
                                        </div>
                                        <p className={isDarkMode ? 'text-muted-foreground mt-2 text-sm' : 'text-muted-foreground/90 mt-2 text-sm'}>
                                            Total {stat.title.toLowerCase()} in the system
                                        </p>
                                    </CardContent>
                                </Card>
                            </motion.div>
                        ))}
                    </motion.div>

                    {/* Recent Users and Organizations */}
                    <motion.div className="mb-10 grid gap-6 md:grid-cols-2" variants={staggerContainer} initial="hidden" animate="visible">
                        <Card
                            className={
                                isDarkMode
                                    ? 'bg-background overflow-hidden rounded-2xl shadow-lg'
                                    : 'from-muted-foreground/5 overflow-hidden rounded-2xl bg-gradient-to-r to-white shadow-lg'
                            }
                        >
                            <CardHeader className={isDarkMode ? 'bg-muted p-6' : 'bg-primary/5 p-6'}>
                                <CardTitle
                                    className={isDarkMode ? 'text-foreground text-xl font-bold' : 'text-muted-foreground/90 text-xl font-bold'}
                                >
                                    Recent Users
                                </CardTitle>
                                <CardDescription className={isDarkMode ? 'text-muted-foreground/50' : 'text-muted-foreground/60'}>
                                    Recently added users to the platform
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                {recentUsers.map((user) => (
                                    <motion.div
                                        key={user.id}
                                        className={
                                            isDarkMode
                                                ? 'bg-muted hover:bg-muted/80 flex items-center gap-4 rounded-xl p-4 transition-colors'
                                                : 'bg-muted-foreground/5 hover:bg-muted-foreground/10 flex items-center gap-4 rounded-xl p-4 transition-colors'
                                        }
                                        variants={fadeInUp}
                                    >
                                        <Avatar className="h-12 w-12">
                                            <AvatarImage src={user.avatar || undefined} alt={`${user.first_name} ${user.last_name}`} />
                                            <AvatarFallback
                                                className={
                                                    isDarkMode
                                                        ? 'bg-primary/90 text-primary/30 font-medium'
                                                        : 'bg-primary/10 text-primary/70 font-medium'
                                                }
                                            >
                                                {getInitials(`${user.first_name} ${user.last_name}`)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1 space-y-1">
                                            <p
                                                className={
                                                    isDarkMode
                                                        ? 'text-foreground text-lg font-medium'
                                                        : 'text-muted-foreground/90 text-lg font-medium'
                                                }
                                            >
                                                {user.first_name} {user.last_name}
                                            </p>
                                            <p className={isDarkMode ? 'text-muted-foreground text-sm' : 'text-muted-foreground/60 text-sm'}>
                                                {user.email}
                                            </p>
                                        </div>
                                        <motion.div variants={buttonHover} whileHover="hover">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                asChild
                                                className={
                                                    isDarkMode
                                                        ? 'from-primary/80 to-primary/90 hover:from-primary/90 hover:to-primary rounded-full bg-gradient-to-r px-4 py-2 text-white shadow-md'
                                                        : 'from-primary/60 to-primary/70 hover:from-primary/70 hover:to-primary/80 rounded-full bg-gradient-to-r px-4 py-2 text-white shadow-md'
                                                }
                                            >
                                                <Link href={`/users/${user.id}/edit`}>Edit</Link>
                                            </Button>
                                        </motion.div>
                                    </motion.div>
                                ))}
                            </CardContent>
                        </Card>

                        <Card
                            className={
                                isDarkMode
                                    ? 'bg-background overflow-hidden rounded-2xl shadow-lg'
                                    : 'from-muted-foreground/5 overflow-hidden rounded-2xl bg-gradient-to-r to-white shadow-lg'
                            }
                        >
                            <CardHeader className={isDarkMode ? 'bg-muted p-6' : 'bg-primary/5 p-6'}>
                                <CardTitle
                                    className={isDarkMode ? 'text-foreground text-xl font-bold' : 'text-muted-foreground/90 text-xl font-bold'}
                                >
                                    Recent Organizations
                                </CardTitle>
                                <CardDescription className={isDarkMode ? 'text-muted-foreground/50' : 'text-muted-foreground/60'}>
                                    Recently added organizations
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6 p-6">
                                {recentOrganizations.map((org) => (
                                    <motion.div
                                        key={org.id}
                                        className={
                                            isDarkMode
                                                ? 'bg-muted hover:bg-muted/80 flex items-center gap-4 rounded-xl p-4 transition-colors'
                                                : 'bg-muted-foreground/5 hover:bg-muted-foreground/10 flex items-center gap-4 rounded-xl p-4 transition-colors'
                                        }
                                        variants={fadeInUp}
                                    >
                                        <div
                                            className={
                                                isDarkMode
                                                    ? 'bg-primary/90 flex h-12 w-12 items-center justify-center rounded-full'
                                                    : 'bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full'
                                            }
                                        >
                                            <Building2 className="text-primary/60 h-6 w-6" />
                                        </div>
                                        <div className="flex-1 space-y-1">
                                            <p
                                                className={
                                                    isDarkMode
                                                        ? 'text-foreground text-lg font-medium'
                                                        : 'text-muted-foreground/90 text-lg font-medium'
                                                }
                                            >
                                                {org.name}
                                            </p>
                                            <p className={isDarkMode ? 'text-muted-foreground text-sm' : 'text-muted-foreground/60 text-sm'}>
                                                {org.contact_email}
                                            </p>
                                        </div>
                                        <div
                                            className={`rounded-full px-3 py-1 text-sm font-medium ${org.status === 'active' ? 'bg-primary/100 text-primary-800' : 'bg-red-100 text-red-800'}`}
                                        >
                                            {org.status}
                                        </div>
                                        <motion.div variants={buttonHover} whileHover="hover">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                asChild
                                                className={
                                                    isDarkMode
                                                        ? 'from-primary/80 to-primary/90 hover:from-primary/90 hover:to-primary rounded-full bg-gradient-to-r px-4 py-2 text-white shadow-md'
                                                        : 'from-primary/60 to-primary/70 hover:from-primary/70 hover:to-primary/80 rounded-full bg-gradient-to-r px-4 py-2 text-white shadow-md'
                                                }
                                            >
                                                <Link href={`/organizations/${org.id}`}>View</Link>
                                            </Button>
                                        </motion.div>
                                    </motion.div>
                                ))}
                            </CardContent>
                        </Card>
                    </motion.div>

                    {/* Platform Management Section */}
                    <motion.div
                        className={
                            isDarkMode
                                ? 'bg-background rounded-2xl p-6 shadow-lg'
                                : 'from-muted-foreground/5 rounded-2xl bg-gradient-to-r to-white p-6 shadow-lg'
                        }
                        variants={fadeInUp}
                        initial="hidden"
                        animate="visible"
                    >
                        <h2
                            className={
                                isDarkMode
                                    ? 'text-foreground text-background/100 text-foreground/100 mb-6 text-2xl font-bold'
                                    : 'text-muted-foreground/60 text-background/100 text-foreground/100 mb-6 text-2xl font-bold'
                            }
                        >
                            Platform Management
                        </h2>
                        <p className={isDarkMode ? 'text-muted-foreground mb-8' : 'text-muted-foreground/60 mb-8'}>
                            Manage platform-wide settings and configurations
                        </p>
                        <div className="grid gap-6 md:grid-cols-3">
                            {[
                                { icon: <Building2 className="text-primary/40 h-10 w-10" />, title: 'Manage Organizations', href: '/organizations' },
                                { icon: <Users className="text-primary/40 h-10 w-10" />, title: 'Manage Users', href: '/users' },
                                { icon: <UserCog className="text-primary/40 h-10 w-10" />, title: 'Manage Roles', href: '/roles' },
                            ].map((item, index) => (
                                <motion.div key={index} variants={platformCardHover} whileHover="hover" className="flex-1">
                                    <Link
                                        href={item.href}
                                        className={
                                            isDarkMode
                                                ? 'text-primary/40 bg-muted flex h-32 flex-col items-center justify-center rounded-2xl p-4 shadow-lg transition-all duration-300'
                                                : 'text-primary/60 bg-muted-foreground/5 flex h-32 flex-col items-center justify-center rounded-2xl p-4 shadow-lg transition-all duration-300'
                                        }
                                        style={{ transitionProperty: 'background, color' }}
                                    >
                                        {item.icon}
                                        <span
                                            className={
                                                isDarkMode ? 'text-foreground mt-4 text-lg font-medium' : 'text-primary/60 mt-4 text-lg font-medium'
                                            }
                                        >
                                            {item.title}
                                        </span>
                                    </Link>
                                </motion.div>
                            ))}
                        </div>
                    </motion.div>
                </div>
            </div>
        </AppLayout>
    );
}
