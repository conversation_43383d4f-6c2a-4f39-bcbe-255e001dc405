import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';

interface BranchesEditProps {
    branch: {
        id: number;
        name: string;
        organization_id: number;
        address: string | null;
        contact_email: string | null;
        contact_phone: string | null;
        is_active: boolean;
        organization: {
            id: number;
            name: string;
        };
    };
    isPlatformAdmin: boolean;
    organizations?: {
        id: number;
        name: string;
    }[];
    organization?: {
        id: number;
        name: string;
    };
}

export default function BranchesEdit({ branch, isPlatformAdmin, organizations }: BranchesEditProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Branches',
            href: '/branches',
        },
        {
            title: branch.name,
            href: `/branches/${branch.id}`,
        },
        {
            title: 'Edit',
            href: `/branches/${branch.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        name: branch.name,
        organization_id: branch.organization_id.toString(),
        address: branch.address || '',
        contact_email: branch.contact_email || '',
        contact_phone: branch.contact_phone || '',
        is_active: branch.is_active,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/branches/${branch.id}`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Branch: ${branch.name}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 text-sm leading-none font-medium">
                <div className="flex items-center justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-2xl font-bold">Edit Branch: {branch.name}</h1>
                </div>

                <form onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>Branch Information</CardTitle>
                            <CardDescription>Update the branch's details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="name">Branch Name</Label>
                                <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} />
                                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                            </div>

                            {isPlatformAdmin && organizations && (
                                <div className="space-y-2">
                                    <Label htmlFor="organization_id">Organization</Label>
                                    <Select value={data.organization_id.toString()} onValueChange={(value) => setData('organization_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {organizations.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.organization_id && <p className="text-sm text-red-500">{errors.organization_id}</p>}
                                </div>
                            )}

                            <div className="space-y-2">
                                <Label htmlFor="address">Address (Optional)</Label>
                                <Textarea id="address" value={data.address} onChange={(e) => setData('address', e.target.value)} />
                                {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="contact_email">Contact Email (Optional)</Label>
                                    <Input
                                        id="contact_email"
                                        type="email"
                                        value={data.contact_email}
                                        onChange={(e) => setData('contact_email', e.target.value)}
                                    />
                                    {errors.contact_email && <p className="text-sm text-red-500">{errors.contact_email}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="contact_phone">Contact Phone</Label>
                                    <Input id="contact_phone" value={data.contact_phone} onChange={(e) => setData('contact_phone', e.target.value)} />
                                    {errors.contact_phone && <p className="text-sm text-red-500">{errors.contact_phone}</p>}
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', Boolean(checked))}
                                />
                                <Label htmlFor="is_active">Active</Label>
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" type="button" onClick={() => window.history.back()}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Update Branch
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
