import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MobileTable, MobileTableBody, MobileTableCell, MobileTableHead, MobileTableHeader, MobileTableRow } from '@/components/ui/mobile-table';
import { useMobileTable } from '@/hooks/use-mobile-table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Building2, GitBranch, MapPin, PlusCircle } from 'lucide-react';

interface BranchesIndexProps {
    branches: {
        id: number;
        name: string;
        organization: {
            id: number;
            name: string;
        };
        address: string | null;
        contact_email: string | null;
        contact_phone: string | null;
        is_active: boolean;
    }[];
    isPlatformAdmin: boolean;
    organizationId?: number;
    canCreateBranch?: boolean;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Branches',
        href: '/branches',
    },
];

export default function BranchesIndex({ branches, isPlatformAdmin, canCreateBranch = true }: BranchesIndexProps) {
    const { shouldShowItem, hasMoreItems, toggleShowAll, showAll } = useMobileTable({ maxVisibleItems: 5 });
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Branches" />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                    <h1 className="text-background/100 text-foreground/100 text-2xl font-bold">Branches</h1>
                    {canCreateBranch && (
                        <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                            <Link href="/branches/create">
                                <PlusCircle className="h-4 w-4 flex-shrink-0" />
                                <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                    New Branch
                                </span>
                            </Link>
                        </Button>
                    )}
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>All Branches</CardTitle>
                        <CardDescription>
                            {isPlatformAdmin ? 'Manage all branches across the platform' : 'Manage branches in your organization'}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {branches.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <GitBranch className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No branches found</h3>
                                    <p className="text-muted-foreground text-sm">Get started by creating a new branch.</p>
                                    {canCreateBranch && (
                                        <Button className="mt-4" asChild>
                                            <Link href="/branches/create">Create Branch</Link>
                                        </Button>
                                    )}
                                </div>
                            ) : (
                                <>
                                    <MobileTable>
                                        <MobileTableHeader>
                                            <tr>
                                                <MobileTableHead>Branch</MobileTableHead>
                                                {isPlatformAdmin && <MobileTableHead>Organization</MobileTableHead>}
                                                <MobileTableHead>Contact</MobileTableHead>
                                                <MobileTableHead>Status</MobileTableHead>
                                                <MobileTableHead className="text-right">Actions</MobileTableHead>
                                            </tr>
                                        </MobileTableHeader>
                                        <MobileTableBody>
                                            {branches
                                                .filter((_, index) => shouldShowItem(index))
                                                .map((branch) => (
                                                    <MobileTableRow
                                                        key={branch.id}
                                                        primaryContent={
                                                            <div className="space-y-2">
                                                                <div className="flex items-center gap-3">
                                                                    <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                                                        <GitBranch className="text-primary h-4 w-4" />
                                                                    </div>
                                                                    <div>
                                                                        <div className="font-medium">{branch.name}</div>
                                                                        {branch.address && (
                                                                            <div className="text-muted-foreground flex items-center text-sm">
                                                                                <MapPin className="mr-1 h-3 w-3" />
                                                                                {branch.address}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                                <div className="flex items-center justify-between">
                                                                    <Badge variant={branch.is_active ? 'default' : 'destructive'}>
                                                                        {branch.is_active ? 'Active' : 'Inactive'}
                                                                    </Badge>
                                                                    <div className="flex gap-2">
                                                                        <Button variant="outline" size="sm" asChild>
                                                                            <Link href={`/branches/${branch.id}`}>View</Link>
                                                                        </Button>
                                                                        <Button variant="outline" size="sm" asChild>
                                                                            <Link href={`/branches/${branch.id}/edit`}>Edit</Link>
                                                                        </Button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }
                                                        expandedContent={
                                                            <div className="space-y-2">
                                                                {isPlatformAdmin && (
                                                                    <MobileTableCell label="Organization">
                                                                        <div className="flex items-center gap-2">
                                                                            <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
                                                                                <Building2 className="h-3 w-3 text-blue-600" />
                                                                            </div>
                                                                            <span className="">{branch.organization.name}</span>
                                                                        </div>
                                                                    </MobileTableCell>
                                                                )}
                                                                <MobileTableCell label="Contact">
                                                                    <div className="text-right">
                                                                        {branch.contact_email && <div className="">{branch.contact_email}</div>}
                                                                        {branch.contact_phone && (
                                                                            <div className="text-muted-foreground">{branch.contact_phone}</div>
                                                                        )}
                                                                        {!branch.contact_email && !branch.contact_phone && (
                                                                            <div className="text-muted-foreground">No contact info</div>
                                                                        )}
                                                                    </div>
                                                                </MobileTableCell>
                                                            </div>
                                                        }
                                                    >
                                                        <MobileTableCell>
                                                            <span className="">{branch.name}</span>
                                                        </MobileTableCell>
                                                        {isPlatformAdmin && (
                                                            <MobileTableCell>
                                                                <span className="">{branch.organization.name}</span>
                                                            </MobileTableCell>
                                                        )}
                                                        <MobileTableCell>
                                                            {branch.contact_email && <div className="">{branch.contact_email}</div>}
                                                            {branch.contact_phone && (
                                                                <div className="text-muted-foreground">{branch.contact_phone}</div>
                                                            )}
                                                            {!branch.contact_email && !branch.contact_phone && (
                                                                <div className="text-muted-foreground">No contact info</div>
                                                            )}
                                                        </MobileTableCell>
                                                        <MobileTableCell>
                                                            <Badge variant={branch.is_active ? 'default' : 'destructive'}>
                                                                {branch.is_active ? 'Active' : 'Inactive'}
                                                            </Badge>
                                                        </MobileTableCell>
                                                        <MobileTableCell>
                                                            <div className="flex justify-end gap-2">
                                                                <Button variant="outline" size="sm" asChild>
                                                                    <Link href={`/branches/${branch.id}`}>View</Link>
                                                                </Button>
                                                                <Button variant="outline" size="sm" asChild>
                                                                    <Link href={`/branches/${branch.id}/edit`}>Edit</Link>
                                                                </Button>
                                                            </div>
                                                        </MobileTableCell>
                                                    </MobileTableRow>
                                                ))}
                                        </MobileTableBody>
                                    </MobileTable>

                                    {/* Show more/less button for mobile */}
                                    {hasMoreItems(branches.length) && (
                                        <div className="mt-4 flex justify-center md:hidden">
                                            <Button variant="outline" onClick={toggleShowAll} className="w-full max-w-xs">
                                                {showAll ? 'Show less' : `Show ${branches.length - 5} more`}
                                            </Button>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}