import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MobileCard, MobileCardInfo, MobileCardSection } from '@/components/ui/mobile-card';
import { useMobileCard } from '@/hooks/use-mobile-card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Briefcase, Building2, Mail, MapPin, Pencil, Phone, Trash2 } from 'lucide-react';

interface BranchesShowProps {
    branch: {
        id: number;
        name: string;
        organization: {
            id: number;
            name: string;
        };
        address: string | null;
        contact_email: string | null;
        contact_phone: string | null;
        is_active: boolean;
        departments: {
            id: number;
            name: string;
            hod_user_id: number | null;
            headOfDepartment?: {
                id: number;
                first_name: string;
                last_name: string;
            } | null;
        }[];
    };
}

export default function BranchesShow({ branch }: BranchesShowProps) {
    useMobileCard();

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Branches',
            href: '/branches',
        },
        {
            title: branch.name,
            href: `/branches/${branch.id}`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Branch: ${branch.name}`} />
            <div className="dark:bg-background/100 flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <h1 className="text-background/100 text-foreground/100 text-xl sm:text-2xl font-bold dark:text-white">Branch Details</h1>
                    <div className="flex gap-2 ml-auto">
                        <Button variant="outline" asChild>
                            <Link href={`/branches/${branch.id}/edit`}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid gap-4 grid-cols-1 lg:grid-cols-3">
                    <MobileCard
                        className="lg:col-span-1"
                        primaryInfo={
                            <div className="space-y-2">
                                <div className="flex items-center gap-3">
                                    <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                                        <Building2 className="text-primary h-5 w-5" />
                                    </div>
                                    <div>
                                        <h3 className="text-foreground/100 text-lg font-semibold">{branch.name}</h3>
                                        <p className="text-muted-foreground text-sm">{branch.organization.name}</p>
                                    </div>
                                </div>
                            </div>
                        }
                        secondaryInfo={
                            <div className="flex items-center gap-2">
                                <Badge variant={branch.is_active ? 'default' : 'destructive'}>{branch.is_active ? 'Active' : 'Inactive'}</Badge>
                            </div>
                        }
                        expandedContent={
                            <div className="space-y-4">
                                <MobileCardSection title="Organization">
                                    <MobileCardInfo
                                        value={
                                            <Link
                                                href={`/organizations/${branch.organization.id}`}
                                                className="text-primary flex items-center gap-2 hover:underline"
                                            >
                                                <Building2 className="h-4 w-4" />
                                                {branch.organization.name}
                                            </Link>
                                        }
                                        priority="high"
                                    />
                                </MobileCardSection>

                                {branch.address && (
                                    <MobileCardSection title="Address">
                                        <MobileCardInfo
                                            value={
                                                <div className="flex items-start gap-2">
                                                    <MapPin className="text-muted-foreground mt-1 h-4 w-4 flex-shrink-0" />
                                                    <span className="text-sm">{branch.address}</span>
                                                </div>
                                            }
                                            priority="medium"
                                        />
                                    </MobileCardSection>
                                )}

                                {(branch.contact_email || branch.contact_phone) && (
                                    <MobileCardSection title="Contact Information">
                                        {branch.contact_email && (
                                            <MobileCardInfo
                                                label="Email"
                                                value={
                                                    <a
                                                        href={`mailto:${branch.contact_email}`}
                                                        className="text-primary flex items-center gap-2 hover:underline"
                                                    >
                                                        <Mail className="h-4 w-4" />
                                                        {branch.contact_email}
                                                    </a>
                                                }
                                                priority="medium"
                                            />
                                        )}
                                        {branch.contact_phone && (
                                            <MobileCardInfo
                                                label="Phone"
                                                value={
                                                    <a
                                                        href={`tel:${branch.contact_phone}`}
                                                        className="text-primary flex items-center gap-2 hover:underline"
                                                    >
                                                        <Phone className="h-4 w-4" />
                                                        {branch.contact_phone}
                                                    </a>
                                                }
                                                priority="medium"
                                            />
                                        )}
                                    </MobileCardSection>
                                )}
                            </div>
                        }
                        expandButtonText="View details"
                        collapseButtonText="Hide details"
                    >
                        <CardHeader>
                            <CardTitle>Branch Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Name</h3>
                                <p className="text-lg font-semibold">{branch.name}</p>
                            </div>

                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Organization</h3>
                                <div className="mt-1 flex items-center">
                                    <Building2 className="text-primary mr-2 h-4 w-4" />
                                    <Link href={`/organizations/${branch.organization.id}`} className="text-primary hover:underline">
                                        {branch.organization.name}
                                    </Link>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-muted-foreground text-sm font-medium">Status</h3>
                                <Badge variant={branch.is_active ? 'default' : 'destructive'} className="mt-1">
                                    {branch.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                            </div>

                            {branch.address && (
                                <div>
                                    <h3 className="text-muted-foreground text-sm font-medium">Address</h3>
                                    <div className="mt-1 flex items-start">
                                        <MapPin className="text-muted-foreground mt-1 mr-2 h-4 w-4" />
                                        <p className="text-sm">{branch.address}</p>
                                    </div>
                                </div>
                            )}

                            {(branch.contact_email || branch.contact_phone) && (
                                <div>
                                    <h3 className="text-muted-foreground text-sm font-medium">Contact Information</h3>
                                    {branch.contact_email && (
                                        <div className="mt-1 flex items-center">
                                            <Mail className="text-muted-foreground mr-2 h-4 w-4" />
                                            <a href={`mailto:${branch.contact_email}`} className="text-primary text-sm hover:underline">
                                                {branch.contact_email}
                                            </a>
                                        </div>
                                    )}
                                    {branch.contact_phone && (
                                        <div className="mt-1 flex items-center">
                                            <Phone className="text-muted-foreground mr-2 h-4 w-4" />
                                            <a href={`tel:${branch.contact_phone}`} className="text-primary text-sm hover:underline">
                                                {branch.contact_phone}
                                            </a>
                                        </div>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </MobileCard>

                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Departments</CardTitle>
                            <CardDescription>Departments in this branch</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {branch.departments.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Briefcase className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No departments found</h3>
                                    <p className="text-muted-foreground text-sm">Create departments for this branch to see them here.</p>
                                    <Button className="mt-4" asChild>
                                        <Link href="/departments/create">Create Department</Link>
                                    </Button>
                                </div>
                            ) : (
                                <div className="overflow-auto max-h-96 border rounded-md">
                                    <table className="min-w-full divide-y divide-muted-foreground/20 dark:divide-muted-foreground/30">
                                        <thead className="bg-muted/50 dark:bg-muted/20">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-4 py-3 text-left text-xs font-medium tracking-wider text-muted-foreground uppercase whitespace-nowrap"
                                                >
                                                    Department
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-4 py-3 text-left text-xs font-medium tracking-wider text-muted-foreground uppercase whitespace-nowrap"
                                                >
                                                    Head of Department
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-4 py-3 text-right text-xs font-medium tracking-wider text-muted-foreground uppercase whitespace-nowrap"
                                                >
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-muted-foreground/20 dark:divide-muted-foreground/30 bg-background dark:bg-background">
                                            {branch.departments.map((department) => (
                                                <tr key={department.id} className="hover:bg-muted/30 dark:hover:bg-muted/20">
                                                    <td className="px-4 py-4 whitespace-nowrap">
                                                        <div className="flex items-center min-w-0">
                                                            <div className="bg-primary/40 flex h-8 w-8 items-center justify-center rounded-full flex-shrink-0">
                                                                <Briefcase className="text-primary h-4 w-4" />
                                                            </div>
                                                            <div className="ml-3 min-w-0">
                                                                <div className="text-sm font-medium text-foreground truncate">
                                                                    {department.name}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-4 py-4 whitespace-nowrap">
                                                        {department.headOfDepartment ? (
                                                            <Link
                                                                href={`/users/${department.headOfDepartment.id}`}
                                                                className="text-primary text-sm hover:underline block truncate max-w-[150px]"
                                                                title={`${department.headOfDepartment.first_name} ${department.headOfDepartment.last_name}`}
                                                            >
                                                                {department.headOfDepartment.first_name} {department.headOfDepartment.last_name}
                                                            </Link>
                                                        ) : (
                                                            <Badge
                                                                variant="outline"
                                                                className="border-accent/80 bg-accent/70 text-background/100 dark:bg-accent/80 dark:text-foreground/100 text-xs"
                                                            >
                                                                No HOD Assigned
                                                            </Badge>
                                                        )}
                                                    </td>
                                                    <td className="px-4 py-4 text-right text-sm font-medium whitespace-nowrap">
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={`/departments/${department.id}`}>View</Link>
                                                        </Button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}