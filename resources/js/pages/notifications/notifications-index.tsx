import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Bell, CheckCircle2, Trash2 } from 'lucide-react';
import NotificationItem from '@/components/notifications/NotificationItem';
import { PaginatedNotifications } from '@/types/notification';
import { useEffect, useState } from 'react';

interface Props {
    notifications: PaginatedNotifications;
    unreadCount: number;
}

export default function NotificationsIndex({ notifications, unreadCount }: Props) {
    const [currentUnreadCount, setCurrentUnreadCount] = useState(unreadCount);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: route('dashboard'),
        },
        {
            title: 'Notifications',
            href: route('notifications.index'),
        },
    ];

    // Poll for new notifications every 30 seconds
    useEffect(() => {
        const interval = setInterval(() => {
            // Check if there are new notifications by comparing unread count
            fetch(route('notifications.unread-count'))
                .then((response) => response.json())
                .then((data) => {
                    if (data.count !== currentUnreadCount) {
                        // Refresh the page to get new notifications in correct order
                        router.reload({ only: ['notifications', 'unreadCount'] });
                    }
                    setCurrentUnreadCount(data.count);
                })
                .catch(console.error);
        }, 30000); // Poll every 30 seconds

        return () => clearInterval(interval);
    }, [currentUnreadCount]);

    const handleMarkAsRead = (id: string) => {
        // Update unread count immediately for UI responsiveness
        const notification = notifications.data.find(n => n.id === id);
        if (notification && !notification.read_at) {
            setCurrentUnreadCount(prev => Math.max(0, prev - 1));
            // Mark the notification as read in the local state
            notification.read_at = new Date().toISOString();
        }
        
        // Trigger immediate notification check to update badge
        if (window.enhancedPollingService) {
            window.enhancedPollingService.triggerImmediateCheck();
        }
        
        // Also make the server call to ensure persistence
        router.post(route('notifications.mark-as-read', id), {}, {
            preserveState: true,
            preserveScroll: true,
            onError: () => {
                // Revert the optimistic update if the server call fails
                if (notification && notification.read_at) {
                    notification.read_at = null;
                    setCurrentUnreadCount(prev => prev + 1);
                }
            }
        });
    };

    const handleMarkAllAsRead = () => {
        router.post(route('notifications.mark-all-as-read'), {}, {
            onSuccess: () => {
                setCurrentUnreadCount(0);
                
                // Trigger immediate notification check to update badge
                if (window.enhancedPollingService) {
                    window.enhancedPollingService.triggerImmediateCheck();
                }
            }
        });
    };

    const handleDelete = (id: string) => {
        router.delete(route('notifications.destroy', id), {
            onSuccess: () => {
                // Update unread count immediately if the deleted notification was unread
                const deletedNotification = notifications.data.find((n) => n.id === id);
                if (deletedNotification && !deletedNotification.read_at) {
                    setCurrentUnreadCount((prev) => Math.max(0, prev - 1));
                }
            },
        });
    };

    const handleDeleteAll = () => {
        if (confirm('Are you sure you want to delete all notifications? This action cannot be undone.')) {
            router.delete(route('notifications.delete-all'), {
                onSuccess: () => {
                    setCurrentUnreadCount(0);
                }
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Notifications" />
            <div className="flex h-full flex-1 flex-col gap-4 p-3 sm:gap-6 sm:p-4 md:p-6">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
                        <h1 className="text-2xl font-bold text-foreground md:text-3xl">
                            Notifications
                        </h1>
                        {currentUnreadCount > 0 && (
                            <Badge variant="default" className="bg-primary text-primary-foreground w-fit">
                                {currentUnreadCount} unread
                            </Badge>
                        )}
                    </div>

                    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
                        {currentUnreadCount > 0 && (
                            <Button
                                variant="outline"
                                onClick={handleMarkAllAsRead}
                                className="flex items-center justify-center gap-2 text-sm"
                                size="sm"
                            >
                                <CheckCircle2 className="h-4 w-4" />
                                <span className="hidden xs:inline">Mark all as read</span>
                                <span className="xs:hidden">Mark all</span>
                            </Button>
                        )}
                        
                        {notifications.data.length > 0 && (
                            <Button
                                variant="outline"
                                onClick={handleDeleteAll}
                                className="flex items-center justify-center gap-2 text-destructive hover:text-destructive text-sm"
                                size="sm"
                            >
                                <Trash2 className="h-4 w-4" />
                                <span className="hidden xs:inline">Delete all</span>
                                <span className="xs:hidden">Delete</span>
                            </Button>
                        )}
                    </div>
                </div>

                <div className="space-y-3 sm:space-y-4">
                    {notifications.data.length > 0 ? (
                        notifications.data.map((notification) => (
                            <NotificationItem
                                key={notification.id}
                                notification={notification}
                                onMarkAsRead={handleMarkAsRead}
                                onDelete={handleDelete}
                            />
                        ))
                    ) : (
                        <Card>
                            <CardContent className="flex flex-col items-center justify-center py-12">
                                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium text-foreground mb-2">
                                    No notifications
                                </h3>
                                <p className="text-muted-foreground text-center">
                                    You're all caught up! New notifications will appear here.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Pagination */}
                {notifications.last_page > 1 && <div className="mt-6 flex justify-center">{/* Add pagination component here */}</div>}
            </div>
        </AppLayout>
    );
}
