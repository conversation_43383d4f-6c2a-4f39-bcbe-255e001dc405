import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { PlusCircle, UserCog } from 'lucide-react';

interface RolesIndexProps {
    roles: {
        id: number;
        name: string;
        description: string | null;
        is_active: boolean;
    }[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Roles',
        href: '/roles',
    },
];

export default function RolesIndex({ roles }: RolesIndexProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Roles" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/100 text-background/100 text-2xl font-bold md:text-3xl">Roles</h1>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href="/roles/create">
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                New Role
                            </span>
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>All Roles</CardTitle>
                        <CardDescription>Manage roles on the platform</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {roles.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <UserCog className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No roles found</h3>
                                    <p className="text-muted-foreground text-sm">Get started by creating a new role.</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full">
                                        <thead>
                                            <tr className="border-b">
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Role</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Description</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Status</th>
                                                <th className="text-muted-foreground h-12 px-4 text-right align-middle font-medium">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {roles.map((role) => (
                                                <tr
                                                    key={role.id}
                                                    className="hover:bg-muted/50 dark:hover:bg-accent/70 data-[state=selected]:bg-muted border-b transition-colors"
                                                >
                                                    <td className="p-4 align-middle">
                                                        <div className="flex items-center">
                                                            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                                                                <UserCog className="text-primary h-4 w-4" />
                                                            </div>
                                                            <div className="ml-4">
                                                                <div className="text-sm font-medium">{role.name}</div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <div className="text-sm">{role.description || 'No description'}</div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <Badge variant={role.is_active ? 'default' : 'destructive'}>
                                                            {role.is_active ? 'Active' : 'Inactive'}
                                                        </Badge>
                                                    </td>
                                                    <td className="p-4 text-right align-middle">
                                                        <div className="flex justify-end gap-2">
                                                            <Button variant="ghost" size="sm" asChild>
                                                                <Link href={`/roles/${role.id}`}>View</Link>
                                                            </Button>
                                                            <Button variant="ghost" size="sm" asChild>
                                                                <Link href={`/roles/${role.id}/edit`}>Edit</Link>
                                                            </Button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
