import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Pencil, Trash2, UserCog } from 'lucide-react';

interface RolesShowProps {
    role: {
        id: number;
        name: string;
        description: string | null;
        is_active: boolean;
        permissions: {
            id: number;
            name: string;
            description: string | null;
        }[];
    };
}

export default function RolesShow({ role }: RolesShowProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Roles',
            href: '/roles',
        },
        {
            title: role.name,
            href: `/roles/${role.id}`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Role: ${role.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/90 text-2xl font-bold md:text-3xl">
                        Role Details
                    </h1>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={`/roles/${role.id}/edit`}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                    <Card className="md:col-span-1">
                        <CardHeader>
                            <CardTitle>Role Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="flex items-center">
                                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                                    <UserCog className="text-primary h-5 w-5" />
                                </div>
                                <div className="ml-4">
                                    <h2 className="text-xl font-semibold">{role.name}</h2>
                                    {role.description && (
                                        <p className="text-sm text-muted-foreground">{role.description}</p>
                                    )}
                                </div>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium">Status</h3>
                                <Badge variant={role.is_active ? 'default' : 'destructive'} className="mt-1">
                                    {role.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle>Permissions</CardTitle>
                            <CardDescription>Permissions assigned to this role</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {role.permissions.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <UserCog className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No permissions assigned</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Edit this role to assign permissions.
                                    </p>
                                    <Button className="mt-4" asChild>
                                        <Link href={`/roles/${role.id}/edit`}>Edit Role</Link>
                                    </Button>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                                    {role.permissions.map((permission) => (
                                        <div key={permission.id} className="rounded-md border p-3">
                                            <div className="flex items-start">
                                                <div className="bg-primary/10 flex h-5 w-5 items-center justify-center rounded-full">
                                                    <UserCog className="text-primary h-3 w-3" />
                                                </div>
                                                <div className="ml-2">
                                                    <h3 className="text-sm font-medium">{permission.name}</h3>
                                                    {permission.description && (
                                                        <p className="text-xs text-muted-foreground">{permission.description}</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
