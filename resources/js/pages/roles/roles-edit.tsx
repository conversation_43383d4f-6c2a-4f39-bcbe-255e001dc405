import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { useEffect, useState } from 'react';

interface RolesEditProps {
    role: {
        id: number;
        name: string;
        description: string | null;
        organization_id: number | null;
        branch_id: number | null;
        department_id: number | null;
        is_active: boolean;
        permissions: {
            id: number;
            name: string;
        }[];
    };
    isPlatformAdmin: boolean;
    organizations?: {
        id: number;
        name: string;
    }[];
    organization?: {
        id: number;
        name: string;
    };
    branches: {
        id: number;
        name: string;
        organization_id: number;
    }[];
    departments: {
        id: number;
        name: string;
        organization_id: number;
        branch_id: number;
    }[];
    permissions: {
        id: number;
        name: string;
        description: string | null;
    }[];
    groupedPermissions: {
        [category: string]: {
            id: number;
            name: string;
            description: string | null;
        }[];
    };
}

export default function RolesEdit({ role, isPlatformAdmin, organizations, branches, departments, groupedPermissions }: RolesEditProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Roles',
            href: '/roles',
        },
        {
            title: role.name,
            href: `/roles/${role.id}`,
        },
        {
            title: 'Edit',
            href: `/roles/${role.id}/edit`,
        },
    ];

    const { data, setData, put, processing, errors } = useForm({
        name: role.name,
        description: role.description || '',
        organization_id: role.organization_id ? role.organization_id.toString() : '',
        branch_id: role.branch_id ? role.branch_id.toString() : '',
        department_id: role.department_id ? role.department_id.toString() : '',
        permission_ids: role.permissions.map((p) => p.id),
        is_active: role.is_active,
    });

    const [selectedPermissions, setSelectedPermissions] = useState<number[]>(data.permission_ids);

    useEffect(() => {
        setSelectedPermissions(data.permission_ids);
    }, [role, data.permission_ids]);

    const filteredBranches = branches.filter(
        (branch) => branch.organization_id === (data.organization_id ? parseInt(data.organization_id.toString()) : null),
    );

    const filteredDepartments = departments.filter(
        (department) =>
            department.organization_id === (data.organization_id ? parseInt(data.organization_id.toString()) : null) &&
            (data.branch_id ? department.branch_id === parseInt(data.branch_id.toString()) : true),
    );

    const handlePermissionChange = (permissionId: number) => {
        const newSelectedPermissions = selectedPermissions.includes(permissionId)
            ? selectedPermissions.filter((id) => id !== permissionId)
            : [...selectedPermissions, permissionId];

        setSelectedPermissions(newSelectedPermissions);
        setData('permission_ids', newSelectedPermissions);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/roles/${role.id}`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Role: ${role.name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/90 text-2xl font-bold md:text-3xl">
                        Edit Role: {role.name}
                    </h1>
                </div>

                <form onSubmit={handleSubmit} className="mx-auto w-full max-w-3xl">
                    <Card>
                        <CardHeader>
                            <CardTitle>Role Information</CardTitle>
                            <CardDescription>Update the role's details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6 p-6">
                            <div className="space-y-2">
                                <Label htmlFor="name" className="text-sm font-medium">
                                    Role Name
                                </Label>
                                <Input
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                />
                                {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description" className="text-sm font-medium">
                                    Description (Optional)
                                </Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    className="min-h-[100px]"
                                />
                                {errors.description && <p className="text-sm text-destructive">{errors.description}</p>}
                            </div>

                            {isPlatformAdmin && organizations && (
                                <div className="space-y-2">
                                    <Label htmlFor="organization_id" className="text-sm font-medium">
                                        Organization (Optional)
                                    </Label>
                                    <Select
                                        value={data.organization_id.toString()}
                                        onValueChange={(value) => {
                                            setData('organization_id', value);
                                            setData('branch_id', '');
                                            setData('department_id', '');
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select organization" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">None (Platform-wide)</SelectItem>
                                            {organizations.map((org) => (
                                                <SelectItem key={org.id} value={org.id.toString()}>
                                                    {org.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.organization_id && <p className="text-sm text-destructive">{errors.organization_id}</p>}
                                </div>
                            )}

                            {data.organization_id && (
                                <div className="space-y-2">
                                    <Label htmlFor="branch_id" className="text-sm font-medium">
                                        Branch (Optional)
                                    </Label>
                                    <Select
                                        value={data.branch_id.toString()}
                                        onValueChange={(value) => {
                                            setData('branch_id', value);
                                            setData('department_id', '');
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select branch" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">None (Organization-wide)</SelectItem>
                                            {filteredBranches.map((branch) => (
                                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                                    {branch.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.branch_id && <p className="text-sm text-destructive">{errors.branch_id}</p>}
                                </div>
                            )}

                            {data.branch_id && (
                                <div className="space-y-2">
                                    <Label htmlFor="department_id" className="text-sm font-medium">
                                        Department (Optional)
                                    </Label>
                                    <Select value={data.department_id.toString()} onValueChange={(value) => setData('department_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select department" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">None (Branch-wide)</SelectItem>
                                            {filteredDepartments.map((department) => (
                                                <SelectItem key={department.id} value={department.id.toString()}>
                                                    {department.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.department_id && <p className="text-sm text-destructive">{errors.department_id}</p>}
                                </div>
                            )}

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', Boolean(checked))}
                                />
                                <Label htmlFor="is_active">Active</Label>
                            </div>

                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-medium">Permissions</h3>
                                    <p className="text-muted-foreground text-sm">Select the permissions for this role</p>
                                </div>

                                {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                                    <div key={category} className="space-y-3">
                                        <h4 className="text-md border-b pb-1 font-medium">{category}</h4>
                                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
                                            {categoryPermissions.map((permission) => (
                                                <div key={permission.id} className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id={`permission-${permission.id}`}
                                                        checked={selectedPermissions.includes(permission.id)}
                                                        onCheckedChange={() => handlePermissionChange(permission.id)}
                                                    />
                                                    <div>
                                                        <Label htmlFor={`permission-${permission.id}`} className="text-sm font-medium">
                                                            {permission.name.replace(/^(manage|view|create|edit|delete|approve|reject)-/, '')}
                                                        </Label>
                                                        {permission.description && (
                                                            <p className="text-muted-foreground text-xs">{permission.description}</p>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                                {errors.permission_ids && <p className="text-sm text-destructive">{errors.permission_ids}</p>}
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" type="button" onClick={() => window.history.back()}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Update Role
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
