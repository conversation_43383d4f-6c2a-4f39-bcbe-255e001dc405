import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Mail, Pencil, Phone, Trash2, Users } from 'lucide-react';

interface UsersShowProps {
    user: User & {
        roles: {
            id: number;
            name: string;
        }[];
        organization: {
            id: number;
            name: string;
        } | null;
    };
}

export default function UsersShow({ user }: UsersShowProps) {
    const getInitials = useInitials();

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Users',
            href: '/users',
        },
        {
            title: `${user.first_name} ${user.last_name}`,
            href: `/users/${user.id}`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`User: ${user.first_name} ${user.last_name}`} />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className=" text-foreground/90 text-2xl font-bold md:text-3xl">
                        User Details
                    </h1>
                    <div className="flex gap-2">
                        <Button variant="outline" asChild>
                            <Link href={`/users/${user.id}/edit`}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                    <Card className="md:col-span-1">
                        <CardHeader>
                            <CardTitle>User Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="flex items-center">
                                <Avatar className="h-10 w-10">
                                    <AvatarFallback className="bg-primary/10 text-primary">
                                        {getInitials(`${user.first_name} ${user.last_name}`)}
                                    </AvatarFallback>
                                </Avatar>
                                <div className="ml-4">
                                    <h2 className="text-xl font-semibold">
                                        {user.first_name} {user.last_name}
                                    </h2>
                                    <p className="text-sm text-muted-foreground">@{user.username}</p>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium">Contact</h3>
                                <div className="mt-1 space-y-1">
                                    <div className="flex items-center text-sm">
                                        <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                                        {user.email}
                                    </div>
                                    {user.phone && (
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                                            {user.phone}
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium">Status</h3>
                                <Badge
                                    variant={user.status === 'active' ? 'default' : user.status === 'invited' ? 'secondary' : 'destructive'}
                                    className="mt-1"
                                >
                                    {user.status}
                                </Badge>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle>Roles & Organization</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-lg font-medium">Roles</h3>
                                    {user.roles && user.roles.length > 0 ? (
                                        <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
                                            {user.roles.map((role) => (
                                                <div key={role.id} className="rounded-md border p-3">
                                                    <div className="flex items-center">
                                                        <Users className="mr-2 h-4 w-4 text-primary" />
                                                        <Link
                                                            href={`/roles/${role.id}`}
                                                            className="text-sm text-primary hover:underline"
                                                        >
                                                            {role.name}
                                                        </Link>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="mt-2 text-sm text-muted-foreground">No roles assigned.</p>
                                    )}
                                </div>

                                <div>
                                    <h3 className="text-lg font-medium">Organization</h3>
                                    {user.organization ? (
                                        <div className="mt-2 rounded-md border p-3">
                                            <div className="flex items-center">
                                                <Users className="mr-2 h-4 w-4 text-primary" />
                                                <Link
                                                    href={`/organizations/${user.organization.id}`}
                                                    className="text-sm text-primary hover:underline"
                                                >
                                                    {user.organization.name}
                                                </Link>
                                            </div>
                                        </div>
                                    ) : (
                                        <p className="mt-2 text-sm text-muted-foreground">No organization assigned.</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
