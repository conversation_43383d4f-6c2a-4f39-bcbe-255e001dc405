import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useInitials } from '@/hooks/use-initials';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { PlusCircle, Users } from 'lucide-react';

interface UsersIndexProps {
    users: User[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Users',
        href: '/users',
    },
];

export default function UsersIndex({ users }: UsersIndexProps) {
    const getInitials = useInitials();

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Users" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-foreground/100 text-background/100 text-2xl font-bold md:text-3xl">Users</h1>
                    <Button asChild className="group relative w-10 overflow-hidden transition-all duration-300 ease-in-out hover:w-auto lg:w-auto">
                        <Link href="/users/create">
                            <PlusCircle className="h-4 w-4 flex-shrink-0" />
                            <span className="absolute left-10 whitespace-nowrap opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 lg:relative lg:left-0 lg:ml-2 lg:opacity-100">
                                New User
                            </span>
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>All Users</CardTitle>
                        <CardDescription>Manage all users on the platform</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {users.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Users className="text-muted-foreground h-12 w-12" />
                                    <h3 className="mt-4 text-lg font-medium">No users found</h3>
                                    <p className="text-muted-foreground text-sm">Get started by creating a new user.</p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full">
                                        <thead>
                                            <tr className="border-b">
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">User</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Email</th>
                                                <th className="text-muted-foreground h-12 px-4 text-left align-middle font-medium">Status</th>
                                                <th className="text-muted-foreground h-12 px-4 text-right align-middle font-medium">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {users.map((user) => (
                                                <tr
                                                    key={user.id}
                                                    className="hover:bg-muted/50 dark:hover:bg-accent/70 data-[state=selected]:bg-muted border-b transition-colors"
                                                >
                                                    <td className="p-4 align-middle">
                                                        <div className="flex items-center">
                                                            <Avatar className="h-8 w-8">
                                                                <AvatarFallback className="bg-primary/10 text-primary">
                                                                    {getInitials(`${user.first_name} ${user.last_name}`)}
                                                                </AvatarFallback>
                                                            </Avatar>
                                                            <div className="ml-4">
                                                                <div className="text-sm font-medium">
                                                                    {user.first_name} {user.last_name}
                                                                </div>
                                                                <div className="text-muted-foreground text-sm">@{user.username}</div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <div className="text-sm">{user.email}</div>
                                                        <div className="text-muted-foreground text-sm">{user.phone || 'No phone'}</div>
                                                    </td>
                                                    <td className="p-4 align-middle">
                                                        <Badge
                                                            variant={
                                                                user.status === 'active'
                                                                    ? 'default'
                                                                    : user.status === 'invited'
                                                                      ? 'outline'
                                                                      : 'destructive'
                                                            }
                                                            className={
                                                                user.status === 'invited'
                                                                    ? 'dark:text-secondary-foreground/100 bg-accent/100 border-accent/300 dark:border-accent/60 dark:bg-accent/90/20 text-accent/80'
                                                                    : ''
                                                            }
                                                        >
                                                            {user.status}
                                                        </Badge>
                                                    </td>
                                                    <td className="p-4 text-right align-middle">
                                                        <div className="flex justify-end gap-2">
                                                            <Button variant="ghost" size="sm" asChild>
                                                                <Link href={`/users/${user.id}`}>View</Link>
                                                            </Button>
                                                            <Button variant="ghost" size="sm" asChild>
                                                                <Link href={`/users/${user.id}/edit`}>Edit</Link>
                                                            </Button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
