import { useCallback, useState } from 'react';
import { useIsMobile } from './use-mobile';

interface UseMobileCardOptions {
    defaultExpanded?: boolean;
    maxPrimaryItems?: number;
    priorityOrder?: ('high' | 'medium' | 'low')[];
}

interface CardItem {
    id: string;
    content: React.ReactNode;
    priority: 'high' | 'medium' | 'low';
    hideOnMobile?: boolean;
}

export function useMobileCard(options: UseMobileCardOptions = {}) {
    const { defaultExpanded = false, maxPrimaryItems = 2, priorityOrder = ['high', 'medium', 'low'] } = options;

    const isMobile = useIsMobile();
    const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());
    const [globalExpanded, setGlobalExpanded] = useState(defaultExpanded);

    const toggleCard = useCallback((cardId: string) => {
        setExpandedCards((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(cardId)) {
                newSet.delete(cardId);
            } else {
                newSet.add(cardId);
            }
            return newSet;
        });
    }, []);

    const isCardExpanded = useCallback(
        (cardId: string) => {
            return expandedCards.has(cardId);
        },
        [expandedCards],
    );

    const toggleGlobalExpanded = useCallback(() => {
        setGlobalExpanded((prev) => !prev);
    }, []);

    const sortItemsByPriority = useCallback(
        (items: CardItem[]) => {
            if (!isMobile) return items;

            return [...items].sort((a, b) => {
                const aPriorityIndex = priorityOrder.indexOf(a.priority);
                const bPriorityIndex = priorityOrder.indexOf(b.priority);
                return aPriorityIndex - bPriorityIndex;
            });
        },
        [isMobile, priorityOrder],
    );

    const splitItemsForMobile = useCallback(
        (items: CardItem[]) => {
            if (!isMobile) {
                return { primaryItems: items, expandedItems: [] };
            }

            const visibleItems = items.filter((item) => !item.hideOnMobile);
            const sortedItems = sortItemsByPriority(visibleItems);

            const primaryItems = sortedItems.slice(0, maxPrimaryItems);
            const expandedItems = sortedItems.slice(maxPrimaryItems);

            return { primaryItems, expandedItems };
        },
        [isMobile, maxPrimaryItems, sortItemsByPriority],
    );

    const shouldShowExpandButton = useCallback(
        (items: CardItem[]) => {
            if (!isMobile) return false;
            const visibleItems = items.filter((item) => !item.hideOnMobile);
            return visibleItems.length > maxPrimaryItems;
        },
        [isMobile, maxPrimaryItems],
    );

    const getVisibleItemsCount = useCallback((items: CardItem[]) => {
        return items.filter((item) => !item.hideOnMobile).length;
    }, []);

    const getHiddenItemsCount = useCallback(
        (items: CardItem[]) => {
            if (!isMobile) return 0;
            const visibleItems = items.filter((item) => !item.hideOnMobile);
            return Math.max(0, visibleItems.length - maxPrimaryItems);
        },
        [isMobile, maxPrimaryItems],
    );

    return {
        isMobile,
        expandedCards,
        globalExpanded,
        toggleCard,
        isCardExpanded,
        toggleGlobalExpanded,
        sortItemsByPriority,
        splitItemsForMobile,
        shouldShowExpandButton,
        getVisibleItemsCount,
        getHiddenItemsCount,
        maxPrimaryItems,
    };
}

// Helper function to create card items
export function createCardItem(
    id: string,
    content: React.ReactNode,
    priority: 'high' | 'medium' | 'low' = 'medium',
    hideOnMobile: boolean = false,
): CardItem {
    return {
        id,
        content,
        priority,
        hideOnMobile,
    };
}

// Helper function to create responsive card data
export function createResponsiveCardData(items: CardItem[]) {
    return {
        items,
        highPriorityItems: items.filter((item) => item.priority === 'high'),
        mediumPriorityItems: items.filter((item) => item.priority === 'medium'),
        lowPriorityItems: items.filter((item) => item.priority === 'low'),
        mobileVisibleItems: items.filter((item) => !item.hideOnMobile),
    };
}
