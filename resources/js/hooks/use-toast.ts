// resources/js/hooks/use-toast.ts
import { useState } from 'react'; //If you are using react

const useToast = () => {
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'info' | 'warning' | null }>({
    message: null,
    type: null,
  });

  const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning') => {
    setToast({ message, type });
    // You'll likely want to clear the toast after a few seconds:
    setTimeout(() => {
      setToast({ message: "", type: null });
    }, 5000); // Clear after 5 seconds (adjust as needed)
  };

  return { toast, showToast };
};

export { useToast };
