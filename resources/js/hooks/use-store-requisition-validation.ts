import { useMemo, useCallback } from 'react';
import { 
  StoreRequisitionFormData, 
  StoreRequisitionItemFormData, 
  StoreRequisitionActionFormData 
} from '@/types/store-requisitions';

interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: unknown) => string | null;
}

interface ValidationRules {
  [key: string]: ValidationRule;
}

interface UseStoreRequisitionValidationReturn {
  // Validation functions
  validateField: (field: string, value: unknown, rules?: ValidationRule) => string | null;
  validateItem: (item: StoreRequisitionItemFormData, index?: number) => Record<string, string>;
  validateForm: (data: StoreRequisitionFormData) => Record<string, string>;
  validateAction: (data: StoreRequisitionActionFormData) => Record<string, string>;
  
  // Validation rules
  formRules: ValidationRules;
  itemRules: ValidationRules;
  actionRules: ValidationRules;
  
  // Helper functions
  isValidEmail: (email: string) => boolean;
  isValidNumber: (value: unknown) => boolean;
  isValidPositiveNumber: (value: unknown) => boolean;
  formatValidationError: (field: string, rule: string, params?: number) => string;
}

// Validation error messages
const ERROR_MESSAGES = {
  required: (field: string) => `${field} is required`,
  min: (field: string, min: number) => `${field} must be at least ${min}`,
  max: (field: string, max: number) => `${field} must not exceed ${max}`,
  minLength: (field: string, min: number) => `${field} must be at least ${min} characters`,
  maxLength: (field: string, max: number) => `${field} must not exceed ${max} characters`,
  pattern: (field: string) => `${field} format is invalid`,
  email: (field: string) => `${field} must be a valid email address`,
  number: (field: string) => `${field} must be a valid number`,
  positiveNumber: (field: string) => `${field} must be a positive number`,
};

export function useStoreRequisitionValidation(): UseStoreRequisitionValidationReturn {
  
  // Validation rules for store requisition form fields
  const formRules: ValidationRules = useMemo(() => ({
    purpose: {
      required: true,
      minLength: 3,
      maxLength: 500,
    },
    branch_id: {
      required: true,
      custom: (value) => {
        const numValue = Number(value);
        if (!value || numValue <= 0) {
          return 'Please select a branch';
        }
        return null;
      },
    },
    department_id: {
      required: true,
      custom: (value) => {
        const numValue = Number(value);
        if (!value || numValue <= 0) {
          return 'Please select a department';
        }
        return null;
      },
    },
  }), []);

  // Validation rules for store requisition items
  const itemRules: ValidationRules = useMemo(() => ({
    inventory_item_id: {
      required: true,
      custom: (value) => {
        // Ensure it's a positive integer
        const numValue = Number(value);
        if (!Number.isInteger(numValue) || numValue <= 0) {
          return 'Please select a valid inventory item';
        }
        return null;
      },
    },
    quantity_requested: {
      required: true,
      min: 1,
      max: 999999,
      custom: (value) => {
        // Ensure it's a positive integer
        const numValue = Number(value);
        if (!Number.isInteger(numValue) || numValue <= 0) {
          return 'Quantity must be a positive whole number';
        }
        if (numValue > 999999) {
          return 'Quantity cannot exceed 999,999';
        }
        return null;
      },
    },
  }), []);

  // Validation rules for store requisition actions
  const actionRules: ValidationRules = useMemo(() => ({
    action: {
      required: true,
      custom: (value) => {
        const validActions = ['submit', 'approve', 'reject', 'issue'];
        if (!validActions.includes(String(value))) {
          return 'Please select a valid action';
        }
        return null;
      },
    },
    rejection_reason: {
      custom: (value) => {
        const stringValue = String(value || '');
        if (value && stringValue.length > 1000) {
          return 'Rejection reason must not exceed 1000 characters';
        }
        return null;
      },
    },
  }), []);

  // Helper functions
  const isValidEmail = useCallback((email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  const isValidNumber = useCallback((value: unknown): boolean => {
    return !isNaN(Number(value)) && isFinite(Number(value));
  }, []);

  const isValidPositiveNumber = useCallback((value: unknown): boolean => {
    return isValidNumber(value) && Number(value) > 0;
  }, [isValidNumber]);

  const formatValidationError = useCallback((field: string, rule: string, params?: number): string => {
    const fieldName = field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    switch (rule) {
      case 'required':
        return ERROR_MESSAGES.required(fieldName);
      case 'min':
        return ERROR_MESSAGES.min(fieldName, params || 0);
      case 'max':
        return ERROR_MESSAGES.max(fieldName, params || 0);
      case 'minLength':
        return ERROR_MESSAGES.minLength(fieldName, params || 0);
      case 'maxLength':
        return ERROR_MESSAGES.maxLength(fieldName, params || 0);
      case 'pattern':
        return ERROR_MESSAGES.pattern(fieldName);
      case 'email':
        return ERROR_MESSAGES.email(fieldName);
      case 'number':
        return ERROR_MESSAGES.number(fieldName);
      case 'positiveNumber':
        return ERROR_MESSAGES.positiveNumber(fieldName);
      default:
        return `${fieldName} is invalid`;
    }
  }, []);

  // Generic field validation function
  const validateField = useCallback((field: string, value: unknown, rules?: ValidationRule): string | null => {
    if (!rules) return null;

    // Required validation
    if (rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return formatValidationError(field, 'required');
    }

    // Skip other validations if value is empty and not required
    if (!value && !rules.required) return null;

    // Min/Max validation for numbers
    if (rules.min !== undefined && Number(value) < rules.min) {
      return formatValidationError(field, 'min', rules.min);
    }

    if (rules.max !== undefined && Number(value) > rules.max) {
      return formatValidationError(field, 'max', rules.max);
    }

    // String length validation
    if (rules.minLength !== undefined && String(value).length < rules.minLength) {
      return formatValidationError(field, 'minLength', rules.minLength);
    }

    if (rules.maxLength !== undefined && String(value).length > rules.maxLength) {
      return formatValidationError(field, 'maxLength', rules.maxLength);
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(String(value))) {
      return formatValidationError(field, 'pattern');
    }

    // Custom validation
    if (rules.custom) {
      return rules.custom(value);
    }

    return null;
  }, [formatValidationError]);

  // Validate individual store requisition item
  const validateItem = useCallback((item: StoreRequisitionItemFormData, index?: number): Record<string, string> => {
    const errors: Record<string, string> = {};
    const prefix = index !== undefined ? `items.${index}` : '';

    Object.keys(itemRules).forEach(field => {
      const fieldKey = prefix ? `${prefix}.${field}` : field;
      const error = validateField(field, item[field as keyof StoreRequisitionItemFormData], itemRules[field]);
      if (error) {
        errors[fieldKey] = error;
      }
    });

    return errors;
  }, [itemRules, validateField]);

  // Validate entire store requisition form
  const validateForm = useCallback((data: StoreRequisitionFormData): Record<string, string> => {
    const errors: Record<string, string> = {};

    // Validate main form fields
    Object.keys(formRules).forEach(field => {
      const error = validateField(field, data[field as keyof StoreRequisitionFormData], formRules[field]);
      if (error) {
        errors[field] = error;
      }
    });

    // Validate store requisition items
    if (!data.items || data.items.length === 0) {
      errors.items = 'At least one item is required';
    } else {
      data.items.forEach((item, index) => {
        const itemErrors = validateItem(item, index);
        Object.assign(errors, itemErrors);
      });
    }

    return errors;
  }, [formRules, validateField, validateItem]);

  // Validate store requisition action
  const validateAction = useCallback((data: StoreRequisitionActionFormData): Record<string, string> => {
    const errors: Record<string, string> = {};

    Object.keys(actionRules).forEach(field => {
      const rule = actionRules[field];
      const error = validateField(field, data[field as keyof StoreRequisitionActionFormData], rule);

      if (error) {
        errors[field] = error;
      }
    });

    return errors;
  }, [actionRules, validateField]);

  return {
    // Validation functions
    validateField,
    validateItem,
    validateForm,
    validateAction,
    
    // Validation rules
    formRules,
    itemRules,
    actionRules,
    
    // Helper functions
    isValidEmail,
    isValidNumber,
    isValidPositiveNumber,
    formatValidationError,
  };
}
