import { useState, useCallback } from 'react';
import { router } from '@inertiajs/react';
import { StoreRequisition } from '@/types/store-requisitions';

interface ApprovalResponse {
    success: boolean;
    message: string;
    requisition?: StoreRequisition;
}

interface ErrorResponse {
    message?: string;
    errors?: Record<string, string[]>;
}

interface UseStoreRequisitionApprovalOptions {
    onSuccess?: (response: ApprovalResponse) => void;
    onError?: (error: ErrorResponse) => void;
}

export function useStoreRequisitionApproval(options: UseStoreRequisitionApprovalOptions = {}) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);



    const handleError = useCallback((error: ErrorResponse | Error | string | unknown) => {
        console.error('Store requisition approval error:', error);

        let errorMessage = 'An error occurred while processing the request';

        if (error && typeof error === 'object' && 'response' in error) {
            const responseError = error as { response?: { data?: { message?: string } } };
            if (responseError.response?.data?.message) {
                errorMessage = responseError.response.data.message;
            }
        } else if (error && typeof error === 'object' && 'message' in error) {
            errorMessage = (error as { message: string }).message;
        } else if (typeof error === 'string') {
            errorMessage = error;
        }

        setError(errorMessage);

        if (options.onError) {
            options.onError({ message: errorMessage });
        } else {
            // Default error handling
            window.showToast?.({
                title: 'Error',
                message: errorMessage,
                type: 'error'
            });
        }
    }, [options]);

    const approveRequisition = useCallback(async (
        requisitionId: number, 
        comments?: string
    ) => {
        setIsSubmitting(true);
        setError(null);
        
        try {
            router.post(
                `/store-requisitions/${requisitionId}/approve`,
                { comments },
                {
                    onSuccess: (page) => {
                        options.onSuccess?.(page as unknown as ApprovalResponse);
                        window.showToast?.({
                            title: 'Success',
                            message: 'Store requisition approved successfully',
                            type: 'success'
                        });
                    },
                    onError: (errors) => {
                        handleError(errors as ErrorResponse);
                    },
                    onFinish: () => {
                        setIsSubmitting(false);
                    },
                }
            );
        } catch (error) {
            handleError(error);
            setIsSubmitting(false);
        }
    }, [options, handleError]);

    const rejectRequisition = useCallback(async (
        requisitionId: number, 
        rejectionReason: string
    ) => {
        if (!rejectionReason.trim()) {
            setError('Rejection reason is required');
            return;
        }

        setIsSubmitting(true);
        setError(null);
        
        try {
            router.post(
                `/store-requisitions/${requisitionId}/reject`,
                { rejection_reason: rejectionReason },
                {
                    onSuccess: (page) => {
                        options.onSuccess?.(page as unknown as ApprovalResponse);
                        window.showToast?.({
                            title: 'Success',
                            message: 'Store requisition rejected successfully',
                            type: 'success'
                        });
                    },
                    onError: (errors) => {
                        handleError(errors as ErrorResponse);
                    },
                    onFinish: () => {
                        setIsSubmitting(false);
                    },
                }
            );
        } catch (error) {
            handleError(error);
            setIsSubmitting(false);
        }
    }, [options, handleError]);

    const clearError = useCallback(() => {
        setError(null);
    }, []);

    return {
        approveRequisition,
        rejectRequisition,
        isSubmitting,
        error,
        clearError,
    };
}

// Hook for batch approval operations
export function useBatchStoreRequisitionApproval(options: UseStoreRequisitionApprovalOptions = {}) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [progress, setProgress] = useState({ completed: 0, total: 0 });
    const [errors, setErrors] = useState<Array<{ id: number; error: string }>>([]);

    const approveBatch = useCallback(async (
        requisitions: Array<{ id: number; comments?: string }>
    ) => {
        setIsSubmitting(true);
        setProgress({ completed: 0, total: requisitions.length });
        setErrors([]);

        const results = [];
        
        for (let i = 0; i < requisitions.length; i++) {
            const requisition = requisitions[i];
            
            try {
                await new Promise<void>((resolve) => {
                    router.post(
                        `/store-requisitions/${requisition.id}/approve`,
                        { comments: requisition.comments },
                        {
                            onSuccess: () => {
                                results.push({ id: requisition.id, success: true });
                                resolve();
                            },
                            onError: (error) => {
                                const errorMessage = error?.message || 'Approval failed';
                                setErrors(prev => [...prev, { id: requisition.id, error: errorMessage }]);
                                results.push({ id: requisition.id, success: false, error: errorMessage });
                                resolve(); // Continue with next item
                            },
                        }
                    );
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                setErrors(prev => [...prev, { id: requisition.id, error: errorMessage }]);
                results.push({ id: requisition.id, success: false, error: errorMessage });
            }
            
            setProgress({ completed: i + 1, total: requisitions.length });
        }

        setIsSubmitting(false);

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;

        if (options.onSuccess && successCount > 0) {
            options.onSuccess({
                success: true,
                message: `${successCount} requisitions approved successfully${failureCount > 0 ? `, ${failureCount} failed` : ''}`
            });
        }

        return results;
    }, [options]);

    return {
        approveBatch,
        isSubmitting,
        progress,
        errors,
    };
}

// Hook for real-time approval status updates
export function useApprovalStatusUpdates() {
    const [statusUpdates, setStatusUpdates] = useState<Record<number, string>>({});

    const updateStatus = useCallback((requisitionId: number, status: string) => {
        setStatusUpdates(prev => ({
            ...prev,
            [requisitionId]: status
        }));
    }, []);

    const clearStatus = useCallback((requisitionId: number) => {
        setStatusUpdates(prev => {
            const updated = { ...prev };
            delete updated[requisitionId];
            return updated;
        });
    }, []);

    const getStatus = useCallback((requisitionId: number) => {
        return statusUpdates[requisitionId];
    }, [statusUpdates]);

    return {
        updateStatus,
        clearStatus,
        getStatus,
        statusUpdates,
    };
}
