import { useCallback, useMemo } from 'react';
import { useForm, router } from '@inertiajs/react';
import {
  StoreRequisitionFormData,
  StoreRequisitionItemFormData
} from '@/types/store-requisitions';

interface UseStoreRequisitionFormOptions {
  initialData?: Partial<StoreRequisitionFormData>;
  onSuccess?: (data: unknown) => void;
  onError?: (errors: Record<string, string>) => void;
}

interface UseStoreRequisitionFormReturn {
  // Form data and setters
  data: StoreRequisitionFormData;
  setData: (field: keyof StoreRequisitionFormData, value: string | number | StoreRequisitionItemFormData[] | undefined) => void;
  
  // Item management
  items: StoreRequisitionItemFormData[];
  addItem: () => void;
  removeItem: (index: number) => void;
  updateItem: (index: number, field: keyof StoreRequisitionItemFormData, value: string | number | null) => void;
  
  // Calculations
  totalQuantity: number;
  
  // Form state
  errors: Record<string, string>;
  processing: boolean;
  isDirty: boolean;
  isValid: boolean;
  
  // Actions
  submit: (url: string) => void;
  submitAsDraft: (url: string) => void;
  reset: () => void;
  clearErrors: () => void;
  
  // Validation
  validateItem: (item: StoreRequisitionItemFormData) => Record<string, string>;
  validateForm: () => Record<string, string>;
}

const DEFAULT_ITEM: StoreRequisitionItemFormData = {
  inventory_item_id: null,
  quantity_requested: 1,
};

const DEFAULT_FORM_DATA: StoreRequisitionFormData = {
  branch_id: undefined,
  department_id: undefined,
  purpose: '',
  items: [{ ...DEFAULT_ITEM }],
};

export function useStoreRequisitionForm(options: UseStoreRequisitionFormOptions = {}): UseStoreRequisitionFormReturn {
  const { initialData, onSuccess, onError } = options;

  // Initialize form with Inertia's useForm
  const formInitialData = {
    branch_id: initialData?.branch_id || '',
    department_id: initialData?.department_id || '',
    purpose: initialData?.purpose || '',
    items: initialData?.items || [{ inventory_item_id: null, quantity_requested: 1 }],
  };

  const { data: formData, setData: setInertiaData, post, put, processing, errors, reset: resetForm, clearErrors } = useForm(formInitialData as never);

  // Cast to our typed data
  const data = formData as unknown as StoreRequisitionFormData;

  // Custom setData wrapper for type safety
  const setData = useCallback((field: keyof StoreRequisitionFormData, value: string | number | StoreRequisitionItemFormData[] | undefined) => {
    (setInertiaData as unknown as (key: string, value: unknown) => void)(field, value);
  }, [setInertiaData]);

  // Item management functions
  const addItem = useCallback(() => {
    const newItems = [...data.items, { ...DEFAULT_ITEM }];
    setData('items', newItems);
  }, [data.items, setData]);

  const removeItem = useCallback((index: number) => {
    if (data.items.length <= 1) return; 
    
    const newItems = data.items.filter((_, i) => i !== index);
    setData('items', newItems);
  }, [data.items, setData]);

  const updateItem = useCallback((
    index: number,
    field: keyof StoreRequisitionItemFormData,
    value: string | number | null
  ) => {
    const newItems = [...data.items];
    newItems[index] = { ...newItems[index], [field]: value };
    setData('items', newItems);
  }, [data.items, setData]);

  // Calculations
  const totalQuantity = useMemo(() => {
    return data.items.reduce((total, item) => {
      return total + (Number(item.quantity_requested) || 0);
    }, 0);
  }, [data.items]);

  // Validation functions
  const validateItem = useCallback((item: StoreRequisitionItemFormData): Record<string, string> => {
    const itemErrors: Record<string, string> = {};

    if (!item.inventory_item_id) {
      itemErrors.inventory_item_id = 'Inventory item is required';
    }

    if (!item.quantity_requested || item.quantity_requested <= 0) {
      itemErrors.quantity_requested = 'Quantity must be greater than 0';
    }

    return itemErrors;
  }, []);

  const validateForm = useCallback((): Record<string, string> => {
    const formErrors: Record<string, string> = {};

    if (!data.branch_id || data.branch_id === '' || data.branch_id === 0) {
      formErrors.branch_id = 'Branch is required';
    }

    if (!data.department_id || data.department_id === '' || data.department_id === 0) {
      formErrors.department_id = 'Department is required';
    }

    if (!data.purpose.trim()) {
      formErrors.purpose = 'Purpose is required';
    }

    if (data.items.length === 0) {
      formErrors.items = 'At least one item is required';
    }

    // Validate each item
    data.items.forEach((item, index) => {
      const itemErrors = validateItem(item);
      Object.keys(itemErrors).forEach(field => {
        formErrors[`items.${index}.${field}`] = itemErrors[field];
      });
    });

    return formErrors;
  }, [data, validateItem]);

  // Form state computed properties
  const isDirty = useMemo(() => {
    const initial = { ...DEFAULT_FORM_DATA, ...initialData };
    return JSON.stringify(data) !== JSON.stringify(initial);
  }, [data, initialData]);

  const isValid = useMemo(() => {
    const formErrors = validateForm();
    return Object.keys(formErrors).length === 0;
  }, [validateForm]);

  // Submit function with enhanced security
  const submit = useCallback((url: string, options: { onSuccess?: (response: unknown) => void; onError?: (errors: Record<string, string>) => void; method?: 'post' | 'put' } = {}) => {
    const { onSuccess: optionsOnSuccess, onError: optionsOnError, method = 'post' } = options;
    // Prevent submission if already processing
    if (processing) {
      return;
    }

    const formErrors = validateForm();

    if (Object.keys(formErrors).length > 0) {
      // Handle client-side validation errors
      if (optionsOnError) {
        optionsOnError(formErrors);
      } else if (onError) {
        onError(formErrors);
      }
      return;
    }

    // Sanitize data before submission
    const sanitizedData = {
      ...data,
      branch_id: Number(data.branch_id) || undefined,
      department_id: Number(data.department_id) || undefined,
      purpose: data.purpose.trim(),
      items: data.items.map(item => ({
        ...item,
        quantity_requested: Math.max(1, Math.floor(Number(item.quantity_requested) || 1))
      })),
      save_as_draft: false, // Submit for approval
    };



    // Update form data with sanitized values before submission
    Object.keys(sanitizedData).forEach(key => {
      (setInertiaData as unknown as (key: string, value: unknown) => void)(key, sanitizedData[key as keyof StoreRequisitionFormData]);
    });

    const submitMethod = method === 'put' ? put : post;
    submitMethod(url, {
      onSuccess: (response) => {
        if (optionsOnSuccess) {
          optionsOnSuccess(response);
        } else if (onSuccess) {
          onSuccess(response);
        }
      },
      onError: (serverErrors) => {
        if (optionsOnError) {
          optionsOnError(serverErrors);
        } else if (onError) {
          onError(serverErrors);
        }
      },
    });
  }, [post, put, validateForm, onSuccess, onError, processing, data, setInertiaData]);

  // Submit as draft function (less strict validation)
  const submitAsDraft = useCallback((url: string, options: { onSuccess?: (response: unknown) => void; onError?: (errors: Record<string, string>) => void; method?: 'post' | 'put' } = {}) => {
    const { onSuccess: optionsOnSuccess, onError: optionsOnError, method = 'post' } = options;
    // Prevent submission if already processing
    if (processing) {
      return;
    }

    // Basic validation for drafts (only check required fields)
    const draftErrors: Record<string, string> = {};

    if (!data.branch_id || data.branch_id === '' || data.branch_id === 0) {
      draftErrors.branch_id = 'Branch is required';
    }

    if (!data.department_id || data.department_id === '' || data.department_id === 0) {
      draftErrors.department_id = 'Department is required';
    }

    if (!data.purpose.trim()) {
      draftErrors.purpose = 'Purpose is required';
    }

    if (Object.keys(draftErrors).length > 0) {
      if (optionsOnError) {
        optionsOnError(draftErrors);
      } else if (onError) {
        onError(draftErrors);
      }
      return;
    }

    // Sanitize data before submission
    const sanitizedData = {
      ...data,
      branch_id: Number(data.branch_id) || undefined,
      department_id: Number(data.department_id) || undefined,
      purpose: data.purpose.trim(),
      items: data.items.filter(item => item.inventory_item_id && item.quantity_requested > 0).map(item => ({
        ...item,
        quantity_requested: Math.max(1, Math.floor(Number(item.quantity_requested) || 1))
      })),
      save_as_draft: true, // Save as draft
    };



    // Ensure at least one item for drafts
    if (sanitizedData.items.length === 0) {
      if (optionsOnError) {
        optionsOnError({ items: 'At least one valid item is required' });
      } else if (onError) {
        onError({ items: 'At least one valid item is required' });
      }
      return;
    }

    // Use router.visit directly with the data
    router.visit(url, {
      method: method,
      data: sanitizedData,
      onSuccess: (response) => {
        if (optionsOnSuccess) {
          optionsOnSuccess(response);
        } else if (onSuccess) {
          onSuccess(response);
        }
      },
      onError: (serverErrors) => {
        if (optionsOnError) {
          optionsOnError(serverErrors);
        } else if (onError) {
          onError(serverErrors);
        }
      },
    });
  }, [onSuccess, onError, processing, data]);

  // Reset function
  const reset = useCallback(() => {
    resetForm();
  }, [resetForm]);

  return {
    // Form data and setters
    data,
    setData,
    
    // Item management
    items: data.items,
    addItem,
    removeItem,
    updateItem,
    
    // Calculations
    totalQuantity,
    
    // Form state
    errors: errors as Record<string, string>,
    processing,
    isDirty,
    isValid,
    
    // Actions
    submit,
    submitAsDraft,
    reset,
    clearErrors,
    
    // Validation
    validateItem,
    validateForm,
  };
}
