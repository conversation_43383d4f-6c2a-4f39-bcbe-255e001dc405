import { useState, useCallback } from 'react';
import { router } from '@inertiajs/react';
import type { FormDataConvertible } from '@inertiajs/core';
import {
  StoreRequisitionIssueItemData,
  StoreRequisitionLoadingStates
} from '@/types/store-requisitions';

// Type for Inertia.js response with flash messages
interface InertiaFlashResponse {
  props?: {
    flash?: {
      success?: string;
      message?: string;
      error?: string;
    };
  };
}

// Type for error responses
interface ErrorResponse {
  message?: string;
  response?: {
    data?: {
      message?: string;
    };
  };
}

interface UseStoreRequisitionActionsOptions {
  onSuccess?: (message: string) => void;
  onError?: (error: string) => void;
}

interface UseStoreRequisitionActionsReturn {
  // Loading states
  loadingStates: StoreRequisitionLoadingStates;

  // Actions
  submitStoreRequisition: (requisitionId: number) => Promise<void>;
  approveStoreRequisition: (requisitionId: number) => Promise<void>;
  rejectStoreRequisition: (requisitionId: number, reason: string) => Promise<void>;
  issueStoreRequisition: (requisitionId: number, items: StoreRequisitionIssueItemData[]) => Promise<void>;
  deleteStoreRequisition: (requisitionId: number) => Promise<void>;

  // Navigation helpers
  navigateToCreate: () => void;
  navigateToShow: (requisitionId: number) => void;
  navigateToList: () => void;
}

export function useStoreRequisitionActions(options: UseStoreRequisitionActionsOptions = {}): UseStoreRequisitionActionsReturn {
  const { onSuccess, onError } = options;
  
  const [loadingStates, setLoadingStates] = useState<StoreRequisitionLoadingStates>({
    submitting: false,
    loading: false,
    processing: false,
    issuing: false,
  });

  // Helper function to update loading state
  const setLoading = useCallback((key: keyof StoreRequisitionLoadingStates, value: boolean) => {
    setLoadingStates(prev => ({ ...prev, [key]: value }));
  }, []);

  // Helper function to handle API responses
  const handleResponse = useCallback((
    response: InertiaFlashResponse,
    successMessage: string
  ) => {
    if (response?.props?.flash?.success || response?.props?.flash?.message) {
      const message = response.props.flash.success || response.props.flash.message || successMessage;
      if (onSuccess) {
        onSuccess(message);
      }
      // Show toast notification if available
      if (window.showToast) {
        window.showToast({
          title: 'Success',
          message: message,
          type: 'success',
        });
      }
    } else if (response?.props?.flash?.error) {
      const error = response.props.flash.error;
      if (onError) {
        onError(error);
      }
      // Show toast notification if available
      if (window.showToast) {
        window.showToast({
          title: 'Error',
          message: error,
          type: 'error',
        });
      }
    } else {
      // Default success handling
      if (onSuccess) {
        onSuccess(successMessage);
      }
      if (window.showToast) {
        window.showToast({
          title: 'Success',
          message: successMessage,
          type: 'success',
        });
      }
    }
  }, [onSuccess, onError]);

  // Helper function to handle errors with enhanced security
  const handleError = useCallback((error: ErrorResponse | unknown, defaultMessage: string = 'An error occurred') => {
    // Sanitize error message to prevent XSS
    let errorMessage = defaultMessage;

    if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
      errorMessage = error.message.replace(/<[^>]*>/g, ''); // Strip HTML tags
    } else if (error && typeof error === 'object' && 'response' in error &&
               error.response && typeof error.response === 'object' &&
               'data' in error.response && error.response.data &&
               typeof error.response.data === 'object' && 'message' in error.response.data &&
               typeof error.response.data.message === 'string') {
      errorMessage = error.response.data.message.replace(/<[^>]*>/g, ''); // Strip HTML tags
    }

    // Log error for debugging 
    if (process.env.NODE_ENV === 'development') {
      console.error('Store Requisition Action Error:', error);
    }

    if (onError) {
      onError(errorMessage);
    }

    if (window.showToast) {
      window.showToast({
        title: 'Error',
        message: errorMessage,
        type: 'error',
      });
    }
  }, [onError]);

  // Store requisition actions
  const submitStoreRequisition = useCallback(async (requisitionId: number) => {
    setLoading('submitting', true);
    
    try {
      router.post(`/store-requisitions/${requisitionId}/submit`, 
        {},
        {
          onSuccess: (response) => {
            handleResponse(response as InertiaFlashResponse, 'Store requisition submitted successfully');
          },
          onError: (errors) => {
            handleError(errors as ErrorResponse, 'Failed to submit store requisition');
          },
          onFinish: () => {
            setLoading('submitting', false);
          },
        }
      );
    } catch (error) {
      handleError(error, 'Failed to submit store requisition');
      setLoading('submitting', false);
    }
  }, [handleResponse, handleError, setLoading]);

  const approveStoreRequisition = useCallback(async (requisitionId: number) => {
    setLoading('processing', true);
    
    try {
      router.post(`/store-requisitions/${requisitionId}/approve`, 
        {},
        {
          onSuccess: (response) => {
            handleResponse(response as InertiaFlashResponse, 'Store requisition approved successfully');
          },
          onError: (errors) => {
            handleError(errors as ErrorResponse, 'Failed to approve store requisition');
          },
          onFinish: () => {
            setLoading('processing', false);
          },
        }
      );
    } catch (error) {
      handleError(error, 'Failed to approve store requisition');
      setLoading('processing', false);
    }
  }, [handleResponse, handleError, setLoading]);

  const rejectStoreRequisition = useCallback(async (requisitionId: number, reason: string) => {
    setLoading('processing', true);
    
    try {
      router.post(`/store-requisitions/${requisitionId}/reject`, 
        { rejection_reason: reason },
        {
          onSuccess: (response) => {
            handleResponse(response as InertiaFlashResponse, 'Store requisition rejected successfully');
          },
          onError: (errors) => {
            handleError(errors as ErrorResponse, 'Failed to reject store requisition');
          },
          onFinish: () => {
            setLoading('processing', false);
          },
        }
      );
    } catch (error) {
      handleError(error, 'Failed to reject store requisition');
      setLoading('processing', false);
    }
  }, [handleResponse, handleError, setLoading]);

  const issueStoreRequisition = useCallback(async (requisitionId: number, items: StoreRequisitionIssueItemData[]) => {
    setLoading('issuing', true);

    try {
      router.post(`/store-requisitions/${requisitionId}/issue`,
        { items } as unknown as Record<string, FormDataConvertible>, // Type assertion for Inertia.js compatibility
        {
          onSuccess: (response) => {
            handleResponse(response as InertiaFlashResponse, 'Store requisition issued successfully');
          },
          onError: (errors) => {
            handleError(errors as ErrorResponse, 'Failed to issue store requisition');
          },
          onFinish: () => {
            setLoading('issuing', false);
          },
        }
      );
    } catch (error) {
      handleError(error, 'Failed to issue store requisition');
      setLoading('issuing', false);
    }
  }, [handleResponse, handleError, setLoading]);

  const deleteStoreRequisition = useCallback(async (requisitionId: number) => {
    setLoading('processing', true);
    
    try {
      router.delete(`/store-requisitions/${requisitionId}`, {
        onSuccess: (response) => {
          handleResponse(response as InertiaFlashResponse, 'Store requisition deleted successfully');
        },
        onError: (errors) => {
          handleError(errors as ErrorResponse, 'Failed to delete store requisition');
        },
        onFinish: () => {
          setLoading('processing', false);
        },
      });
    } catch (error) {
      handleError(error, 'Failed to delete store requisition');
      setLoading('processing', false);
    }
  }, [handleResponse, handleError, setLoading]);

  // Navigation helpers
  const navigateToCreate = useCallback(() => {
    router.visit('/store-requisitions/create');
  }, []);

  const navigateToShow = useCallback((requisitionId: number) => {
    router.visit(`/store-requisitions/${requisitionId}`);
  }, []);

  const navigateToList = useCallback(() => {
    router.visit('/store-requisitions');
  }, []);

  return {
    // Loading states
    loadingStates,
    
    // Actions
    submitStoreRequisition,
    approveStoreRequisition,
    rejectStoreRequisition,
    issueStoreRequisition,
    deleteStoreRequisition,
    
    // Navigation helpers
    navigateToCreate,
    navigateToShow,
    navigateToList,
  };
}
