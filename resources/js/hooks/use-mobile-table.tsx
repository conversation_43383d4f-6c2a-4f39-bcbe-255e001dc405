import { useState, useCallback } from 'react'
import { useIsMobile } from './use-mobile'

interface UseMobileTableOptions {
  defaultExpanded?: boolean
  maxVisibleItems?: number
}

export function useMobileTable(options: UseMobileTableOptions = {}) {
  const { defaultExpanded = false, maxVisibleItems = 3 } = options
  const isMobile = useIsMobile()
  const [expandedRows, setExpandedRows] = useState<Set<string | number>>(new Set())
  const [showAll, setShowAll] = useState(defaultExpanded)

  const toggleRow = useCallback((id: string | number) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }, [])

  const isRowExpanded = useCallback((id: string | number) => {
    return expandedRows.has(id)
  }, [expandedRows])

  const toggleShowAll = useCallback(() => {
    setShowAll(prev => !prev)
  }, [])

  const shouldShowItem = useCallback((index: number) => {
    if (!isMobile) return true
    return showAll || index < maxVisibleItems
  }, [isMobile, showAll, maxVisibleItems])

  const hasMoreItems = useCallback((totalItems: number) => {
    return isMobile && totalItems > maxVisibleItems
  }, [isMobile, maxVisibleItems])

  return {
    isMobile,
    expandedRows,
    showAll,
    toggleRow,
    isRowExpanded,
    toggleShowAll,
    shouldShowItem,
    hasMoreItems,
    maxVisibleItems
  }
}
