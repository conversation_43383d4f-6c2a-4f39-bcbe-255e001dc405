import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import { Toaster } from '@/components/ui/toaster';
import ToastContainer from '@/components/notifications/ToastContainer';
import enhancedPollingService from '@/services/enhancedPollingService';
import { type BreadcrumbItem } from '@/types';
import { type PropsWithChildren, useEffect } from 'react';

export default function AppSidebarLayout({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    useEffect(() => {
        // Start enhanced polling after a short delay to ensure ToastContainer is ready
        // Only start if not already active to prevent multiple instances
        const timer = setTimeout(() => {
            if (!enhancedPollingService.isActive) {
                console.log('Starting enhanced polling service...');
                enhancedPollingService.start();
            }
        }, 1000); // 1 second delay

        // Don't stop polling on unmount - let it persist across navigation
        return () => {
            clearTimeout(timer);
            // enhancedPollingService.stop(); // Commented out to persist across navigation
        };
    }, []);

    return (
        <AppShell variant="sidebar">
            <AppSidebar />
            <AppContent variant="sidebar">
                <AppSidebarHeader breadcrumbs={breadcrumbs} />
                {children}
            </AppContent>
            <Toaster />
            <ToastContainer />
        </AppShell>
    );
}
