import type React from 'react';
import type { Metadata } from 'next';
import AppLayoutTemplate from '@/layouts/app/app-sidebar-layout';
import { type BreadcrumbItem } from '@/types';

interface AppLayoutProps {
    breadcrumbs: BreadcrumbItem[];
    children: React.ReactNode;
    className?: string;
}

export const metadata: Metadata = {
    title: 'Auth Pages',
    description: 'Authentication pages with modern design',
};

export function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en">
            <body>{children}</body>
        </html>
    );
}

export default ({ children, breadcrumbs, ...props }: AppLayoutProps) => (
    <AppLayoutTemplate breadcrumbs={breadcrumbs} {...props}>
        {children}
    </AppLayoutTemplate>
);
