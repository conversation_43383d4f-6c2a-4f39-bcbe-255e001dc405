'use client';

import { motion } from 'framer-motion';
import type React from 'react';
import { useEffect, useState } from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';

interface AuthLayoutProps {
    children: React.ReactNode;
    title: string;
    description?: string;
    showBranding?: boolean;
    reverseLayout?: boolean;
    brandingComponent?: React.ReactNode;
    useAcrylicBackground?: boolean;
}

export default function AuthLayout({
    children,
    title,
    description,
    showBranding = true,
    reverseLayout = false,
    brandingComponent,
    useAcrylicBackground = false,
}: AuthLayoutProps) {
    const pathname = usePathname();
    const [mounted, setMounted] = useState(false);

    // Prevent hydration mismatch
    useEffect(() => {
        setMounted(true);
    }, []);

    if (!mounted) return null;

    // Default branding content with reverseLayout support
    const DefaultBranding = (
        <>
            {/* Background elements reversed based on layout */}
            <div
                className={cn(
                    'bg-primary absolute h-64 w-64 rounded-full opacity-20',
                    reverseLayout ? 'bottom-0 left-0 -mb-32 -ml-32' : 'top-0 right-0 -mt-32 -mr-32',
                )}
            ></div>

            <div
                className={cn(
                    'bg-primary absolute h-64 w-64 rounded-full opacity-20',
                    reverseLayout ? 'top-0 right-0 -mt-32 -mr-32' : 'bottom-0 left-0 -mb-32 -ml-32',
                )}
            ></div>

            {/* Animated circles with position reversing based on layout */}
            <motion.div
                animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.2, 0.3],
                }}
                transition={{
                    duration: 8,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatType: 'reverse',
                }}
                className={cn(
                    'bg-primary absolute h-32 w-32 rounded-full opacity-30 blur-xl',
                    reverseLayout ? 'right-1/4 bottom-1/4' : 'top-1/4 left-1/4',
                )}
            ></motion.div>

            <motion.div
                animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.2, 0.3, 0.2],
                }}
                transition={{
                    duration: 6,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatType: 'reverse',
                    delay: 1,
                }}
                className={cn(
                    'bg-primary absolute h-40 w-40 rounded-full opacity-20 blur-xl',
                    reverseLayout ? 'top-1/3 left-1/4' : 'right-1/4 bottom-1/3',
                )}
            ></motion.div>

            {brandingComponent || (
                <div className="text-primary z-10 max-w-md px-8 text-center">
                    <motion.div initial={{ scale: 0.9 }} animate={{ scale: 1 }} transition={{ duration: 0.5 }} className="mb-6 flex justify-center">
                        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                            <motion.path
                                initial={{ pathLength: 0 }}
                                animate={{ pathLength: 1 }}
                                transition={{ duration: 1.5, ease: 'easeInOut' }}
                                d="M40 8C22.36 8 8 22.36 8 40C8 57.64 22.36 72 40 72C57.64 72 72 57.64 72 40C72 22.36 57.64 8 40 8ZM40 16C53.2 16 64 26.8 64 40C64 53.2 53.2 64 40 64C26.8 64 16 53.2 16 40C16 26.8 26.8 16 40 16Z"
                                fill="white"
                                stroke="white"
                                strokeWidth="2"
                            />
                            <motion.path
                                initial={{ pathLength: 0 }}
                                animate={{ pathLength: 1 }}
                                transition={{ duration: 1.5, ease: 'easeInOut', delay: 0.5 }}
                                d="M40 24C31.2 24 24 31.2 24 40C24 48.8 31.2 56 40 56C48.8 56 56 48.8 56 40C56 31.2 48.8 24 40 24ZM40 48C35.6 48 32 44.4 32 40C32 35.6 35.6 32 40 32C44.4 32 48 35.6 48 40C48 44.4 44.4 48 40 48Z"
                                fill="white"
                                stroke="white"
                                strokeWidth="2"
                            />
                        </svg>
                    </motion.div>
                    <motion.h1
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6, duration: 0.5 }}
                        className="mb-6 text-5xl font-bold"
                    >
                        Sippar
                    </motion.h1>
                    <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8, duration: 0.5 }}
                        className="text-xl"
                    >
                        Simplify your organization's financial management with our powerful platform.
                    </motion.p>
                </div>
            )}
        </>
    );

    // Content for the form side
    const FormContent = (
        <Card
            className={cn(
                'flex w-full max-w-md flex-col items-center justify-center border-none p-8 shadow-lg',
                useAcrylicBackground ? 'acrylic-card bg-foreground/10 backdrop-blur-md backdrop-saturate-150' : 'bg-background',
            )}
        >
            <CardContent className="pt-6">
                <motion.div
                    key={pathname}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                >
                    <div className="space-y-2">
                        <motion.h2
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1, duration: 0.3 }}
                            className="text-primary text-3xl font-bold"
                        >
                            {title}
                        </motion.h2>
                        {description && (
                            <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2, duration: 0.3 }}
                                className="text-muted-foreground text-sm"
                            >
                                {description}
                            </motion.p>
                        )}
                    </div>
                    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3, duration: 0.3 }}>
                        {children}
                    </motion.div>
                </motion.div>
            </CardContent>
        </Card>
    );

    // Determine which side gets branding and which gets form
    const BrandingSide = DefaultBranding;
    const FormSide = FormContent;

    return (
        <div className="flex min-h-screen lg:flex-row">
            {/* Mobile: Show only form */}
            <div className="bg-background flex min-h-screen w-full flex-col justify-start p-4 lg:hidden">
                <div className="flex min-h-full w-full flex-col justify-center py-8">{FormSide}</div>
            </div>

            {/* Desktop: Left column */}
            <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
                className={cn(
                    'relative hidden flex-col items-center justify-center overflow-hidden p-8 lg:flex lg:w-1/2',
                    reverseLayout
                        ? useAcrylicBackground
                            ? 'bg-background order-1'
                            : 'bg-background order-1'
                        : 'from-primary/60 to-primary/80 order-1 bg-gradient-to-br',
                    !showBranding && reverseLayout ? 'lg:flex' : !showBranding ? 'lg:hidden' : '',
                )}
            >
                {reverseLayout ? FormSide : BrandingSide}
            </motion.div>

            {/* Desktop: Right column */}
            <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, ease: 'easeOut' }}
                className={cn(
                    'relative hidden flex-col items-center justify-center overflow-hidden p-8 lg:flex lg:w-1/2',
                    reverseLayout
                        ? 'from-primary/60 to-primary/80 order-2 bg-gradient-to-br'
                        : useAcrylicBackground
                          ? 'bg-background order-2'
                          : 'bg-background order-2',
                )}
            >
                {reverseLayout ? BrandingSide : FormSide}
            </motion.div>
        </div>
    );
}
