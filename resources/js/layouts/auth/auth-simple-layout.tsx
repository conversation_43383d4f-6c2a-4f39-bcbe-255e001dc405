import type React from 'react';

interface AuthSimpleLayoutProps {
    children: React.ReactNode;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: AuthSimpleLayoutProps) {
    return (
        <div className="flex min-h-screen flex-col lg:flex-row">
            {/* Left Column - Form content */}
            <div className="flex w-full items-center justify-center p-8 lg:w-1/2">
                <div className="w-full max-w-md">
                    <div className="mb-8">
                        <h2 className="text-3xl font-bold text-gray-800">{title}</h2>
                        {description && <p className="mt-2 text-gray-600">{description}</p>}
                    </div>
                    {children}
                </div>
            </div>

            {/* Right column - Green background with branding */}
            <div className="relative flex flex-col items-center justify-center overflow-hidden bg-emerald-500 p-8 lg:w-1/2">
                <div className="absolute top-0 right-0 -mt-32 -mr-32 h-64 w-64 rounded-full bg-emerald-400 opacity-20"></div>
                <div className="absolute bottom-0 right-0 -mb-32 -ml-32 h-64 w-64 rounded-full bg-emerald-400 opacity-20"></div>

                <div className="z-10 max-w-md px-8 text-center text-white">
                    <h1 className="mb-6 text-5xl font-bold">Welcome Back</h1>
                    <p className="text-xl">Log in to get the full experience.</p>
                </div>
            </div>
        </div>
    );
}
