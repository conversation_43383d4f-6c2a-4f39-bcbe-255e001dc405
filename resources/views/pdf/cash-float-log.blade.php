<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Petty Cash Log - {{ $cashFloat->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            margin: 0;
            text-transform: uppercase;
        }
        
        .info-section {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
        }
        
        .info-left, .info-right {
            flex: 1;
        }
        
        .info-right {
            text-align: right;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8px;
        }
        
        th, td {
            border: 1px solid #333;
            padding: 4px 2px;
            text-align: left;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .cash-in {
            background-color: #f0fff0;
        }
        
        .cash-out {
            background-color: #fff0f0;
        }
        
        .balance {
            background-color: #f0f8ff;
            font-weight: bold;
        }
        
        .total-row {
            background-color: #e0e0e0;
            font-weight: bold;
        }

        /* Reconciliation Section Styles */
        .reconciliation-section {
            border-top: 2px solid #000;
        }

        .reconciliation-title {
            background-color: #e2e8f0;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
            writing-mode: vertical-lr;
            text-orientation: mixed;
            width: 40px;
            border-right: 2px solid #000;
        }

        .reconciliation-header {
            background-color: #f1f5f9;
            font-weight: bold;
        }

        .reconciliation-row {
            background-color: #ffffff;
        }

        .reconciliation-row-alt {
            background-color: #f7fafc;
        }

        .reconciliation-final {
            background-color: #ffffff;
            font-weight: bold;
        }

        .checkmark {
            color: #38a169;
            font-weight: bold;
            font-size: 12px;
        }

        .footer {
            margin-top: 20px;
            font-size: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Petty Cash Log</h1>
    </div>
    
    <div class="info-section">
        <div class="info-left">
            <strong>Person Responsible:</strong> {{ $financeManager ? trim($financeManager->first_name . ' ' . $financeManager->last_name) : 'N/A' }}<br>
            <strong>Cash Float:</strong> {{ $cashFloat->name }}<br>
            @if($cashFloat->organization)
                <strong>Organization:</strong> {{ $cashFloat->organization->name }}<br>
            @endif
            @if($cashFloat->department)
                <strong>Department:</strong> {{ $cashFloat->department->name }}<br>
            @endif
            @if($cashFloat->branch)
                <strong>Branch:</strong> {{ $cashFloat->branch->name }}<br>
            @endif
        </div>
        <div class="info-right">
            <strong>Reporting Period:</strong> 
            @if($filters['start_date'] && $filters['end_date'])
                {{ \Carbon\Carbon::parse($filters['start_date'])->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($filters['end_date'])->format('d/m/Y') }}
            @else
                All Transactions
            @endif
            <br>
            <strong>Generated:</strong> {{ now()->format('d/m/Y H:i') }}<br>
            <strong>Initial Amount:</strong> {{ number_format($cashFloat->initial_amount, 2) }}<br>
            <strong>Current Balance:</strong> {{ number_format($cashFloat->current_balance, 2) }}
        </div>
    </div>
    
    @if($cashFloat->transactions->count() > 0)
        <table>
            <thead>
                <tr>
                    <th style="width: 8%;">Date</th>
                    <th style="width: 20%;">Particulars</th>
                    <th style="width: 12%;">Paid To/Received From</th>
                    @foreach($chartOfAccounts as $account)
                        <th style="width: 6%;">{{ $account->name }}</th>
                    @endforeach
                    <th style="width: 8%;">Transaction Cost</th>
                    <th style="width: 8%;" class="cash-out">Cash Out</th>
                    <th style="width: 8%;" class="cash-in">Cash In</th>
                    <th style="width: 8%;" class="balance">Balance</th>
                </tr>
            </thead>
            <tbody>
                @foreach($cashFloat->transactions as $transaction)
                    @php
                        // Calculate amounts by chart of account
                        $accountAmounts = [];
                        foreach($chartOfAccounts as $account) {
                            $accountAmounts[$account->id] = 0;
                        }
                        
                        foreach($transaction->items as $item) {
                            if($item->chartOfAccount && isset($accountAmounts[$item->chartOfAccount->id])) {
                                $accountAmounts[$item->chartOfAccount->id] += $item->total_price;
                            }
                        }
                        
                        // Get particulars
                        $particulars = $transaction->is_initial_issuance 
                            ? 'Initial Float Issuance'
                            : ($transaction->requisition->purpose ?? 
                               collect($transaction->items)->pluck('description')->join(', ') ?? 
                               ucfirst(str_replace('_', ' ', $transaction->transaction_type)));
                        
                        // Get paid to/received from
                        $isCashIn = $transaction->transaction_type === 'float_issuance';
                        $paidToFrom = $transaction->requisition->requester->name ?? 
                                     $transaction->creator->name ?? 
                                     ($isCashIn ? 'Company' : '-');
                    @endphp
                    <tr>
                        <td class="text-center">{{ \Carbon\Carbon::parse($transaction->created_at)->format('d/m/Y') }}</td>
                        <td>{{ Str::limit($particulars, 40) }}</td>
                        <td>{{ $paidToFrom }}</td>
                        @foreach($chartOfAccounts as $account)
                            <td class="text-right">
                                {{ $accountAmounts[$account->id] > 0 ? number_format($accountAmounts[$account->id], 2) : '-' }}
                            </td>
                        @endforeach
                        <td class="text-right">
                            {{ $transaction->transaction_cost ? number_format($transaction->transaction_cost, 2) : '-' }}
                        </td>
                        <td class="text-right cash-out">
                            {{ $transaction->cash_out > 0 ? number_format($transaction->cash_out, 2) : '-' }}
                        </td>
                        <td class="text-right cash-in">
                            {{ $transaction->cash_in > 0 ? number_format($transaction->cash_in, 2) : 
                               ($transaction->is_initial_issuance ? '(' . number_format($transaction->total_amount, 2) . ')' : '-') }}
                        </td>
                        <td class="text-right balance">
                            {{ number_format($transaction->running_balance, 2) }}
                        </td>
                    </tr>
                @endforeach
                
                <!-- Reconciliation Section -->
                <!-- Row 1: Total (Cash Out)/In - Header with totals -->
                <tr class="reconciliation-section">
                    <td rowspan="4" class="reconciliation-title">
                        Petty Cash Reconciliation
                    </td>
                    <td class="reconciliation-header">Total (Cash Out)/In</td>
                    <td class="text-center reconciliation-header">-</td>
                    @foreach($chartOfAccounts as $account)
                        @php
                            $totalAccountAmount = 0;
                            foreach($cashFloat->transactions as $transaction) {
                                if($transaction->items && $transaction->items->count() > 0) {
                                    foreach($transaction->items as $item) {
                                        if($item->chartOfAccount && $item->chartOfAccount->id == $account->id) {
                                            $totalAccountAmount += $item->total_price;
                                        }
                                    }
                                }
                            }
                        @endphp
                        <td class="text-right reconciliation-header">
                            {{ $totalAccountAmount > 0 ? number_format($totalAccountAmount, 2) : '0.00' }}
                        </td>
                    @endforeach
                    <td class="text-right reconciliation-header">
                        {{ number_format($cashFloat->transactions->sum('transaction_cost'), 2) }}
                    </td>
                    <td class="text-right reconciliation-header cash-out">
                        {{ number_format($cashFloat->transactions->sum('cash_out'), 2) }}
                    </td>
                    <td class="text-right reconciliation-header cash-in">
                        {{ number_format($cashFloat->transactions->sum('cash_in'), 2) }}
                    </td>
                    <td class="text-right reconciliation-header balance">
                        {{ number_format($cashFloat->current_balance, 2) }}
                    </td>
                </tr>

                <!-- Row 2: Cash at Hand - with checkmarks and negative balance -->
                <tr class="reconciliation-row">
                    <td class="reconciliation-row">Cash at Hand</td>
                    <td class="text-center">-</td>
                    @foreach($chartOfAccounts as $account)
                        <td class="text-center">
                            <span class="checkmark">✓</span>
                        </td>
                    @endforeach
                    <td class="text-center">-</td>
                    <td class="text-center cash-out">-</td>
                    <td class="text-center cash-in">-</td>
                    <td class="text-right balance">
                        {{ number_format($cashFloat->current_balance, 2) }}
                    </td>
                </tr>

                <!-- Row 3: Petty Cash reimbursement - empty row -->
                <tr class="reconciliation-row-alt">
                    <td class="reconciliation-row-alt">Petty Cash reimbursement</td>
                    <td class="text-center">-</td>
                    @foreach($chartOfAccounts as $account)
                        <td class="text-center">-</td>
                    @endforeach
                    <td class="text-center">-</td>
                    <td class="text-center cash-out">-</td>
                    <td class="text-center cash-in">-</td>
                    <td class="text-right balance">
                        0.00
                    </td>
                </tr>

                <!-- Row 4: Balance Carried forward - final balance -->
                <tr class="reconciliation-final">
                    <td class="reconciliation-final">Balance Carried forward</td>
                    <td class="text-center">-</td>
                    @foreach($chartOfAccounts as $account)
                        <td class="text-center">-</td>
                    @endforeach
                    <td class="text-center">-</td>
                    <td class="text-center cash-out">-</td>
                    <td class="text-center cash-in">-</td>
                    <td class="text-right balance">
                        {{ number_format($cashFloat->current_balance, 2) }}
                    </td>
                </tr>
            </tbody>
        </table>
    @else
        <div style="text-align: center; padding: 40px;">
            <p>No transactions found for this cash float in the selected period.</p>
        </div>
    @endif
    
    <div class="footer">
        <p><strong>Notes:</strong></p>
        <ul>
            <li>This report shows all transactions for the {{ $cashFloat->name }} petty cash float.</li>
            <li>Cash In represents money added to the float (reimbursements, additional issuances).</li>
            <li>Cash Out represents money spent from the float (expenses, disbursements, returns).</li>
            <li>Balance column shows the running balance after each transaction.</li>
            <li><strong>Reconciliation Section:</strong></li>
            <ul>
                <li><strong>Total (Cash Out)/In:</strong> Summary of all transactions by category</li>
                <li><strong>Cash at Hand:</strong> Physical verification of cash (✓ indicates verified)</li>
                <li><strong>Petty Cash reimbursement:</strong> Space for recording reimbursements</li>
                <li><strong>Balance Carried forward:</strong> Final reconciled balance</li>
            </ul>
        </ul>
    </div>
</body>
</html>
