@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Low Stock Alert

Dear {{ $notifiable->full_name }},

{{ $itemCount }} inventory items are running low and need replenishment.

**Alert Summary:**
- Total Items: {{ $itemCount }}
- Alert Time: {{ now()->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') }}

**Items Requiring Attention:**
@foreach($inventoryItems->take(10) as $item)
- **{{ $item->name }}** (SKU: {{ $item->sku }})
  - Current Stock: {{ $item->quantity_on_hand }} {{ $item->unit_of_measure }}
  - Reorder Level: {{ $item->reorder_level }} {{ $item->unit_of_measure }}
  @if($item->branch)
  - Branch: {{ $item->branch->name }}
  @endif

@endforeach

@if($itemCount > 10)
**Note:** Showing first 10 items. View all low stock items using the button below.
@endif

**Action Required:**
Please review these items and initiate replenishment orders to maintain adequate stock levels.

@component('mail::button', ['url' => $url])
View All Low Stock Items
@endcomponent

If you have any questions about inventory management, please contact SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent
