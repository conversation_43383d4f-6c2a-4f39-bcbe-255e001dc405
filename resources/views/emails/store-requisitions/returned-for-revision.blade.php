@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Store Requisition Returned for Revision

Dear {{ $notifiable->full_name }},

Your store requisition has been returned for revision.

**Requisition Details:**
- Reference: #{{ $storeRequisition->id }}
- Purpose: {{ $storeRequisition->purpose }}
- Department: {{ $storeRequisition->department->name ?? 'N/A' }}
- Total Items: {{ $totalItems }}
- Returned at: {{ now()->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') }}

@if($comments)
**Revision Comments:**
{{ $comments }}
@endif

**Next Steps:**
Please review the comments and make the necessary revisions to your store requisition. Once updated, you can resubmit it for approval.

@component('mail::button', ['url' => $url])
Edit Store Requisition
@endcomponent

**Note:** Your requisition is now in draft status and can be edited until you resubmit it.

If you have any questions about the required revisions, please contact your approver or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent
