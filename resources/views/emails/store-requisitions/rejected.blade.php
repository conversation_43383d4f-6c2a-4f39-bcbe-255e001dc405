@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Store Requisition Rejected

Dear {{ $notifiable->full_name }},

Unfortunately, your store requisition has been rejected.

**Requisition Details:**
- Reference: #{{ $storeRequisition->id }}
- Purpose: {{ $storeRequisition->purpose }}
- Department: {{ $storeRequisition->department->name ?? 'N/A' }}
- Total Items: {{ $totalItems }}
- Rejected at: {{ now()->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') }}

@if($rejectionReason)
**Rejection Reason:**
{{ $rejectionReason }}
@endif

**Next Steps:**
You may review the rejection reason and create a new store requisition if needed. Please ensure all requirements are met before resubmitting.

@component('mail::button', ['url' => $url])
View Store Requisition
@endcomponent

If you have any questions about this rejection, please contact your approver or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent
