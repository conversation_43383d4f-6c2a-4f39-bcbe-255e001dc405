@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Store Requisition Approved

Dear {{ $notifiable->full_name }},

Great news! Your store requisition has been approved and is ready for issuing.

**Requisition Details:**
- Reference: #{{ $storeRequisition->id }}
- Purpose: {{ $storeRequisition->purpose }}
- Department: {{ $storeRequisition->department->name ?? 'N/A' }}
- Total Items: {{ $totalItems }}
- Approved at: {{ $storeRequisition->approved_at ? $storeRequisition->approved_at->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') : 'N/A' }}

**Next Steps:**
Your approved requisition will be processed by the store keeper for item issuing. You will receive another notification once the items have been issued.

@component('mail::button', ['url' => $url])
View Store Requisition
@endcomponent

**Note:** Please ensure you are available to collect the items once they are ready for issuing.

If you have any questions, please contact SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent
