@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Store Requisition {{ $isPartialIssue ? 'Partially ' : '' }}Issued

Dear {{ $notifiable->full_name }},

@if($isPartialIssue)
Your store requisition has been partially issued. Some items have been distributed and are ready for collection.
@else
Great news! Your store requisition has been fully issued and all items are ready for collection.
@endif

**Requisition Details:**
- Reference: #{{ $storeRequisition->id }}
- Purpose: {{ $storeRequisition->purpose }}
- Department: {{ $storeRequisition->department->name ?? 'N/A' }}
- Total Items: {{ $totalItems }}
- {{ $isPartialIssue ? 'Partially ' : '' }}Issued at: {{ now()->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') }}

**Next Steps:**
@if($isPartialIssue)
Please collect the available items from the store. The remaining items will be issued when they become available, and you will receive another notification.
@else
Please collect your items from the store at your earliest convenience.
@endif

@component('mail::button', ['url' => $url])
View Store Requisition Details
@endcomponent

**Important:** Please bring this notification or your requisition reference number when collecting the items.

If you have any questions about item collection, please contact the store keeper or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent
