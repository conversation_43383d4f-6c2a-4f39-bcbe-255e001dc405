@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Action Required: Store Requisition Needs Your Approval

Dear {{ $notifiable->full_name }},

A store requisition requires your approval.

**Requisition Details:**
- Reference: #{{ $storeRequisition->id }}
- Purpose: {{ $storeRequisition->purpose }}
- Department: {{ $storeRequisition->department->name ?? 'N/A' }}
- Total Items: {{ $totalItems }}
- Submitted by: {{ $storeRequisition->requester->full_name }}
- Submitted at: {{ $storeRequisition->requested_at ? $storeRequisition->requested_at->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') : 'N/A' }}

Please review the store requisition and take appropriate action (approve, reject, or return for revision) based on your assessment.

@component('mail::button', ['url' => $url])
Review Store Requisition
@endcomponent

**Note:** This requisition will remain pending until approved or rejected.

If you have any questions about this requisition, please contact the requester directly or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent
