@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Transaction Ready for Disbursement

Dear {{ $notifiable->full_name }},

A new transaction is ready for disbursement processing:

**Transaction Details:**
- Requisition Number: {{ $transaction->requisition->requisition_number }}
- Purpose: {{ $transaction->requisition->purpose }}
- Amount: {{ number_format($transaction->total_amount, 2) }}
- Requester: {{ $transaction->requisition->requester->full_name }}

Please review and process the disbursement at your earliest convenience.

@component('mail::button', ['url' => route('transactions.show', $transaction->id)])
Process Disbursement
@endcomponent

If you have any questions or need assistance, please contact SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent 