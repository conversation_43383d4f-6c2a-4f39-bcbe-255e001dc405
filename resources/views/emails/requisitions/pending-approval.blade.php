@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Action Required: Requisition Needs Your Approval

Dear {{ $notifiable->full_name }},

A requisition requires your approval.

**Requisition Details:**
- Reference: #{{ $requisition->requisition_number }}
- Purpose: {{ $requisition->purpose }}
- Department: {{ $requisition->department->name }}
- Amount: KSH {{ number_format($totalAmount, 2) }}
- Submitted by: {{ $requisition->requester->full_name }}

**Approval Details:**
- {{ $workflowStep->description ?? 'Approval Required' }}
@if($workflowStep->role_id)
- Required Role: {{ $workflowStep->role->name }}
@endif

Please review the requisition and take appropriate action (approve or reject) based on your assessment.

@component('mail::button', ['url' => $url])
Review Requisition
@endcomponent

**Note:** This requisition will remain pending until all required approvals are obtained.

If you have any questions about this requisition, please contact the requester directly or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent 