@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Action Required: Provide Payment Details

Dear {{ $notifiable->full_name }},

Your requisition has been fully approved and is now ready for disbursement.

**Requisition Details:**
- Reference: #{{ $requisition->requisition_number }}
- Purpose: {{ $requisition->purpose }}
- Amount: KSH {{ number_format($totalAmount, 2) }}

**Next Step: Provide Your Payment Details**
To proceed with the disbursement, we need you to provide your payment details.

**Required Information:**
- MPESA Account Name
- MPESA Account Number
- Any additional payment details

Please click the button below to provide your payment details:

@component('mail::button', ['url' => $url])
Provide Payment Details
@endcomponent

**Important Notes:**
1. The disbursement process will begin once you submit your payment details
2. Please ensure the account details you provide are accurate to avoid any delays
3. You will receive another notification once the payment has been processed

If you have any questions about the payment process, please contact the finance department or SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent 