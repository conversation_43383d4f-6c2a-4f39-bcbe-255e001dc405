@component('mail::message')

<img src="{{ asset('public/logo.png') }}" alt="{{ config('app.name') }} Logo" style="height: 60px; margin-bottom: 20px;">

# Requisition Rejected

Dear {{ $notifiable->full_name }},

Your requisition **#{{ $requisition->requisition_number }}** has been rejected.

## Requisition Details
- **Amount:** KSH {{ number_format($requisition->total_amount, 2) }}
- **Purpose:** {{ $requisition->purpose }}
- **Department:** {{ $requisition->department->name ?? 'N/A' }}
- **Rejected at:** {{ now()->setTimezone('Africa/Nairobi')->format('F j, Y g:i A') }}
- **Rejected by:** {{ $rejector->first_name }} {{ $rejector->last_name }}

@if($comments)
## Rejection Comments
{{ $comments }}
@endif

## Next Steps
Please review the rejection comments and make necessary adjustments before resubmitting.

@component('mail::button', ['url' => $url])
View Requisition
@endcomponent

If you have any questions or need assistance, please contact SIPPAR <NAME_EMAIL>.

Thanks,<br>
{{ config('app.name') }}
@endcomponent 