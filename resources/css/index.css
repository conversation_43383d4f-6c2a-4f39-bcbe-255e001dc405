/* This file is kept minimal since app.css handles most styling */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Only include if not using Laravel/Inertia setup */
/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lexend:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Instrument+Sans:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap'); */

/* Fallback CSS variables for React-only setup */
/* 
@layer base {
  :root {
    --background: 220 27% 8%;
    --foreground: 210 40% 98%;
    --card: 220 27% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 220 27% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 160 84% 39%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 160 84% 55%;
    --primary-dark: 160 84% 25%;
    --secondary: 220 27% 18%;
    --secondary-foreground: 210 40% 98%;
    --muted: 220 27% 16%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 160 84% 39%;
    --accent-foreground: 210 40% 98%;
    --success: 142 76% 36%;
    --success-light: 142 76% 50%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-light: 38 92% 65%;
    --warning-foreground: 220 27% 8%;
    --info: 199 89% 48%;
    --info-light: 199 89% 65%;
    --info-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-light: 0 84.2% 75%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 27% 20%;
    --input: 220 27% 18%;
    --ring: 160 84% 39%;
    --radius: 0.75rem;
    --chart-1: 160 84% 39%;
    --chart-2: 199 89% 48%;
    --chart-3: 271 81% 56%;
    --chart-4: 38 92% 50%;
    --chart-5: 0 84.2% 60.2%;
    --gradient-from: 160 84% 39%;
    --gradient-to: 199 89% 48%;
    --sidebar-background: 220 27% 10%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 160 84% 39%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 220 27% 16%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 220 27% 20%;
    --sidebar-ring: 160 84% 39%;
    --surface: 220 27% 14%;
    --surface-foreground: 210 40% 98%;
    --outline: 220 27% 25%;
  }
}
*/