@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius: 0.75rem;
    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    /* Light theme by default - emerald focused */
    --background: #ffffff;
    --foreground: #090d16;

    --card: #ffffff;
    --card-foreground: #0f172a;

    --popover: #ffffff;
    --popover-foreground: #0f172a;

    /* Primary emerald colors */
    --primary: #059669;
    --primary-foreground: #ffffff;
    --primary-light: #34d399;
    --primary-dark: #065f46;

    --secondary: #f1f5f9;
    --secondary-foreground: #0f172a;

    --muted: #f8fafc;
    --muted-foreground: #64748b;

    --accent: #059669;
    --accent-foreground: #ffffff;

    /* Success colors */
    --success: #059669;
    --success-light: #10b981;
    --success-foreground: #ffffff;

    /* Warning colors */
    --warning: #d97706;
    --warning-light: #f59e0b;
    --warning-foreground: #ffffff;

    /* Info colors */
    --info: #0284c7;
    --info-light: #0ea5e9;
    --info-foreground: #ffffff;

    /* Destructive colors */
    --destructive: #dc2626;
    --destructive-light: #ef4444;
    --destructive-foreground: #ffffff;

    /* Status badge colors for light theme */
    --status-draft: #f8fafc;
    --status-draft-foreground: #64748b;
    --status-pending: #fef3c7;
    --status-pending-foreground: #92400e;
    --status-approved: #d1fae5;
    --status-approved-foreground: #065f46;
    --status-rejected: #fee2e2;
    --status-rejected-foreground: #991b1b;
    --status-issued: #dbeafe;
    --status-issued-foreground: #1e40af;
    --status-partial: #fed7aa;
    --status-partial-foreground: #9a3412;

    --border: #e2e8f0;
    --input: #ffffff;
    --ring: #059669;

    /* Chart colors */
    --chart-1: #059669;
    --chart-2: #0284c7;
    --chart-3: #7c3aed;
    --chart-4: #d97706;
    --chart-5: #dc2626;

    /* Gradient colors */
    --gradient-from: #059669;
    --gradient-to: #0284c7;

    /* Sidebar colors */
    --sidebar-background: #f8fafc;
    --sidebar-foreground: #0f172a;
    --sidebar-primary: #059669;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #f1f5f9;
    --sidebar-accent-foreground: #0f172a;
    --sidebar-border: #e2e8f0;
    --sidebar-ring: #059669;

    /* Additional semantic colors */
    --surface: #f9fafb;
    --surface-foreground: #0f172a;
    --outline: #cbd5e1;

    /* Map to Tailwind color system */
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar-background);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/* Dark theme variables */
@media (prefers-color-scheme: dark) {
    @theme {
        --background: #0a0e1a;
        --foreground: #f8fafc;

        --card: #0f172a;
        --card-foreground: #f8fafc;

        --popover: #1e293b;
        --popover-foreground: #f8fafc;

        --primary: #10b981;
        --primary-foreground: #ffffff;

        --secondary: #1e293b;
        --secondary-foreground: #f8fafc;

        --muted: #1e293b;
        --muted-foreground: #94a3b8;

        --accent: #10b981;
        --accent-foreground: #ffffff;

        --destructive: #ef4444;
        --destructive-foreground: #ffffff;

        /* Status badge colors for dark theme */
        --status-draft: #1e293b;
        --status-draft-foreground: #94a3b8;
        --status-pending: rgba(251, 191, 36, 0.2);
        --status-pending-foreground: #fbbf24;
        --status-approved: rgba(34, 197, 94, 0.2);
        --status-approved-foreground: #22c55e;
        --status-rejected: rgba(239, 68, 68, 0.2);
        --status-rejected-foreground: #ef4444;
        --status-issued: rgba(59, 130, 246, 0.2);
        --status-issued-foreground: #3b82f6;
        --status-partial: rgba(249, 115, 22, 0.2);
        --status-partial-foreground: #f97316;

        --border: #334155;
        --input: #1e293b;
        --ring: #10b981;

        --sidebar-background: #0f172a;
        --sidebar-foreground: #f8fafc;
        --sidebar-primary: #10b981;
        --sidebar-primary-foreground: #ffffff;
        --sidebar-accent: #1e293b;
        --sidebar-accent-foreground: #f8fafc;
        --sidebar-border: #334155;
        --sidebar-ring: #10b981;
    }
}

/* Light theme class-based - explicit override */
.light {
    --background: #ffffff;
    --foreground: #090d16;

    --card: #ffffff;
    --card-foreground: #0f172a;

    --popover: #ffffff;
    --popover-foreground: #0f172a;

    --primary: #059669;
    --primary-foreground: #ffffff;

    --secondary: #f1f5f9;
    --secondary-foreground: #0f172a;

    --muted: #f8fafc;
    --muted-foreground: #64748b;

    --accent: #059669;
    --accent-foreground: #ffffff;

    --destructive: #dc2626;
    --destructive-foreground: #ffffff;

    --border: #e2e8f0;
    --input: #ffffff;
    --ring: #059669;

    --sidebar-background: #f8fafc;
    --sidebar-foreground: #0f172a;
    --sidebar-primary: #059669;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #f1f5f9;
    --sidebar-accent-foreground: #0f172a;
    --sidebar-border: #e2e8f0;
    --sidebar-ring: #059669;
}

/* Dark theme class-based */
.dark {
    --background: #0a0e1a;
    --foreground: #f8fafc;

    --card: #0f172a;
    --card-foreground: #f8fafc;

    --popover: #1e293b;
    --popover-foreground: #f8fafc;

    --primary: #10b981;
    --primary-foreground: #ffffff;

    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;

    --muted: #1e293b;
    --muted-foreground: #94a3b8;

    --accent: #10b981;
    --accent-foreground: #ffffff;

    --destructive: #ef4444;
    --destructive-foreground: #ffffff;

    /* Status badge colors for dark theme */
    --status-draft: #1e293b;
    --status-draft-foreground: #94a3b8;
    --status-pending: rgba(251, 191, 36, 0.2);
    --status-pending-foreground: #fbbf24;
    --status-approved: rgba(34, 197, 94, 0.2);
    --status-approved-foreground: #22c55e;
    --status-rejected: rgba(239, 68, 68, 0.2);
    --status-rejected-foreground: #ef4444;
    --status-issued: rgba(59, 130, 246, 0.2);
    --status-issued-foreground: #3b82f6;
    --status-partial: rgba(249, 115, 22, 0.2);
    --status-partial-foreground: #f97316;

    --border: #334155;
    --input: #1e293b;
    --ring: #10b981;

    --sidebar-background: #0f172a;
    --sidebar-foreground: #f8fafc;
    --sidebar-primary: #10b981;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #1e293b;
    --sidebar-accent-foreground: #f8fafc;
    --sidebar-border: #334155;
    --sidebar-ring: #10b981;
}

@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--border);
    }
}

/* Custom gradient utilities */
@layer utilities {
    .gradient-emerald {
        background: linear-gradient(135deg, var(--gradient-from) 0%, var(--gradient-to) 100%);
    }

    .gradient-emerald-soft {
        background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(2, 132, 199, 0.1) 100%);
    }

    .glass {
        backdrop-filter: blur(12px);
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Global dark theme text utilities - removed to avoid conflicts */

    /* Acrylic card styles */
    .acrylic-card {
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border: 1px solid hsl(var(--accent) / 0.9);
    }

    /* Acrylic button styles */
    .acrylic-button {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border: 1px solid hsl(var(--accent) / 0.7);
    }
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(5, 150, 105, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(5, 150, 105, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(5, 150, 105, 0.5);
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scale-in {
    0% {
        transform: scale(0.95);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-glow {
    animation: glow 1.5s infinite;
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

.animate-scale-in {
    animation: scale-in 0.2s ease-out;
}

/* Toast styling */
.toast-bg {
    background-color: var(--background);
    opacity: 1;
}

.toast-border {
    border-color: var(--border);
}

.toast-text {
    color: var(--foreground);
}

.dark .toast-bg {
    background-color: var(--dark-background);
    opacity: 1;
}

.dark .toast-border {
    border-color: var(--dark-border);
}

.dark .toast-text {
    color: var(--dark-foreground);
}
