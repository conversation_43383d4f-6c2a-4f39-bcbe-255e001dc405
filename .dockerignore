# Node modules (will be installed in Docker)
node_modules

# Development files
.env
.env.local
.env.development
.env.testing
.env.production

# Keep .env.docker for container setup
!.env.docker

# Testing
tests
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation
README.md
*.md
docs/

# Git
.git
.gitignore
.gitattributes

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Build artifacts (will be built in Docker)
public/build
public/hot

# Cache directories
storage/logs/*
storage/framework/cache/*
storage/framework/sessions/*
storage/framework/views/*
bootstrap/cache/*

# Database files (using PostgreSQL now)
database/database.sqlite

# Temporary files
*.tmp
*.temp
/tmp

# Docker files
docker-compose.yml
docker-compose.override.yml
Dockerfile.dev

# Scripts
*.sh
setup.sh

# Keep .env.example for container setup
# Keep artisan - it's needed for Laravel!
# Keep composer files - needed for dependencies
