# Sippar - Petty Cash Management Solution

Sippar is a comprehensive web-based petty cash management platform built with <PERSON><PERSON> and React. It provides a streamlined system for tracking, approving, and reconciling petty cash transactions within organizations to improve transparency, reduce misuse, and streamline reimbursement processes.

## Project Overview

A web-based tool (with mobile responsiveness) to track, approve, and reconcile petty cash transactions within a company to improve transparency, reduce misuse, and streamline reimbursement processes.

## Core Features

### Transaction Management
- Create new petty cash requests (requisitions)
- Approve/reject/amend requisitions
- Track petty cash expenses with customizable categories (e.g., office supplies, petty transport, kitchen supplies)
- Attach receipts to requisitions (file upload)
- Specify payment details (M-PESA number or bank details)

### Float Management
- Record cash float issued to departments or individuals
- Alert when float balance is low (admin, finance, and manager)

### User Roles & Permissions
- **Admin**: Full access, including setup and reporting (Finance Team)
- **Cashier**: Handles disbursements and reconciliations (Finance team)
- **Requestor**: Submits expense requests (anybody within the organization)
- **Reviewer**: Acknowledges expense items (Head of Department/procurement)
- **Approver**: Manager who approves/rejects requests

### Reporting & Reconciliation
- Daily/weekly/monthly expense reports
- Outstanding balances
- Export to Excel/PDF
- Audit trail of changes

### Notifications
- Email notifications for approvals, rejections, amendments, or low float alerts

### Integration Capabilities
- Integration with accounting systems
- M-PESA integration
- Employee and supplier directory integration

## Requirements

- PHP 8.2 or higher
- Composer
- Node.js 18.0.0 or higher
- npm
- SQLite (for development)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/Sippar-project/sippar.git
   cd sippar
   ```

2. Run the setup script:
   ```bash
   ./setup.sh
   ```

   This script will:
   - Check for required dependencies
   - Install PHP and Node.js dependencies
   - Create and configure the .env file
   - Set up the database
   - Run migrations and seeders
   - Build assets
   - Set file permissions
   - Clear caches

3. Start the development server:
   ```bash
   composer run dev
   ```

4. Visit http://localhost:8000 in your browser

## cache issues

By default the setup script runs this commands, but if you do run into a wall of red errors claiming to be cache issues, be sure to run this commands

```bash
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p storage/framework/cache/data

php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

## User Interface Requirements
- Mobile-responsive design
- Simple form for adding expenses
- Intuitive dashboard for different user roles

## Security Requirements
- User authentication (email/password)
- Role-based access control
- Audit logs

## Role Responsibilities

### Platform Administrator
- Can manage organizations
- Can create and designate organization administrators
- Has access to platform-wide settings and configurations
- Cannot directly manage internal organization structure (departments, branches)

### Organization Administrator
- Can manage their organization's details
- Can create and manage branches within their organization
- Can create and manage departments within their branches
- Can assign Heads of Department (HODs)
- Can manage users within their organization
- Can define and assign roles and permissions within their organization

### Admin (e.g Finance Team)
- Full access to the system
- Can manage all petty cash transactions
- Can generate reports and perform reconciliations
- Can manage float allocations

### Cashier (e.g Finance Team)
- Handles disbursements and reconciliations
- Records cash float issued to departments or individuals
- Processes approved requisitions

### Requestor
- Can submit expense requests
- Can attach receipts to requisitions
- Can view status of their own requisitions

### Reviewer (e.g Head of Department/Procurement)
- Can acknowledge expense items
- Can review requisitions from their department
- Can recommend approval or rejection

### Approver (e.g Manager)
- Can approve or reject requisitions
- Can view reports related to their area of responsibility

## Development

- Start the Laravel development server:
  ```bash
  php artisan serve
  ```

- Start the Vite development server for hot reloading:
  ```bash
  npm run dev
  ```

- Build assets for production:
  ```bash
  npm run build
  ```

- Run tests:
  ```bash
  composer test
  ```

- Lint frontend code:
  ```bash
  npm run lint
  ```

- Format frontend code:
  ```bash
  npm run format
  ```

## Docker for production

### Build the image
docker build -t sippar .

### Run the container
docker run -p 8080:80 sippar

## License

Copyright (c) 2025 Zone 01 Kisumu.
All rights reserved.

