/.phpunit.cache
/bootstrap/ssr
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/database/*.sqlite
/database/*.sqlite-journal
/vendor
/storage
.env
.env.backup
.env.*.local
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
yarn.lock
pnpm-lock.yaml
/auth.json
/.fleet
/.idea
/.nova
/.vscode
/.zed
.qodo
*txt
package-lock.json

# IDE & Editor specific
*.sublime-project
*.sublime-workspace
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Laravel specific
/public/css
/public/js
/public/mix-manifest.json
.php-cs-fixer.cache
_ide_helper.php
.phpstorm.meta.php
ide_helper_models.php

# development documentation
/refactor
.env.docker