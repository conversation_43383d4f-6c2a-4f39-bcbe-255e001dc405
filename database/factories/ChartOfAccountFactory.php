<?php

namespace Database\Factories;

use App\Models\ChartOfAccount;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

class ChartOfAccountFactory extends Factory
{
    protected $model = ChartOfAccount::class;

    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(), // Associate with an Organization factory
            'name' => $this->faker->company() . ' Expenses',
            'code' => strtoupper($this->faker->unique()->lexify('???-###')), // Example code
            'account_type' => $this->faker->randomElement(['asset','liability','equity','revenue','expense']),
            'is_active' => true,
            'description' => $this->faker->optional()->sentence,
        ];
    }
}
