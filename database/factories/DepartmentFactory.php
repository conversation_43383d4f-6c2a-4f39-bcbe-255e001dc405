<?php

namespace Database\Factories;

use App\Models\Department;
use App\Models\Organization;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Factories\Factory;

class DepartmentFactory extends Factory
{
    protected $model = Department::class;

    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'name' => $this->faker->unique()->word() . ' Department',
        ];
    }
}
