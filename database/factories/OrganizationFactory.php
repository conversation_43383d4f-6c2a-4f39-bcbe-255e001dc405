<?php

namespace Database\Factories;

use App\Models\Organization; // Make sure this namespace is correct
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Organization>
 */
class OrganizationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Organization::class; // Ensure this points to your Organization model

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Refer to your organizations table migration for required fields
        return [
            'name' => $this->faker->company(),
            'contact_email' => $this->faker->optional()->safeEmail(), // Use optional() if nullable
            'contact_phone' => $this->faker->optional()->phoneNumber(), // Use optional() if nullable
            'address' => $this->faker->optional()->address(),
            'status' => $this->faker->randomElement(['active', 'inactive']), // Use enum values from migration
            // Add any other non-nullable fields required by your migration
            // 'organization_account_details' => null, // Assuming this is nullable text
        ];
    }
}
