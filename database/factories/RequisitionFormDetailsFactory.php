<?php

namespace Database\Factories;

use App\Models\RequisitionFormDetails;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Department;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class RequisitionFormDetailsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RequisitionFormDetails::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'requisition_form_uuid' => Str::uuid(), // Use Str::uuid() to generate UUIDs
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'department_id' => Department::factory(),
            'requester_user_id' => User::factory(),
            'status' => $this->faker->randomElement(['draft', 'pending_approval', 'approved', 'rejected']),
        ];
    }
}
