<?php

namespace Database\Factories;

use App\Models\Attachment;
use App\Models\User;
use App\Models\Requisition;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttachmentFactory extends Factory
{
    protected $model = Attachment::class;

    public function definition(): array
    {
        return [
            'attachable_type' => Requisition::class,
            'attachable_id' => Requisition::factory(),
            'uploaded_by' => User::factory(),
            'original_name' => $this->faker->word() . '.' . $this->faker->randomElement(['pdf', 'jpg', 'png', 'doc', 'xlsx']),
            'file_name' => $this->faker->uuid() . '.' . $this->faker->randomElement(['pdf', 'jpg', 'png', 'doc', 'xlsx']),
            'file_path' => 'attachments/requisition/' . date('Y/m') . '/' . $this->faker->uuid() . '.pdf',
            'file_size' => $this->faker->numberBetween(1024, 10485760), // 1KB to 10MB
            'mime_type' => $this->faker->randomElement([
                'application/pdf',
                'image/jpeg',
                'image/png',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ]),
            'file_hash' => $this->faker->sha256(),
            'description' => $this->faker->optional()->sentence(),
            'is_evidence' => true,
            'uploaded_at_step' => $this->faker->randomElement(['draft', 'pending_approval', 'approved']),
        ];
    }

    /**
     * Indicate that the attachment is for a transaction.
     */
    public function forTransaction(): static
    {
        return $this->state(fn (array $attributes) => [
            'attachable_type' => \App\Models\Transaction::class,
            'attachable_id' => \App\Models\Transaction::factory(),
            'uploaded_at_step' => $this->faker->randomElement(['opened', 'updated', 'completed']),
        ]);
    }

    /**
     * Indicate that the attachment is not evidence.
     */
    public function notEvidence(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_evidence' => false,
        ]);
    }
}
