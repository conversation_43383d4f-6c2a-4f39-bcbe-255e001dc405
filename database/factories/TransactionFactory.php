<?php

namespace Database\Factories;

use App\Models\Transaction;
use App\Models\Requisition;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class TransactionFactory extends Factory
{
    protected $model = Transaction::class;

    public function definition(): array
    {
        return [
            'requisition_id' => Requisition::factory(),
            'status' => $this->faker->randomElement(['opened', 'updated', 'completed']),
            'payment_method' => $this->faker->randomElement(['bank_transfer', 'mpesa', 'cash']),
            'mpesa_transaction_id' => null,
            'account_details' => null,
            'disbursement_transaction_id' => null,
            'approvers_details' => [],
            'total_amount' => $this->faker->randomFloat(2, 10, 10000),
            'created_by' => User::factory(),
            'updated_by' => null,
        ];
    }

    /**
     * Indicate that the transaction is opened.
     */
    public function opened(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'opened',
        ]);
    }

    /**
     * Indicate that the transaction is updated.
     */
    public function updated(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'updated',
            'account_details' => json_encode([
                'account_name' => $this->faker->name(),
                'account_number' => $this->faker->bankAccountNumber(),
                'bank_name' => $this->faker->company(),
                'bank_code' => $this->faker->numerify('###'),
            ]),
        ]);
    }

    /**
     * Indicate that the transaction is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'disbursement_transaction_id' => $this->faker->uuid(),
            'account_details' => json_encode([
                'account_name' => $this->faker->name(),
                'account_number' => $this->faker->bankAccountNumber(),
                'bank_name' => $this->faker->company(),
                'bank_code' => $this->faker->numerify('###'),
            ]),
        ]);
    }

    /**
     * Indicate that the transaction uses M-Pesa.
     */
    public function withMpesa(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'mpesa',
            'mpesa_transaction_id' => $this->faker->uuid(),
        ]);
    }
}
