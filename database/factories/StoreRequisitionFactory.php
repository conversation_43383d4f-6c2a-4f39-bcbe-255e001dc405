<?php

namespace Database\Factories;

use App\Models\StoreRequisition;
use App\Models\User;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Department;
use Illuminate\Database\Eloquent\Factories\Factory;

class StoreRequisitionFactory extends Factory
{
    protected $model = StoreRequisition::class;

    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'department_id' => Department::factory(),
            'requester_user_id' => User::factory(),
            'purpose' => $this->faker->sentence(),
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ];
    }

    public function draft(): self
    {
        return $this->state([
            'status' => StoreRequisition::STATUS_DRAFT,
            'requested_at' => null,
        ]);
    }

    public function approved(): self
    {
        return $this->state([
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => User::factory(),
            'approved_at' => now(),
        ]);
    }

    public function rejected(): self
    {
        return $this->state([
            'status' => StoreRequisition::STATUS_REJECTED,
            'approver_user_id' => User::factory(),
            'rejection_reason' => $this->faker->sentence(),
        ]);
    }

    public function issued(): self
    {
        return $this->state([
            'status' => StoreRequisition::STATUS_ISSUED,
            'approver_user_id' => User::factory(),
            'approved_at' => now(),
            'issuer_user_id' => User::factory(),
            'issued_at' => now(),
        ]);
    }

    public function partiallyIssued(): self
    {
        return $this->state([
            'status' => StoreRequisition::STATUS_PARTIALLY_ISSUED,
            'approver_user_id' => User::factory(),
            'approved_at' => now(),
            'issuer_user_id' => User::factory(),
            'issued_at' => now(),
        ]);
    }
}
