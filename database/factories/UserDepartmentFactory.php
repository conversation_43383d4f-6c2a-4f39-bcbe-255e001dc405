<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Department;
use App\Models\UserDepartment; // Import the UserDepartment model
use Illuminate\Database\Eloquent\Factories\Factory;

class UserDepartmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserDepartment::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'department_id' => Department::factory(),
        ];
    }
}
