<?php

namespace Database\Factories;

use App\Models\Requisition;
use App\Models\User; // Assuming User model exists
use App\Models\Organization; // Assuming Organization model exists
use App\Models\Branch; // Assuming Branch model exists
use App\Models\Department; // Assuming Department model exists
use Illuminate\Database\Eloquent\Factories\Factory;

class RequisitionFactory extends Factory
{
    protected $model = Requisition::class;

    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'branch_id' => Branch::factory(),
            'department_id' => Department::factory(),
            'requester_user_id' => User::factory(),
            // We'll add more attributes as we define the Requisition schema
        ];
    }
}
