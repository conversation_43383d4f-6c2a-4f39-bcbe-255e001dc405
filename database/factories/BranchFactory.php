<?php

namespace Database\Factories;

use App\Models\Branch;      // Ensure namespace is correct
use App\Models\Organization; // We need Organization to link the branch
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Branch>
 */
class BranchFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Branch::class; // Ensure this points to your Branch model

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Refer to your branches table migration
        return [
            // Associate with an Organization. If one isn't provided when calling Branch::factory(),
            // this will automatically create a new Organization using its factory.
            'organization_id' => Organization::factory(),
            'name' => $this->faker->companySuffix() . ' Branch', // e.g., "Inc Branch", "LLC Branch"
            'address' => $this->faker->optional()->streetAddress(),
            'contact_email' => $this->faker->optional()->safeEmail(),
            'contact_phone' => $this->faker->optional()->phoneNumber(),
            'is_active' => $this->faker->boolean(90), // 90% chance of being true
        ];
    }
}
