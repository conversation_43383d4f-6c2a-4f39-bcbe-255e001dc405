<?php

namespace Database\Factories;

use App\Models\ChartOfAccount;
use App\Models\Requisition;
use App\Models\RequisitionItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class RequisitionItemFactory extends Factory
{
    protected $model = RequisitionItem::class;

    public function definition(): array
    {
        return [
            'requisition_id' => Requisition::factory(), // Will create a Requisition when needed
            'chart_of_account_id' => ChartOfAccount::factory(),
            'description' => $this->faker->sentence(3),
            'quantity' => $this->faker->numberBetween(1, 10),
            'unit_price' => $this->faker->randomFloat(2, 1, 100),
            'total_price' => 0.00, // Will be calculated on save
        ];
    }
}
