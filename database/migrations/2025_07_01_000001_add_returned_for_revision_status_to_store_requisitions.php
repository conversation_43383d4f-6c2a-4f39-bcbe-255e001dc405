<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add returned_for_revision status support to store requisitions
        // This brings store requisitions in line with petty cash requisitions
        
        // Note: No schema changes needed as status is stored as string
        // We just need to update the model constants and business logic
        
        // Add audit trail table for store requisition changes
        Schema::create('store_requisition_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_requisition_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('action'); // created, edited, submitted, approved, rejected, returned_for_revision, issued
            $table->text('comments')->nullable();
            $table->json('changes')->nullable(); // Store what fields were changed
            $table->timestamps();

            $table->index(['store_requisition_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('store_requisition_histories');
    }
};
