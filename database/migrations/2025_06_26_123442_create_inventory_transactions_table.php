<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('inventory_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('inventory_item_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->enum('transaction_type', ['issuance', 'receipt', 'adjustment']);
            $table->decimal('quantity_change', 10, 2);
            $table->unsignedBigInteger('related_document_id')->nullable();
            $table->string('related_document_type', 100)->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('transaction_date');
            $table->timestamps();

            $table->index(['inventory_item_id', 'transaction_date']);
            $table->index(['user_id', 'transaction_date']);
            $table->index(['related_document_type', 'related_document_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('inventory_transactions');
    }
};
