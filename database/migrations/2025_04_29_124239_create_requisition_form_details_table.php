<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRequisitionFormDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('requisition_form_details', function (Blueprint $table) {
            $table->uuid('requisition_form_uuid')->primary(); // Use UUID as primary key
            $table->foreignId('organization_id')->constrained('organizations');
            $table->foreignId('branch_id')->nullable()->constrained('branches');
            $table->foreignId('department_id')->nullable()->constrained('departments');
            $table->foreignId('requester_user_id')->constrained('users');
            $table->enum('status', ['draft', 'pending_approval', 'approved', 'rejected', 'fulfilled', 'cancelled'])->default('draft');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('requisition_form_details');
    }
}
