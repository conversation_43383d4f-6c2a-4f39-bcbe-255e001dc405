<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            // Index for user + timestamp queries (most important)
            $table->index(['notifiable_id', 'created_at'], 'idx_user_created');

            // Index for unread count queries
            $table->index(['notifiable_id', 'read_at'], 'idx_user_read');

            // Index for timestamp-based queries
            $table->index('created_at', 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropIndex('idx_user_created');
            $table->dropIndex('idx_user_read');
            $table->dropIndex('idx_created_at');
        });
    }
};
