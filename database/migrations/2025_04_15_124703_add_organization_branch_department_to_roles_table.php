<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->foreignId('organization_id')->nullable()->constrained()->nullOnDelete()->after('guard_name');
            $table->foreignId('branch_id')->nullable()->constrained()->nullOnDelete()->after('organization_id');
            $table->foreignId('department_id')->nullable()->constrained()->nullOnDelete()->after('branch_id');
            $table->text('description')->nullable()->after('department_id');
            $table->boolean('is_active')->default(true)->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn(['organization_id', 'branch_id', 'department_id', 'description', 'is_active']);
        });
    }
};
