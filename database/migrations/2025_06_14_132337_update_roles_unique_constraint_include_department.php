<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            // Drop the existing unique constraint that prevents multiple HOD roles per organization
            $table->dropUnique('roles_name_guard_organization_unique');

            // Add a new unique constraint that includes department_id
            // This allows multiple HOD roles per organization as long as they're for different departments
            $table->unique(['name', 'guard_name', 'organization_id', 'department_id'], 'roles_name_guard_org_dept_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('roles_name_guard_org_dept_unique');

            // Restore the original unique constraint
            $table->unique(['name', 'guard_name', 'organization_id'], 'roles_name_guard_organization_unique');
        });
    }
};
