<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('requisitions', function (Blueprint $table) {
            $table->foreignId('approval_workflow_id')->nullable()->constrained()->onDelete('set null')->after('status');
            $table->foreignId('current_approval_step_id')->nullable()->constrained('approval_workflow_steps')->onDelete('set null')->after('approval_workflow_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('requisitions', function (Blueprint $table) {
            $table->dropForeign(['approval_workflow_id']);
            $table->dropColumn('approval_workflow_id');
            $table->dropForeign(['current_approval_step_id']);
            $table->dropColumn('current_approval_step_id');
        });
    }
};
