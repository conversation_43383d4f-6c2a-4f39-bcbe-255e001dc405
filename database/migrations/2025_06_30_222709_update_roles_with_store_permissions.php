<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Database\Seeders\StoreInventoryPermissionsSeeder;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration ensures that existing installations get store permissions
     * added to their roles by running the StoreInventoryPermissionsSeeder.
     */
    public function up(): void
    {
        // Run the store inventory permissions seeder to update existing roles
        \Artisan::call('db:seed', [
            '--class' => 'Database\\Seeders\\StoreInventoryPermissionsSeeder'
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * Note: We don't remove permissions in down() to avoid breaking functionality
     * Store permissions can be manually removed if needed.
     */
    public function down(): void
    {
        // Intentionally left empty - removing permissions could break functionality
        // If rollback is needed, manually remove store permissions from roles
    }
};
