<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cash_floats', function (Blueprint $table) {
            $table->timestamp('last_alert_sent_at')->nullable()->after('alert_threshold');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cash_floats', function (Blueprint $table) {
            $table->dropColumn('last_alert_sent_at');
        });
    }
};