<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('store_requisition_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_requisition_id')->constrained()->cascadeOnDelete();
            $table->foreignId('inventory_item_id')->constrained()->cascadeOnDelete();
            $table->decimal('quantity_requested', 10, 2);
            $table->decimal('quantity_issued', 10, 2)->default(0);
            $table->timestamps();

            $table->index(['store_requisition_id']);
            $table->index(['inventory_item_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('store_requisition_items');
    }
};
