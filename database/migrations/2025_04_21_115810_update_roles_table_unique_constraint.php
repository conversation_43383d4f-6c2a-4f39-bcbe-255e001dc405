<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            // Drop the existing unique constraint
            $table->dropUnique(['name', 'guard_name']);

            // Add a new unique constraint that includes organization_id
            $table->unique(['name', 'guard_name', 'organization_id'], 'roles_name_guard_organization_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('roles_name_guard_organization_unique');

            // Restore the original unique constraint
            $table->unique(['name', 'guard_name']);
        });
    }
};
