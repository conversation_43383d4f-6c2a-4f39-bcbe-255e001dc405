<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('requisition_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('cash_float_id')->nullable()->constrained('cash_floats')->onDelete('set null');
            $table->enum('transaction_type', ['disbursement', 'reimbursement', 'float_issuance', 'float_return', 'expense', 'other'])->default('expense');
            $table->enum('status', ['opened', 'updated', 'completed'])->default('opened');
             $table->text('description')->nullable();
            $table->text('account_details')->nullable();
            $table->string('disbursement_transaction_id')->nullable();
            $table->json('approvers_details')->nullable();
            $table->enum('payment_method', ['mpesa', 'bank', 'cash'])->nullable();
            $table->text('rejection_reason')->nullable();
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->decimal('transaction_cost', 15, 2)->nullable()->comment('Additional costs associated with the transaction (e.g., processing fees)');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
