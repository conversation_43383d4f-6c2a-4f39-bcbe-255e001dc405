<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mpesa_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('transaction_type', ['payment', 'balance_check', 'refund', 'reversal', 'other'])->default('payment');
            $table->string('related_id')->nullable()->comment('ID of related item (e.g., Requisition ID)');
            $table->string('related_type')->nullable()->comment('Type of related item');
            $table->string('mpesa_transaction_id')->nullable()->comment('Transaction ID from M-Pesa API');
            $table->string('mpesa_receipt_number')->nullable()->comment('Receipt number from M-Pesa API');
            $table->string('checkout_request_id')->nullable();
            $table->string('merchant_request_id')->nullable();
            $table->decimal('amount', 15, 2);
            $table->string('recipient_phone')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('reference')->nullable();
            $table->string('description')->nullable();
            $table->string('result_code')->nullable();
            $table->string('result_desc')->nullable();
            $table->decimal('confirmed_amount', 15, 2)->nullable();
            $table->string('confirmed_phone')->nullable();
            $table->timestamp('transaction_date')->nullable();
            $table->enum('status', ['initiated', 'success', 'failed', 'pending', 'cancelled'])->default('initiated');
            $table->json('request_payload')->nullable()->comment('For debugging/auditing');
            $table->json('response_payload')->nullable()->comment('For debugging/auditing');
            $table->timestamp('transaction_time')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mpesa_transactions');
    }
};