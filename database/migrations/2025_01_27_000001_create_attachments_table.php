<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            
            // Polymorphic relationship - can attach to any model
            $table->string('attachable_type'); // App\Models\Requisition, App\Models\Transaction, etc.
            $table->unsignedBigInteger('attachable_id');
            $table->index(['attachable_type', 'attachable_id']);
            
            // User who uploaded the file
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            
            // File information
            $table->string('original_name'); // Original filename as uploaded
            $table->string('file_name'); // Stored filename (usually hashed)
            $table->string('file_path'); // Path in storage
            $table->unsignedBigInteger('file_size'); // File size in bytes
            $table->string('mime_type'); // MIME type
            $table->string('file_hash')->nullable(); // File hash for integrity checking
            
            // Metadata
            $table->text('description')->nullable(); // User-provided description
            $table->boolean('is_evidence')->default(true); // Whether this is evidence/supporting document
            $table->string('uploaded_at_step')->nullable(); // Which step/stage this was uploaded at
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attachments');
    }
};
