<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cash_floats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('department_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name')->comment('Descriptive name for the float');
            $table->decimal('initial_amount', 15, 2)->comment('Original amount assigned to this float');
            $table->timestamp('issued_at')->comment('Date and time when this float was initially set up or issued');
            $table->decimal('alert_threshold', 15, 2)->nullable()->comment('Balance amount below which an alert should be triggered');
            $table->enum('status', ['active', 'inactive', 'reconciled', 'closed'])->default('active');
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['organization_id', 'status']);
            $table->index(['branch_id', 'status']);
            $table->index(['department_id', 'status']);
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cash_floats');
    }
};
