<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\UserOrganization;
use App\Models\UserBranch;
use App\Models\UserDepartment;
use App\Models\Branch;
use App\Models\Department;

class DemoOrganizationDefaults extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        /*
        * Create entry for the new user in:
        * 1. user_organizations table
        * 2. branches table
        * 3. user_branches table
        * 4. departments table
        * 5. user_departments table
        */
        // 1. Ensure UserOrganization exists
        $userid = 2;
        $organizationId = 1;

        // 2. Find or create default Branch
        $branch = Branch::firstOrCreate(
            [
                "organization_id" => $organizationId,
                "name" => "default",
            ],
            [
                "is_active" => true,
            ]
        );
        if(!$branch) {
            dd("No branch");
        }

        // 3. Ensure UserBranch exists
        $userBranch = UserBranch::firstOrCreate([
            "user_id" => $userid,
            "branch_id" => $branch->id,
        ]);
        if(!$userBranch) {
            dd("No userBranch");
        }

        // 4. Find or create default Department
        $department = Department::firstOrCreate(
            [
                "organization_id" => $organizationId,
                "branch_id" => $branch->id,
                "name" => "default",
            ]
        );

        // 5. Ensure UserDepartment exists
        $userDepartment = UserDepartment::firstOrCreate([
            "user_id" => $userid,
            "department_id" => $department->id,
        ]);
    }
}
