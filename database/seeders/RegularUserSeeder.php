<?php

namespace Database\Seeders;

use App\Models\Department;
use App\Models\Organization;
use App\Models\Role;
use App\Models\User;
use App\Models\Branch;
use App\Models\UserBranch;
use App\Models\UserDepartment;
use Illuminate\Database\Seeder;
use App\Models\UserOrganization;

class RegularUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first department and organization
        $department = Department::first();
        $organization = Organization::first();

        // Create or get regular user (Employee)
        $user = $this->createOrGetUser(
            'user',
            'Regular',
            'User',
            '<EMAIL>',
            '+1987654321',
            'Employee'
        );

        // Create default relationships for the user
        UserOrganization::firstOrCreate([
            'user_id' => $user->id,
            'organization_id' => 1, // matches the Demo Organization id
        ]);
        UserBranch::create([
            'user_id' => $user->id,
            'branch_id' => 1, // matches the Demo Organization id
        ]);

        // Find or create default Branch
        $branch = Branch::firstOrCreate(
            [
                "organization_id" => 1,
                "name" => "default",
            ],
            [
                "is_active" => true,
            ]
        );

        // Ensure UserBranch exists
        UserBranch::firstOrCreate([
            "user_id" => $user->id,
            "branch_id" => $branch->id,
        ]);

        // Assign user to department if not already assigned
        if ($department && $user) {
            if (!$user->departments()->where('department_id', $department->id)->exists()) {
                $user->departments()->attach($department->id);
            }

            // Ensure UserDepartment exists
            UserDepartment::firstOrCreate([
                "user_id" => $user->id,
                "department_id" => $department->id,
            ]);
        }

        // Create or get finance manager user
        $financeManager = $this->createOrGetUser(
            'finance',
            'Finance',
            'Manager',
            '<EMAIL>',
            '+1777777777',
            'Finance Manager'
        );
        // Create default relationships for the finance manager
        UserOrganization::firstOrCreate([
            'user_id' => $financeManager->id,
            'organization_id' => 1, // matches the Demo Organization id
        ]);

        // Ensure UserBranch exists
        UserBranch::firstOrCreate([
            "user_id" => $financeManager->id,
            "branch_id" => $branch->id,
        ]);

        // Assign finance manager to department if not already assigned
        if ($department && $financeManager) {
            if (!$financeManager->departments()->where('department_id', $department->id)->exists()) {
                $financeManager->departments()->attach($department->id);
            }

            // Ensure UserDepartment exists
            UserDepartment::firstOrCreate([
                "user_id" => $financeManager->id,
                "department_id" => $department->id,
            ]);
        }

        // Create or get organization admin
        if ($organization) {
            $orgAdmin = $this->createOrGetUser(
                'orgadmin',
                'Organization',
                'Admin',
                '<EMAIL>',
                '+1555555555',
                'Organization Admin'
            );
            // Create default relationships for the organization admin
            UserOrganization::firstOrCreate([
                'user_id' => $orgAdmin->id,
                'organization_id' => 1, // matches the Demo Organization id
            ]);

            // Ensure UserBranch exists
            UserBranch::firstOrCreate([
                "user_id" => $orgAdmin->id,
                "branch_id" => $branch->id,
            ]);

            // Ensure UserDepartment exists
            if ($department) {
                UserDepartment::firstOrCreate([
                    "user_id" => $orgAdmin->id,
                    "department_id" => $department->id,
                ]);
            }

            // Get the Organization Admin role template
            $orgAdminRoleTemplate = Role::where('name', 'Organization Admin')->first();

            if ($orgAdminRoleTemplate) {
                // Check if an Organization Admin role already exists for this organization
                $orgAdminRole = Role::where('name', 'Organization Admin')
                    ->where('organization_id', $organization->id)
                    ->first();

                if (!$orgAdminRole) {
                    try {
                        // Create a new Organization Admin role for this organization
                        $orgAdminRole = Role::create([
                            'name' => 'Organization Admin',
                            'organization_id' => $organization->id,
                            'description' => 'Administrator for ' . $organization->name,
                            'guard_name' => 'web'
                        ]);
                    } catch (\Exception $e) {
                        // If the role already exists, try to find it again
                        $orgAdminRole = Role::where('name', 'Organization Admin')
                            ->where('organization_id', $organization->id)
                            ->first();

                        if (!$orgAdminRole) {
                            // If we still can't find it, rethrow the exception
                            throw $e;
                        }
                    }
                }

                // Copy permissions from the template role to the organization-specific role
                // First, clear existing permissions to avoid duplicates
                $orgAdminRole->permissions()->detach();

                // Then copy permissions from the template role
                $permissions = $orgAdminRoleTemplate->permissions;
                $orgAdminRole->givePermissionTo($permissions);

                // Assign the organization-specific admin role to the user
                $orgAdmin->assignRole($orgAdminRole);
            }
        }

        // Create or get HOD user
        if ($department && $organization) {
            $hod = $this->createOrGetUser(
                'hod',
                'Department',
                'Head',
                '<EMAIL>',
                '+1666666666',
                'HOD'
            );
            // Create default relationships for the HOD
            UserOrganization::firstOrCreate([
                'user_id' => $hod->id,
                'organization_id' => 1, // matches the Demo Organization id
            ]);

            // Ensure UserBranch exists
            UserBranch::firstOrCreate([
                "user_id" => $hod->id,
                "branch_id" => $branch->id,
            ]);

            if ($hod) {
                // Get the HOD role template
                $hodRoleTemplate = Role::where('name', 'HOD')
                    ->whereNull('organization_id')
                    ->whereNull('department_id')
                    ->first();

                if ($hodRoleTemplate) {
                    // Check if a department-specific HOD role already exists
                    $hodRole = Role::where('name', 'HOD')
                        ->where('organization_id', $organization->id)
                        ->where('department_id', $department->id)
                        ->first();

                    if (!$hodRole) {
                        try {
                            // Create a new HOD role for this department
                            $hodRole = Role::create([
                                'name' => 'HOD',
                                'organization_id' => $organization->id,
                                'branch_id' => $department->branch_id,
                                'department_id' => $department->id,
                                'description' => 'Head of ' . $department->name,
                                'guard_name' => 'web'
                            ]);
                        } catch (\Exception $e) {
                            // If the role already exists, try to find it again
                            $hodRole = Role::where('name', 'HOD')
                                ->where('organization_id', $organization->id)
                                ->where('department_id', $department->id)
                                ->first();

                            if (!$hodRole) {
                                // If we still can't find it, rethrow the exception
                                throw $e;
                            }
                        }

                        // Copy permissions from the template role
                        $hodRole->syncPermissions($hodRoleTemplate->permissions);
                    }

                    // Assign the department-specific HOD role to the user
                    $hod->assignRole($hodRole);

                    // Assign HOD to department if not already assigned
                    if (!$hod->departments()->where('department_id', $department->id)->exists()) {
                        $hod->departments()->attach($department->id);
                    }

                    // Ensure UserDepartment exists
                    UserDepartment::firstOrCreate([
                        "user_id" => $hod->id,
                        "department_id" => $department->id,
                    ]);

                    // Update department to set HOD if not already set
                    if ($department->hod_user_id !== $hod->id) {
                        $department->update(['hod_user_id' => $hod->id]);
                    }
                }
            }
        }
    }

    /**
     * Create a user if it doesn't exist, or get the existing one
     */
    private function createOrGetUser(string $username, string $firstName, string $lastName, string $email, string $phone, string $roleName): ?User
    {
        // Check if user already exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser) {
            return $existingUser;
        }

        // Create new user
        $user = User::factory()->create([
            'username' => $username,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $email,
            'phone' => $phone,
            'password' => bcrypt('password'),
            'is_platform_admin' => false,
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // For Employee role, create an organization-specific role
        if ($roleName === 'Employee') {
            $organization = Organization::first();
            if ($organization) {
                // Get the Employee role template
                $employeeRoleTemplate = Role::where('name', 'Employee')
                    ->whereNull('organization_id')
                    ->first();

                if ($employeeRoleTemplate) {
                    // Check if an organization-specific Employee role already exists
                    $employeeRole = Role::where('name', 'Employee')
                        ->where('organization_id', $organization->id)
                        ->first();

                    if (!$employeeRole) {
                        try {
                            // Create a new Employee role for this organization
                            $employeeRole = Role::create([
                                'name' => 'Employee',
                                'organization_id' => $organization->id,
                                'description' => 'Employee of ' . $organization->name,
                                'guard_name' => 'web'
                            ]);

                            // Copy permissions from the template role
                            $employeeRole->syncPermissions($employeeRoleTemplate->permissions);
                        } catch (\Exception $e) {
                            // If the role already exists, try to find it again
                            $employeeRole = Role::where('name', 'Employee')
                                ->where('organization_id', $organization->id)
                                ->first();

                            if (!$employeeRole) {
                                // If we still can't find it, rethrow the exception
                                throw $e;
                            }
                        }
                    }

                    // Assign the organization-specific Employee role to the user
                    $user->assignRole($employeeRole);
                }
            }
        }

        // For HOD role, also ensure we have a generic HOD role for the organization
        if ($roleName === 'HOD') {
            $organization = Organization::first();
            if ($organization) {
                // Get the HOD role template
                $hodRoleTemplate = Role::where('name', 'HOD')
                    ->whereNull('organization_id')
                    ->first();

                if ($hodRoleTemplate) {
                    // Check if a generic HOD role already exists for this organization
                    $genericHodRole = Role::where('name', 'HOD')
                        ->where('organization_id', $organization->id)
                        ->whereNull('department_id')
                        ->first();

                    if (!$genericHodRole) {
                        try {
                            // Create a generic HOD role for this organization
                            $genericHodRole = Role::create([
                                'name' => 'HOD',
                                'organization_id' => $organization->id,
                                'department_id' => null,
                                'description' => 'Head of Department (Generic) for ' . $organization->name,
                                'guard_name' => 'web'
                            ]);

                            // Copy permissions from the template role
                            $genericHodRole->syncPermissions($hodRoleTemplate->permissions);
                        } catch (\Exception $e) {
                            // If the role already exists, try to find it again
                            $genericHodRole = Role::where('name', 'HOD')
                                ->where('organization_id', $organization->id)
                                ->whereNull('department_id')
                                ->first();

                            if (!$genericHodRole) {
                                // If we still can't find it, rethrow the exception
                                throw $e;
                            }
                        }
                    }
                }
            }
        } else if ($roleName === 'Finance Manager') {
            $organization = Organization::first();
            if ($organization) {
                // Get the Finance Manager role template
                $financeManagerRoleTemplate = Role::where('name', 'Finance Manager')
                    ->whereNull('organization_id')
                    ->first();

                if ($financeManagerRoleTemplate) {
                    // Check if an organization-specific Finance Manager role already exists
                    $financeManagerRole = Role::where('name', 'Finance Manager')
                        ->where('organization_id', $organization->id)
                        ->first();

                    if (!$financeManagerRole) {
                        try {
                            // Create a new Finance Manager role for this organization
                            $financeManagerRole = Role::create([
                                'name' => 'Finance Manager',
                                'organization_id' => $organization->id,
                                'description' => 'Finance Manager of ' . $organization->name,
                                'guard_name' => 'web'
                            ]);

                            // Copy permissions from the template role
                            $financeManagerRole->syncPermissions($financeManagerRoleTemplate->permissions);
                        } catch (\Exception $e) {
                            // If the role already exists, try to find it again
                            $financeManagerRole = Role::where('name', 'Finance Manager')
                                ->where('organization_id', $organization->id)
                                ->first();

                            if (!$financeManagerRole) {
                                // If we still can't find it, rethrow the exception
                                throw $e;
                            }
                        }
                    }

                    // Assign the organization-specific Finance Manager role to the user
                    $user->assignRole($financeManagerRole);
                }
            }
        } else if ($roleName !== 'HOD' && $roleName !== 'Organization Admin') {
            // For other roles (except HOD and Organization Admin which are handled separately)
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $user->assignRole($role);
            }
        }

        return $user;
    }
}
