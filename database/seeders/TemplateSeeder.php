<?php

namespace Database\Seeders;

use App\Models\DepartmentTemplate;
use App\Models\RoleTemplate;
use Illuminate\Database\Seeder;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create department templates
        $financeTemplate = DepartmentTemplate::create([
            'name' => 'Finance Department',
            'description' => 'Manages financial operations, petty cash, budgeting, and accounting',
            'is_default' => true,
        ]);

        $hrTemplate = DepartmentTemplate::create([
            'name' => 'Human Resources',
            'description' => 'Manages recruitment, employee relations, and personnel administration',
            'is_default' => true,
        ]);

        $itTemplate = DepartmentTemplate::create([
            'name' => 'IT Department',
            'description' => 'Manages technology infrastructure, software, and technical support',
            'is_default' => true,
        ]);

        $operationsTemplate = DepartmentTemplate::create([
            'name' => 'Operations',
            'description' => 'Manages day-to-day operational activities and logistics',
            'is_default' => true,
        ]);

        $marketingTemplate = DepartmentTemplate::create([
            'name' => 'Marketing',
            'description' => 'Manages brand promotion, advertising, and market research',
            'is_default' => true,
        ]);

        $salesTemplate = DepartmentTemplate::create([
            'name' => 'Sales',
            'description' => 'Manages sales activities, client relationships, and revenue generation',
            'is_default' => true,
        ]);

        // Create role templates for Finance Department
        RoleTemplate::create([
            'name' => 'Finance Manager',
            'description' => 'Oversees all financial operations and approves expenditures',
            'department_template_id' => $financeTemplate->id,
            'is_default' => true,
            'permissions' => [
              'view-requisition',
            'approve-requisition',
            'reject-requisition',
            'amend-requisition',
            'manage-floats',
            'create-float',
            'view-float',
            'edit-float',
            'issue-float',
            'manage-reports',
            'view-reports',
            'export-reports',
            'view-audit-logs',
            'manage-reconciliations',
            'approve-reconciliation',
            'reject-reconciliation',
            'approver',
            'manage-transactions',
            'view-transactions',
            'create-transactions',
            'edit-transactions',
            'delete-transactions',
            ],
        ]);

   

        // Create role templates for HR Department
        RoleTemplate::create([
            'name' => 'HOD',
            'description' => 'Oversees department operations and reviews requisitions',
            'department_template_id' => $hrTemplate->id,
            'is_default' => true,
            'permissions' => [
                'create-requisition',
                'view-requisition',
                'edit-requisition',
                'delete-requisition',
                'review-requisition',
                'view-reports',
                'view-float',
                'approver',
            ],
        ]);

        // Create role templates for IT Department
        RoleTemplate::create([
            'name' => 'HOD',
            'description' => 'Oversees department operations and reviews requisitions',
            'department_template_id' => $itTemplate->id,
            'is_default' => true,
            'permissions' => [
                'create-requisition',
                'view-requisition',
                'edit-requisition',
                'delete-requisition',
                'review-requisition',
                'view-reports',
                'view-float',
                'approver',
            ],
        ]);

        // Create role templates for Operations Department
        RoleTemplate::create([
            'name' => 'HOD',
            'description' => 'Oversees department operations and reviews requisitions',
            'department_template_id' => $operationsTemplate->id,
            'is_default' => true,
            'permissions' => [
                'create-requisition',
                'view-requisition',
                'edit-requisition',
                'delete-requisition',
                'review-requisition',
                'view-reports',
                'view-float',
                'approver',
            ],
        ]);

        // Create role templates for Marketing Department
        RoleTemplate::create([
            'name' => 'HOD',
            'description' => 'Oversees department operations and reviews requisitions',
            'department_template_id' => $marketingTemplate->id,
            'is_default' => true,
            'permissions' => [
                'create-requisition',
                'view-requisition',
                'edit-requisition',
                'delete-requisition',
                'review-requisition',
                'view-reports',
                'view-float',
                'approver'
            ],
        ]);

        // Create role templates for Sales Department
        RoleTemplate::create([
            'name' => 'HOD',
            'description' => 'Oversees department operations and reviews requisitions',
            'department_template_id' => $salesTemplate->id,
            'is_default' => true,
            'permissions' => [
                'create-requisition',
                'view-requisition',
                'edit-requisition',
                'delete-requisition',
                'review-requisition',
                'view-reports',
                'view-float',
                'approver'
            ],
        ]);

        // Create general employee role template (not tied to a specific department)
        RoleTemplate::create([
            'name' => 'Employee',
            'description' => 'Regular employee with basic access',
            'department_template_id' => null,
            'is_default' => true,
            'permissions' => [
                'create-requisition',
                'view-requisition',
                'edit-requisition',
                'delete-requisition',
                
            ],
        ]);
    }
}
