<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\Department;
use App\Models\Organization;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $organization = Organization::first();
        $branches = Branch::all();

        if ($organization && $branches->count() > 0) {
            // Create departments for the main branch
            $mainBranch = $branches->first();

            Department::create([
                'organization_id' => $organization->id,
                'branch_id' => $mainBranch->id,
                'name' => 'IT Department',
                'hod_user_id' => null, // Will be set later when users are created
            ]);

            Department::create([
                'organization_id' => $organization->id,
                'branch_id' => $mainBranch->id,
                'name' => 'Finance Department',
                'hod_user_id' => null,
            ]);

            Department::create([
                'organization_id' => $organization->id,
                'branch_id' => $mainBranch->id,
                'name' => 'HR Department',
                'hod_user_id' => null,
            ]);

            // Create departments for the secondary branch if it exists
            if ($branches->count() > 1) {
                $secondaryBranch = $branches->skip(1)->first();

                Department::create([
                    'organization_id' => $organization->id,
                    'branch_id' => $secondaryBranch->id,
                    'name' => 'Operations Department',
                    'hod_user_id' => null,
                ]);

                Department::create([
                    'organization_id' => $organization->id,
                    'branch_id' => $secondaryBranch->id,
                    'name' => 'Marketing Department',
                    'hod_user_id' => null,
                ]);
            }
        }
    }
}
