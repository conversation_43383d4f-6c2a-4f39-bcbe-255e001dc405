<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class StoreInventoryPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();
        $permissions = [
            ['name' => 'view-inventory', 'description' => 'View inventory item list and current stock levels'],
            ['name' => 'manage-inventory', 'description' => 'Create/edit inventory items and make manual stock adjustments'],
            ['name' => 'create-store-requisition', 'description' => 'Create and submit new requisitions for store items'],
            ['name' => 'edit-store-requisition', 'description' => 'Edit draft, rejected, or returned store requisitions'],
            ['name' => 'view-store-requisitions', 'description' => 'View store requisition history and status'],
            ['name' => 'approve-store-requisition', 'description' => 'Approve or reject pending store requisitions'],
            ['name' => 'issue-store-items', 'description' => 'Fulfill approved requisitions and create issuance transactions'],
            ['name' => 'store-keep', 'description' => 'All permissions related to inventory management (meta-permission)'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                ['description' => $permission['description']]
            );
        }

        $storeRelatedPermissions = [
            'view-inventory',
            'manage-inventory',
            'create-store-requisition',
            'edit-store-requisition',
            'view-store-requisitions',
            'approve-store-requisition',
            'issue-store-items'
        ];

        // Create Store Keeper role template (follows established pattern)
        $storeKeeperRole = Role::firstOrCreate(
            ['name' => 'Store Keeper', 'organization_id' => null],
            ['description' => 'Manages inventory and processes requisitions']
        );

        $storeKeeperRole->givePermissionTo([
            ...$storeRelatedPermissions,
            'store-keep'
        ]);

        // Update Platform Admin role with store permissions
        $platformAdmin = Role::where('name', 'Platform Admin')->first();
        if ($platformAdmin) {
            $platformAdmin->givePermissionTo(array_column($permissions, 'name'));
        }

        // Update role templates (organization_id = null) following established pattern
        // Employee role template - basic store requisition permissions
        // Employees can create requisitions and view their own requisition history (no inventory access)
        $employeeRoleTemplate = Role::where('name', 'Employee')
            ->whereNull('organization_id')
            ->first();

        if ($employeeRoleTemplate) {
            $employeeRoleTemplate->givePermissionTo([
                'create-store-requisition',
                'edit-store-requisition',
                'view-store-requisitions'
            ]);
        }

        // HOD role template - can view and create store requisitions but CANNOT approve
        $hodRoleTemplate = Role::where('name', 'HOD')
            ->whereNull('organization_id')
            ->first();

        if ($hodRoleTemplate) {
            $hodRoleTemplate->givePermissionTo([
                'create-store-requisition',
                'edit-store-requisition',
                'view-store-requisitions'
            ]);
        }

        // Finance Manager role template - full store oversight except issuing items
        $financeManagerRoleTemplate = Role::where('name', 'Finance Manager')
            ->whereNull('organization_id')
            ->first();

        if ($financeManagerRoleTemplate) {
            $financeManagerRoleTemplate->givePermissionTo([
                'create-store-requisition',
                'edit-store-requisition',
                'view-inventory',
                'view-store-requisitions',
                'approve-store-requisition',
                'manage-inventory'
            ]);
        }

        // Organization Admin role template - management permissions except issuing items
        $organizationAdminRoleTemplate = Role::where('name', 'Organization Admin')
            ->whereNull('organization_id')
            ->first();

        if ($organizationAdminRoleTemplate) {
            $organizationAdminRoleTemplate->givePermissionTo([
                'view-inventory',
                'manage-inventory',
                'view-store-requisitions',
                'approve-store-requisition',
                'create-store-requisition',
                'edit-store-requisition'
            ]);
        }

        // Update existing organization-specific roles (for existing organizations)
        // Employee roles - basic store requisition permissions
        $orgEmployeeRoles = Role::where('name', 'Employee')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgEmployeeRoles as $role) {
            $role->givePermissionTo([
                'create-store-requisition',
                'edit-store-requisition',
                'view-store-requisitions'
            ]);
        }

        // HOD roles - can view and create store requisitions but CANNOT approve
        $orgHodRoles = Role::where('name', 'HOD')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgHodRoles as $role) {
            $role->givePermissionTo([
                'create-store-requisition',
                'edit-store-requisition',
                'view-store-requisitions'
            ]);
        }

        // Finance Manager roles - all store permissions except issuing items
        $orgFinanceManagerRoles = Role::where('name', 'Finance Manager')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgFinanceManagerRoles as $role) {
            $role->givePermissionTo([
                'view-inventory',
                'manage-inventory',
                'create-store-requisition',
                'edit-store-requisition',
                'view-store-requisitions',
                'approve-store-requisition'
            ]);
        }

        // Organization Admin roles - all store permissions except issuing items
        $orgAdminRoles = Role::where('name', 'Organization Admin')
            ->whereNotNull('organization_id')
            ->get();

        foreach ($orgAdminRoles as $role) {
            $role->givePermissionTo([
                'view-inventory',
                'manage-inventory',
                'create-store-requisition',
                'edit-store-requisition',
                'view-store-requisitions',
                'approve-store-requisition'
            ]);
        }

        // Create Store Keeper roles for existing organizations
        $existingOrganizations = \App\Models\Organization::all();
        foreach ($existingOrganizations as $organization) {
            $orgStoreKeeperRole = Role::firstOrCreate(
                ['name' => 'Store Keeper', 'organization_id' => $organization->id],
                ['description' => 'Manages inventory and processes requisitions']
            );

            $orgStoreKeeperRole->givePermissionTo([
                ...$storeRelatedPermissions,
                'store-keep'
            ]);
        }

        $this->command->info('✅ Store inventory permissions created successfully!');
        $this->command->info('✅ Store Keeper role template created with all inventory permissions.');
        $this->command->info('✅ Store Keeper roles created for existing organizations.');
        $this->command->info('✅ Role templates updated with appropriate store permissions:');
        $this->command->info('   - Store Keeper: All permissions including issue-store-items');
        $this->command->info('   - Employee: create-store-requisition, edit-store-requisition, view-store-requisitions');
        $this->command->info('   - HOD/Finance Manager/Organization Admin: All permissions except issue-store-items');
        $this->command->info('✅ Existing organization-specific roles updated with store permissions.');
        $this->command->info('📋 Users will now inherit store permissions through their roles.');
    }
}
