<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use App\Models\UserOrganization;

class PlatformAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::factory()->create([
            'username' => 'admin',
            'first_name' => 'Platform',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => bcrypt('password'), // You might want to change this in production
            'is_platform_admin' => true,
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        UserOrganization::create([
            'user_id' => $admin->id,
            'organization_id' => 1, // matches the Demo Organization id
        ]);

        // Assign Platform Admin role
        $platformAdminRole = Role::where('name', 'Platform Admin')->first();
        if ($platformAdminRole) {
            $admin->assignRole($platformAdminRole);
        }
    }
}
