<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\Organization;
use Illuminate\Database\Seeder;

class BranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $organization = Organization::first();

        if ($organization) {
            Branch::create([
                'organization_id' => $organization->id,
                'name' => 'Main Branch',
                'address' => '123 Main Street, Nairobi, Kenya',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+254711111111',
                'is_active' => true,
            ]);

            Branch::create([
                'organization_id' => $organization->id,
                'name' => 'Secondary Branch',
                'address' => '456 Secondary Street, Mombasa, Kenya',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+254722222222',
                'is_active' => true,
            ]);
        }
    }
}
