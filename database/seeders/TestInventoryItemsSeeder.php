<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\Branch;

class TestInventoryItemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first organization and branch for testing
        $organization = Organization::first();
        $branch = Branch::first();

        if (!$organization) {
            $this->command->error('No organization found. Please create an organization first.');
            return;
        }

        $testItems = [
            [
                'sku' => 'PAPER-A4-001',
                'name' => 'A4 Paper',
                'description' => 'White A4 printing paper, 80gsm, 500 sheets per ream',
                'unit_of_measure' => 'ream',
                'quantity_on_hand' => 50,
                'reorder_level' => 10,
            ],
            [
                'sku' => 'PEN-BLUE-001',
                'name' => 'Blue Ballpoint Pen',
                'description' => 'Standard blue ink ballpoint pen',
                'unit_of_measure' => 'piece',
                'quantity_on_hand' => 100,
                'reorder_level' => 20,
            ],
            [
                'sku' => 'STAPLER-001',
                'name' => 'Desktop Stapler',
                'description' => 'Heavy duty desktop stapler with staples',
                'unit_of_measure' => 'piece',
                'quantity_on_hand' => 5,
                'reorder_level' => 2,
            ],
            [
                'sku' => 'FOLDER-001',
                'name' => 'Manila Folder',
                'description' => 'Letter size manila file folder',
                'unit_of_measure' => 'piece',
                'quantity_on_hand' => 25,
                'reorder_level' => 5,
            ],
            [
                'sku' => 'MARKER-001',
                'name' => 'Permanent Marker',
                'description' => 'Black permanent marker',
                'unit_of_measure' => 'piece',
                'quantity_on_hand' => 15,
                'reorder_level' => 3,
            ],
            [
                'sku' => 'NOTEBOOK-001',
                'name' => 'Spiral Notebook',
                'description' => 'A5 spiral bound notebook, 100 pages',
                'unit_of_measure' => 'piece',
                'quantity_on_hand' => 30,
                'reorder_level' => 8,
            ],
            [
                'sku' => 'TAPE-001',
                'name' => 'Scotch Tape',
                'description' => 'Clear adhesive tape, 1 inch width',
                'unit_of_measure' => 'roll',
                'quantity_on_hand' => 12,
                'reorder_level' => 3,
            ],
            [
                'sku' => 'CLIPS-001',
                'name' => 'Paper Clips',
                'description' => 'Standard size paper clips, box of 100',
                'unit_of_measure' => 'box',
                'quantity_on_hand' => 8,
                'reorder_level' => 2,
            ],
        ];

        foreach ($testItems as $item) {
            InventoryItem::firstOrCreate(
                ['sku' => $item['sku']],
                [
                    'organization_id' => $organization->id,
                    'branch_id' => $branch?->id,
                    'name' => $item['name'],
                    'description' => $item['description'],
                    'unit_of_measure' => $item['unit_of_measure'],
                    'quantity_on_hand' => $item['quantity_on_hand'],
                    'reorder_level' => $item['reorder_level'],
                ]
            );
        }

        $this->command->info('✅ Test inventory items created successfully!');
        $this->command->info('📦 Created ' . count($testItems) . ' inventory items for testing.');
        $this->command->info('🏢 Organization: ' . $organization->name);
        if ($branch) {
            $this->command->info('🏪 Branch: ' . $branch->name);
        }
    }
}
