<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Organization;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\DB;

class COASeeder extends Seeder

{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Use a transaction for data integrity
        DB::transaction(function () {
            // Check if the top-level categories exist before seeding
            if (ChartOfAccount::whereNull('organization_id')->count() == 0) {
                // Create standard top-level chart of accounts (not tied to any organization)
                ChartOfAccount::factory()->create([
                    'organization_id' => null,
                    'code' => 'AST-000',
                    'name' => 'asset',
                    'description' => 'General Chart of Asset',
                    'account_type' => 'asset',
                ]);

                ChartOfAccount::factory()->create([
                    'organization_id' => null,
                    'code' => 'LBT-000',
                    'name' => 'liability',
                    'description' => 'Account for Liability',
                    'account_type' => 'liability',
                ]);

                ChartOfAccount::factory()->create([
                    'organization_id' => null,
                    'code' => 'EQT-000',
                    'name' => 'equity',
                    'description' => 'Account for Equity',
                    'account_type' => 'equity',
                ]);

                ChartOfAccount::factory()->create([
                    'organization_id' => null,
                    'code' => 'REV-000',
                    'name' => 'revenue',
                    'description' => 'Account for Revenue',
                    'account_type' => 'revenue',
                ]);

                ChartOfAccount::factory()->create([
                    'organization_id' => null,
                    'code' => 'EXP-000',
                    'name' => 'expense',
                    'description' => 'Account for Expenses',
                    'account_type' => 'expense',
                ]);

                $this->command->info('Created shared top-level chart of accounts.');
            } else {
                $this->command->info('Shared top-level chart of accounts already exist, skipping.');
            }
        });
    }
}
