<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WorkflowTemplate;

class WorkflowTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'Basic Purchase Approval',
                'category' => 'Purchase Management',
                'description' => 'A simple two-step approval process for purchase requisitions.',
                'template_data' => [
                    'name' => 'Basic Purchase Approval',
                    'is_default' => false,
                    'description' => 'Two-step approval process for purchase requisitions',
                    'steps' => [
                        [
                            'step_number' => 1,
                            'role_id' => null, // Will be filled based on organization
                            'approver_user_id' => null,
                            'description' => 'Department Head Approval',
                        ],
                        [
                            'step_number' => 2,
                            'role_id' => null, // Will be filled based on organization
                            'approver_user_id' => null,
                            'description' => 'Finance Manager Approval',
                        ],
                    ],
                ],
                'validation_rules' => [],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Advanced Purchase Approval',
                'category' => 'Purchase Management',
                'description' => 'A comprehensive three-step approval process for high-value purchases.',
                'template_data' => [
                    'name' => 'Advanced Purchase Approval',
                    'is_default' => false,
                    'description' => 'Three-step approval process for high-value purchases',
                    'steps' => [
                        [
                            'step_number' => 1,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Department Head Approval',
                        ],
                        [
                            'step_number' => 2,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Finance Manager Approval',
                        ],
                        [
                            'step_number' => 3,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Organization Admin Final Approval',
                        ],
                    ],
                ],
                'validation_rules' => [],
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Express Approval',
                'category' => 'Purchase Management',
                'description' => 'Single-step approval for low-value or urgent purchases.',
                'template_data' => [
                    'name' => 'Express Approval',
                    'is_default' => false,
                    'description' => 'Single-step approval for urgent or low-value purchases',
                    'steps' => [
                        [
                            'step_number' => 1,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Department Head Approval',
                        ],
                    ],
                ],
                'validation_rules' => [],
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Budget Approval Workflow',
                'category' => 'Financial Management',
                'description' => 'Multi-level approval process for budget requests and modifications.',
                'template_data' => [
                    'name' => 'Budget Approval Workflow',
                    'is_default' => false,
                    'description' => 'Multi-level approval for budget requests',
                    'steps' => [
                        [
                            'step_number' => 1,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Department Head Review',
                        ],
                        [
                            'step_number' => 2,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Finance Manager Analysis',
                        ],
                        [
                            'step_number' => 3,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Organization Admin Final Approval',
                        ],
                    ],
                ],
                'validation_rules' => [],
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Leave Request Approval',
                'category' => 'Human Resources',
                'description' => 'Standard approval process for employee leave requests.',
                'template_data' => [
                    'name' => 'Leave Request Approval',
                    'is_default' => false,
                    'description' => 'Standard approval process for leave requests',
                    'steps' => [
                        [
                            'step_number' => 1,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'Direct Supervisor Approval',
                        ],
                        [
                            'step_number' => 2,
                            'role_id' => null,
                            'approver_user_id' => null,
                            'description' => 'HR Manager Final Approval',
                        ],
                    ],
                ],
                'validation_rules' => [],
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($templates as $template) {
            WorkflowTemplate::create($template);
        }
    }
}
