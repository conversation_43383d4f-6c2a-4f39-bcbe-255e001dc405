<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UpdateRolesWithApproverPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure the 'approver' permission exists
        $approverPermission = Permission::where('name', 'approver')->first();
        
        if (!$approverPermission) {
            $approverPermission = Permission::create([
                'name' => 'approver',
                'description' => 'User can approve requisitions and view requisitions they are involved in as approvers',
                'guard_name' => 'web',
            ]);
            
            $this->command->info('Created the approver permission');
        } else {
            $this->command->info('Approver permission already exists');
        }
        
        // Roles that should have the approver permission
        $rolesToUpdate = [
            'Finance Manager',
            'HOD',
            'Finance Admin',
            'Platform Admin',
            'Organization Admin'
        ];
        
        foreach ($rolesToUpdate as $roleName) {
            $roles = Role::where('name', $roleName)->get();
            
            foreach ($roles as $role) {
                if (!$role->hasPermissionTo('approver')) {
                    $role->givePermissionTo('approver');
                    $this->command->info("Added approver permission to {$roleName} role (ID: {$role->id})");
                } else {
                    $this->command->info("{$roleName} role (ID: {$role->id}) already has approver permission");
                }
            }
        }
        
        // Update users who are approvers in workflow steps
        $this->command->info('Updating users who are approvers in workflow steps...');
        
        $approverUserIds = \App\Models\ApprovalWorkflowStep::whereNotNull('approver_user_id')
            ->pluck('approver_user_id')
            ->unique();
            
        foreach ($approverUserIds as $userId) {
            $user = \App\Models\User::find($userId);
            if ($user && !$user->hasPermissionTo('approver')) {
                $user->givePermissionTo('approver');
                $this->command->info("Added approver permission to user {$user->email} (ID: {$user->id})");
            }
        }
        
        // Update users who have roles that are used in workflow steps
        $this->command->info('Updating users who have roles used in workflow steps...');
        
        $roleIds = \App\Models\ApprovalWorkflowStep::whereNull('approver_user_id')
            ->whereNotNull('role_id')
            ->pluck('role_id')
            ->unique();
            
        $usersWithRoles = \App\Models\User::whereHas('roles', function($query) use ($roleIds) {
            $query->whereIn('id', $roleIds);
        })->get();
        
        foreach ($usersWithRoles as $user) {
            if (!$user->hasPermissionTo('approver')) {
                $user->givePermissionTo('approver');
                $this->command->info("Added approver permission to user {$user->email} (ID: {$user->id}) via role");
            }
        }
        
        $this->command->info('Finished updating roles and users with approver permission');
    }
}
