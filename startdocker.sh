#!/bin/bash

# Build and start everything
docker-compose up --build -d

echo "⏳ Waiting for containers to be ready..."
sleep 10  # Give containers time to fully start

echo "Running database migrations and seeders..."
docker-compose exec app php artisan migrate:fresh --seed

echo "✅ Done! Your application should be available at:"
echo "   - http://localhost:8080 (Apache)"
echo "   - http://localhost:8000 (Laravel dev server)"
echo ""
echo "📊 Container status:"
docker-compose ps

echo ""
echo "📝 To view logs:"
echo "   docker-compose logs -f app"
