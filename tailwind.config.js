/** @type {import('tailwindcss').Config} */
export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
		"./resources/**/*.blade.php",
		"./resources/**/*.js",
		"./resources/**/*.vue",
		"*.{js,ts,jsx,tsx,mdx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				// Theme color system - using CSS variables for consistency
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",

				// Card colors
				card: {
					DEFAULT: "hsl(var(--card))",
					foreground: "hsl(var(--card-foreground))",
				},

				// Popover colors
				popover: {
					DEFAULT: "hsl(var(--popover))",
					foreground: "hsl(var(--popover-foreground))",
				},

				// Primary brand colors - CSS variables + numeric scale
				primary: {
					DEFAULT: "hsl(var(--primary))",
					foreground: "hsl(var(--primary-foreground))",
					light: "hsl(var(--primary-light))",
					dark: "hsl(var(--primary-dark))",
					// Emerald numeric scale for flexibility
					50: '#ecfdf5',
					100: '#d1fae5',
					200: '#a7f3d0',
					300: '#6ee7b7',
					400: '#34d399',
					500: '#10b981',
					600: '#059669',
					700: '#047857',
					800: '#065f46',
					900: '#064e3b',
				},

				// Secondary colors
				secondary: {
					DEFAULT: "hsl(var(--secondary))",
					foreground: "hsl(var(--secondary-foreground))",
				},

				// Muted colors for subtle elements
				muted: {
					DEFAULT: "hsl(var(--muted))",
					foreground: "hsl(var(--muted-foreground))",
				},

				// Accent colors for highlights
				accent: {
					DEFAULT: "hsl(var(--accent))",
					foreground: "hsl(var(--accent-foreground))",
				},

				// Success colors
				success: {
					DEFAULT: "hsl(var(--success))",
					light: "hsl(var(--success-light))",
					foreground: "hsl(var(--success-foreground))",
				},

				// Warning colors
				warning: {
					DEFAULT: "hsl(var(--warning))",
					light: "hsl(var(--warning-light))",
					foreground: "hsl(var(--warning-foreground))",
				},

				// Info colors
				info: {
					DEFAULT: "hsl(var(--info))",
					light: "hsl(var(--info-light))",
					foreground: "hsl(var(--info-foreground))",
				},

				// Destructive colors for errors/warnings
				destructive: {
					DEFAULT: "hsl(var(--destructive))",
					light: "hsl(var(--destructive-light))",
					foreground: "hsl(var(--destructive-foreground))",
				},

				// Status badge colors
				status: {
					draft: "var(--status-draft)",
					"draft-foreground": "var(--status-draft-foreground)",
					pending: "var(--status-pending)",
					"pending-foreground": "var(--status-pending-foreground)",
					approved: "var(--status-approved)",
					"approved-foreground": "var(--status-approved-foreground)",
					rejected: "var(--status-rejected)",
					"rejected-foreground": "var(--status-rejected-foreground)",
					issued: "var(--status-issued)",
					"issued-foreground": "var(--status-issued-foreground)",
					partial: "var(--status-partial)",
					"partial-foreground": "var(--status-partial-foreground)",
				},

				// Interface elements
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",

				// Chart colors for data visualization
				chart: {
					1: "hsl(var(--chart-1))",
					2: "hsl(var(--chart-2))",
					3: "hsl(var(--chart-3))",
					4: "hsl(var(--chart-4))",
					5: "hsl(var(--chart-5))",
				},

				// Gradient colors
				gradient: {
					from: "hsl(var(--gradient-from))",
					to: "hsl(var(--gradient-to))",
				},

				// Sidebar specific colors
				sidebar: {
					DEFAULT: "hsl(var(--sidebar-background))",
					foreground: "hsl(var(--sidebar-foreground))",
					primary: "hsl(var(--sidebar-primary))",
					"primary-foreground": "hsl(var(--sidebar-primary-foreground))",
					accent: "hsl(var(--sidebar-accent))",
					"accent-foreground": "hsl(var(--sidebar-accent-foreground))",
					border: "hsl(var(--sidebar-border))",
					ring: "hsl(var(--sidebar-ring))",
				},

				// Additional semantic colors
				surface: "hsl(var(--surface))",
				"surface-foreground": "hsl(var(--surface-foreground))",
				outline: "hsl(var(--outline))",
			},

			// Border radius system
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},

			// Typography
			fontFamily: {
				sans: [
					'Instrument Sans',
					'ui-sans-serif',
					'system-ui',
					'sans-serif',
					'Apple Color Emoji',
					'Segoe UI Emoji',
					'Segoe UI Symbol',
					'Noto Color Emoji'
				],
			},

			// Custom animations - these will be overridden by app.css
			keyframes: {
				fadeInUp: {
					from: {
						opacity: '0',
						transform: 'translateY(20px)',
					},
					to: {
						opacity: '1',
						transform: 'translateY(0)',
					},
				},
				glow: {
					'0%': {
						boxShadow: '0 0 5px rgba(16, 185, 129, 0.5)',
					},
					'50%': {
						boxShadow: '0 0 20px rgba(16, 185, 129, 0.8)',
					},
					'100%': {
						boxShadow: '0 0 5px rgba(16, 185, 129, 0.5)',
					},
				},
				"accordion-down": {
					from: { height: "0" },
					to: { height: "var(--radix-accordion-content-height)" },
				},
				"accordion-up": {
					from: { height: "var(--radix-accordion-content-height)" },
					to: { height: "0" },
				},
				"fade-in": {
					"0%": {
						opacity: "0",
						transform: "translateY(10px)"
					},
					"100%": {
						opacity: "1",
						transform: "translateY(0)"
					}
				},
				"scale-in": {
					"0%": {
						transform: "scale(0.95)",
						opacity: "0"
					},
					"100%": {
						transform: "scale(1)",
						opacity: "1"
					}
				},
			},

			animation: {
				fadeInUp: 'fadeInUp 0.8s ease-out forwards',
				glow: 'glow 1.5s infinite',
				"accordion-down": "accordion-down 0.2s ease-out",
				"accordion-up": "accordion-up 0.2s ease-out",
				"fade-in": "fade-in 0.3s ease-out",
				"scale-in": "scale-in 0.2s ease-out",
			},

			// Box shadows for depth
			boxShadow: {
				'glow': '0 0 5px rgba(16, 185, 129, 0.5)',
				'glow-lg': '0 0 20px rgba(16, 185, 129, 0.8)',
			},
		},
	},
	plugins: [
		require("tailwindcss-animate"),
		require('@tailwindcss/forms'),
		require('@tailwindcss/line-clamp'),
	],
};