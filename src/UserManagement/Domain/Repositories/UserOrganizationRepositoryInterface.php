<?php

namespace Src\UserManagement\Domain\Repositories;

use App\Models\UserOrganization;

interface UserOrganizationRepositoryInterface
{
    /**
     * Create a user-organization relationship.
     *
     * @param array $data
     * @return UserOrganization
     */
    public function create(array $data): UserOrganization;

    /**
     * Find or create a user-organization relationship.
     *
     * @param array $attributes
     * @param array $values
     * @return UserOrganization
     */
    public function firstOrCreate(array $attributes, array $values = []): UserOrganization;

    /**
     * Get organization ID for a user.
     *
     * @param int $userId
     * @return int|null
     */
    public function getOrganizationIdByUserId(int $userId): ?int;

    /**
     * Check if user belongs to organization.
     *
     * @param int $userId
     * @param int $organizationId
     * @return bool
     */
    public function userBelongsToOrganization(int $userId, int $organizationId): bool;

    /**
     * Remove user from organization.
     *
     * @param int $userId
     * @param int $organizationId
     * @return bool
     */
    public function removeUserFromOrganization(int $userId, int $organizationId): bool;
}
