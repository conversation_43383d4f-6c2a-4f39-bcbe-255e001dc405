<?php

namespace Src\UserManagement\Application\DTOs;

class CreateOrganizationAdminDTO
{
    public function __construct(
        public readonly string $username,
        public readonly string $firstName,
        public readonly string $lastName,
        public readonly string $email,
        public readonly string $phone,
        public readonly string $password,
        public readonly int $organizationId,
        public readonly bool $sendNotification = true,
        public readonly bool $autoVerifyEmail = true,
        public readonly bool $createDefaultStructures = true
    ) {}

    /**
     * Create DTO from array data.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            username: $data['admin_username'] ?? $data['username'],
            firstName: $data['admin_first_name'] ?? $data['first_name'] ?? $data['username'] ?? '',
            lastName: $data['admin_last_name'] ?? $data['last_name'] ?? $data['username'] ?? '',
            email: $data['admin_email'] ?? $data['email'],
            phone: $data['admin_phone'] ?? $data['phone'] ?? '',
            password: $data['admin_password'] ?? $data['password'],
            organizationId: $data['organization_id'],
            sendNotification: $data['send_notification'] ?? true,
            autoVerifyEmail: $data['auto_verify_email'] ?? true,
            createDefaultStructures: $data['create_default_structures'] ?? true
        );
    }

    /**
     * Convert to CreateUserDTO.
     *
     * @return CreateUserDTO
     */
    public function toCreateUserDTO(): CreateUserDTO
    {
        return new CreateUserDTO(
            username: $this->username,
            email: $this->email,
            password: $this->password,
            firstName: $this->firstName,
            lastName: $this->lastName,
            phone: $this->phone,
            isPlatformAdmin: false,
            status: 'active',
            organizationId: $this->organizationId,
            roleName: 'Organization Admin',
            sendNotification: $this->sendNotification,
            autoVerifyEmail: $this->autoVerifyEmail
        );
    }
}
