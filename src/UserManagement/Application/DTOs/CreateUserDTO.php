<?php

namespace Src\UserManagement\Application\DTOs;

class CreateUserDTO
{
    public function __construct(
        public readonly string $username,
        public readonly string $email,
        public readonly string $password,
        public readonly ?string $firstName = null,
        public readonly ?string $lastName = null,
        public readonly ?string $phone = null,
        public readonly bool $isPlatformAdmin = false,
        public readonly string $status = 'active',
        public readonly ?int $organizationId = null,
        public readonly ?string $roleName = null,
        public readonly ?int $roleId = null,
        public readonly ?int $branchId = null,
        public readonly ?int $departmentId = null,
        public readonly bool $sendNotification = true,
        public readonly bool $autoVerifyEmail = true
    ) {}

    /**
     * Create DTO from array data.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            username: $data['username'],
            email: $data['email'],
            password: $data['password'],
            firstName: $data['first_name'] ?? null,
            lastName: $data['last_name'] ?? null,
            phone: $data['phone'] ?? null,
            isPlatformAdmin: $data['is_platform_admin'] ?? false,
            status: $data['status'] ?? 'active',
            organizationId: $data['organization_id'] ?? null,
            roleName: $data['role_name'] ?? null,
            roleId: $data['role_id'] ?? null,
            branchId: $data['branch_id'] ?? null,
            departmentId: $data['department_id'] ?? null,
            sendNotification: $data['send_notification'] ?? true,
            autoVerifyEmail: $data['auto_verify_email'] ?? true
        );
    }

    /**
     * Convert DTO to array for user creation.
     *
     * @return array
     */
    public function toUserArray(): array
    {
        return [
            'username' => $this->username,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'email' => $this->email,
            'phone' => $this->phone,
            'password' => $this->password, // Should be hashed before calling this
            'is_platform_admin' => $this->isPlatformAdmin,
            'status' => $this->status,
            'email_verified_at' => $this->autoVerifyEmail ? now() : null,
        ];
    }
}
