<?php

namespace Src\UserManagement\Application\Services;

use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Department;
use App\Models\UserBranch;
use App\Models\UserDepartment;
use App\Models\Organization;
use App\Models\ApprovalWorkflow;
use App\Notifications\UserOnboardingNotification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Src\UserManagement\Domain\Repositories\UserRepositoryInterface;
use Src\UserManagement\Domain\Repositories\UserOrganizationRepositoryInterface;
use Src\UserManagement\Application\DTOs\CreateUserDTO;
use Src\UserManagement\Application\DTOs\CreateOrganizationAdminDTO;

class UserCreationService
{
    public function __construct(
        private readonly UserRepositoryInterface $userRepository,
        private readonly UserOrganizationRepositoryInterface $userOrganizationRepository
    ) {}

    /**
     * Create a regular user within an organization.
     *
     * @param CreateUserDTO $dto
     * @param User|null $createdBy
     * @return User
     * @throws \Exception
     */
    public function createUser(CreateUserDTO $dto, ?User $createdBy = null): User
    {
        return DB::transaction(function () use ($dto, $createdBy) {
            // Validate organization context
            if (!$dto->organizationId && $createdBy) {
                $organizationId = $this->userOrganizationRepository->getOrganizationIdByUserId($createdBy->id);
                if (!$organizationId) {
                    throw new \Exception('Unable to determine organization. Please contact administrator.');
                }
                $dto = new CreateUserDTO(
                    username: $dto->username,
                    email: $dto->email,
                    password: $dto->password,
                    firstName: $dto->firstName,
                    lastName: $dto->lastName,
                    phone: $dto->phone,
                    isPlatformAdmin: $dto->isPlatformAdmin,
                    status: $dto->status,
                    organizationId: $organizationId,
                    roleName: $dto->roleName,
                    roleId: $dto->roleId,
                    branchId: $dto->branchId,
                    departmentId: $dto->departmentId,
                    sendNotification: $dto->sendNotification,
                    autoVerifyEmail: $dto->autoVerifyEmail
                );
            }

            if (!$dto->organizationId) {
                throw new \Exception('Organization ID is required for user creation.');
            }

            // Create the user
            $userData = $dto->toUserArray();
            $userData['password'] = Hash::make($dto->password);

            $user = $this->userRepository->create($userData);

            // Create user-organization relationship
            $this->userOrganizationRepository->firstOrCreate([
                'user_id' => $user->id,
                'organization_id' => $dto->organizationId,
            ]);

            // Assign role if specified
            if ($dto->roleName || $dto->roleId) {
                $this->assignRoleToUser($user, $dto->organizationId, $dto->roleName, $dto->roleId);
            }

            // Create default organizational relationships
            $this->createDefaultRelationships($user, $dto->organizationId, $dto->branchId, $dto->departmentId);

            // Send notification if requested
            if ($dto->sendNotification && $createdBy) {
                $user->notify(new UserOnboardingNotification($user, $dto->password, $createdBy));
            }

            return $user;
        });
    }

    /**
     * Create an organization admin user.
     *
     * @param CreateOrganizationAdminDTO $dto
     * @return User
     * @throws \Exception
     */
    public function createOrganizationAdmin(CreateOrganizationAdminDTO $dto): User
    {
        return DB::transaction(function () use ($dto) {
            // Convert to CreateUserDTO
            $userDTO = $dto->toCreateUserDTO();

            // Create the user with hashed password
            $userData = $userDTO->toUserArray();
            $userData['password'] = Hash::make($dto->password);

            $user = $this->userRepository->create($userData);

            // Create user-organization relationship
            $this->userOrganizationRepository->firstOrCreate([
                'user_id' => $user->id,
                'organization_id' => $dto->organizationId,
            ]);

            // Assign Organization Admin role
            $this->assignOrganizationAdminRole($user, $dto->organizationId);

            // Always create basic approval workflow for organization admins
            $this->createDefaultApprovalWorkflow($dto->organizationId);

            if ($dto->createDefaultStructures) {
                // Create default organizational structures and relationships
                $this->createDefaultOrganizationalStructures($user, $dto->organizationId);
            }

            return $user;
        });
    }

    /**
     * Assign a role to a user.
     *
     * @param User $user
     * @param int $organizationId
     * @param string|null $roleName
     * @param int|null $roleId
     * @return void
     * @throws \Exception
     */
    private function assignRoleToUser(User $user, int $organizationId, ?string $roleName = null, ?int $roleId = null): void
    {
        $role = null;

        if ($roleId) {
            $role = Role::find($roleId);
        } elseif ($roleName) {
            // Find organization-specific role first, then fall back to template role
            $role = Role::where('name', $roleName)
                ->where('organization_id', $organizationId)
                ->first();

            if (!$role) {
                $role = Role::where('name', $roleName)
                    ->whereNull('organization_id')
                    ->first();
            }
        }

        if (!$role) {
            throw new \Exception('Role not found.');
        }

        // Special handling for Organization Admin role
        if ($role->name === 'Organization Admin') {
            $this->assignOrganizationAdminRole($user, $organizationId);
        } else {
            $user->assignRole($role);
        }
    }

    /**
     * Assign Organization Admin role to a user.
     *
     * @param User $user
     * @param int $organizationId
     * @return void
     * @throws \Exception
     */
    private function assignOrganizationAdminRole(User $user, int $organizationId): void
    {
        // Find the Organization Admin template role
        $orgAdminRole = Role::where('name', 'Organization Admin')
            ->whereNull('organization_id')
            ->first();

        if (!$orgAdminRole) {
            throw new \Exception('Organization Admin role template not found.');
        }

        // Clone the role for this specific organization
        $organizationSpecificRole = Role::create([
            'name' => 'Organization Admin',
            'guard_name' => 'web',
            'organization_id' => $organizationId,
            'description' => 'Administrator for organization ID ' . $organizationId,
            'is_active' => true,
        ]);

        // Copy permissions from the template role
        $permissions = $orgAdminRole->permissions;
        $organizationSpecificRole->syncPermissions($permissions);

        // Assign the role to the user
        $user->assignRole($organizationSpecificRole);

        // Create a Regular User (Employee) role for this organization if it doesn't exist
        $this->createEmployeeRoleForOrganization($organizationId);

        // Create a generic HOD role for this organization if it doesn't exist
        $this->createGenericHodRoleForOrganization($organizationId);
    }

    /**
     * Create Employee role for organization if it doesn't exist.
     *
     * @param int $organizationId
     * @return void
     */
    private function createEmployeeRoleForOrganization(int $organizationId): void
    {
        $employeeRoleTemplate = Role::where('name', 'Employee')
            ->whereNull('organization_id')
            ->first();

        if ($employeeRoleTemplate) {
            $existingEmployeeRole = Role::where('name', 'Employee')
                ->where('organization_id', $organizationId)
                ->first();

            if (!$existingEmployeeRole) {
                $organizationEmployeeRole = Role::create([
                    'name' => 'Employee',
                    'guard_name' => 'web',
                    'organization_id' => $organizationId,
                    'description' => 'Regular employee for organization ID ' . $organizationId,
                    'is_active' => true,
                ]);

                // Copy permissions from the template role
                $permissions = $employeeRoleTemplate->permissions;
                $organizationEmployeeRole->syncPermissions($permissions);
            }
        }
    }

    /**
     * Create generic HOD role for organization if it doesn't exist.
     *
     * @param int $organizationId
     * @return void
     */
    private function createGenericHodRoleForOrganization(int $organizationId): void
    {
        $hodRoleTemplate = Role::where('name', 'HOD')
            ->whereNull('organization_id')
            ->first();

        if ($hodRoleTemplate) {
            $existingGenericHodRole = Role::where('name', 'HOD')
                ->where('organization_id', $organizationId)
                ->whereNull('department_id')
                ->first();

            if (!$existingGenericHodRole) {
                $organizationGenericHodRole = Role::create([
                    'name' => 'HOD',
                    'guard_name' => 'web',
                    'organization_id' => $organizationId,
                    'department_id' => null,
                    'description' => 'Head of Department (Generic) for organization ID ' . $organizationId,
                    'is_active' => true,
                ]);

                // Copy permissions from the template role
                $permissions = $hodRoleTemplate->permissions;
                $organizationGenericHodRole->syncPermissions($permissions);
            }
        }
    }

    /**
     * Create default organizational relationships for a user.
     *
     * @param User $user
     * @param int $organizationId
     * @param int|null $branchId
     * @param int|null $departmentId
     * @return void
     */
    private function createDefaultRelationships(User $user, int $organizationId, ?int $branchId = null, ?int $departmentId = null): void
    {
        // Find or create default branch if not specified
        if (!$branchId) {
            $branch = Branch::firstOrCreate(
                [
                    'organization_id' => $organizationId,
                    'name' => 'default',
                ],
                [
                    'is_active' => true,
                ]
            );
            $branchId = $branch->id;
        }

        // Create user-branch relationship
        UserBranch::firstOrCreate([
            'user_id' => $user->id,
            'branch_id' => $branchId,
        ]);

        // Find or create default department if not specified
        if (!$departmentId) {
            $department = Department::firstOrCreate(
                [
                    'organization_id' => $organizationId,
                    'branch_id' => $branchId,
                    'name' => 'default',
                ]
            );
            $departmentId = $department->id;
        }

        // Create user-department relationship
        UserDepartment::firstOrCreate([
            'user_id' => $user->id,
            'department_id' => $departmentId,
        ]);
    }

    /**
     * Create default organizational structures for organization admin.
     *
     * @param User $user
     * @param int $organizationId
     * @return void
     */
    private function createDefaultOrganizationalStructures(User $user, int $organizationId): void
    {
        // Create default branch
        $branch = Branch::firstOrCreate(
            [
                'organization_id' => $organizationId,
                'name' => 'default',
            ],
            [
                'is_active' => true,
            ]
        );

        // Create user-branch relationship
        UserBranch::firstOrCreate([
            'user_id' => $user->id,
            'branch_id' => $branch->id,
        ]);

        // Create default department
        $department = Department::firstOrCreate(
            [
                'organization_id' => $organizationId,
                'branch_id' => $branch->id,
                'name' => 'default',
            ]
        );

        // Create user-department relationship
        UserDepartment::firstOrCreate([
            'user_id' => $user->id,
            'department_id' => $department->id,
        ]);
    }

    /**
     * Create default approval workflow for organization.
     *
     * @param int $organizationId
     * @return void
     */
    private function createDefaultApprovalWorkflow(int $organizationId): void
    {
        // Get the organization-specific roles
        $organizationAdminRole = Role::where('name', 'Organization Admin')
            ->where('organization_id', $organizationId)
            ->first();

        $employeeRole = Role::where('name', 'Employee')
            ->where('organization_id', $organizationId)
            ->first();

        // Only create workflow if Organization Admin role exists
        // Employee role will be created later through the setup wizard
        if ($organizationAdminRole) {
            $organization = Organization::find($organizationId);

            $defaultWorkflow = ApprovalWorkflow::create([
                'name' => 'Default Approval Workflow',
                'description' => 'Default approval workflow for ' . ($organization ? $organization->name : 'organization'),
                'organization_id' => $organizationId,
                'is_default' => true,
            ]);

            // Create workflow steps - if Employee role doesn't exist yet, create a single-step workflow
            $steps = [
                [
                    'step_number' => 1,
                    'role_id' => $organizationAdminRole->id,
                    'description' => 'Approval by Organization Admin'
                ]
            ];

            // If Employee role exists, add it as second step
            if ($employeeRole) {
                $steps[] = [
                    'step_number' => 2,
                    'role_id' => $employeeRole->id,
                    'description' => 'Final approval by Department Head'
                ];
            }

            $defaultWorkflow->steps()->createMany($steps);
        }
    }
}
