<?php

namespace Src\UserManagement\Infrastructure\Repositories;

use App\Models\UserOrganization;
use Src\UserManagement\Domain\Repositories\UserOrganizationRepositoryInterface;
use Illuminate\Support\Facades\DB;

class EloquentUserOrganizationRepository implements UserOrganizationRepositoryInterface
{
    /**
     * Create a user-organization relationship.
     *
     * @param array $data
     * @return UserOrganization
     */
    public function create(array $data): UserOrganization
    {
        return UserOrganization::create($data);
    }

    /**
     * Find or create a user-organization relationship.
     *
     * @param array $attributes
     * @param array $values
     * @return UserOrganization
     */
    public function firstOrCreate(array $attributes, array $values = []): UserOrganization
    {
        return UserOrganization::firstOrCreate($attributes, $values);
    }

    /**
     * Get organization ID for a user.
     *
     * @param int $userId
     * @return int|null
     */
    public function getOrganizationIdByUserId(int $userId): ?int
    {
        return DB::table('user_organizations')
            ->where('user_id', $userId)
            ->value('organization_id');
    }

    /**
     * Check if user belongs to organization.
     *
     * @param int $userId
     * @param int $organizationId
     * @return bool
     */
    public function userBelongsToOrganization(int $userId, int $organizationId): bool
    {
        return UserOrganization::where('user_id', $userId)
            ->where('organization_id', $organizationId)
            ->exists();
    }

    /**
     * Remove user from organization.
     *
     * @param int $userId
     * @param int $organizationId
     * @return bool
     */
    public function removeUserFromOrganization(int $userId, int $organizationId): bool
    {
        return UserOrganization::where('user_id', $userId)
            ->where('organization_id', $organizationId)
            ->delete() > 0;
    }
}
