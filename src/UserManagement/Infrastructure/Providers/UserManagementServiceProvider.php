<?php

namespace Src\UserManagement\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use Src\UserManagement\Application\Services\UserCreationService;
use Src\UserManagement\Domain\Repositories\UserRepositoryInterface;
use Src\UserManagement\Domain\Repositories\UserOrganizationRepositoryInterface;
use Src\UserManagement\Infrastructure\Repositories\EloquentUserRepository;
use Src\UserManagement\Infrastructure\Repositories\EloquentUserOrganizationRepository;

class UserManagementServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(
            UserRepositoryInterface::class,
            EloquentUserRepository::class
        );

        $this->app->bind(
            UserOrganizationRepositoryInterface::class,
            EloquentUserOrganizationRepository::class
        );

        // Register the service
        $this->app->bind(UserCreationService::class, function ($app) {
            return new UserCreationService(
                $app->make(UserRepositoryInterface::class),
                $app->make(UserOrganizationRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Routes are defined in routes/web.php to keep all framework-specific code together
        // and separate from the domain logic.
    }
}
