<?php

namespace Src\ChartOfAccounts\Domain\Repositories;

use App\Models\ChartOfAccount;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Interface ChartOfAccountRepositoryInterface
 *
 * This interface defines the contract for chart of accounts data access.
 */
interface ChartOfAccountRepositoryInterface
{
    /**
     * Get all chart of accounts with optional filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllWithFilters(array $filters, int $perPage = 10): LengthAwarePaginator;

    /**
     * Get all chart of accounts for an organization.
     *
     * @param int $organizationId
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getByOrganizationId(int $organizationId, array $filters = [], int $perPage = 10): LengthAwarePaginator;

    /**
     * Find a chart of account by its ID.
     *
     * @param int $id
     * @return ChartOfAccount|null
     */
    public function findById(int $id): ?ChartOfAccount;

    /**
     * Create a new chart of account.
     *
     * @param array $data
     * @return ChartOfAccount
     */
    public function create(array $data): ChartOfAccount;

    /**
     * Update an existing chart of account.
     *
     * @param int $id
     * @param array $data
     * @return ChartOfAccount|null
     */
    public function update(int $id, array $data): ?ChartOfAccount;

    /**
     * Delete a chart of account.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get all parent chart of accounts for an organization.
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getParentAccountsByOrganizationId(int $organizationId);

    /**
     * Get all chart of accounts for an organization as a hierarchical structure.
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getHierarchicalAccountsByOrganizationId(int $organizationId);
}
