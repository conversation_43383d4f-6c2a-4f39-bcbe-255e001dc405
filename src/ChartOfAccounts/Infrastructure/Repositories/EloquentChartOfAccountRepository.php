<?php

namespace Src\ChartOfAccounts\Infrastructure\Repositories;

use App\Models\ChartOfAccount;
use Src\ChartOfAccounts\Domain\Repositories\ChartOfAccountRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class EloquentChartOfAccountRepository implements ChartOfAccountRepositoryInterface
{
    /**
     * @var ChartOfAccount
     */
    protected $model;

    /**
     * EloquentChartOfAccountRepository constructor.
     *
     * @param ChartOfAccount $model
     */
    public function __construct(ChartOfAccount $model)
    {
        $this->model = $model;
    }

    /**
     * Get all chart of accounts with optional filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllWithFilters(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        $query = $this->model->with(['organization:id,name', 'branch:id,name', 'parent:id,name']);

        // Apply filters
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if (isset($filters['account_type'])) {
            $query->where('account_type', $filters['account_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['organization_id'])) {
            $query->where('organization_id', $filters['organization_id']);
        }

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (isset($filters['parent_id'])) {
            $query->where('parent_id', $filters['parent_id']);
        }

        // Apply sorting
        $sortField = $filters['sort'] ?? 'name';
        $sortDirection = $filters['direction'] ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get all chart of accounts for an organization.
     *
     * @param int $organizationId
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getByOrganizationId(int $organizationId, array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        $filters['organization_id'] = $organizationId;
        return $this->getAllWithFilters($filters, $perPage);
    }

    /**
     * Find a chart of account by its ID.
     *
     * @param int $id
     * @return ChartOfAccount|null
     */
    public function findById(int $id): ?ChartOfAccount
    {
        return $this->model->with(['organization:id,name', 'branch:id,name', 'parent:id,name,code', 'children:id,name,code,parent_id'])->find($id);
    }

    /**
     * Create a new chart of account.
     *
     * @param array $data
     * @return ChartOfAccount
     */
    public function create(array $data): ChartOfAccount
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing chart of account.
     *
     * @param int $id
     * @param array $data
     * @return ChartOfAccount|null
     */
    public function update(int $id, array $data): ?ChartOfAccount
    {
        $chartOfAccount = $this->findById($id);
        if (!$chartOfAccount) {
            return null;
        }

        $chartOfAccount->update($data);
        return $chartOfAccount->fresh();
    }

    /**
     * Delete a chart of account.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $chartOfAccount = $this->findById($id);
        if (!$chartOfAccount) {
            return false;
        }

        return $chartOfAccount->delete();
    }

    /**
     * Get all parent chart of accounts for an organization.
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getParentAccountsByOrganizationId(int $organizationId)
    {
        return $this->model
            ->where('organization_id', $organizationId)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get all chart of accounts for an organization as a hierarchical structure.
     *
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getHierarchicalAccountsByOrganizationId(int $organizationId)
    {
        // Get all parent accounts
        $parents = $this->getParentAccountsByOrganizationId($organizationId);
        
        // Load children for each parent
        $parents->load(['children' => function ($query) {
            $query->orderBy('name');
        }]);
        
        return $parents;
    }
}
