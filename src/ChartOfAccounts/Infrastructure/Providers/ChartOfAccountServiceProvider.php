<?php

namespace Src\ChartOfAccounts\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use Src\ChartOfAccounts\Domain\Repositories\ChartOfAccountRepositoryInterface;
use Src\ChartOfAccounts\Infrastructure\Repositories\EloquentChartOfAccountRepository;
use Src\ChartOfAccounts\Application\Services\ChartOfAccountService;

class ChartOfAccountServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the repository
        $this->app->bind(
            ChartOfAccountRepositoryInterface::class,
            EloquentChartOfAccountRepository::class
        );

        // Register the service
        $this->app->bind(ChartOfAccountService::class, function ($app) {
            return new ChartOfAccountService(
                $app->make(ChartOfAccountRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * Note: Routes are defined in routes/web.php to keep all framework-specific code together
     * and separate from the domain logic.
     */
    public function boot(): void
    {
        // Routes are defined in routes/web.php
    }
}
