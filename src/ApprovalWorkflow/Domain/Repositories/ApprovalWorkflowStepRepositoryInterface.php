<?php

namespace Src\ApprovalWorkflow\Domain\Repositories;

use App\Models\ApprovalWorkflowStep;

interface ApprovalWorkflowStepRepositoryInterface
{
    /**
     * Get all steps for a workflow.
     *
     * @param int $workflowId
     * @return mixed
     */
    public function getByWorkflowId(int $workflowId);

    /**
     * Find a step by ID.
     *
     * @param int $id
     * @return ApprovalWorkflowStep|null
     */
    public function findById(int $id): ?ApprovalWorkflowStep;

    /**
     * Create a new workflow step.
     *
     * @param array $data
     * @return ApprovalWorkflowStep
     */
    public function create(array $data): ApprovalWorkflowStep;

    /**
     * Update a workflow step.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a workflow step.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get the first step of a workflow.
     *
     * @param int $workflowId
     * @return ApprovalWorkflowStep|null
     */
    public function getFirstStep(int $workflowId): ?ApprovalWorkflowStep;

    /**
     * Get the next step in a workflow.
     *
     * @param int $workflowId
     * @param int $currentStepNumber
     * @return ApprovalWorkflowStep|null
     */
    public function getNextStep(int $workflowId, int $currentStepNumber): ?ApprovalWorkflowStep;

    /**
     * Get the previous step in a workflow.
     *
     * @param int $workflowId
     * @param int $currentStepNumber
     * @return ApprovalWorkflowStep|null
     */
    public function getPreviousStep(int $workflowId, int $currentStepNumber): ?ApprovalWorkflowStep;
}
