<?php

namespace Src\ApprovalWorkflow\Domain\Repositories;

use App\Models\ApprovalWorkflow;

interface ApprovalWorkflowRepositoryInterface
{
    /**
     * Get all approval workflows.
     *
     * @return mixed
     */
    public function getAll();

    /**
     * Get approval workflows by organization ID.
     *
     * @param int $organizationId
     * @return mixed
     */
    public function getByOrganizationId(int $organizationId);

    /**
     * Find an approval workflow by ID.
     *
     * @param int $id
     * @return ApprovalWorkflow|null
     */
    public function findById(int $id): ?ApprovalWorkflow;

    /**
     * Create a new approval workflow.
     *
     * @param array $data
     * @return ApprovalWorkflow
     */
    public function create(array $data): ApprovalWorkflow;

    /**
     * Update an approval workflow.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete an approval workflow.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Find the appropriate workflow for a department, branch, or organization.
     *
     * @param int $organizationId
     * @param int|null $branchId
     * @param int|null $departmentId
     * @return ApprovalWorkflow|null
     */
    public function findWorkflowForContext(int $organizationId, ?int $branchId = null, ?int $departmentId = null): ?ApprovalWorkflow;

    /**
     * Set a workflow as default for its context (organization, branch, department).
     *
     * @param int $id
     * @return bool
     */
    public function setAsDefault(int $id): bool;
}
