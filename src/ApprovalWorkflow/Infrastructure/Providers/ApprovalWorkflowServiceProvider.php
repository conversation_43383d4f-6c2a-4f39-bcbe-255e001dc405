<?php

namespace Src\ApprovalWorkflow\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use Src\ApprovalWorkflow\Application\Services\ApprovalWorkflowService;
use App\Models\ApprovalWorkflow;
use App\Models\ApprovalWorkflowStep;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface;
use Src\ApprovalWorkflow\Infrastructure\Repositories\EloquentApprovalWorkflowRepository;
use Src\ApprovalWorkflow\Infrastructure\Repositories\EloquentApprovalWorkflowStepRepository;

class ApprovalWorkflowServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register services
        $this->app->bind(ApprovalWorkflowService::class, function ($app) {
            return new ApprovalWorkflowService(
                $app->make(ApprovalWorkflowRepositoryInterface::class),
                $app->make(ApprovalWorkflowStepRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // No need to load routes as they are defined in web.php
    }
}
