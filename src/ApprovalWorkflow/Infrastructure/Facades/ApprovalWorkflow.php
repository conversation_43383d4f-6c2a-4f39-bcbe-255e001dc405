<?php

namespace Src\ApprovalWorkflow\Infrastructure\Facades;

use Illuminate\Support\Facades\Facade;
use Src\ApprovalWorkflow\Application\Services\ApprovalWorkflowService;

/**
 * @method static array findWorkflowAndStep(int $organizationId, ?int $branchId = null, ?int $departmentId = null)
 * @method static int findApproverForStep(int $stepId, int $organizationId, int $fallbackUserId)
 * @method static bool createApprovalRecord(int $requisitionId, int $stepId, int $approverId, string $action, string $comments)
 * @method static object|null getNextStep(int $workflowId, int $currentStepNumber)
 * 
 * @see \Src\ApprovalWorkflow\Application\Services\ApprovalWorkflowService
 */
class ApprovalWorkflow extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return ApprovalWorkflowService::class;
    }
}
