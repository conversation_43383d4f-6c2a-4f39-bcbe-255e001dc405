<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApprovalWorkflowController;

Route::middleware(['auth', 'verified'])->group(function () {
    // Define routes explicitly to avoid model binding issues
    Route::get('approval-workflows', [ApprovalWorkflowController::class, 'index'])->name('approval-workflows.index');
    Route::get('approval-workflows/create', [ApprovalWorkflowController::class, 'create'])->name('approval-workflows.create');
    Route::post('approval-workflows', [ApprovalWorkflowController::class, 'store'])->name('approval-workflows.store');
    Route::get('approval-workflows/{id}', [ApprovalWorkflowController::class, 'show'])->name('approval-workflows.show');
    Route::get('approval-workflows/{id}/edit', [ApprovalWorkflowController::class, 'edit'])->name('approval-workflows.edit');
    Route::put('approval-workflows/{id}', [ApprovalWorkflowController::class, 'update'])->name('approval-workflows.update');
    Route::delete('approval-workflows/{id}', [ApprovalWorkflowController::class, 'destroy'])->name('approval-workflows.destroy');
});
