<?php

namespace Src\ApprovalWorkflow\Infrastructure\Repositories;

use App\Models\ApprovalWorkflowStep;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface;

class EloquentApprovalWorkflowStepRepository implements ApprovalWorkflowStepRepositoryInterface
{
    /**
     * @var ApprovalWorkflowStep
     */
    protected $model;

    /**
     * EloquentApprovalWorkflowStepRepository constructor.
     *
     * @param ApprovalWorkflowStep $model
     */
    public function __construct(ApprovalWorkflowStep $model)
    {
        $this->model = $model;
    }

    /**
     * Get all steps for a workflow.
     *
     * @param int $workflowId
     * @return mixed
     */
    public function getByWorkflowId(int $workflowId)
    {
        return $this->model->where('approval_workflow_id', $workflowId)
            ->orderBy('step_number')
            ->get();
    }

    /**
     * Find a step by ID.
     *
     * @param int $id
     * @return ApprovalWorkflowStep|null
     */
    public function findById(int $id): ?ApprovalWorkflowStep
    {
        return $this->model->find($id);
    }

    /**
     * Create a new workflow step.
     *
     * @param array $data
     * @return ApprovalWorkflowStep
     */
    public function create(array $data): ApprovalWorkflowStep
    {
        return $this->model->create($data);
    }

    /**
     * Update a workflow step.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $step = $this->findById($id);
        if (!$step) {
            return false;
        }

        return $step->update($data);
    }

    /**
     * Delete a workflow step.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $step = $this->findById($id);
        if (!$step) {
            return false;
        }

        return $step->delete();
    }

    /**
     * Get the first step of a workflow.
     *
     * @param int $workflowId
     * @return ApprovalWorkflowStep|null
     */
    public function getFirstStep(int $workflowId): ?ApprovalWorkflowStep
    {
        return $this->model->where('approval_workflow_id', $workflowId)
            ->orderBy('step_number')
            ->first();
    }

    /**
     * Get the next step in a workflow.
     *
     * @param int $workflowId
     * @param int $currentStepNumber
     * @return ApprovalWorkflowStep|null
     */
    public function getNextStep(int $workflowId, int $currentStepNumber): ?ApprovalWorkflowStep
    {
        return $this->model->where('approval_workflow_id', $workflowId)
            ->where('step_number', '>', $currentStepNumber)
            ->orderBy('step_number')
            ->first();
    }

    /**
     * Get the previous step in a workflow.
     *
     * @param int $workflowId
     * @param int $currentStepNumber
     * @return ApprovalWorkflowStep|null
     */
    public function getPreviousStep(int $workflowId, int $currentStepNumber): ?ApprovalWorkflowStep
    {
        return $this->model->where('approval_workflow_id', $workflowId)
            ->where('step_number', '<', $currentStepNumber)
            ->orderBy('step_number', 'desc')
            ->first();
    }
}
