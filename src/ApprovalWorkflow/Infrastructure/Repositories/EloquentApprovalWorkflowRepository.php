<?php

namespace Src\ApprovalWorkflow\Infrastructure\Repositories;

use App\Models\ApprovalWorkflow;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface;
use Illuminate\Support\Facades\DB;

class EloquentApprovalWorkflowRepository implements ApprovalWorkflowRepositoryInterface
{
    /**
     * @var ApprovalWorkflow
     */
    protected $model;

    /**
     * EloquentApprovalWorkflowRepository constructor.
     *
     * @param ApprovalWorkflow $model
     */
    public function __construct(ApprovalWorkflow $model)
    {
        $this->model = $model;
    }

    /**
     * Get all approval workflows.
     *
     * @return mixed
     */
    public function getAll()
    {
        return $this->model->with(['organization:id,name', 'branch:id,name', 'department:id,name'])->get();
    }

    /**
     * Get approval workflows by organization ID.
     *
     * @param int $organizationId
     * @return mixed
     */
    public function getByOrganizationId(int $organizationId)
    {
        return $this->model->where('organization_id', $organizationId)
            ->with(['branch:id,name', 'department:id,name'])
            ->get();
    }

    /**
     * Find an approval workflow by ID.
     *
     * @param int $id
     * @return ApprovalWorkflow|null
     */
    public function findById(int $id): ?ApprovalWorkflow
    {
        return $this->model->with(['organization:id,name', 'branch:id,name', 'department:id,name'])->find($id);
    }

    /**
     * Create a new approval workflow.
     *
     * @param array $data
     * @return ApprovalWorkflow
     */
    public function create(array $data): ApprovalWorkflow
    {
        return $this->model->create($data);
    }

    /**
     * Update an approval workflow.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $workflow = $this->findById($id);
        if (!$workflow) {
            return false;
        }

        return $workflow->update($data);
    }

    /**
     * Delete an approval workflow.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $workflow = $this->findById($id);
        if (!$workflow) {
            return false;
        }

        return $workflow->delete();
    }

    /**
     * Find the appropriate workflow for a department, branch, or organization.
     *
     * @param int $organizationId
     * @param int|null $branchId
     * @param int|null $departmentId
     * @return ApprovalWorkflow|null
     */
    public function findWorkflowForContext(int $organizationId, ?int $branchId = null, ?int $departmentId = null): ?ApprovalWorkflow
    {
        // First try to find default workflows at each level

        // Try to find a department-specific default workflow
        if ($departmentId) {
            $workflow = $this->model->where('organization_id', $organizationId)
                ->where('department_id', $departmentId)
                ->where('is_default', true)
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Try to find a branch-specific default workflow
        if ($branchId) {
            $workflow = $this->model->where('organization_id', $organizationId)
                ->where('branch_id', $branchId)
                ->whereNull('department_id')
                ->where('is_default', true)
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Try to find an organization-level default workflow
        $workflow = $this->model->where('organization_id', $organizationId)
            ->whereNull('branch_id')
            ->whereNull('department_id')
            ->where('is_default', true)
            ->first();

        if ($workflow) {
            return $workflow;
        }

        // If no default workflows found, try to find any workflow at each level

        // Try to find any department-specific workflow
        if ($departmentId) {
            $workflow = $this->model->where('organization_id', $organizationId)
                ->where('department_id', $departmentId)
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Try to find any branch-specific workflow
        if ($branchId) {
            $workflow = $this->model->where('organization_id', $organizationId)
                ->where('branch_id', $branchId)
                ->whereNull('department_id')
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Fall back to any organization-level workflow
        return $this->model->where('organization_id', $organizationId)
            ->whereNull('branch_id')
            ->whereNull('department_id')
            ->first();
    }

    /**
     * Set a workflow as default for its context (organization, branch, department).
     *
     * @param int $id
     * @return bool
     */
    public function setAsDefault(int $id): bool
    {
        $workflow = $this->findById($id);
        if (!$workflow) {
            return false;
        }

        // Start a transaction
        return DB::transaction(function () use ($workflow) {
            $query = $this->model->where('organization_id', $workflow->organization_id);

            // If this is a department-specific workflow
            if ($workflow->department_id) {
                $query->where('department_id', $workflow->department_id);
            }
            // If this is a branch-specific workflow
            else if ($workflow->branch_id) {
                $query->where('branch_id', $workflow->branch_id)
                      ->whereNull('department_id');
            }
            // If this is an organization-level workflow
            else {
                $query->whereNull('branch_id')
                      ->whereNull('department_id');
            }

            // Unset any existing defaults with the same scope
            $query->where('id', '!=', $workflow->id)
                  ->where('is_default', true)
                  ->update(['is_default' => false]);

            // Set this workflow as default
            return $workflow->update(['is_default' => true]);
        });
    }
}
