<?php

namespace Src\ApprovalWorkflow\Application\Services;

use App\Models\ApprovalWorkflow;
use App\Models\ApprovalWorkflowStep;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApprovalWorkflowService
{
    /**
     * @var ApprovalWorkflowRepositoryInterface
     */
    public $workflowRepository;

    /**
     * @var ApprovalWorkflowStepRepositoryInterface
     */
    public $stepRepository;

    /**
     * ApprovalWorkflowService constructor.
     *
     * @param ApprovalWorkflowRepositoryInterface $workflowRepository
     * @param ApprovalWorkflowStepRepositoryInterface $stepRepository
     */
    public function __construct(
        ApprovalWorkflowRepositoryInterface $workflowRepository,
        ApprovalWorkflowStepRepositoryInterface $stepRepository
    ) {
        $this->workflowRepository = $workflowRepository;
        $this->stepRepository = $stepRepository;
    }
    /**
     * Find the appropriate approval workflow and its first step.
     *
     * @param int $organizationId
     * @param int|null $branchId
     * @param int|null $departmentId
     * @return array
     */
    /**
     * Find the appropriate approval workflow and its first step.
     *
     * @param int $organizationId
     * @param int|null $branchId
     * @param int|null $departmentId
     * @return array
     * @throws \Exception If no appropriate workflow is found.
     */
    public function findWorkflowAndStep(int $organizationId, ?int $branchId = null, ?int $departmentId = null): array
    {
        // Check if there are any approval workflows in the database
        $totalWorkflows = ApprovalWorkflow::count();
        Log::info('Total approval workflows in database: ' . $totalWorkflows);

        if ($totalWorkflows > 0) {
            // Log all workflows for debugging
            $allWorkflows = $this->workflowRepository->getAll();
            foreach ($allWorkflows as $wf) {
                Log::info('Workflow: ', [
                    'id' => $wf->id,
                    'name' => $wf->name,
                    'organization_id' => $wf->organization_id,
                    'branch_id' => $wf->branch_id,
                    'department_id' => $wf->department_id,
                    'is_default' => $wf->is_default,
                ]);

                // Log steps for this workflow
                $steps = $this->stepRepository->getByWorkflowId($wf->id);
                Log::info('Steps for workflow ' . $wf->id . ': ' . $steps->count());
                foreach ($steps as $step) {
                    Log::info('Step: ', [
                        'id' => $step->id,
                        'step_number' => $step->step_number,
                        'role_id' => $step->role_id,
                    ]);
                }
            }
        }

        // Find the appropriate workflow for this context
        $workflow = $this->workflowRepository->findWorkflowForContext($organizationId, $branchId, $departmentId);

        // Log the workflow selection process
        if ($workflow) {
            Log::info('Found appropriate workflow: ', [
                'id' => $workflow->id,
                'name' => $workflow->name,
                'organization_id' => $workflow->organization_id,
                'branch_id' => $workflow->branch_id,
                'department_id' => $workflow->department_id,
                'is_default' => $workflow->is_default,
                'context' => [
                    'organization_id' => $organizationId,
                    'branch_id' => $branchId,
                    'department_id' => $departmentId
                ]
            ]);
        } else {
            Log::warning('No appropriate workflow found for context: ', [
                'organization_id' => $organizationId,
                'branch_id' => $branchId,
                'department_id' => $departmentId
            ]);

            throw new \Exception('No approval workflow found for this requisition context.');
        }

        // Log the workflow for debugging
        Log::info('Workflow found: Yes, ID: ' . $workflow->id);

        // Get the first step of the workflow
        $firstStep = $this->stepRepository->getFirstStep($workflow->id);

        // Log the first step for debugging
        Log::info('First step found: ' . ($firstStep ? 'Yes, ID: ' . $firstStep->id : 'No'));

        if (!$firstStep) {
            Log::warning('No steps found for workflow ID: ' . $workflow->id);
            throw new \Exception('No steps found in the approval workflow.');
        } else {
            // Log the step details
            Log::info('Step details: ', [
                'id' => $firstStep->id,
                'step_number' => $firstStep->step_number,
                'role_id' => $firstStep->role_id,
            ]);
        }

        return [
            'workflow' => $workflow,
            'step' => $firstStep
        ];
    }

    /**
     * Find an approver for a workflow step.
     *
     * @param int $stepId
     * @param int $organizationId
     * @param int $fallbackUserId Fallback user ID if no approver is found
     * @return int
     */
    public function findApproverForStep(int $stepId, int $organizationId, int $fallbackUserId): int
    {
        $step = $this->stepRepository->findById($stepId);

        if (!$step) {
            Log::error('Step not found: ' . $stepId);
            return $fallbackUserId;
        }

        // If a specific approver is assigned to this step, use that user
        if ($step->approver_user_id) {
            Log::info("Using specific approver user ID {$step->approver_user_id} for step {$stepId}");
            return $step->approver_user_id;
        }

        $roleId = $step->role_id;

        // Find users with this role in this organization
        $potentialApprovers = DB::table('model_has_roles')
            ->join('users', 'model_has_roles.model_id', '=', 'users.id')
            ->join('user_organizations', 'users.id', '=', 'user_organizations.user_id')
            ->where('model_has_roles.role_id', $roleId)
            ->where('user_organizations.organization_id', $organizationId)
            ->where('model_has_roles.model_type', 'App\\Models\\User')
            ->select('users.id')
            ->get();

        if ($potentialApprovers->isNotEmpty()) {
            return $potentialApprovers->first()->id;
        } else {
            // Fallback: if no user with this role is found, log an error
            Log::error("No approver found with role ID {$roleId} in organization {$organizationId}");
            // Use the fallback user as a fallback
            return $fallbackUserId;
        }
    }

    /**
     * Create an approval record for a requisition.
     *
     * @param int $requisitionId
     * @param int $stepId
     * @param int $approverId
     * @param string $action
     * @param string $comments
     * @return bool
     */
    public function createApprovalRecord(int $requisitionId, int $stepId, int $approverId, string $action, string $comments): bool
    {
        return DB::table('requisition_approvals')->insert([
            'requisition_id' => $requisitionId,
            'approval_workflow_step_id' => $stepId,
            'approver_user_id' => $approverId,
            'action' => $action,
            'comments' => $comments,
            'action_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Get the next step in a workflow.
     *
     * @param int $workflowId
     * @param int $currentStepNumber
     * @return object|null
     */
    public function getNextStep(int $workflowId, int $currentStepNumber)
    {
        return $this->stepRepository->getNextStep($workflowId, $currentStepNumber);
    }
}
