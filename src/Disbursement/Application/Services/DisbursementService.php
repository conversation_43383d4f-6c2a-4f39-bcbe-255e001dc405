<?php

namespace Src\Disbursement\Application\Services;

use App\Models\Requisition;
use Src\Attachment\Application\Services\AttachmentService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Src\Disbursement\Domain\Repositories\TransactionItemRepositoryInterface;
use Src\Disbursement\Domain\Repositories\TransactionRepositoryInterface;
use Src\Requisition\Domain\Repositories\RequisitionHistoryRepositoryInterface;

class DisbursementService
{
    /**
     * @var TransactionRepositoryInterface
     */
    protected $transactionRepository;

    /**
     * @var TransactionItemRepositoryInterface
     */
    protected $transactionItemRepository;

    /**
     * @var RequisitionHistoryRepositoryInterface
     */
    protected $requisitionHistoryRepository;

    /**
     * @var AttachmentService
     */
    protected $attachmentService;

    /**
     * DisbursementService constructor.
     *
     * @param TransactionRepositoryInterface $transactionRepository
     * @param TransactionItemRepositoryInterface $transactionItemRepository
     * @param RequisitionHistoryRepositoryInterface $requisitionHistoryRepository
     * @param AttachmentService $attachmentService
     */
    public function __construct(
        TransactionRepositoryInterface $transactionRepository,
        TransactionItemRepositoryInterface $transactionItemRepository,
        RequisitionHistoryRepositoryInterface $requisitionHistoryRepository,
        AttachmentService $attachmentService
    ) {
        $this->transactionRepository = $transactionRepository;
        $this->transactionItemRepository = $transactionItemRepository;
        $this->requisitionHistoryRepository = $requisitionHistoryRepository;
        $this->attachmentService = $attachmentService;
    }

    /**
     * Create a new transaction from an approved requisition.
     *
     * @param Requisition $requisition
     * @param array $requisitionItems
     * @return mixed
     */
    public function openTransaction(Requisition $requisition, array $requisitionItems)
    {
        return DB::transaction(function () use ($requisition, $requisitionItems) {
            $user = Auth::user();

            // Always use the requisition requester's ID for created_by
            // This ensures the transaction is associated with the requisition creator
            $userId = $requisition->requester_user_id;

            // If no requester_user_id is available (which shouldn't happen), fall back to other options
            if (!$userId) {
                if ($user) {
                    $userId = $user->id;
                } elseif ($requisition->approved_by) {
                    $userId = $requisition->approved_by;
                } else {
                    // If no user is authenticated and no approved_by, use the first approver or a default value
                    $approvers = DB::table('requisition_approvals')
                        ->where('requisition_id', $requisition->id)
                        ->where('action', 'approved')
                        ->pluck('approver_user_id')
                        ->toArray();

                    if (!empty($approvers)) {
                        $userId = $approvers[0];
                    } else {
                        // As a last resort, get any user from the database
                        $anyUser = DB::table('users')->first();
                        $userId = $anyUser ? $anyUser->id : 1; // Default to ID 1 if no users found
                    }
                }
            }

            // Get approvers details from requisition approvals
            $approversDetails = $this->getApproversDetails($requisition->id);

            // Create the transaction
            $transaction = $this->transactionRepository->create([
                'requisition_id' => $requisition->id,
                'status' => 'opened',
                'total_amount' => $requisition->total_amount,
                'approvers_details' => $approversDetails,
                'created_by' => $userId,
                'updated_by' => $userId,
            ]);

            if (!$transaction) {
                Log::error('Failed to create transaction for requisition', ['requisition_id' => $requisition->id]);
                throw new \Exception('Failed to create transaction for requisition');
            }

            // Create transaction items
            foreach ($requisitionItems as $item) {
                $this->transactionItemRepository->create([
                    'transaction_id' => $transaction->id,
                    'chart_of_account_id' => $item['chart_of_account_id'],
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['total_price'],
                    'reference_number' => 'TRX-' . date('Ymd') . '-' . rand(1000, 9999),
                ]);
            }

            // Copy attachments from requisition to transaction
            try {
                $copiedAttachments = $this->attachmentService->copyAttachments(
                    $requisition,
                    $transaction,
                    'moved_to_transaction'
                );

                Log::info('Copied attachments from requisition to transaction', [
                    'requisition_id' => $requisition->id,
                    'transaction_id' => $transaction->id,
                    'attachments_copied' => count($copiedAttachments)
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to copy attachments from requisition to transaction', [
                    'requisition_id' => $requisition->id,
                    'transaction_id' => $transaction->id,
                    'error' => $e->getMessage()
                ]);
                // Don't fail the transaction creation if attachment copying fails
            }

            // Create history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisition->id,
                'user_id' => $userId,
                'action' => 'moved_to_disbursement',
                'comments' => 'Requisition moved to disbursement process',
                'created_at' => now(),
            ]);

            Log::info('Transaction created successfully for requisition', [
                'requisition_id' => $requisition->id,
                'transaction_id' => $transaction->id
            ]);

            return $transaction;
        });
    }

    /**
     * Update a transaction with account details.
     *
     * @param int $transactionId
     * @param array $accountDetails
     * @return mixed
     */
    public function updateTransactionWithAccountDetails(int $transactionId, array $accountDetails)
    {
        return DB::transaction(function () use ($transactionId, $accountDetails) {
            $user = Auth::user();
            $transaction = $this->transactionRepository->findById($transactionId);

            if (!$transaction) {
                throw new \Exception('Transaction not found');
            }

            // Update the transaction with account details
            $transaction = $this->transactionRepository->update($transactionId, [
                'account_details' => json_encode($accountDetails),
                'status' => 'updated',
                'updated_by' => $user->id,
            ]);

            // Create history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $transaction->requisition_id,
                'user_id' => $user->id,
                'action' => 'account_details_provided',
                'comments' => 'Account details provided for disbursement',
                'created_at' => now(),
            ]);

            Log::info('Transaction updated with account details', [
                'transaction_id' => $transactionId,
                'requisition_id' => $transaction->requisition_id
            ]);

            return $transaction;
        });
    }

    /**
     * Update a transaction with the provided data.
     *
     * @param int $transactionId
     * @param array $data
     * @return mixed
     */
    public function updateTransaction(int $transactionId, array $data)
    {
        return DB::transaction(function () use ($transactionId, $data) {
            $user = Auth::user();
            $transaction = $this->transactionRepository->findById($transactionId);

            if (!$transaction) {
                throw new \Exception('Transaction not found');
            }

            // If account_details is provided as an array, encode it to JSON
            if (isset($data['account_details']) && is_array($data['account_details'])) {
                $data['account_details'] = json_encode($data['account_details']);
            }

            // Update the transaction
            $transaction = $this->transactionRepository->update($transactionId, $data);

            // Create history record
            $action = 'transaction_updated';
            $comments = 'Transaction details updated';

            if (isset($data['status']) && $data['status'] === 'updated') {
                $action = 'transaction_ready_for_disbursement';
                $comments = 'Transaction marked as ready for disbursement';
            } elseif (isset($data['status']) && $data['status'] === 'completed') {
                $action = 'disbursement_completed';
                $comments = 'Disbursement completed';
            }

            $this->requisitionHistoryRepository->create([
                'requisition_id' => $transaction->requisition_id,
                'user_id' => $user->id,
                'action' => $action,
                'comments' => $comments,
                'created_at' => now(),
            ]);

            Log::info('Transaction updated', [
                'transaction_id' => $transactionId,
                'requisition_id' => $transaction->requisition_id,
                'status' => $data['status'] ?? 'not_changed'
            ]);

            return $transaction;
        });
    }

    /**
     * Mark a transaction as completed with disbursement ID.
     *
     * @param int $transactionId
     * @param string $disbursementId
     * @return mixed
     */
    public function markTransactionAsCompleted(int $transactionId, string $disbursementId)
    {
        return DB::transaction(function () use ($transactionId, $disbursementId) {
            $user = Auth::user();
            $transaction = $this->transactionRepository->findById($transactionId);

            if (!$transaction) {
                throw new \Exception('Transaction not found');
            }

            // Update the transaction as completed
            $transaction = $this->transactionRepository->update($transactionId, [
                'disbursement_transaction_id' => $disbursementId,
                'status' => 'completed',
                'updated_by' => $user->id,
            ]);

            // Create history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $transaction->requisition_id,
                'user_id' => $user->id,
                'action' => 'disbursement_completed',
                'comments' => 'Disbursement completed with transaction ID: ' . $disbursementId,
                'created_at' => now(),
            ]);

            Log::info('Transaction marked as completed', [
                'transaction_id' => $transactionId,
                'disbursement_id' => $disbursementId,
                'requisition_id' => $transaction->requisition_id
            ]);

            // Fire TransactionDisbursementCompleted event
            event(new \App\Events\TransactionDisbursementCompleted($transaction));

            return $transaction;
        });
    }

    /**
     * Get a transaction by ID.
     *
     * @param int $id
     * @return mixed
     */
    public function getTransaction(int $id)
    {
        return $this->transactionRepository->findById($id);
    }

    /**
     * Get a transaction with its items.
     *
     * @param int $id
     * @return mixed
     */
    public function getTransactionWithItems(int $id)
    {
        return $this->transactionRepository->findWithItems($id);
    }

    /**
     * Get transactions for a user.
     *
     * @param int $userId
     * @param array $filters
     * @param int $perPage
     * @return mixed
     */
    public function getTransactionsForUser(int $userId, array $filters = [], int $perPage = 10)
    {
        $query = $this->transactionRepository->getQueryBuilder();

        // Filter by user (created by)
        $query->where('created_by', $userId);

        // Apply status filter if provided
        if (isset($filters['status']) && $filters['status']) {
            $query->where('status', $filters['status']);
        }

        // Apply search filter if provided
        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($query) use ($search) {
                $query->where('id', 'like', "%{$search}%")
                    ->orWhere('disbursement_transaction_id', 'like', "%{$search}%");
            });
        }

        // Apply date filters if provided
        if (isset($filters['date_from']) && $filters['date_from']) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to']) && $filters['date_to']) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sort = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';
        $query->orderBy($sort, $direction);

        // Eager load relationships
        $query->with(['requisition', 'creator', 'items']);

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Get transactions where the user is the requisition requester.
     *
     * @param int $userId
     * @param array $filters
     * @param int $perPage
     * @return mixed
     */
    public function getTransactionsForRequester(int $userId, array $filters = [], int $perPage = 10)
    {
        $query = $this->transactionRepository->getQueryBuilder();

        // Join with requisitions to filter by requester_user_id
        $query->join('requisitions', 'transactions.requisition_id', '=', 'requisitions.id')
            ->where('requisitions.requester_user_id', $userId)
            ->select('transactions.*');

        // Apply status filter if provided
        if (isset($filters['status']) && $filters['status']) {
            $query->where('transactions.status', $filters['status']);
        }

        // Apply search filter if provided
        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($query) use ($search) {
                $query->where('transactions.id', 'like', "%{$search}%")
                    ->orWhere('transactions.disbursement_transaction_id', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sort = $filters['sort'] ?? 'transactions.created_at';
        $direction = $filters['direction'] ?? 'desc';
        $query->orderBy($sort, $direction);

        // Eager load relationships
        $query->with(['requisition', 'creator', 'items']);

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Check if a user is the requester of a transaction's requisition.
     *
     * @param mixed $transaction
     * @param int $userId
     * @return bool
     */
    public function isRequester($transaction, int $userId): bool
    {
        // If the transaction has a requisition relationship loaded
        if ($transaction->requisition) {
            return $transaction->requisition->requester_user_id === $userId;
        }

        // Otherwise, query the database
        $requisition = DB::table('requisitions')
            ->where('id', $transaction->requisition_id)
            ->first();

        return $requisition && $requisition->requester_user_id === $userId;
    }

    /**
     * Get approvers details from requisition approvals.
     *
     * @param int $requisitionId
     * @return array
     */
    private function getApproversDetails(int $requisitionId): array
    {
        // Select individual fields and handle concatenation in PHP
        $query = DB::table('requisition_approvals')
            ->where('requisition_id', $requisitionId)
            ->where('action', 'approved')
            ->join('users', 'requisition_approvals.approver_user_id', '=', 'users.id')
            ->select(
                'users.id',
                'users.first_name',
                'users.last_name'
            );

        // Check if the users table has a department_id column
        $hasUserDepartment = Schema::hasColumn('users', 'department_id');

        if ($hasUserDepartment) {
            $query->leftJoin('departments', 'users.department_id', '=', 'departments.id')
                ->addSelect('departments.name as department');
        }

        // Get the results
        $approvers = $query->get();

        // Transform the results to include the full name and ensure department is set
        return $approvers->map(function($approver) use ($hasUserDepartment) {
            $data = [
                'id' => $approver->id,
                'name' => trim($approver->first_name . ' ' . $approver->last_name),
                'department' => $hasUserDepartment && isset($approver->department) ? $approver->department : 'Not Assigned'
            ];
            return $data;
        })->toArray();
    }

    /**
     * Get all transactions with filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return mixed
     */
    public function getAllTransactions(array $filters = [], int $perPage = 10)
    {
        $query = $this->transactionRepository->getQueryBuilder();

        // Apply status filter if provided
        if (isset($filters['status']) && $filters['status']) {
            $query->where('status', $filters['status']);
        }

        // Apply search filter if provided
        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($query) use ($search) {
                $query->where('id', 'like', "%{$search}%")
                    ->orWhere('disbursement_transaction_id', 'like', "%{$search}%")
                    ->orWhereHas('requisition', function($q) use ($search) {
                        $q->where('requisition_number', 'like', "%{$search}%");
                    });
            });
        }

        // Apply date filters if provided
        if (isset($filters['date_from']) && $filters['date_from']) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to']) && $filters['date_to']) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sort = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';
        $query->orderBy($sort, $direction);

        // Eager load relationships
        $query->with(['requisition', 'creator', 'items']);

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Get requisition approval history.
     *
     * @param int $requisitionId
     * @return mixed
     */
    public function getRequisitionApprovalHistory(int $requisitionId)
    {
        return DB::table('requisition_approvals')
            ->where('requisition_id', $requisitionId)
            ->join('users', 'requisition_approvals.approver_user_id', '=', 'users.id')
            ->join('approval_workflow_steps', 'requisition_approvals.approval_workflow_step_id', '=', 'approval_workflow_steps.id')
            ->leftJoin('roles', 'approval_workflow_steps.role_id', '=', 'roles.id')
            ->select(
                'requisition_approvals.id',
                'requisition_approvals.action',
                'requisition_approvals.comments',
                'requisition_approvals.created_at',
                'users.id as approver_id',
                'users.first_name as approver_first_name',
                'users.last_name as approver_last_name',
                'roles.name as role_name',
                'approval_workflow_steps.step_number',
                'approval_workflow_steps.description as step_description'
            )
            ->orderBy('requisition_approvals.created_at', 'asc')
            ->get()
            ->map(function($item) {
                $item->approver_name = trim($item->approver_first_name . ' ' . $item->approver_last_name);
                return $item;
            });
    }
}
