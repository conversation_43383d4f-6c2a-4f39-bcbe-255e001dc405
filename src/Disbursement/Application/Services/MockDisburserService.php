<?php

namespace Src\Disbursement\Application\Services;

use Illuminate\Support\Facades\Log;

/**
 * Mock service for external disbursement system.
 * This is a placeholder for the actual integration with an external disbursement system.
 */
class MockDisburserService
{
    /**
     * Process a disbursement.
     *
     * @param int $transactionId
     * @param float $amount
     * @param array $accountDetails
     * @return string
     */
    public function processDisbursement(int $transactionId, float $amount, array $accountDetails): string
    {
        // Log the disbursement request
        Log::info('Processing disbursement', [
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'account_details' => $accountDetails,
        ]);
        
        // Simulate processing time
        sleep(1);
        
        // Generate a mock disbursement ID
        $disbursementId = 'DISB-' . date('YmdHis') . '-' . rand(1000, 9999);
        
        // Log the disbursement response
        Log::info('Disbursement processed successfully', [
            'transaction_id' => $transactionId,
            'disbursement_id' => $disbursementId,
        ]);
        
        return $disbursementId;
    }
    
    /**
     * Check the status of a disbursement.
     *
     * @param string $disbursementId
     * @return string
     */
    public function checkDisbursementStatus(string $disbursementId): string
    {
        // Log the status check
        Log::info('Checking disbursement status', [
            'disbursement_id' => $disbursementId,
        ]);
        
        // Simulate processing time
        sleep(1);
        
        // Generate a random status (for demonstration purposes)
        $statuses = ['pending', 'processing', 'completed', 'failed'];
        $status = $statuses[array_rand($statuses)];
        
        // Log the status response
        Log::info('Disbursement status check result', [
            'disbursement_id' => $disbursementId,
            'status' => $status,
        ]);
        
        return $status;
    }
    
    /**
     * Cancel a disbursement.
     *
     * @param string $disbursementId
     * @return bool
     */
    public function cancelDisbursement(string $disbursementId): bool
    {
        // Log the cancellation request
        Log::info('Cancelling disbursement', [
            'disbursement_id' => $disbursementId,
        ]);
        
        // Simulate processing time
        sleep(1);
        
        // Generate a random result (for demonstration purposes)
        $success = (bool) rand(0, 1);
        
        // Log the cancellation response
        Log::info('Disbursement cancellation result', [
            'disbursement_id' => $disbursementId,
            'success' => $success,
        ]);
        
        return $success;
    }
}
