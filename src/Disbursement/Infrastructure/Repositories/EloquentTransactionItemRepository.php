<?php

namespace Src\Disbursement\Infrastructure\Repositories;

use App\Models\TransactionItem;
use Src\Disbursement\Domain\Repositories\TransactionItemRepositoryInterface;

class EloquentTransactionItemRepository implements TransactionItemRepositoryInterface
{
    /**
     * @var TransactionItem
     */
    protected $model;

    /**
     * EloquentTransactionItemRepository constructor.
     *
     * @param TransactionItem $model
     */
    public function __construct(TransactionItem $model)
    {
        $this->model = $model;
    }

    /**
     * Get all transaction items.
     *
     * @return mixed
     */
    public function getAll()
    {
        return $this->model->all();
    }

    /**
     * Find a transaction item by ID.
     *
     * @param int $id
     * @return mixed
     */
    public function findById(int $id)
    {
        return $this->model->find($id);
    }

    /**
     * Get transaction items by transaction ID.
     *
     * @param int $transactionId
     * @return mixed
     */
    public function getByTransactionId(int $transactionId)
    {
        return $this->model->where('transaction_id', $transactionId)->get();
    }

    /**
     * Create a new transaction item.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        return $this->model->create($data);
    }

    /**
     * Create multiple transaction items.
     *
     * @param array $items
     * @return mixed
     */
    public function createMany(array $items)
    {
        return $this->model->insert($items);
    }

    /**
     * Update a transaction item.
     *
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function update(int $id, array $data)
    {
        $item = $this->findById($id);
        if ($item) {
            $item->update($data);
            return $item;
        }
        return null;
    }

    /**
     * Delete a transaction item.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $item = $this->findById($id);
        if ($item) {
            return $item->delete();
        }
        return false;
    }

    /**
     * Delete all items for a transaction.
     *
     * @param int $transactionId
     * @return bool
     */
    public function deleteByTransactionId(int $transactionId): bool
    {
        return $this->model->where('transaction_id', $transactionId)->delete() > 0;
    }
}
