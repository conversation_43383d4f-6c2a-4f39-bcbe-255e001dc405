<?php

namespace Src\Disbursement\Infrastructure\Repositories;

use App\Models\Transaction;
use Illuminate\Database\Eloquent\Builder;
use Src\Disbursement\Domain\Repositories\TransactionRepositoryInterface;

class EloquentTransactionRepository implements TransactionRepositoryInterface
{
    /**
     * @var Transaction
     */
    protected $model;

    /**
     * EloquentTransactionRepository constructor.
     *
     * @param Transaction $model
     */
    public function __construct(Transaction $model)
    {
        $this->model = $model;
    }

    /**
     * Get all transactions.
     *
     * @return mixed
     */
    public function getAll()
    {
        return $this->model->all();
    }

    /**
     * Find a transaction by ID.
     *
     * @param int $id
     * @return mixed
     */
    public function findById(int $id)
    {
        return $this->model->find($id);
    }

    /**
     * Find a transaction by requisition ID.
     *
     * @param int $requisitionId
     * @return mixed
     */
    public function findByRequisitionId(int $requisitionId)
    {
        return $this->model->where('requisition_id', $requisitionId)->first();
    }

    /**
     * Find a transaction with its items.
     *
     * @param int $id
     * @return mixed
     */
    public function findWithItems(int $id)
    {
        return $this->model->with([
            'items',
            'requisition.requester',
            'requisition.department',
            'requisition.branch',
            'requisition.organization'
        ])->find($id);
    }

    /**
     * Create a new transaction.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        return $this->model->create($data);
    }

    /**
     * Update a transaction.
     *
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function update(int $id, array $data)
    {
        $transaction = $this->findById($id);
        if ($transaction) {
            $transaction->update($data);
            return $transaction;
        }
        return null;
    }

    /**
     * Delete a transaction.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $transaction = $this->findById($id);
        if ($transaction) {
            return $transaction->delete();
        }
        return false;
    }

    /**
     * Get transactions by status.
     *
     * @param string $status
     * @return mixed
     */
    public function getByStatus(string $status)
    {
        return $this->model->where('status', $status)->get();
    }

    /**
     * Get transactions by user ID (created by).
     *
     * @param int $userId
     * @return mixed
     */
    public function getByUserId(int $userId)
    {
        return $this->model->where('created_by', $userId)->get();
    }

    /**
     * Get query builder for transactions.
     *
     * @return Builder
     */
    public function getQueryBuilder(): Builder
    {
        return $this->model->newQuery();
    }
}
