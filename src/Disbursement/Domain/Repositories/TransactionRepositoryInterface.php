<?php

namespace Src\Disbursement\Domain\Repositories;

interface TransactionRepositoryInterface
{
    /**
     * Get all transactions.
     *
     * @return mixed
     */
    public function getAll();

    /**
     * Find a transaction by ID.
     *
     * @param int $id
     * @return mixed
     */
    public function findById(int $id);

    /**
     * Find a transaction by requisition ID.
     *
     * @param int $requisitionId
     * @return mixed
     */
    public function findByRequisitionId(int $requisitionId);

    /**
     * Find a transaction with its items.
     *
     * @param int $id
     * @return mixed
     */
    public function findWithItems(int $id);

    /**
     * Create a new transaction.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data);

    /**
     * Update a transaction.
     *
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function update(int $id, array $data);

    /**
     * Delete a transaction.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get transactions by status.
     *
     * @param string $status
     * @return mixed
     */
    public function getByStatus(string $status);

    /**
     * Get transactions by user ID (created by).
     *
     * @param int $userId
     * @return mixed
     */
    public function getByUserId(int $userId);

    /**
     * Get query builder for transactions.
     *
     * @return mixed
     */
    public function getQueryBuilder();
}
