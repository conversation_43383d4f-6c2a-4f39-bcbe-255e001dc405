<?php

namespace Src\Disbursement\Domain\Repositories;

interface TransactionItemRepositoryInterface
{
    /**
     * Get all transaction items.
     *
     * @return mixed
     */
    public function getAll();

    /**
     * Find a transaction item by ID.
     *
     * @param int $id
     * @return mixed
     */
    public function findById(int $id);

    /**
     * Get transaction items by transaction ID.
     *
     * @param int $transactionId
     * @return mixed
     */
    public function getByTransactionId(int $transactionId);

    /**
     * Create a new transaction item.
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data);

    /**
     * Create multiple transaction items.
     *
     * @param array $items
     * @return mixed
     */
    public function createMany(array $items);

    /**
     * Update a transaction item.
     *
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function update(int $id, array $data);

    /**
     * Delete a transaction item.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Delete all items for a transaction.
     *
     * @param int $transactionId
     * @return bool
     */
    public function deleteByTransactionId(int $transactionId): bool;
}
