<?php

namespace Src\Requisition\Infrastructure\Repositories;

use App\Models\Requisition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Src\Requisition\Domain\Repositories\RequisitionRepositoryInterface;

/**
 * Class EloquentRequisitionRepository
 *
 * This class implements the RequisitionRepositoryInterface using Eloquent ORM.
 */
class EloquentRequisitionRepository implements RequisitionRepositoryInterface
{
    /**
     * @var Requisition
     */
    protected $model;

    /**
     * EloquentRequisitionRepository constructor.
     *
     * @param Requisition $model
     */
    public function __construct(Requisition $model)
    {
        $this->model = $model;
    }

    /**
     * Get all requisitions with optional filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllWithFilters(array $filters, int $perPage = 10): LengthAwarePaginator
    {
        $query = $this->model->query();

        // Apply filters
        if (isset($filters['user_id'])) {
            $query->where('requester_user_id', $filters['user_id']);
        }

        if (isset($filters['department_id'])) {
            $query->where('department_id', $filters['department_id']);
        }

        if (isset($filters['status']) && $filters['status'] !== 'all') {
            $query->where('status', $filters['status']);
        }

        // Apply sorting
        $sortField = $filters['sort'] ?? 'created_at';
        $sortDirection = $filters['direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        // Eager load relationships if needed
        if (isset($filters['with']) && is_array($filters['with'])) {
            $query->with($filters['with']);
        }

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Find a requisition by its ID.
     *
     * @param int $id
     * @return Requisition|null
     */
    public function findById(int $id): ?Requisition
    {
        return $this->model->find($id);
    }

    /**
     * Create a new requisition.
     *
     * @param array $data
     * @return Requisition
     */
    public function create(array $data): Requisition
    {
        // Log the data being passed to create
        \Log::info('Creating requisition with data: ', [
            'approval_workflow_id' => $data['approval_workflow_id'] ?? null,
            'current_approval_step_id' => $data['current_approval_step_id'] ?? null,
            'current_workflow_step' => $data['current_workflow_step'] ?? null,
        ]);

        $requisition = $this->model->create($data);

        // Log the created requisition
        \Log::info('Created requisition: ', [
            'id' => $requisition->id,
            'approval_workflow_id' => $requisition->approval_workflow_id,
            'current_approval_step_id' => $requisition->current_approval_step_id,
            'current_workflow_step' => $requisition->current_workflow_step,
        ]);

        return $requisition;
    }

    /**
     * Update an existing requisition.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $requisition = $this->findById($id);

        if (!$requisition) {
            return false;
        }

        return $requisition->update($data);
    }

    /**
     * Delete a requisition.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $requisition = $this->findById($id);

        if (!$requisition) {
            return false;
        }

        return $requisition->delete();
    }

    /**
     * Get requisitions by user ID with optional department filtering.
     *
     * @param int $userId
     * @param int|null $departmentId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getByUserIdWithDepartmentFilter(int $userId, ?int $departmentId = null, int $perPage = 10): LengthAwarePaginator
    {
        $query = $this->model->where('requester_user_id', $userId);

        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        return $query->with(['department'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage)
            ->withQueryString();
    }

    /**
     * Get requisitions for approval by department ID.
     *
     * @param int $departmentId
     * @param string $status
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getForApprovalByDepartmentId(int $departmentId, string $status = 'pending_approval', int $perPage = 10): LengthAwarePaginator
    {
        $query = $this->model->where('department_id', $departmentId);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        return $query->with(['requester', 'department'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage)
            ->withQueryString();
    }

    /**
     * Get a query builder instance for requisitions.
     * This allows for more complex queries to be built.
     *
     * @return Builder
     */
    public function getQueryBuilder(): Builder
    {
        return $this->model->query();
    }
}
