<?php

namespace Src\Requisition\Infrastructure\Repositories;

use App\Models\RequisitionItem;
use Illuminate\Database\Eloquent\Collection;
use Src\Requisition\Domain\Repositories\RequisitionItemRepositoryInterface;

/**
 * Class EloquentRequisitionItemRepository
 * 
 * This class implements the RequisitionItemRepositoryInterface using Eloquent ORM.
 */
class EloquentRequisitionItemRepository implements RequisitionItemRepositoryInterface
{
    /**
     * @var RequisitionItem
     */
    protected $model;
    
    /**
     * EloquentRequisitionItemRepository constructor.
     *
     * @param RequisitionItem $model
     */
    public function __construct(RequisitionItem $model)
    {
        $this->model = $model;
    }
    
    /**
     * Create a new requisition item.
     *
     * @param array $data
     * @return RequisitionItem
     */
    public function create(array $data): RequisitionItem
    {
        return $this->model->create($data);
    }
    
    /**
     * Create multiple requisition items.
     *
     * @param array $items
     * @return Collection
     */
    public function createMany(array $items): Collection
    {
        $createdItems = new Collection();
        
        foreach ($items as $item) {
            $createdItems->push($this->create($item));
        }
        
        return $createdItems;
    }
    
    /**
     * Get items by requisition ID.
     *
     * @param int $requisitionId
     * @return Collection
     */
    public function getByRequisitionId(int $requisitionId): Collection
    {
        return $this->model->where('requisition_id', $requisitionId)->get();
    }
    
    /**
     * Delete items by requisition ID.
     *
     * @param int $requisitionId
     * @return bool
     */
    public function deleteByRequisitionId(int $requisitionId): bool
    {
        return $this->model->where('requisition_id', $requisitionId)->delete();
    }
}
