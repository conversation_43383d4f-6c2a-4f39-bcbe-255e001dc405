<?php

namespace Src\Requisition\Infrastructure\Repositories;

use App\Models\RequisitionHistory;
use Illuminate\Database\Eloquent\Collection;
use Src\Requisition\Domain\Repositories\RequisitionHistoryRepositoryInterface;

/**
 * Class EloquentRequisitionHistoryRepository
 * 
 * This class implements the RequisitionHistoryRepositoryInterface using Eloquent ORM.
 */
class EloquentRequisitionHistoryRepository implements RequisitionHistoryRepositoryInterface
{
    /**
     * @var RequisitionHistory
     */
    protected $model;
    
    /**
     * EloquentRequisitionHistoryRepository constructor.
     *
     * @param RequisitionHistory $model
     */
    public function __construct(RequisitionHistory $model)
    {
        $this->model = $model;
    }
    
    /**
     * Create a new history record.
     *
     * @param array $data
     * @return RequisitionHistory
     */
    public function create(array $data): RequisitionHistory
    {
        return $this->model->create($data);
    }
    
    /**
     * Get history by requisition ID.
     *
     * @param int $requisitionId
     * @return Collection
     */
    public function getByRequisitionId(int $requisitionId): Collection
    {
        return $this->model->where('requisition_id', $requisitionId)
            ->with('user:id,first_name,last_name')
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
