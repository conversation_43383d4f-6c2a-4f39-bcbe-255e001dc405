<?php

namespace Src\Requisition\Application\Services;

use Src\ApprovalWorkflow\Application\Services\ApprovalWorkflowService;
use Src\Disbursement\Application\Services\DisbursementService;
use App\Models\ChartOfAccount;
use App\Models\Department;
use App\Models\RequisitionFormDetails;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Helpers\DatabaseHelper;
use Illuminate\Pagination\LengthAwarePaginator;
use Src\Requisition\Domain\Repositories\RequisitionRepositoryInterface;
use Src\Requisition\Domain\Repositories\RequisitionItemRepositoryInterface;
use Src\Requisition\Domain\Repositories\RequisitionHistoryRepositoryInterface;

/**
 * Class RequisitionService
 *
 * This class encapsulates the business logic for requisitions.
 */
class RequisitionService
{
    /**
     * @var RequisitionRepositoryInterface
     */
    protected $requisitionRepository;

    /**
     * @var RequisitionItemRepositoryInterface
     */
    protected $requisitionItemRepository;

    /**
     * @var RequisitionHistoryRepositoryInterface
     */
    protected $requisitionHistoryRepository;

    /**
     * @var ApprovalWorkflowService
     */
    protected $approvalWorkflowService;

    /**
     * @var DisbursementService
     */
    protected $disbursementService;

    /**
     * RequisitionService constructor.
     *
     * @param RequisitionRepositoryInterface $requisitionRepository
     * @param RequisitionItemRepositoryInterface $requisitionItemRepository
     * @param RequisitionHistoryRepositoryInterface $requisitionHistoryRepository
     * @param ApprovalWorkflowService $approvalWorkflowService
     * @param DisbursementService $disbursementService
     */
    public function __construct(
        RequisitionRepositoryInterface $requisitionRepository,
        RequisitionItemRepositoryInterface $requisitionItemRepository,
        RequisitionHistoryRepositoryInterface $requisitionHistoryRepository,
        ApprovalWorkflowService $approvalWorkflowService,
        DisbursementService $disbursementService
    ) {
        $this->requisitionRepository = $requisitionRepository;
        $this->requisitionItemRepository = $requisitionItemRepository;
        $this->requisitionHistoryRepository = $requisitionHistoryRepository;
        $this->approvalWorkflowService = $approvalWorkflowService;
        $this->disbursementService = $disbursementService;
    }

    /**
     * Get user requisition history with optional department filtering.
     *
     * @param int $userId
     * @param int|null $departmentId
     * @param int $perPage
     * @return mixed
     */
    public function getUserRequisitionHistory(int $userId, ?int $departmentId = null, int $perPage = 10)
    {
        return $this->requisitionRepository->getByUserIdWithDepartmentFilter($userId, $departmentId, $perPage);
    }

    /**
     * Create a new requisition.
     *
     * @param array $data
     * @return mixed
     * @throws \Exception
     */
    public function createRequisition(array $data)
    {
        return DB::transaction(function () use ($data) {
            $user = Auth::user();

            // 1. Get the form details
            $formDetails = RequisitionFormDetails::where('requisition_form_uuid', $data['requisition_form_uuid'])->first();
            if (!$formDetails) {
                throw new \Exception('Invalid requisition form UUID');
            }

            // 2. Create the requisition
            $requisitionData = [
                'organization_id' => $formDetails->organization_id,
                'branch_id' => $formDetails->branch_id,
                'department_id' => $formDetails->department_id,
                'requester_user_id' => $user->id,
                'purpose' => $data['purpose'],
                'notes' => $data['notes'] ?? null,
                'status' => 'pending_approval',
            ];

            // Find the appropriate workflow and its first step using the ApprovalWorkflowService
            $workflowData = $this->approvalWorkflowService->findWorkflowAndStep(
                $formDetails->organization_id,
                $formDetails->branch_id,
                $formDetails->department_id
            );

            $workflow = $workflowData['workflow'];
            $firstStep = $workflowData['step'];

            // Add workflow and step data to requisition if found
            if ($workflow) {
                $requisitionData['approval_workflow_id'] = $workflow->id;

                if ($firstStep) {
                    $requisitionData['current_approval_step_id'] = $firstStep->id;
                    $requisitionData['current_workflow_step'] = $firstStep->step_number;
                }
            }

            // Create the requisition
            $requisition = $this->requisitionRepository->create($requisitionData);

            // Create approval record for the first step if a workflow exists
            if ($workflow && $firstStep) {
                // Find an approver for the step using the ApprovalWorkflowService
                $approverId = $this->approvalWorkflowService->findApproverForStep(
                    $firstStep->id,
                    $formDetails->organization_id,
                    $user->id
                );

                // Create the approval record using the ApprovalWorkflowService
                $this->approvalWorkflowService->createApprovalRecord(
                    $requisition->id,
                    $firstStep->id,
                    $approverId,
                    'submitted',
                    'Requisition submitted for approval'
                );

                \Log::info('Created approval record for requisition: ' . $requisition->id);
            }

            // 3. Create the requisition items
            $totalAmount = 0;
            foreach ($data['requisition_items'] as $itemData) {
                // Use the chart_of_account_id directly from the request
                $chartOfAccountId = $itemData['chart_of_account_id'];

                // Verify that the chart of account exists
                $chartOfAccount = ChartOfAccount::find($chartOfAccountId);
                if (!$chartOfAccount) {
                    throw new \Exception('Invalid chart of account ID: ' . $chartOfAccountId);
                }

                $itemData = [
                    'requisition_id' => $requisition->id,
                    'chart_of_account_id' => $chartOfAccountId,
                    'description' => $itemData['description'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $itemData['quantity'] * $itemData['unit_price'],
                ];

                $item = $this->requisitionItemRepository->create($itemData);
                $totalAmount += $item->total_price;
            }

            // 4. Update the total amount on the requisition
            $this->requisitionRepository->update($requisition->id, [
                'total_amount' => $totalAmount,
            ]);

            // 5. Create a history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisition->id,
                'user_id' => $user->id,
                'action' => 'created',
                'comments' => 'Requisition created and submitted for approval',
                'created_at' => now(),
            ]);

            // 6. Delete the form details as it's no longer needed
            $formDetails->delete();

            return $requisition;
        });
    }

    /**
     * Get a requisition by ID.
     *
     * @param int $id
     * @return mixed
     */
    public function getRequisitionById(int $id)
    {
        return $this->requisitionRepository->findById($id);
    }

    /**
     * Get requisition history.
     *
     * @param int $requisitionId
     * @return mixed
     */
    public function getRequisitionHistory(int $requisitionId)
    {
        return $this->requisitionHistoryRepository->getByRequisitionId($requisitionId);
    }

    /**
     * Approve a requisition.
     *
     * @param int $requisitionId
     * @param string $comments
     * @param mixed|null $userOverride User object to use instead of Auth::user() (for testing)
     * @return mixed
     * @throws \Exception
     */
    public function approveRequisition(int $requisitionId, string $comments, $userOverride = null)
    {
        return DB::transaction(function () use ($requisitionId, $comments, $userOverride) {
            $user = $userOverride ?: Auth::user();
            $requisition = $this->requisitionRepository->findById($requisitionId);

            if (!$requisition) {
                throw new \Exception('Requisition not found');
            }

            if ($requisition->status !== 'pending_approval') {
                throw new \Exception('Requisition is not pending approval');
            }

            // Check if the requisition has an approval workflow and current step
            if ($requisition->approval_workflow_id && $requisition->current_approval_step_id) {
                // Get the current step using the ApprovalWorkflowService
                $currentStep = $this->approvalWorkflowService->stepRepository->findById($requisition->current_approval_step_id);

                if (!$currentStep) {
                    throw new \Exception('Current approval step not found');
                }

                // Create approval record using the ApprovalWorkflowService
                $this->approvalWorkflowService->createApprovalRecord(
                    $requisition->id,
                    $currentStep->id,
                    $user->id,
                    'approved',
                    $comments
                );

                // Check if all required approvers for this step have approved
                $allApproversApproved = $this->checkAllApproversApproved($requisition, $currentStep);

                if ($allApproversApproved) {
                    // Check if there is a next step using the ApprovalWorkflowService
                    $nextStep = $this->approvalWorkflowService->getNextStep(
                        $requisition->approval_workflow_id,
                        $currentStep->step_number
                    );

                    if ($nextStep) {
                        // Move to the next step
                        $this->requisitionRepository->update($requisitionId, [
                            'current_approval_step_id' => $nextStep->id,
                            'current_workflow_step' => $nextStep->step_number,
                        ]);
                    } else {
                        // This was the final step, mark the requisition as fully approved
                        $this->requisitionRepository->update($requisitionId, [
                            'status' => 'approved',
                            'approved_by' => $user->id,
                            'approved_at' => now(),
                        ]);

                        // Move the requisition to the disbursement process
                        $this->moveToDisbursal($requisition);
                    }
                } else {
                    // Not all required approvers have approved yet, so we stay at the current step
                    \Log::info('Not all required approvers have approved requisition ' . $requisitionId . ' at step ' . $currentStep->step_number);
                }
            } else {
                // No workflow, just approve the requisition
                $this->requisitionRepository->update($requisitionId, [
                    'status' => 'approved',
                    'approved_by' => $user->id,
                    'approved_at' => now(),
                ]);

                // Move the requisition to the disbursement process
                $this->moveToDisbursal($requisition);
            }

            // Create history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisitionId,
                'user_id' => $user->id,
                'action' => 'approved',
                'comments' => $comments,
                'created_at' => now(),
            ]);

            return $requisition;
        });
    }

    /**
     * Reject a requisition.
     *
     * @param int $requisitionId
     * @param string $comments
     * @return mixed
     * @throws \Exception
     */
    public function rejectRequisition(int $requisitionId, string $comments)
    {
        return DB::transaction(function () use ($requisitionId, $comments) {
            $user = Auth::user();
            $requisition = $this->requisitionRepository->findById($requisitionId);

            if (!$requisition) {
                throw new \Exception('Requisition not found');
            }

            if ($requisition->status !== 'pending_approval') {
                throw new \Exception('Requisition is not pending approval');
            }

            // Check if the requisition has an approval workflow and current step
            if ($requisition->approval_workflow_id && $requisition->current_approval_step_id) {
                // Get the current step using the ApprovalWorkflowService
                $currentStep = $this->approvalWorkflowService->stepRepository->findById($requisition->current_approval_step_id);

                if (!$currentStep) {
                    throw new \Exception('Current approval step not found');
                }

                // Create approval record using the ApprovalWorkflowService
                $this->approvalWorkflowService->createApprovalRecord(
                    $requisition->id,
                    $currentStep->id,
                    $user->id,
                    'rejected',
                    $comments
                );
            }

            // Update requisition status
            $this->requisitionRepository->update($requisitionId, [
                'status' => 'rejected',
                'rejected_by' => $user->id,
                'rejected_at' => now(),
            ]);

            // Create history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisitionId,
                'user_id' => $user->id,
                'action' => 'rejected',
                'comments' => $comments,
                'created_at' => now(),
            ]);

            return $requisition;
        });
    }

    /**
     * Get requisitions that need approval for a specific department or user.
     *
     * @param int $userId
     * @param int|null $departmentId
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getRequisitionsForApproval(int $userId, ?int $departmentId = null, array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        // Get departments where user is HOD
        $hodDepartments = Department::where('hod_user_id', $userId)->pluck('id')->toArray();

        if (empty($hodDepartments)) {
            // Return empty paginator if user is not HOD of any department
            return new LengthAwarePaginator([], 0, $perPage);
        }

        // Build query
        $query = $this->requisitionRepository->getQueryBuilder();

        // Filter by department if specified, otherwise use all departments where user is HOD
        if ($departmentId && in_array($departmentId, $hodDepartments)) {
            $query->where('department_id', $departmentId);
        } else {
            $query->whereIn('department_id', $hodDepartments);
        }

        // Only show requisitions created by other users (not the HOD)
        $query->where('requester_user_id', '!=', $userId);

        // Apply filters
        if (isset($filters['status']) && $filters['status'] !== 'all') {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($query) use ($search) {
                $query->where('requisition_number', 'like', "%{$search}%")
                    ->orWhere('purpose', 'like', "%{$search}%")
                    ->orWhereHas('requester', function ($query) use ($search) {
                        $query->where(DB::raw(DatabaseHelper::fullName()), 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    });
            });
        }

        // Apply sorting
        $sort = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';
        $query->orderBy($sort, $direction);

        // Eager load relationships
        $query->with('requester:id,first_name,last_name,email');

        // Paginate results
        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Get a specific requisition for approval review.
     *
     * @param int $requisitionId
     * @param int $departmentId
     * @param int $userId
     * @return mixed
     */
    public function getRequisitionForApprovalReview(int $requisitionId, int $departmentId, int $userId)
    {
        return $this->requisitionRepository->getQueryBuilder()
            ->where('id', $requisitionId)
            ->where('department_id', $departmentId)
            ->where('requester_user_id', '!=', $userId) // Not created by the HOD
            ->with(['requester:id,first_name,last_name,email', 'items', 'department', 'attachments.uploader'])
            ->first();
    }

    /**
     * Return a requisition for revision.
     *
     * @param int $requisitionId
     * @param string $comments
     * @return mixed
     * @throws \Exception
     */
    public function returnRequisitionForRevision(int $requisitionId, string $comments)
    {
        return DB::transaction(function () use ($requisitionId, $comments) {
            $user = Auth::user();
            $requisition = $this->requisitionRepository->findById($requisitionId);

            if (!$requisition) {
                throw new \Exception('Requisition not found');
            }

            if ($requisition->status !== 'pending_approval') {
                throw new \Exception('Requisition is not pending approval');
            }

            // Check if the requisition has an approval workflow and current step
            if ($requisition->approval_workflow_id && $requisition->current_approval_step_id) {
                // Get the current step using the ApprovalWorkflowService
                $currentStep = $this->approvalWorkflowService->stepRepository->findById($requisition->current_approval_step_id);

                if (!$currentStep) {
                    throw new \Exception('Current approval step not found');
                }

                // Create approval record using the ApprovalWorkflowService
                $this->approvalWorkflowService->createApprovalRecord(
                    $requisition->id,
                    $currentStep->id,
                    $user->id,
                    'returned_for_revision',
                    $comments
                );
            }

            // Update requisition status
            $this->requisitionRepository->update($requisitionId, [
                'status' => 'returned_for_revision',
                'current_approval_step_id' => null, // Reset the approval step
            ]);

            // Create history record
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisitionId,
                'user_id' => $user->id,
                'action' => 'returned_for_revision',
                'comments' => $comments,
                'created_at' => now(),
            ]);

            return $requisition;
        });
    }

    /**
     * Resubmit a requisition that was returned for revision.
     *
     * @param int $requisitionId
     * @param array $data
     * @param string $comments
     * @return mixed
     * @throws \Exception
     */
    public function resubmitRequisition(int $requisitionId, array $data, string $comments)
    {
        return DB::transaction(function () use ($requisitionId, $data, $comments) {
            $user = Auth::user();
            $requisition = $this->requisitionRepository->findById($requisitionId);

            if (!$requisition) {
                throw new \Exception('Requisition not found');
            }

            // Check if the user is the original requester
            if ($requisition->requester_user_id !== $user->id) {
                throw new \Exception('Only the original requester can resubmit this requisition');
            }

            // Check if requisition is in returned_for_revision status
            if ($requisition->status !== 'returned_for_revision') {
                throw new \Exception('This requisition cannot be resubmitted because it is not in returned for revision status');
            }

            // Update the requisition with new data
            $updateData = [
                'purpose' => $data['purpose'],
                'notes' => $data['notes'] ?? null,
                'status' => 'pending_approval',
            ];

            // If the requisition has an approval workflow, set it back to the first step
            if ($requisition->approval_workflow_id) {
                $workflow = $requisition->approvalWorkflow;
                $firstStep = $workflow->steps()->orderBy('step_number')->first();

                if ($firstStep) {
                    $updateData['current_approval_step_id'] = $firstStep->id;
                    $updateData['current_workflow_step'] = $firstStep->step_number;
                }
            }

            $this->requisitionRepository->update($requisitionId, $updateData);

            // Create history record for resubmission
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisitionId,
                'user_id' => $user->id,
                'action' => 'resubmitted',
                'comments' => $comments,
                'created_at' => now(),
            ]);

            return $requisition;
        });
    }

    /**
     * Check if all required approvers for a step have approved the requisition.
     *
     * @param Requisition $requisition
     * @param ApprovalWorkflowStep $currentStep
     * @return bool
     */
    private function checkAllApproversApproved($requisition, $currentStep): bool
    {
        // If a specific approver is assigned to this step
        if ($currentStep->approver_user_id) {
            // Check if this specific user has approved
            $approved = DB::table('requisition_approvals')
                ->where('requisition_id', $requisition->id)
                ->where('approval_workflow_step_id', $currentStep->id)
                ->where('approver_user_id', $currentStep->approver_user_id)
                ->where('action', 'approved')
                ->exists();

            return $approved;
        } else {
            // This step requires approval from users with a specific role
            // Get all users with this role
            $usersWithRole = DB::table('model_has_roles')
                ->where('role_id', $currentStep->role_id)
                ->where('model_type', 'App\\Models\\User')
                ->pluck('model_id')
                ->toArray();

            if (empty($usersWithRole)) {
                // If no users have this role, consider it approved (edge case)
                \Log::warning('No users found with role ID ' . $currentStep->role_id . ' for step ' . $currentStep->id);
                return true;
            }

            // Get all approvals for this step
            $approvals = DB::table('requisition_approvals')
                ->where('requisition_id', $requisition->id)
                ->where('approval_workflow_step_id', $currentStep->id)
                ->where('action', 'approved')
                ->pluck('approver_user_id')
                ->toArray();

            // Check if at least one user with the required role has approved
            // This is the current behavior - we only need one user with the role to approve
            $roleApprovers = array_intersect($usersWithRole, $approvals);

            return !empty($roleApprovers);
        }
    }

    /**
     * Get requisitions where the user is an approver in the current step.
     *
     * @param int $userId
     * @param array $filters
     * @param int $perPage
     * @param int|null $departmentId
     * @return LengthAwarePaginator
     */
    public function getRequisitionsForUserApproval(int $userId, array $filters = [], int $perPage = 10, ?int $departmentId = null): LengthAwarePaginator
    {
        $query = $this->requisitionRepository->getQueryBuilder();

        // Filter by department if provided
        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        // Get requisitions where:
        // 1. The requisition is in pending_approval status
        $query->where('status', 'pending_approval');

        // 2. Exclude requisitions created by this user
        $query->where('requester_user_id', '!=', $userId);

        // 3. The current step is assigned directly to this user OR to a role the user has
        $query->where(function($q) use ($userId) {
            // Either the current step is assigned directly to this user
            $q->whereHas('currentApprovalStep', function ($query) use ($userId) {
                $query->where('approver_user_id', $userId);
            });

            // OR the current step is assigned to a role that this user has
            $q->orWhereHas('currentApprovalStep', function ($query) use ($userId) {
                $query->whereNull('approver_user_id')
                    ->whereHas('role', function ($query) use ($userId) {
                        $query->whereHas('users', function ($query) use ($userId) {
                            $query->where('users.id', $userId);
                        });
                    });
            });
        });

        // 4. Exclude requisitions that this user has already approved at the current step
        $query->whereNotExists(function ($query) use ($userId) {
            $query->select(DB::raw(1))
                ->from('requisition_approvals')
                ->whereRaw('requisition_approvals.requisition_id = requisitions.id')
                ->whereRaw('requisition_approvals.approval_workflow_step_id = requisitions.current_approval_step_id')
                ->where('requisition_approvals.approver_user_id', $userId)
                ->where('requisition_approvals.action', 'approved');
        });

        // Apply filters
        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($query) use ($search) {
                $query->where('requisition_number', 'like', "%{$search}%")
                    ->orWhere('purpose', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sort = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';
        $query->orderBy($sort, $direction);

        // Eager load relationships
        $query->with(['requester', 'department', 'currentApprovalStep']);

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Get count of pending requisitions that need the user's approval.
     *
     * @param int $userId
     * @return int
     */
    public function getPendingApprovalsCount(int $userId): int
    {
        $query = $this->requisitionRepository->getQueryBuilder();

        // Get requisitions where:
        // 1. The requisition is in pending_approval status
        $query->where('status', 'pending_approval');

        // 2. The current step is assigned directly to this user OR to a role the user has
        $query->where(function($q) use ($userId) {
            // Either the current step is assigned directly to this user
            $q->whereHas('currentApprovalStep', function ($query) use ($userId) {
                $query->where('approver_user_id', $userId);
            });

            // OR the current step is assigned to a role that this user has
            $q->orWhereHas('currentApprovalStep', function ($query) use ($userId) {
                $query->whereNull('approver_user_id')
                    ->whereHas('role', function ($query) use ($userId) {
                        $query->whereHas('users', function ($query) use ($userId) {
                            $query->where('users.id', $userId);
                        });
                    });
            });
        });

        // 3. Exclude requisitions that this user has already approved at the current step
        $query->whereNotExists(function ($query) use ($userId) {
            $query->select(DB::raw(1))
                ->from('requisition_approvals')
                ->whereRaw('requisition_approvals.requisition_id = requisitions.id')
                ->whereRaw('requisition_approvals.approval_workflow_step_id = requisitions.current_approval_step_id')
                ->where('requisition_approvals.approver_user_id', $userId)
                ->where('requisition_approvals.action', 'approved');
        });

        return $query->count();
    }

    /**
     * Get count of requisitions by status for a user.
     *
     * @param int $userId
     * @param string $status
     * @return int
     */
    public function getRequisitionCountByStatus(int $userId, string $status): int
    {
        $query = $this->requisitionRepository->getQueryBuilder();

        // Get requisitions created by the user with the specified status
        $query->where('requester_user_id', $userId)
              ->where('status', $status);

        return $query->count();
    }

    /**
     * Edit a rejected requisition and resubmit it.
     *
     * @param int $requisitionId
     * @param array $data
     * @param string $comments
     * @return mixed
     * @throws \Exception
     */
    public function editRejectedRequisition(int $requisitionId, array $data, string $comments)
    {
        return DB::transaction(function () use ($requisitionId, $data, $comments) {
            $user = Auth::user();
            $requisition = $this->requisitionRepository->findById($requisitionId);

            if (!$requisition) {
                throw new \Exception('Requisition not found');
            }

            // Check if the user is the original requester
            if ($requisition->requester_user_id !== $user->id) {
                throw new \Exception('Only the original requester can edit this requisition');
            }

            // Check if requisition is in rejected status
            if ($requisition->status !== 'rejected') {
                throw new \Exception('This requisition cannot be edited because it is not in rejected status');
            }

            // Update the requisition with new data
            $updateData = [
                'purpose' => $data['purpose'],
                'notes' => $data['notes'] ?? null,
                'status' => 'pending_approval',
                // Reset approval-related fields
                'approved_by' => null,
                'approved_at' => null,
                'rejected_by' => null,
                'rejected_at' => null,
            ];

            // Find the appropriate workflow and its first step using the ApprovalWorkflowService
            $workflowData = $this->approvalWorkflowService->findWorkflowAndStep(
                $requisition->organization_id,
                $requisition->branch_id,
                $requisition->department_id
            );

            $workflow = $workflowData['workflow'];
            $firstStep = $workflowData['step'];

            // Add workflow and step data to requisition
            if ($workflow) {
                $updateData['approval_workflow_id'] = $workflow->id;

                if ($firstStep) {
                    $updateData['current_approval_step_id'] = $firstStep->id;
                    $updateData['current_workflow_step'] = $firstStep->step_number;
                }
            }

            // First update the requisition basic data
            $this->requisitionRepository->update($requisitionId, $updateData);

            // Update requisition items if provided
            if (isset($data['requisition_items']) && is_array($data['requisition_items'])) {
                // First, delete existing items
                DB::table('requisition_items')->where('requisition_id', $requisitionId)->delete();

                // Then create new items
                $totalAmount = 0;
                foreach ($data['requisition_items'] as $itemData) {
                    // Determine chart_of_account_id based on chart_of_account_type or use existing one
                    $chartOfAccountId = $itemData['chart_of_account_id'] ?? null;
                    if (!$chartOfAccountId && isset($itemData['chart_of_account_type'])) {
                        $chartOfAccount = ChartOfAccount::where('account_type', $itemData['chart_of_account_type'])->first();
                        if (!$chartOfAccount) {
                            throw new \Exception('Invalid chart of account type: ' . $itemData['chart_of_account_type']);
                        }
                        $chartOfAccountId = $chartOfAccount->id;
                    }

                    if (!$chartOfAccountId) {
                        throw new \Exception('Chart of account ID is required for requisition items');
                    }

                    $newItemData = [
                        'requisition_id' => $requisition->id,
                        'chart_of_account_id' => $chartOfAccountId,
                        'description' => $itemData['description'],
                        'quantity' => $itemData['quantity'],
                        'unit_price' => $itemData['unit_price'],
                        'total_price' => $itemData['quantity'] * $itemData['unit_price'],
                    ];

                    $item = $this->requisitionItemRepository->create($newItemData);
                    $totalAmount += $item->total_price;
                }

                // Update the total amount on the requisition
                $this->requisitionRepository->update($requisitionId, [
                    'total_amount' => $totalAmount,
                ]);
            }

            // Create history record for resubmission
            $this->requisitionHistoryRepository->create([
                'requisition_id' => $requisitionId,
                'user_id' => $user->id,
                'action' => 'edited_and_resubmitted',
                'comments' => $comments . ' (Requisition was edited after rejection and resubmitted for approval)',
                'created_at' => now(),
            ]);

            // Create approval record for the first step if a workflow exists
            // This must be done AFTER all other updates to ensure the requisition is in the correct state
            if ($workflow && $firstStep) {
                // Find an approver for the step using the ApprovalWorkflowService
                $approverId = $this->approvalWorkflowService->findApproverForStep(
                    $firstStep->id,
                    $requisition->organization_id,
                    $user->id
                );

                // Create the approval record using the ApprovalWorkflowService
                $this->approvalWorkflowService->createApprovalRecord(
                    $requisition->id,
                    $firstStep->id,
                    $approverId,
                    'submitted',
                    'Requisition was edited after rejection and resubmitted for approval. It will go through the entire approval process again.'
                );

                \Log::info('Created approval record for resubmitted requisition: ' . $requisition->id);

                // Refresh the requisition to get the latest data
                $requisition = $this->requisitionRepository->findById($requisitionId);
            }

            return $requisition;
        });
    }

    /**
     * Get all requisitions where the user is or was an approver.
     *
     * @param int $userId
     * @param array $filters
     * @param int $perPage
     * @param int|null $departmentId
     * @return LengthAwarePaginator
     */
    public function getAllUserApprovalRequisitions(int $userId, array $filters = [], int $perPage = 10, ?int $departmentId = null): LengthAwarePaginator
    {
        $query = $this->requisitionRepository->getQueryBuilder();

        // Filter by department if provided
        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        // Get requisitions where the user has been involved in the approval process
        $query->whereHas('approvals', function ($query) use ($userId) {
            $query->where('approver_user_id', $userId);
        });

        // Or where the user is currently assigned as an approver
        $query->orWhere(function($q) use ($userId) {
            $q->where('status', 'pending_approval')
              ->where(function($sq) use ($userId) {
                  // Either the current step is assigned directly to this user
                  $sq->whereHas('currentApprovalStep', function ($query) use ($userId) {
                      $query->where('approver_user_id', $userId);
                  });

                  // OR the current step is assigned to a role that this user has
                  $sq->orWhereHas('currentApprovalStep', function ($query) use ($userId) {
                      $query->whereNull('approver_user_id')
                          ->whereHas('role', function ($query) use ($userId) {
                              $query->whereHas('users', function ($query) use ($userId) {
                                  $query->where('users.id', $userId);
                              });
                          });
                  });
              });
        });

        // Apply status filter if provided
        if (isset($filters['status']) && $filters['status'] && $filters['status'] !== 'all') {
            $query->where('status', $filters['status']);
        }

        // Apply search filter if provided
        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($query) use ($search) {
                $query->where('requisition_number', 'like', "%{$search}%")
                    ->orWhere('purpose', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sort = $filters['sort'] ?? 'created_at';
        $direction = $filters['direction'] ?? 'desc';
        $query->orderBy($sort, $direction);

        // Eager load relationships
        $query->with(['requester', 'department', 'currentApprovalStep', 'approvals']);

        return $query->paginate($perPage)->withQueryString();
    }

    /**
     * Move a requisition to the disbursement process.
     *
     * @param mixed $requisition
     * @return void
     */
    private function moveToDisbursal($requisition): void
    {
        try {
            // Get all items for the requisition
            $items = $this->requisitionItemRepository->getByRequisitionId($requisition->id);
            if ($items->isEmpty()) {
                \Log::info('No items to move to disbursement', ['requisition_id' => $requisition->id]);
                return;
            }

            // Convert items to array format for the disbursement service
            $itemsArray = $items->map(function ($item) {
                return [
                    'chart_of_account_id' => $item->chart_of_account_id,
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                ];
            })->toArray();

            // Create a transaction in the disbursement domain
            $transaction = $this->disbursementService->openTransaction($requisition, $itemsArray);

            if ($transaction) {
                \Log::info('Successfully moved requisition to disbursement process', [
                    'requisition_id' => $requisition->id,
                    'transaction_id' => $transaction->id
                ]);

                // Fire TransactionCreated event instead of RequisitionCompleted
                event(new \App\Events\TransactionCreated($requisition, $transaction));
            } else {
                \Log::error('Failed to move requisition to disbursement process', [
                    'requisition_id' => $requisition->id
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error moving requisition to disbursement process', [
                'requisition_id' => $requisition->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
