<?php

namespace Src\Requisition\Application\DTOs;

/**
 * Class RequisitionDTO
 * 
 * This class represents a Data Transfer Object for Requisition.
 */
class RequisitionDTO
{
    /**
     * @var int|null
     */
    public ?int $id;
    
    /**
     * @var int
     */
    public int $organizationId;
    
    /**
     * @var int
     */
    public int $branchId;
    
    /**
     * @var int
     */
    public int $departmentId;
    
    /**
     * @var int
     */
    public int $requesterUserId;
    
    /**
     * @var string
     */
    public string $purpose;
    
    /**
     * @var string|null
     */
    public ?string $notes;
    
    /**
     * @var string
     */
    public string $status;
    
    /**
     * @var float
     */
    public float $totalAmount;
    
    /**
     * @var int|null
     */
    public ?int $approvalWorkflowId;
    
    /**
     * @var int|null
     */
    public ?int $currentApprovalStepId;
    
    /**
     * @var int|null
     */
    public ?int $currentWorkflowStep;
    
    /**
     * @var array
     */
    public array $items = [];
    
    /**
     * Create a new DTO from an array.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $dto = new self();
        
        $dto->id = $data['id'] ?? null;
        $dto->organizationId = $data['organization_id'];
        $dto->branchId = $data['branch_id'];
        $dto->departmentId = $data['department_id'];
        $dto->requesterUserId = $data['requester_user_id'];
        $dto->purpose = $data['purpose'];
        $dto->notes = $data['notes'] ?? null;
        $dto->status = $data['status'] ?? 'draft';
        $dto->totalAmount = $data['total_amount'] ?? 0;
        $dto->approvalWorkflowId = $data['approval_workflow_id'] ?? null;
        $dto->currentApprovalStepId = $data['current_approval_step_id'] ?? null;
        $dto->currentWorkflowStep = $data['current_workflow_step'] ?? null;
        
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as $item) {
                $dto->items[] = RequisitionItemDTO::fromArray($item);
            }
        }
        
        return $dto;
    }
    
    /**
     * Convert DTO to array.
     *
     * @return array
     */
    public function toArray(): array
    {
        $items = [];
        foreach ($this->items as $item) {
            $items[] = $item->toArray();
        }
        
        return [
            'id' => $this->id,
            'organization_id' => $this->organizationId,
            'branch_id' => $this->branchId,
            'department_id' => $this->departmentId,
            'requester_user_id' => $this->requesterUserId,
            'purpose' => $this->purpose,
            'notes' => $this->notes,
            'status' => $this->status,
            'total_amount' => $this->totalAmount,
            'approval_workflow_id' => $this->approvalWorkflowId,
            'current_approval_step_id' => $this->currentApprovalStepId,
            'current_workflow_step' => $this->currentWorkflowStep,
            'items' => $items,
        ];
    }
}
