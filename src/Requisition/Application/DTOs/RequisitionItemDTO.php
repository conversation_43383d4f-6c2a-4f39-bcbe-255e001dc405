<?php

namespace Src\Requisition\Application\DTOs;

/**
 * Class RequisitionItemDTO
 * 
 * This class represents a Data Transfer Object for RequisitionItem.
 */
class RequisitionItemDTO
{
    /**
     * @var int|null
     */
    public ?int $id;
    
    /**
     * @var int|null
     */
    public ?int $requisitionId;
    
    /**
     * @var int
     */
    public int $chartOfAccountId;
    
    /**
     * @var string
     */
    public string $description;
    
    /**
     * @var int
     */
    public int $quantity;
    
    /**
     * @var float
     */
    public float $unitPrice;
    
    /**
     * @var float
     */
    public float $totalPrice;
    
    /**
     * Create a new DTO from an array.
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $dto = new self();
        
        $dto->id = $data['id'] ?? null;
        $dto->requisitionId = $data['requisition_id'] ?? null;
        $dto->chartOfAccountId = $data['chart_of_account_id'];
        $dto->description = $data['description'];
        $dto->quantity = $data['quantity'];
        $dto->unitPrice = $data['unit_price'];
        $dto->totalPrice = $data['total_price'] ?? ($data['quantity'] * $data['unit_price']);
        
        return $dto;
    }
    
    /**
     * Convert DTO to array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'requisition_id' => $this->requisitionId,
            'chart_of_account_id' => $this->chartOfAccountId,
            'description' => $this->description,
            'quantity' => $this->quantity,
            'unit_price' => $this->unitPrice,
            'total_price' => $this->totalPrice,
        ];
    }
}
