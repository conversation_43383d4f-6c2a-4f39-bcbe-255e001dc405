<?php

namespace Src\Requisition\Domain\Repositories;

use App\Models\RequisitionItem;
use Illuminate\Database\Eloquent\Collection;

/**
 * Interface RequisitionItemRepositoryInterface
 * 
 * This interface defines the contract for requisition item data access.
 */
interface RequisitionItemRepositoryInterface
{
    /**
     * Create a new requisition item.
     *
     * @param array $data
     * @return RequisitionItem
     */
    public function create(array $data): RequisitionItem;
    
    /**
     * Create multiple requisition items.
     *
     * @param array $items
     * @return Collection
     */
    public function createMany(array $items): Collection;
    
    /**
     * Get items by requisition ID.
     *
     * @param int $requisitionId
     * @return Collection
     */
    public function getByRequisitionId(int $requisitionId): Collection;
    
    /**
     * Delete items by requisition ID.
     *
     * @param int $requisitionId
     * @return bool
     */
    public function deleteByRequisitionId(int $requisitionId): bool;
}
