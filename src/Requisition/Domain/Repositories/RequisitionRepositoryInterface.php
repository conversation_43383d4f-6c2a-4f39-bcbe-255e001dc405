<?php

namespace Src\Requisition\Domain\Repositories;

use App\Models\Requisition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Interface RequisitionRepositoryInterface
 *
 * This interface defines the contract for requisition data access.
 */
interface RequisitionRepositoryInterface
{
    /**
     * Get all requisitions with optional filtering and pagination.
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllWithFilters(array $filters, int $perPage = 10): LengthAwarePaginator;

    /**
     * Find a requisition by its ID.
     *
     * @param int $id
     * @return Requisition|null
     */
    public function findById(int $id): ?Requisition;

    /**
     * Create a new requisition.
     *
     * @param array $data
     * @return Requisition
     */
    public function create(array $data): Requisition;

    /**
     * Update an existing requisition.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a requisition.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get requisitions by user ID with optional department filtering.
     *
     * @param int $userId
     * @param int|null $departmentId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getByUserIdWithDepartmentFilter(int $userId, ?int $departmentId = null, int $perPage = 10): LengthAwarePaginator;

    /**
     * Get requisitions for approval by department ID.
     *
     * @param int $departmentId
     * @param string $status
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getForApprovalByDepartmentId(int $departmentId, string $status = 'pending_approval', int $perPage = 10): LengthAwarePaginator;

    /**
     * Get a query builder instance for requisitions.
     * This allows for more complex queries to be built.
     *
     * @return Builder
     */
    public function getQueryBuilder(): Builder;
}
