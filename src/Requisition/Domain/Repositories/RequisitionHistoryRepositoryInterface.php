<?php

namespace Src\Requisition\Domain\Repositories;

use App\Models\RequisitionHistory;
use Illuminate\Database\Eloquent\Collection;

/**
 * Interface RequisitionHistoryRepositoryInterface
 * 
 * This interface defines the contract for requisition history data access.
 */
interface RequisitionHistoryRepositoryInterface
{
    /**
     * Create a new history record.
     *
     * @param array $data
     * @return RequisitionHistory
     */
    public function create(array $data): RequisitionHistory;
    
    /**
     * Get history by requisition ID.
     *
     * @param int $requisitionId
     * @return Collection
     */
    public function getByRequisitionId(int $requisitionId): Collection;
}
