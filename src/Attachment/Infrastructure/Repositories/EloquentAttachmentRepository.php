<?php

namespace Src\Attachment\Infrastructure\Repositories;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Src\Attachment\Domain\Repositories\AttachmentRepositoryInterface;

/**
 * Class EloquentAttachmentRepository
 *
 * This class implements the AttachmentRepositoryInterface using Eloquent ORM.
 */
class EloquentAttachmentRepository implements AttachmentRepositoryInterface
{
    /**
     * @var Attachment
     */
    protected $model;

    /**
     * EloquentAttachmentRepository constructor.
     *
     * @param Attachment $model
     */
    public function __construct(Attachment $model)
    {
        $this->model = $model;
    }

    /**
     * Find an attachment by its ID.
     *
     * @param int $id
     * @return Attachment|null
     */
    public function findById(int $id): ?Attachment
    {
        return $this->model->find($id);
    }

    /**
     * Create a new attachment.
     *
     * @param array $data
     * @return Attachment
     */
    public function create(array $data): Attachment
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing attachment.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $attachment = $this->findById($id);

        if (!$attachment) {
            return false;
        }

        return $attachment->update($data);
    }

    /**
     * Delete an attachment.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $attachment = $this->findById($id);

        if (!$attachment) {
            return false;
        }

        return $attachment->delete();
    }

    /**
     * Get attachments for a specific attachable model.
     *
     * @param Model $attachable
     * @return Collection
     */
    public function getByAttachable(Model $attachable): Collection
    {
        return $this->model->where('attachable_type', get_class($attachable))
            ->where('attachable_id', $attachable->id)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get evidence attachments for a specific attachable model.
     *
     * @param Model $attachable
     * @return Collection
     */
    public function getEvidenceByAttachable(Model $attachable): Collection
    {
        return $this->model->where('attachable_type', get_class($attachable))
            ->where('attachable_id', $attachable->id)
            ->where('is_evidence', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get attachments by type.
     *
     * @param string $attachableType
     * @return Collection
     */
    public function getByType(string $attachableType): Collection
    {
        return $this->model->where('attachable_type', $attachableType)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get attachments uploaded at a specific step.
     *
     * @param string $step
     * @return Collection
     */
    public function getByUploadStep(string $step): Collection
    {
        return $this->model->where('uploaded_at_step', $step)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get attachments uploaded by a specific user.
     *
     * @param int $userId
     * @return Collection
     */
    public function getByUploader(int $userId): Collection
    {
        return $this->model->where('uploaded_by', $userId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Check if an attachment exists.
     *
     * @param int $id
     * @return bool
     */
    public function exists(int $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    /**
     * Get attachments with their uploaders.
     *
     * @param Model $attachable
     * @return Collection
     */
    public function getWithUploader(Model $attachable): Collection
    {
        return $this->model->where('attachable_type', get_class($attachable))
            ->where('attachable_id', $attachable->id)
            ->with('uploader')
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
