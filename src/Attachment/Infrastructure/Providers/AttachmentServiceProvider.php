<?php

namespace Src\Attachment\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use Src\Attachment\Domain\Repositories\AttachmentRepositoryInterface;
use Src\Attachment\Infrastructure\Repositories\EloquentAttachmentRepository;
use Src\Attachment\Application\Services\AttachmentService;

/**
 * Class AttachmentServiceProvider
 *
 * This service provider registers all attachment-related services and repositories.
 */
class AttachmentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the repository
        $this->app->bind(
            AttachmentRepositoryInterface::class,
            EloquentAttachmentRepository::class
        );

        // Register the service
        $this->app->bind(AttachmentService::class, function ($app) {
            return new AttachmentService(
                $app->make(AttachmentRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Routes are defined in routes/web.php to keep all framework-specific code together
        // and separate from the domain logic.
    }
}
