<?php

namespace Src\Attachment\Application\DTOs;

/**
 * Class AttachmentDTO
 * 
 * This class represents a Data Transfer Object for Attachment.
 */
class AttachmentDTO
{
    /**
     * @var int|null
     */
    public ?int $id;
    
    /**
     * @var string
     */
    public string $attachableType;
    
    /**
     * @var int
     */
    public int $attachableId;
    
    /**
     * @var int
     */
    public int $uploadedBy;
    
    /**
     * @var string
     */
    public string $originalName;
    
    /**
     * @var string
     */
    public string $fileName;
    
    /**
     * @var string
     */
    public string $filePath;
    
    /**
     * @var int
     */
    public int $fileSize;
    
    /**
     * @var string
     */
    public string $mimeType;
    
    /**
     * @var string|null
     */
    public ?string $fileHash;
    
    /**
     * @var string|null
     */
    public ?string $description;
    
    /**
     * @var bool
     */
    public bool $isEvidence;
    
    /**
     * @var string|null
     */
    public ?string $uploadedAtStep;

    /**
     * AttachmentDTO constructor.
     *
     * @param array $data
     */
    public function __construct(array $data = [])
    {
        $this->id = $data['id'] ?? null;
        $this->attachableType = $data['attachable_type'] ?? '';
        $this->attachableId = $data['attachable_id'] ?? 0;
        $this->uploadedBy = $data['uploaded_by'] ?? 0;
        $this->originalName = $data['original_name'] ?? '';
        $this->fileName = $data['file_name'] ?? '';
        $this->filePath = $data['file_path'] ?? '';
        $this->fileSize = $data['file_size'] ?? 0;
        $this->mimeType = $data['mime_type'] ?? '';
        $this->fileHash = $data['file_hash'] ?? null;
        $this->description = $data['description'] ?? null;
        $this->isEvidence = $data['is_evidence'] ?? true;
        $this->uploadedAtStep = $data['uploaded_at_step'] ?? null;
    }

    /**
     * Convert DTO to array.
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'attachable_type' => $this->attachableType,
            'attachable_id' => $this->attachableId,
            'uploaded_by' => $this->uploadedBy,
            'original_name' => $this->originalName,
            'file_name' => $this->fileName,
            'file_path' => $this->filePath,
            'file_size' => $this->fileSize,
            'mime_type' => $this->mimeType,
            'file_hash' => $this->fileHash,
            'description' => $this->description,
            'is_evidence' => $this->isEvidence,
            'uploaded_at_step' => $this->uploadedAtStep,
        ];

        if ($this->id !== null) {
            $data['id'] = $this->id;
        }

        return $data;
    }

    /**
     * Create DTO from Eloquent model.
     *
     * @param \App\Models\Attachment $attachment
     * @return static
     */
    public static function fromModel(\App\Models\Attachment $attachment): self
    {
        return new self([
            'id' => $attachment->id,
            'attachable_type' => $attachment->attachable_type,
            'attachable_id' => $attachment->attachable_id,
            'uploaded_by' => $attachment->uploaded_by,
            'original_name' => $attachment->original_name,
            'file_name' => $attachment->file_name,
            'file_path' => $attachment->file_path,
            'file_size' => $attachment->file_size,
            'mime_type' => $attachment->mime_type,
            'file_hash' => $attachment->file_hash,
            'description' => $attachment->description,
            'is_evidence' => $attachment->is_evidence,
            'uploaded_at_step' => $attachment->uploaded_at_step,
        ]);
    }
}
