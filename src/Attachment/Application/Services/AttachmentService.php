<?php

namespace Src\Attachment\Application\Services;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Src\Attachment\Domain\Repositories\AttachmentRepositoryInterface;
use Src\Attachment\Application\DTOs\AttachmentDTO;

/**
 * Class AttachmentService
 *
 * This service handles all attachment-related business logic.
 */
class AttachmentService
{
    /**
     * @var AttachmentRepositoryInterface
     */
    protected AttachmentRepositoryInterface $attachmentRepository;

    /**
     * AttachmentService constructor.
     *
     * @param AttachmentRepositoryInterface $attachmentRepository
     */
    public function __construct(AttachmentRepositoryInterface $attachmentRepository)
    {
        $this->attachmentRepository = $attachmentRepository;
    }

    /**
     * Upload and store a file attachment.
     *
     * @param UploadedFile $file
     * @param Model $attachable
     * @param string|null $description
     * @param bool $isEvidence
     * @param string|null $uploadedAtStep
     * @return Attachment
     */
    public function uploadFile(
        UploadedFile $file,
        Model $attachable,
        ?string $description = null,
        bool $isEvidence = true,
        ?string $uploadedAtStep = null
    ): Attachment {
        // Generate unique filename
        $fileName = $this->generateUniqueFileName($file);
        
        // Define storage path based on attachable type and ID
        $storagePath = $this->getStoragePath($attachable, $fileName);
        
        // Store the file
        $filePath = Storage::disk('private')->putFileAs(
            dirname($storagePath),
            $file,
            basename($storagePath)
        );
        
        // Calculate file hash for integrity
        $fileHash = hash_file('sha256', $file->getRealPath());
        
        // Create attachment DTO
        $attachmentDTO = new AttachmentDTO([
            'attachable_type' => get_class($attachable),
            'attachable_id' => $attachable->id,
            'uploaded_by' => Auth::id(),
            'original_name' => $file->getClientOriginalName(),
            'file_name' => $fileName,
            'file_path' => $filePath,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'file_hash' => $fileHash,
            'description' => $description,
            'is_evidence' => $isEvidence,
            'uploaded_at_step' => $uploadedAtStep,
        ]);

        // Create attachment record
        return $this->attachmentRepository->create($attachmentDTO->toArray());
    }

    /**
     * Upload multiple files at once.
     *
     * @param array $files
     * @param Model $attachable
     * @param array|null $descriptions
     * @param bool $isEvidence
     * @param string|null $uploadedAtStep
     * @return array
     */
    public function uploadMultipleFiles(
        array $files,
        Model $attachable,
        ?array $descriptions = null,
        bool $isEvidence = true,
        ?string $uploadedAtStep = null
    ): array {
        $attachments = [];
        
        foreach ($files as $index => $file) {
            if ($file instanceof UploadedFile) {
                $description = $descriptions[$index] ?? null;
                $attachments[] = $this->uploadFile(
                    $file,
                    $attachable,
                    $description,
                    $isEvidence,
                    $uploadedAtStep
                );
            }
        }
        
        return $attachments;
    }

    /**
     * Delete an attachment and its file.
     *
     * @param int $attachmentId
     * @return bool
     */
    public function deleteAttachment(int $attachmentId): bool
    {
        $attachment = $this->attachmentRepository->findById($attachmentId);
        
        if (!$attachment) {
            return false;
        }

        // Delete the file from storage
        $fileDeleted = $attachment->deleteFile();
        
        // Delete the database record
        $recordDeleted = $this->attachmentRepository->delete($attachmentId);
        
        return $fileDeleted && $recordDeleted;
    }

    /**
     * Copy attachments from one model to another.
     *
     * @param Model $source
     * @param Model $target
     * @param string|null $newStep
     * @return array
     */
    public function copyAttachments(Model $source, Model $target, ?string $newStep = null): array
    {
        $sourceAttachments = $this->attachmentRepository->getByAttachable($source);
        $copiedAttachments = [];
        
        foreach ($sourceAttachments as $attachment) {
            // Copy the file to new location
            $newFileName = $this->generateUniqueFileName(null, $attachment->original_name);
            $newStoragePath = $this->getStoragePath($target, $newFileName);
            
            // Copy file in storage
            $copied = Storage::disk('private')->copy(
                $attachment->file_path,
                $newStoragePath
            );
            
            if ($copied) {
                // Create new attachment DTO
                $attachmentDTO = new AttachmentDTO([
                    'attachable_type' => get_class($target),
                    'attachable_id' => $target->id,
                    'uploaded_by' => $attachment->uploaded_by,
                    'original_name' => $attachment->original_name,
                    'file_name' => $newFileName,
                    'file_path' => $newStoragePath,
                    'file_size' => $attachment->file_size,
                    'mime_type' => $attachment->mime_type,
                    'file_hash' => $attachment->file_hash,
                    'description' => $attachment->description,
                    'is_evidence' => $attachment->is_evidence,
                    'uploaded_at_step' => $newStep ?? 'copied_from_' . strtolower(class_basename($source)),
                ]);

                $copiedAttachments[] = $this->attachmentRepository->create($attachmentDTO->toArray());
            }
        }
        
        return $copiedAttachments;
    }

    /**
     * Get attachments for a model.
     *
     * @param Model $attachable
     * @param bool $withUploader
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAttachments(Model $attachable, bool $withUploader = false): \Illuminate\Database\Eloquent\Collection
    {
        if ($withUploader) {
            return $this->attachmentRepository->getWithUploader($attachable);
        }

        return $this->attachmentRepository->getByAttachable($attachable);
    }

    /**
     * Get evidence attachments for a model.
     *
     * @param Model $attachable
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getEvidenceAttachments(Model $attachable): \Illuminate\Database\Eloquent\Collection
    {
        return $this->attachmentRepository->getEvidenceByAttachable($attachable);
    }

    /**
     * Get file content for download.
     *
     * @param int $attachmentId
     * @return string|null
     */
    public function getFileContent(int $attachmentId): ?string
    {
        $attachment = $this->attachmentRepository->findById($attachmentId);
        
        if (!$attachment || !$attachment->fileExists()) {
            return null;
        }
        
        return Storage::disk('private')->get($attachment->file_path);
    }

    /**
     * Validate file upload.
     *
     * @param UploadedFile $file
     * @return array
     */
    public function validateFile(UploadedFile $file): array
    {
        $errors = [];
        
        // Check file size (max 10MB)
        if ($file->getSize() > 10 * 1024 * 1024) {
            $errors[] = 'File size must not exceed 10MB.';
        }
        
        // Check allowed file types
        $allowedMimes = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
        ];
        
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            $errors[] = 'File type not allowed. Allowed types: PDF, Images, Word, Excel, Text files.';
        }
        
        return $errors;
    }

    /**
     * Generate a unique filename.
     *
     * @param UploadedFile|null $file
     * @param string|null $originalName
     * @return string
     */
    private function generateUniqueFileName(?UploadedFile $file = null, ?string $originalName = null): string
    {
        $extension = $file ? $file->getClientOriginalExtension() : pathinfo($originalName, PATHINFO_EXTENSION);
        return Str::uuid() . '.' . $extension;
    }

    /**
     * Get storage path for an attachable model.
     *
     * @param Model $attachable
     * @param string $fileName
     * @return string
     */
    private function getStoragePath(Model $attachable, string $fileName): string
    {
        $modelName = strtolower(class_basename($attachable));
        $modelId = $attachable->id;
        $year = date('Y');
        $month = date('m');
        
        return "attachments/{$modelName}/{$year}/{$month}/{$modelId}/{$fileName}";
    }
}
