<?php

namespace Src\Attachment\Domain\Repositories;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

/**
 * Interface AttachmentRepositoryInterface
 *
 * This interface defines the contract for attachment data access.
 */
interface AttachmentRepositoryInterface
{
    /**
     * Find an attachment by its ID.
     *
     * @param int $id
     * @return Attachment|null
     */
    public function findById(int $id): ?Attachment;

    /**
     * Create a new attachment.
     *
     * @param array $data
     * @return Attachment
     */
    public function create(array $data): Attachment;

    /**
     * Update an existing attachment.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete an attachment.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get attachments for a specific attachable model.
     *
     * @param Model $attachable
     * @return Collection
     */
    public function getByAttachable(Model $attachable): Collection;

    /**
     * Get evidence attachments for a specific attachable model.
     *
     * @param Model $attachable
     * @return Collection
     */
    public function getEvidenceByAttachable(Model $attachable): Collection;

    /**
     * Get attachments by type.
     *
     * @param string $attachableType
     * @return Collection
     */
    public function getByType(string $attachableType): Collection;

    /**
     * Get attachments uploaded at a specific step.
     *
     * @param string $step
     * @return Collection
     */
    public function getByUploadStep(string $step): Collection;

    /**
     * Get attachments uploaded by a specific user.
     *
     * @param int $userId
     * @return Collection
     */
    public function getByUploader(int $userId): Collection;

    /**
     * Check if an attachment exists.
     *
     * @param int $id
     * @return bool
     */
    public function exists(int $id): bool;

    /**
     * Get attachments with their uploaders.
     *
     * @param Model $attachable
     * @return Collection
     */
    public function getWithUploader(Model $attachable): Collection;
}
