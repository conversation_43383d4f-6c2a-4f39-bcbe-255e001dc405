<?php

namespace Src\WorkflowTemplate\Application\DTOs;

class TemplateData
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly string $category,
        public readonly ?string $description,
        public readonly array $templateData,
        public readonly array $validationRules,
        public readonly bool $isActive,
        public readonly int $sortOrder
    ) {}

    /**
     * Create from array data.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'],
            name: $data['name'],
            category: $data['category'],
            description: $data['description'] ?? null,
            templateData: $data['template_data'] ?? [],
            validationRules: $data['validation_rules'] ?? [],
            isActive: $data['is_active'] ?? true,
            sortOrder: $data['sort_order'] ?? 0
        );
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'category' => $this->category,
            'description' => $this->description,
            'template_data' => $this->templateData,
            'validation_rules' => $this->validationRules,
            'is_active' => $this->isActive,
            'sort_order' => $this->sortOrder,
        ];
    }

    /**
     * Generate workflow data for the given context.
     */
    public function generateWorkflowData(array $context = []): array
    {
        $workflowData = $this->templateData;

        // Apply context-specific transformations
        if (isset($context['organization_id'])) {
            $workflowData['organization_id'] = $context['organization_id'];
        }

        if (isset($context['branch_id'])) {
            $workflowData['branch_id'] = $context['branch_id'];
        }

        if (isset($context['department_id'])) {
            $workflowData['department_id'] = $context['department_id'];
        }

        // Ensure name is customizable
        if (isset($context['custom_name'])) {
            $workflowData['name'] = $context['custom_name'];
        } else {
            $workflowData['name'] = $this->name . ' - ' . now()->format('Y-m-d H:i');
        }

        return $workflowData;
    }

    /**
     * Get validation rules merged with base workflow validation.
     */
    public function getValidationRules(): array
    {
        $baseRules = [
            'name' => 'required|string|max:255',
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'steps' => 'required|array|min:1',
            'steps.*.step_number' => 'required|integer|min:1',
            'steps.*.role_id' => 'nullable|exists:roles,id',
            'steps.*.approver_user_id' => 'nullable|exists:users,id',
            'steps.*.description' => 'nullable|string|max:255',
        ];

        return array_merge($baseRules, $this->validationRules);
    }
}
