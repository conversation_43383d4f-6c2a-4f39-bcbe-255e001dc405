<?php

namespace Src\WorkflowTemplate\Application\Services;

use Src\WorkflowTemplate\Domain\Repositories\WorkflowTemplateRepositoryInterface;
use Src\WorkflowTemplate\Application\DTOs\TemplateData;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Models\Role;

class WorkflowTemplateService
{
    public function __construct(
        private WorkflowTemplateRepositoryInterface $templateRepository,
        private ApprovalWorkflowRepositoryInterface $workflowRepository,
        private ApprovalWorkflowStepRepositoryInterface $stepRepository
    ) {}

    /**
     * Get all active templates grouped by category.
     */
    public function getTemplatesByCategory(): array
    {
        $templates = $this->templateRepository->getAllActive();
        $grouped = [];

        foreach ($templates as $template) {
            $grouped[$template->category][] = TemplateData::fromArray($template->toArray());
        }

        return $grouped;
    }

    /**
     * Get templates for a specific category.
     */
    public function getTemplatesForCategory(string $category): Collection
    {
        return $this->templateRepository->getByCategory($category)
            ->map(fn($template) => TemplateData::fromArray($template->toArray()));
    }

    /**
     * Get a template by ID.
     */
    public function getTemplate(int $id): ?TemplateData
    {
        $template = $this->templateRepository->findById($id);

        if (!$template) {
            return null;
        }

        return TemplateData::fromArray($template->toArray());
    }

    /**
     * Create a workflow from a template.
     */
    public function createWorkflowFromTemplate(int $templateId, array $context): array
    {
        $template = $this->getTemplate($templateId);

        if (!$template) {
            throw new \InvalidArgumentException('Template not found');
        }

        // Generate workflow data from template
        $workflowData = $template->generateWorkflowData($context);

        // Map template steps to actual roles
        $workflowData = $this->mapTemplateStepsToRoles($workflowData, $context);

        // Validate the generated data
        $validator = Validator::make($workflowData, $template->getValidationRules());

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Create the workflow using existing infrastructure
        return DB::transaction(function () use ($workflowData, $templateId) {
            // Extract steps data
            $stepsData = $workflowData['steps'];
            unset($workflowData['steps']);

            // If this is set as default, unset any existing defaults with the same scope
            if ($workflowData['is_default'] ?? false) {
                $this->workflowRepository->setAsDefault($workflowData['organization_id']);
            }

            // Create the workflow
            $workflow = $this->workflowRepository->create($workflowData);

            // Create the workflow steps
            foreach ($stepsData as $step) {
                $this->stepRepository->create([
                    'approval_workflow_id' => $workflow->id,
                    'step_number' => $step['step_number'],
                    'role_id' => $step['role_id'],
                    'approver_user_id' => $step['approver_user_id'] ?? null,
                    'description' => $step['description'] ?? null,
                ]);
            }

            return [
                'workflow' => $workflow,
                'template_used' => $templateId,
                'success' => true
            ];
        });
    }

    /**
     * Preview workflow data from template.
     */
    public function previewWorkflowFromTemplate(int $templateId, array $context): array
    {
        $template = $this->getTemplate($templateId);

        if (!$template) {
            throw new \InvalidArgumentException('Template not found');
        }

        $workflowData = $template->generateWorkflowData($context);

        // Map template steps to actual roles
        $workflowData = $this->mapTemplateStepsToRoles($workflowData, $context);

        return $workflowData;
    }

    /**
     * Get all available categories.
     */
    public function getCategories(): array
    {
        return $this->templateRepository->getCategories();
    }

    /**
     * Validate template data against context.
     */
    public function validateTemplateData(int $templateId, array $context): array
    {
        $template = $this->getTemplate($templateId);

        if (!$template) {
            throw new \InvalidArgumentException('Template not found');
        }

        $workflowData = $template->generateWorkflowData($context);

        // Map template steps to actual roles
        $workflowData = $this->mapTemplateStepsToRoles($workflowData, $context);

        // Basic validation
        $validator = Validator::make($workflowData, $template->getValidationRules());
        $errors = $validator->errors()->toArray();

        // Custom validation for missing roles
        $customErrors = $this->validateRoleAssignments($workflowData, $context);
        $errors = array_merge($errors, $customErrors);

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'data' => $workflowData
        ];
    }

    /**
     * Map template steps to actual roles in the organization.
     */
    private function mapTemplateStepsToRoles(array $workflowData, array $context): array
    {
        if (!isset($workflowData['steps']) || !isset($context['organization_id'])) {
            return $workflowData;
        }

        $organizationId = $context['organization_id'];
        $branchId = $context['branch_id'] ?? null;
        $departmentId = $context['department_id'] ?? null;

        // Define role mapping based on step descriptions
        $roleMapping = [
            'Department Head Approval' => 'HOD',
            'Direct Supervisor Approval' => 'HOD',
            'Finance Manager Approval' => 'Finance Manager',
            'Finance Manager Analysis' => 'Finance Manager',
            'Organization Admin Final Approval' => 'Organization Admin',
            'Department Head Review' => 'HOD',
            'HR Manager Final Approval' => 'HR Manager',
        ];

        foreach ($workflowData['steps'] as &$step) {
            if ($step['role_id'] === null && isset($step['description'])) {
                $roleName = $roleMapping[$step['description']] ?? null;

                if ($roleName) {
                    // For department-specific roles like HOD, ensure we have department context
                    if ($roleName === 'HOD' && !$departmentId) {
                        // If no department specified, we cannot assign a department-specific HOD
                        // Leave role_id as null and add a validation error
                        continue;
                    }

                    $role = $this->findRoleInOrganization($roleName, $organizationId, $branchId, $departmentId);
                    if ($role) {
                        $step['role_id'] = $role->id;
                    }
                }
            }
        }

        return $workflowData;
    }

    /**
     * Find a role in the organization hierarchy.
     */
    private function findRoleInOrganization(string $roleName, int $organizationId, ?int $branchId = null, ?int $departmentId = null): ?Role
    {
        // For department-specific roles like HOD, only look in the specified department
        if ($departmentId && in_array($roleName, ['HOD', 'Department Head'])) {
            return Role::where('name', $roleName)
                ->where('organization_id', $organizationId)
                ->where('department_id', $departmentId)
                ->first();
        }

        // Try to find role at department level first (for non-HOD roles)
        if ($departmentId) {
            $role = Role::where('name', $roleName)
                ->where('organization_id', $organizationId)
                ->where('department_id', $departmentId)
                ->first();

            if ($role) {
                return $role;
            }
        }

        // Try to find role at branch level
        if ($branchId) {
            $role = Role::where('name', $roleName)
                ->where('organization_id', $organizationId)
                ->where('branch_id', $branchId)
                ->whereNull('department_id')
                ->first();

            if ($role) {
                return $role;
            }
        }

        // Try to find role at organization level
        $role = Role::where('name', $roleName)
            ->where('organization_id', $organizationId)
            ->whereNull('branch_id')
            ->whereNull('department_id')
            ->first();

        if ($role) {
            return $role;
        }

        // For non-HOD roles, if no department context provided, try to find any role with this name
        // This allows Finance Manager, HR Manager, etc. to be found even without department context
        if (!in_array($roleName, ['HOD', 'Department Head'])) {
            $role = Role::where('name', $roleName)
                ->where('organization_id', $organizationId)
                ->first();

            return $role;
        }

        return null;
    }

    /**
     * Validate that all workflow steps have proper role assignments.
     */
    private function validateRoleAssignments(array $workflowData, array $context): array
    {
        $errors = [];

        if (!isset($workflowData['steps'])) {
            return $errors;
        }

        foreach ($workflowData['steps'] as $index => $step) {
            $stepKey = "steps.{$index}";

            // Check if step has neither role_id nor approver_user_id
            if (empty($step['role_id']) && empty($step['approver_user_id'])) {
                $stepDescription = $step['description'] ?? "Step " . ($index + 1);

                // Provide specific error messages based on step type
                if (strpos($stepDescription, 'Department Head') !== false || strpos($stepDescription, 'HOD') !== false) {
                    if (empty($context['department_id'])) {
                        $errors["{$stepKey}.department_required"] = [
                            "Department selection is required for '{$stepDescription}'. Please select a department to assign the appropriate Head of Department."
                        ];
                    } else {
                        $departmentName = $this->getDepartmentName($context['department_id']);
                        $errors["{$stepKey}.role_missing"] = [
                            "No Head of Department role found for {$departmentName}. Please create an HOD role for this department or select a different template."
                        ];
                    }
                } else {
                    $errors["{$stepKey}.role_missing"] = [
                        "No suitable role found for '{$stepDescription}'. Please ensure the required roles exist in your organization."
                    ];
                }
            }
        }

        return $errors;
    }

    /**
     * Get department name by ID.
     */
    private function getDepartmentName(int $departmentId): string
    {
        $department = \App\Models\Department::find($departmentId);
        return $department ? $department->name : "Department ID {$departmentId}";
    }
}
