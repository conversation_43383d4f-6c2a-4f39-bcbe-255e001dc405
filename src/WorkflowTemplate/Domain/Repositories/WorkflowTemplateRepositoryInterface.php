<?php

namespace Src\WorkflowTemplate\Domain\Repositories;

use App\Models\WorkflowTemplate;
use Illuminate\Support\Collection;

interface WorkflowTemplateRepositoryInterface
{
    /**
     * Get all active workflow templates.
     *
     * @return Collection
     */
    public function getAllActive(): Collection;

    /**
     * Get templates by category.
     *
     * @param string $category
     * @return Collection
     */
    public function getByCategory(string $category): Collection;

    /**
     * Find a template by ID.
     *
     * @param int $id
     * @return WorkflowTemplate|null
     */
    public function findById(int $id): ?WorkflowTemplate;

    /**
     * Create a new workflow template.
     *
     * @param array $data
     * @return WorkflowTemplate
     */
    public function create(array $data): WorkflowTemplate;

    /**
     * Update a workflow template.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a workflow template.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool;

    /**
     * Get all available categories.
     *
     * @return array
     */
    public function getCategories(): array;

    /**
     * Get templates ordered by sort order and name.
     *
     * @return Collection
     */
    public function getOrdered(): Collection;

    /**
     * Toggle template active status.
     *
     * @param int $id
     * @return bool
     */
    public function toggleActive(int $id): bool;
}
