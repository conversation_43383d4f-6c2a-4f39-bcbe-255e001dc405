<?php

namespace Src\WorkflowTemplate\Infrastructure\Providers;

use Illuminate\Support\ServiceProvider;
use Src\WorkflowTemplate\Application\Services\WorkflowTemplateService;
use App\Models\WorkflowTemplate;
use Src\WorkflowTemplate\Domain\Repositories\WorkflowTemplateRepositoryInterface;
use Src\WorkflowTemplate\Infrastructure\Repositories\EloquentWorkflowTemplateRepository;

class WorkflowTemplateServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind the repository interface to its implementation
        $this->app->bind(WorkflowTemplateRepositoryInterface::class, function ($app) {
            return new EloquentWorkflowTemplateRepository($app->make(WorkflowTemplate::class));
        });

        // Register the service
        $this->app->singleton(WorkflowTemplateService::class, function ($app) {
            return new WorkflowTemplateService(
                $app->make(WorkflowTemplateRepositoryInterface::class),
                $app->make(\Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface::class),
                $app->make(\Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
