<?php

namespace Src\WorkflowTemplate\Infrastructure\Repositories;

use App\Models\WorkflowTemplate;
use Src\WorkflowTemplate\Domain\Repositories\WorkflowTemplateRepositoryInterface;
use Illuminate\Support\Collection;

class EloquentWorkflowTemplateRepository implements WorkflowTemplateRepositoryInterface
{
    /**
     * @var WorkflowTemplate
     */
    protected $model;

    /**
     * EloquentWorkflowTemplateRepository constructor.
     *
     * @param WorkflowTemplate $model
     */
    public function __construct(WorkflowTemplate $model)
    {
        $this->model = $model;
    }

    /**
     * Get all active workflow templates.
     *
     * @return Collection
     */
    public function getAllActive(): Collection
    {
        return $this->model->active()->ordered()->get();
    }

    /**
     * Get templates by category.
     *
     * @param string $category
     * @return Collection
     */
    public function getByCategory(string $category): Collection
    {
        return $this->model->active()->byCategory($category)->ordered()->get();
    }

    /**
     * Find a template by ID.
     *
     * @param int $id
     * @return WorkflowTemplate|null
     */
    public function findById(int $id): ?WorkflowTemplate
    {
        return $this->model->find($id);
    }

    /**
     * Create a new workflow template.
     *
     * @param array $data
     * @return WorkflowTemplate
     */
    public function create(array $data): WorkflowTemplate
    {
        return $this->model->create($data);
    }

    /**
     * Update a workflow template.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update(int $id, array $data): bool
    {
        $template = $this->findById($id);
        if (!$template) {
            return false;
        }

        return $template->update($data);
    }

    /**
     * Delete a workflow template.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $template = $this->findById($id);
        if (!$template) {
            return false;
        }

        return $template->delete();
    }

    /**
     * Get all available categories.
     *
     * @return array
     */
    public function getCategories(): array
    {
        return $this->model->getCategories();
    }

    /**
     * Get templates ordered by sort order and name.
     *
     * @return Collection
     */
    public function getOrdered(): Collection
    {
        return $this->model->ordered()->get();
    }

    /**
     * Toggle template active status.
     *
     * @param int $id
     * @return bool
     */
    public function toggleActive(int $id): bool
    {
        $template = $this->findById($id);
        if (!$template) {
            return false;
        }

        return $template->update(['is_active' => !$template->is_active]);
    }
}
