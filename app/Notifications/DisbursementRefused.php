<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DisbursementRefused extends Notification implements ShouldQueue
{
    use Queueable;

    protected $requisition;
    protected $reason;

    public function __construct(Requisition $requisition, string $reason)
    {
        $this->requisition = $requisition;
        $this->reason = $reason;
    }

    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Requisition Disbursement Refused - ' . $this->requisition->title)
            ->markdown('emails.requisitions.disbursement-refused', [
                'requisition' => $this->requisition,
                'reason' => $this->reason,
                'user' => $notifiable,
            ]);
    }

    public function toArray($notifiable): array
    {
        return [
            'requisition_id' => $this->requisition->id,
            'title' => $this->requisition->title,
            'reason' => $this->reason,
            'type' => 'disbursement_refused',
            'message' => "Your requisition disbursement has been refused: {$this->reason}"
        ];
    }
} 