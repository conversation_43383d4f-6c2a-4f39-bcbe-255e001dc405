<?php

namespace App\Notifications;

use App\Models\StoreRequisition;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StoreRequisitionIssued extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $storeRequisition;
    private $isPartialIssue;

    public function __construct(StoreRequisition $storeRequisition, $isPartialIssue = false)
    {
        $this->storeRequisition = $storeRequisition;
        $this->isPartialIssue = $isPartialIssue;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $actionUrl = $this->relativeRouteWithQuery('store-requisitions.show', [
            'storeRequisition' => $this->storeRequisition->id
        ], true);

        $subject = $this->isPartialIssue 
            ? 'Store Requisition Partially Issued - #' . $this->storeRequisition->id
            : 'Store Requisition Issued - #' . $this->storeRequisition->id;

        return (new MailMessage)
            ->subject($subject)
            ->markdown('emails.store-requisitions.issued', [
                'notifiable' => $notifiable,
                'storeRequisition' => $this->storeRequisition,
                'isPartialIssue' => $this->isPartialIssue,
                'totalItems' => $this->storeRequisition->items->count(),
                'url' => url($actionUrl)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $time = now()->setTimezone('Africa/Nairobi');
        $title = $this->isPartialIssue ? 'Store Requisition Partially Issued' : 'Store Requisition Issued';
        $message = $this->isPartialIssue 
            ? "Your store requisition #{$this->storeRequisition->id} has been partially issued"
            : "Your store requisition #{$this->storeRequisition->id} has been fully issued";

        return [
            'title' => $title,
            'message' => $message,
            'type' => $this->isPartialIssue ? 'store_requisition_partially_issued' : 'store_requisition_issued',
            'store_requisition_id' => $this->storeRequisition->id,
            'action_url' => $this->relativeRoute('store-requisitions.show', $this->storeRequisition->id),
            'metadata' => [
                'requisition_id' => $this->storeRequisition->id,
                'purpose' => $this->storeRequisition->purpose,
                'total_items' => $this->storeRequisition->items->count(),
                'department_name' => $this->storeRequisition->department->name ?? 'Unknown',
                'is_partial_issue' => $this->isPartialIssue,
                'issued_at' => $time->format('M j, Y g:i A')
            ]
        ];
    }
}
