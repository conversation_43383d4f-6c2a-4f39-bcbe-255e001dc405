<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class RequisitionApproved extends Notification
{
    use Queueable;

    private $requisition;
    private $workflowStep;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $workflowStep)
    {
        $this->requisition = $requisition;
        $this->workflowStep = $workflowStep;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toDatabase($notifiable)
    {
        return [
            'title' => 'Step Approved',
            'message' => "#{$this->requisition->requisition_number} approved at step {$this->workflowStep->step_number}",
            'type' => 'requisition_step_approved',
            'requisition_id' => $this->requisition->id,
            'workflow_step_id' => $this->workflowStep->id,
            'action_url' => $this->relativeRoute('requisitions.show', $this->requisition->id),
            'metadata' => [
                'requisition_number' => $this->requisition->requisition_number,
                'purpose' => $this->requisition->purpose,
                'total_amount' => $this->requisition->total_amount,
                'department_name' => $this->requisition->department->name ?? 'Unknown'
            ]
        ];
    }
}
