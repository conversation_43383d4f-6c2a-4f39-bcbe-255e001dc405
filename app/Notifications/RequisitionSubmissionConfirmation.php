<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RequisitionSubmissionConfirmation extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $requisition;
    private $firstStep;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $firstStep)
    {
        $this->requisition = $requisition;
        $this->firstStep = $firstStep;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        // Ensure we have the latest data
        $this->requisition->refresh();
        $this->requisition->load(['department']);

        $url = $this->relativeRoute('requisitions.show', $this->requisition->id, true);
        
        return (new MailMessage)
            ->subject("Requisition #{$this->requisition->requisition_number} Submitted Successfully")
            ->markdown('emails.requisitions.submission-confirmation', [
                'notifiable' => $notifiable,
                'requisition' => $this->requisition,
                'firstStep' => $this->firstStep,
                'url' => $url
            ]);
    }

    public function toDatabase($notifiable)
    {
        $time = now()->setTimezone('Africa/Nairobi');
        // Ensure we get the latest total amount by refreshing the model
        $this->requisition->refresh();
        $totalAmount = $this->requisition->total_amount > 0 ? $this->requisition->total_amount : 0;
        

        return [
            'title' => 'Requisition Submitted',
            'message' => "#{$this->requisition->requisition_number} submitted for approval",
            'type' => 'requisition_submission_confirmation',
            'requisition_id' => $this->requisition->id,
            'workflow_step_id' => $this->firstStep->id,
            'action_url' => $this->relativeRoute('requisitions.show', $this->requisition->id),
            'metadata' => [
                'requisition_number' => $this->requisition->requisition_number,
                'purpose' => $this->requisition->purpose,
                'total_amount' => $totalAmount,
                'first_step_name' => $this->firstStep->step_name,
                'department_name' => $this->requisition->department->name ?? 'Unknown',
                'submitted_at' => $this->requisition->created_at,
                'notification_time' => $time->format('g:i A'),
                'created_at' => $time->toISOString(),
                'timezone' => 'Africa/Nairobi'
            ]
        ];
    }
}
