<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RequisitionMovedToTransaction extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    public function __construct(
        public Transaction $transaction
    ) {}

    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable): MailMessage
    {
        // Ensure we have the latest data
        $this->transaction->refresh();
        $this->transaction->load('requisition');

        return (new MailMessage)
            ->subject('Action Required: Payment Details for Requisition #' . $this->transaction->requisition->requisition_number)
            ->markdown('emails.requisitions.moved-to-transaction', [
                'requisition' => $this->transaction->requisition,
                'notifiable' => $notifiable,
                'totalAmount' => $this->transaction->total_amount,
                'url' => $this->relativeRoute('disbursement.show', $this->transaction->id, true)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $time = now()->setTimezone('Africa/Nairobi');
        
        return [
            'title' => 'Provide Payment Details',
            'message' => "#{$this->transaction->requisition->requisition_number} ready for disbursement",
            'type' => 'requisition_moved_to_transaction',
            'requisition_id' => $this->transaction->requisition_id,
            'transaction_id' => $this->transaction->id,
            'action_url' => $this->relativeRoute('disbursement.show', $this->transaction->id),
            'metadata' => [
                'requisition_number' => $this->transaction->requisition->requisition_number,
                'purpose' => $this->transaction->requisition->purpose,
                'total_amount' => number_format($this->transaction->total_amount, 2),
                'department_name' => $this->transaction->requisition->department->name ?? 'Unknown Department',
                'created_at' => $time->toISOString()
            ]
        ];
    }
}
