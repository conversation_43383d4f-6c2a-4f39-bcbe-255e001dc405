<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use App\Models\User;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class RequisitionRejected extends Notification
{
    use Queueable, GeneratesRelativeUrls;

    private $requisition;
    private $workflowStep;
    private $comments;
    private $rejector;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $workflowStep, $comments = null)
    {
        $this->requisition = $requisition;
        $this->workflowStep = $workflowStep;
        $this->comments = $comments;
        $this->rejector = auth()->user();
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $url = $this->relativeRoute('requisitions.show', $this->requisition->id, true);
        
        return (new MailMessage)
            ->subject("Requisition #{$this->requisition->requisition_number} Rejected")
            ->markdown('emails.requisitions.rejected', [
                'notifiable' => $notifiable,
                'requisition' => $this->requisition,
                'workflowStep' => $this->workflowStep,
                'comments' => $this->comments,
                'url' => $url,
                'rejector' => $this->rejector
            ]);
    }

    public function toDatabase($notifiable)
    {
        // Ensure we get the latest total amount by refreshing the model
        $this->requisition->refresh();
        $totalAmount = $this->requisition->total_amount > 0 ? $this->requisition->total_amount : 0;

        $actionUrl = $this->relativeRoute('requisitions.show', $this->requisition->id);

        $rejectorName = $this->rejector ? $this->rejector->first_name . ' ' . $this->rejector->last_name : 'Unknown';
        
        return [
            'title' => 'Requisition Rejected',
            'message' => "#{$this->requisition->requisition_number} rejected by {$rejectorName}",
            'type' => 'requisition_rejected',
            'requisition_id' => $this->requisition->id,
            'workflow_step_id' => $this->workflowStep->id,
            'action_url' => $actionUrl,
            'metadata' => [
                'requisition_number' => $this->requisition->requisition_number,
                'purpose' => $this->requisition->purpose,
                'total_amount' => $totalAmount,
                'comments' => $this->comments,
                'department_name' => $this->requisition->department->name ?? 'Unknown',
                'rejector_name' => $rejectorName
            ]
        ];
    }
}
