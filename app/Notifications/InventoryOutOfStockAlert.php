<?php

namespace App\Notifications;

use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class InventoryOutOfStockAlert extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $inventoryItems;

    public function __construct($inventoryItems)
    {
        $this->inventoryItems = collect($inventoryItems);
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $itemCount = $this->inventoryItems->count();
        $actionUrl = $this->relativeRoute('inventory.index', [], true) . '?filter=out-of-stock';

        return (new MailMessage)
            ->subject("URGENT: Out of Stock Alert - {$itemCount} Items")
            ->markdown('emails.inventory.out-of-stock-alert', [
                'notifiable' => $notifiable,
                'inventoryItems' => $this->inventoryItems,
                'itemCount' => $itemCount,
                'url' => url($actionUrl)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $itemCount = $this->inventoryItems->count();
        $criticalItem = $this->inventoryItems->first();

        return [
            'title' => 'Out of Stock Alert',
            'message' => "{$itemCount} inventory items are out of stock and need immediate replenishment",
            'type' => 'inventory_out_of_stock_alert',
            'action_url' => $this->relativeRoute('inventory.index') . '?filter=out-of-stock',
            'metadata' => [
                'item_count' => $itemCount,
                'critical_item' => $criticalItem->name ?? 'Unknown',
                'alert_time' => now()->setTimezone('Africa/Nairobi')->format('M j, Y g:i A')
            ]
        ];
    }
}
