<?php

namespace App\Notifications;

use App\Models\StoreRequisition;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StoreRequisitionPendingApproval extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $storeRequisition;

    public function __construct(StoreRequisition $storeRequisition)
    {
        $this->storeRequisition = $storeRequisition;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $actionUrl = $this->relativeRouteWithQuery('store-requisitions.show', [
            'storeRequisition' => $this->storeRequisition->id
        ], true);

        return (new MailMessage)
            ->subject('Store Requisition Pending Your Approval - #' . $this->storeRequisition->id)
            ->markdown('emails.store-requisitions.pending-approval', [
                'notifiable' => $notifiable,
                'storeRequisition' => $this->storeRequisition,
                'totalItems' => $this->storeRequisition->items->count(),
                'url' => url($actionUrl)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $time = now()->setTimezone('Africa/Nairobi');

        return [
            'title' => 'Store Requisition Pending Approval',
            'message' => "Store requisition #{$this->storeRequisition->id} from {$this->storeRequisition->requester->full_name} requires your approval",
            'type' => 'store_requisition_pending_approval',
            'store_requisition_id' => $this->storeRequisition->id,
            'action_url' => $this->relativeRoute('store-requisitions.show', $this->storeRequisition->id),
            'metadata' => [
                'requisition_id' => $this->storeRequisition->id,
                'purpose' => $this->storeRequisition->purpose,
                'total_items' => $this->storeRequisition->items->count(),
                'department_name' => $this->storeRequisition->department->name ?? 'Unknown',
                'requester_name' => $this->storeRequisition->requester->full_name,
                'created_at' => $time->format('M j, Y g:i A')
            ]
        ];
    }
}
