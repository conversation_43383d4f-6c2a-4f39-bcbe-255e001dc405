<?php

namespace App\Notifications;

use App\Models\CashFloat;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class LowCashFloatAlert extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    protected $cashFloat;
    protected $currentBalance;
    protected $alertThreshold;

    /**
     * Create a new notification instance.
     */
    public function __construct(CashFloat $cashFloat)
    {
        $this->cashFloat = $cashFloat;
        $this->currentBalance = $cashFloat->current_balance;
        $this->alertThreshold = $cashFloat->alert_threshold;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $percentageRemaining = 0;
        if ($this->cashFloat->initial_amount > 0) {
            $percentageRemaining = ($this->currentBalance / $this->cashFloat->initial_amount) * 100;
        }

        $actionUrl = $this->relativeRouteWithQuery('cash-floats.show', [
            'cashFloat' => $this->cashFloat->id
        ]);

        return (new MailMessage)
            ->subject('Low Cash Float Alert - ' . $this->cashFloat->name)
            ->markdown('emails.cash-floats.low-balance-alert', [
                'notifiable' => $notifiable,
                'cashFloat' => $this->cashFloat,
                'currentBalance' => $this->currentBalance,
                'alertThreshold' => $this->alertThreshold,
                'percentageRemaining' => $percentageRemaining,
                'actionUrl' => url($actionUrl)
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase($notifiable): array
    {
        try {
            $percentageRemaining = 0;
            if ($this->cashFloat->initial_amount > 0) {
                $percentageRemaining = ($this->currentBalance / $this->cashFloat->initial_amount) * 100;
            }

            Log::info("Creating low cash float notification", [
                'cash_float_id' => $this->cashFloat->id,
                'current_balance' => $this->currentBalance,
                'alert_threshold' => $this->alertThreshold,
                'percentage_remaining' => $percentageRemaining
            ]);

            return [
                'title' => 'Low Cash Float Alert',
                'message' => "'{$this->cashFloat->name}' below threshold",
                'type' => 'low_cash_float_alert',
                'action_url' => $this->relativeRouteWithQuery('cash-floats.show', ['cashFloat' => $this->cashFloat->id]),
                'cash_float_id' => $this->cashFloat->id,
                'metadata' => [
                    'cash_float_name' => $this->cashFloat->name,
                    'branch_name' => $this->cashFloat->branch?->name,
                    'current_balance' => $this->currentBalance,
                    'alert_threshold' => $this->alertThreshold,
                    'percentage_remaining' => number_format($percentageRemaining, 1) . '%',
                    'organization_name' => $this->cashFloat->organization->name,
                ],
                'organization_id' => $this->cashFloat->organization_id,
                'department_id' => $this->cashFloat->department_id,
                'branch_id' => $this->cashFloat->branch_id,
                'user_id' => $this->cashFloat->user_id,
            ];
        } catch (\Exception $e) {
            Log::error('Error creating LowCashFloatAlert notification: ' . $e->getMessage(), [
                'cash_float_id' => $this->cashFloat->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return a basic notification structure to prevent complete failure
            return [
                'title' => 'Low Cash Float Alert',
                'message' => "Cash float is below the alert threshold.",
                'type' => 'low_cash_float_alert',
                'action_url' => '/cash-floats',
                'cash_float_id' => $this->cashFloat->id,
            ];
        }
    }
}