<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class DisbursementCompletedForFinance extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    /**
     * The transaction instance.
     *
     * @var \App\Models\Transaction
     */
    protected $transaction;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Transaction  $transaction
     * @return void
     */
    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Disbursement Completed - #' . $this->transaction->requisition->requisition_number)
            ->markdown('emails.requisitions.disbursement-completed-for-finance', [
                'transaction' => $this->transaction,
                'notifiable' => $notifiable,
            ]);
    }

    /**
     * Generate a relative route URL.
     *
     * @param  string  $name
     * @param  mixed  $parameters
     * @return string
     */
    protected function relativeRoute($name, $parameters = [])
    {
        return route($name, $parameters, false);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        $time = now()->setTimezone(config('app.timezone'));
        $requesterName = $this->transaction->requisition->requester 
            ? $this->transaction->requisition->requester->full_name 
            : 'Unknown User';

        return [
            'title' => 'Disbursement Processed Successfully',
            'message' => "Disbursement for transaction #{$this->transaction->id} (Requisition #{$this->transaction->requisition->requisition_number}) to {$requesterName} has been completed successfully (" . number_format($this->transaction->total_amount, 2) . ") at " . $time->format('g:i A'),
            'type' => 'disbursement_completed_for_finance',
            'requisition_id' => $this->transaction->requisition_id,
            'transaction_id' => $this->transaction->id,
            'action_url' => $this->relativeRoute('transactions.show', $this->transaction->id),
            'metadata' => [
                'requisition_number' => $this->transaction->requisition->requisition_number,
                'purpose' => $this->transaction->requisition->purpose,
                'total_amount' => $this->transaction->total_amount,
                'transaction_id' => $this->transaction->id,
                'requester_name' => $requesterName,
                'disbursement_transaction_id' => $this->transaction->disbursement_transaction_id,
                'department_name' => $this->transaction->requisition->department->name ?? 'Unknown',
                'notification_time' => $time->format('g:i A'),
                'created_at' => $time->toISOString()
            ]
        ];
    }
}