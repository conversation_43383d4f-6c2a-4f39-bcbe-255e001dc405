<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TransactionReadyForDisbursement extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Transaction Ready for Disbursement - #' . $this->transaction->requisition->requisition_number)
            ->markdown('emails.requisitions.ready-for-disbursement', [
                'transaction' => $this->transaction,
                'notifiable' => $notifiable,
            ]);
    }

    public function toDatabase($notifiable)
    {
        // Get requester name
        $requesterName = $this->transaction->requisition->requester ?
            ($this->transaction->requisition->requester->first_name . ' ' . $this->transaction->requisition->requester->last_name) :
            'Unknown User';

        return [
            'title' => 'Ready for Disbursement',
            'message' => "#{$this->transaction->requisition->requisition_number} from {$requesterName} ready for disbursement (" . number_format($this->transaction->total_amount, 2) . ")",
            'type' => 'transaction_ready_for_disbursement',
            'requisition_id' => $this->transaction->requisition_id,
            'transaction_id' => $this->transaction->id,
            'action_url' => $this->relativeRoute('transactions.show', $this->transaction->id),
            'metadata' => [
                'requisition_number' => $this->transaction->requisition->requisition_number,
                'purpose' => $this->transaction->requisition->purpose,
                'total_amount' => $this->transaction->total_amount,
                'transaction_id' => $this->transaction->id,
                'payment_method' => $this->transaction->payment_method,
                'requester_name' => $requesterName
            ]
        ];
    }
}
