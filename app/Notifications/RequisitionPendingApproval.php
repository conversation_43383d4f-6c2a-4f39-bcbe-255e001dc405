<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RequisitionPendingApproval extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    protected $requisition;
    protected $step;

    /**
     * Create a new notification instance.
     */
    public function __construct(Requisition $requisition, ApprovalWorkflowStep $step)
    {
        $this->requisition = $requisition;
        $this->step = $step;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        // Ensure both email and database channels are used
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        // Build the approval URL with department and requisition parameters (using absolute URL)
        $actionUrl = $this->relativeRouteWithQuery('requisitions.approvals', [
            'department' => $this->requisition->department_id,
            'requisition' => $this->requisition->id
        ], true);

        return (new MailMessage)
            ->subject('Requisition Pending Your Approval - #' . $this->requisition->requisition_number)
            ->markdown('emails.requisitions.pending-approval', [
                'notifiable' => $notifiable,
                'requisition' => $this->requisition,
                'workflowStep' => $this->step,
                'totalAmount' => $this->requisition->total_amount,
                'url' => url($actionUrl)
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase($notifiable): array
    {
        // Get requester name
        $requesterName = $this->requisition->requester ? 
            $this->requisition->requester->first_name . ' ' . $this->requisition->requester->last_name :
            'Unknown User';

        // Use the new method that includes query parameters
        $actionUrl = $this->relativeRouteWithQuery('requisitions.approvals', [
            'department' => $this->requisition->department_id,
            'requisition' => $this->requisition->id
        ]);
        
        return [
            'title' => 'Requisition Pending Approval',
            'message' => "Requisition #{$this->requisition->requisition_number} from {$requesterName} requires your approval (KSH " . number_format($this->requisition->total_amount, 2) . ")",
            'type' => 'requisition_pending_approval',
            'requisition_id' => $this->requisition->id,
            'action_url' => $actionUrl,
            'metadata' => [
                'requisition_number' => $this->requisition->requisition_number,
                'purpose' => $this->requisition->purpose,
                'total_amount' => $this->requisition->total_amount,
                'requester_name' => $requesterName,
                'department_name' => $this->requisition->department->name ?? 'Unknown Department',
            ]
        ];
    }
}