<?php

namespace App\Notifications;

use App\Models\User;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserOnboardingNotification extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    protected $newUser;
    protected $plainPassword;
    protected $organizationAdmin;

    public function __construct(User $newUser, string $plainPassword, User $organizationAdmin)
    {
        $this->newUser = $newUser;
        $this->plainPassword = $plainPassword;
        $this->organizationAdmin = $organizationAdmin;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $url = $this->relativeRoute('login', [], true);
        
        return (new MailMessage)
            ->subject("Welcome to " . config('app.name') . " - Your Account Details")
            ->markdown('emails.users.onboarding-notification', [
                'notifiable' => $notifiable,
                'newUser' => $this->newUser,
                'plainPassword' => $this->plainPassword,
                'organizationAdmin' => $this->organizationAdmin,
                'url' => $url
            ]);
    }

    public function toDatabase($notifiable)
    {
        $time = now()->setTimezone('Africa/Nairobi');

        return [
            'title' => 'Welcome to ' . config('app.name'),
            'message' => "Your account has been created by {$this->organizationAdmin->full_name}. Please check your email for login credentials and change your password after first login.",
            'type' => 'user_onboarding',
            'user_id' => $this->newUser->id,
            'action_url' => $this->relativeRoute('login'),
            'metadata' => [
                'username' => $this->newUser->username,
                'email' => $this->newUser->email,
                'created_by' => $this->organizationAdmin->full_name,
                'created_by_id' => $this->organizationAdmin->id,
                'created_at' => $time->toISOString(),
                'timezone' => 'Africa/Nairobi'
            ]
        ];
    }
}