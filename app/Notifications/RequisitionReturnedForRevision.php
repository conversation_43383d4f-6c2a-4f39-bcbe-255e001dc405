<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class RequisitionReturnedForRevision extends Notification
{
    use Queueable;

    private $requisition;
    private $workflowStep;
    private $comments;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $workflowStep, $comments = null)
    {
        $this->requisition = $requisition;
        $this->workflowStep = $workflowStep;
        $this->comments = $comments;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toDatabase($notifiable)
    {
        // Ensure we get the latest total amount by refreshing the model
        $this->requisition->refresh();
        $totalAmount = $this->requisition->total_amount > 0 ? $this->requisition->total_amount : 0;

        $actionUrl = $this->relativeRoute('requisitions.show', $this->requisition->id, true);

        return [
            'title' => 'Requisition Returned for Revision',
            'message' => "Your requisition #{$this->requisition->requisition_number} has been returned for revision (" . number_format($totalAmount, 2) . ") at " . now()->format('h:i A'),
            'type' => 'requisition_returned_for_revision',
            'requisition_id' => $this->requisition->id,
            'workflow_step_id' => $this->workflowStep->id,
            'action_url' => $actionUrl,
            'metadata' => [
                'requisition_number' => $this->requisition->requisition_number,
                'purpose' => $this->requisition->purpose,
                'total_amount' => $totalAmount,
                'comments' => $this->comments,
                'department_name' => $this->requisition->department->name ?? 'Unknown'
            ]
        ];
    }
}
