<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DisbursementCompleted extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls; // Add the GeneratesRelativeUrls trait here

    private $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        // Ensure we have the latest data
        $this->transaction->refresh();
        $this->transaction->load('requisition');

        // Use absolute URL for email action
        $actionUrl = $this->relativeRouteWithQuery('transactions.show', [
            'id' => $this->transaction->id
        ], true);

        return (new MailMessage)
            ->subject('Disbursement Completed - #' . $this->transaction->requisition->requisition_number)
            ->markdown('emails.requisitions.disbursement-completed', [
                'notifiable' => $notifiable,
                'requisition' => $this->transaction->requisition,
                'transaction' => $this->transaction
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        $actionUrl = $this->relativeRouteWithQuery('transactions.show', [
            'id' => $this->transaction->id
        ]);

        return [
            'title' => 'Disbursement Completed',
            'message' => "#{$this->transaction->requisition->requisition_number} disbursed. Upload evidence required.",
            'type' => 'disbursement_completed',
            'action_url' => $actionUrl,
            'metadata' => [
                'transaction_id' => $this->transaction->id,
                'requisition_id' => $this->transaction->requisition_id,
                'requisition_number' => $this->transaction->requisition->requisition_number,
                'amount' => $this->transaction->total_amount,
                'purpose' => $this->transaction->requisition->purpose,
                'evidence_required' => true,
                'evidence_reminder' => 'Please upload evidence of expenditure to complete the process.'
            ]
        ];
    }
}
