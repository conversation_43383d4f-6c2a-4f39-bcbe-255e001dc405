<?php

namespace App\Notifications;

use App\Models\StoreRequisition;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StoreRequisitionReturnedForRevision extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $storeRequisition;
    private $comments;

    public function __construct(StoreRequisition $storeRequisition, $comments = null)
    {
        $this->storeRequisition = $storeRequisition;
        $this->comments = $comments;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $actionUrl = $this->relativeRouteWithQuery('store-requisitions.edit', [
            'storeRequisition' => $this->storeRequisition->id
        ], true);

        return (new MailMessage)
            ->subject('Store Requisition Returned for Revision - #' . $this->storeRequisition->id)
            ->markdown('emails.store-requisitions.returned-for-revision', [
                'notifiable' => $notifiable,
                'storeRequisition' => $this->storeRequisition,
                'comments' => $this->comments,
                'totalItems' => $this->storeRequisition->items->count(),
                'url' => url($actionUrl)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $time = now()->setTimezone('Africa/Nairobi');

        return [
            'title' => 'Store Requisition Returned for Revision',
            'message' => "Your store requisition #{$this->storeRequisition->id} has been returned for revision" . ($this->comments ? ": {$this->comments}" : ''),
            'type' => 'store_requisition_returned_for_revision',
            'store_requisition_id' => $this->storeRequisition->id,
            'action_url' => $this->relativeRoute('store-requisitions.edit', $this->storeRequisition->id),
            'metadata' => [
                'requisition_id' => $this->storeRequisition->id,
                'purpose' => $this->storeRequisition->purpose,
                'total_items' => $this->storeRequisition->items->count(),
                'department_name' => $this->storeRequisition->department->name ?? 'Unknown',
                'comments' => $this->comments,
                'returned_at' => $time->format('M j, Y g:i A')
            ]
        ];
    }
}
