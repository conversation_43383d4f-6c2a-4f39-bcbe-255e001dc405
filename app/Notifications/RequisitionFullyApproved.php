<?php

namespace App\Notifications;

use App\Models\Requisition;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class RequisitionFullyApproved extends Notification
{
    use Queueable, GeneratesRelativeUrls;

    private $requisition;

    public function __construct(Requisition $requisition)
    {
        $this->requisition = $requisition;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

   

    public function toDatabase($notifiable)
    {
        // Ensure we get the latest total amount by refreshing the model
        $this->requisition->refresh();
        $totalAmount = $this->requisition->total_amount > 0 ? $this->requisition->total_amount : 0;

        $actionUrl = $this->relativeRoute('requisitions.show', $this->requisition->id, true);

        return [
            'title' => 'Requisition Approved',
            'message' => "#{$this->requisition->requisition_number} fully approved",
            'type' => 'requisition_fully_approved',
            'requisition_id' => $this->requisition->id,
            'action_url' => $actionUrl,
            'metadata' => [
                'requisition_number' => $this->requisition->requisition_number,
                'purpose' => $this->requisition->purpose,
                'total_amount' => $totalAmount,
                'department_name' => $this->requisition->department->name ?? 'Unknown',
                'approved_at' => $this->requisition->approved_at
            ]
        ];
    }
}
