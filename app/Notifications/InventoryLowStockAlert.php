<?php

namespace App\Notifications;

use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class InventoryLowStockAlert extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $inventoryItems;

    public function __construct($inventoryItems)
    {
        $this->inventoryItems = collect($inventoryItems);
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $itemCount = $this->inventoryItems->count();
        $actionUrl = $this->relativeRoute('inventory.index', [], true) . '?filter=low-stock';

        return (new MailMessage)
            ->subject("Low Stock Alert - {$itemCount} Items Need Attention")
            ->markdown('emails.inventory.low-stock-alert', [
                'notifiable' => $notifiable,
                'inventoryItems' => $this->inventoryItems,
                'itemCount' => $itemCount,
                'url' => url($actionUrl)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $itemCount = $this->inventoryItems->count();
        $criticalItem = $this->inventoryItems->first();

        return [
            'title' => 'Low Stock Alert',
            'message' => "{$itemCount} inventory items are running low and need replenishment",
            'type' => 'inventory_low_stock_alert',
            'action_url' => $this->relativeRoute('inventory.index') . '?filter=low-stock',
            'metadata' => [
                'item_count' => $itemCount,
                'critical_item' => $criticalItem->name ?? 'Unknown',
                'critical_quantity' => $criticalItem->quantity_on_hand ?? 0,
                'alert_time' => now()->setTimezone('Africa/Nairobi')->format('M j, Y g:i A')
            ]
        ];
    }
}
