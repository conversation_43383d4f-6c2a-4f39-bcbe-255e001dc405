<?php

namespace App\Notifications;

use App\Models\StoreRequisition;
use App\Traits\GeneratesRelativeUrls;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class StoreRequisitionApproved extends Notification implements ShouldQueue
{
    use Queueable, GeneratesRelativeUrls;

    private $storeRequisition;

    public function __construct(StoreRequisition $storeRequisition)
    {
        $this->storeRequisition = $storeRequisition;
    }

    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $actionUrl = $this->relativeRouteWithQuery('store-requisitions.show', [
            'storeRequisition' => $this->storeRequisition->id
        ], true);

        return (new MailMessage)
            ->subject('Store Requisition Approved - #' . $this->storeRequisition->id)
            ->markdown('emails.store-requisitions.approved', [
                'notifiable' => $notifiable,
                'storeRequisition' => $this->storeRequisition,
                'totalItems' => $this->storeRequisition->items->count(),
                'url' => url($actionUrl)
            ]);
    }

    public function toDatabase($notifiable): array
    {
        $time = now()->setTimezone('Africa/Nairobi');

        return [
            'title' => 'Store Requisition Approved',
            'message' => "Your store requisition #{$this->storeRequisition->id} has been approved and is ready for issuing",
            'type' => 'store_requisition_approved',
            'store_requisition_id' => $this->storeRequisition->id,
            'action_url' => $this->relativeRoute('store-requisitions.show', $this->storeRequisition->id),
            'metadata' => [
                'requisition_id' => $this->storeRequisition->id,
                'purpose' => $this->storeRequisition->purpose,
                'total_items' => $this->storeRequisition->items->count(),
                'department_name' => $this->storeRequisition->department->name ?? 'Unknown',
                'approved_at' => $time->format('M j, Y g:i A')
            ]
        ];
    }
}
