<?php

namespace App\Notifications;

use App\Models\EmailVerificationCode;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class EmailVerificationCodeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $verificationCode;

    public function __construct(EmailVerificationCode $verificationCode)
    {
        $this->verificationCode = $verificationCode;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        // Log the verification code for development
        Log::info('Email Verification Code', [
            'email' => $this->verificationCode->email,
            'code' => $this->verificationCode->code,
            'expires_at' => $this->verificationCode->expires_at->format('Y-m-d H:i:s'),
        ]);

        $fullName = $notifiable->full_name ?? $notifiable->name ?? 'User';
        return (new MailMessage)
            ->subject('Email Verification Code - ' . config('app.name'))
            ->greeting('Dear ' . $fullName . ',')
            ->line('Thank you for registering with ' . config('app.name') . '.')
            ->line('To complete your registration, please enter the following verification code:')
            ->line('**Verification Code: ' . $this->verificationCode->code . '**')
            ->line('This code will expire in 15 minutes.')
            ->line('If you did not create an account, no further action is required.')
            ->line('')
            ->line('If you have any questions or need assistance, please contact SIPPAR <NAME_EMAIL>.')
            ->salutation('Thanks, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'verification_code_id' => $this->verificationCode->id,
            'email' => $this->verificationCode->email,
            'expires_at' => $this->verificationCode->expires_at,
        ];
    }
}
