<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StrongPassword implements ValidationRule
{
    /**
     * Common passwords to reject
     */
    private array $commonPasswords = [
        'password', 'password123', '123456', '123456789', 'qwerty', 'abc123',
        'password1', 'admin', 'letmein', 'welcome', 'monkey', '1234567890',
        'dragon', 'master', 'hello', 'freedom', 'whatever', 'qazwsx',
        'trustno1', 'jordan', 'harley', 'robert', 'matthew', 'daniel',
        'andrew', 'joshua', 'anthony', 'william', 'david', 'charles',
        'thomas', 'christopher', 'joseph', 'michael', 'john', 'james',
        'superman', 'batman', 'football', 'baseball', 'basketball',
        'soccer', 'hockey', 'tennis', 'golf', 'swimming', 'running'
    ];

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }

        $password = $value;
        $errors = [];

        // Check minimum length
        if (strlen($password) < 8) {
            $errors[] = 'at least 8 characters';
        }

        // Check for uppercase letter
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'at least one uppercase letter';
        }

        // Check for lowercase letter
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'at least one lowercase letter';
        }

        // Check for number
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'at least one number';
        }

        // Check for special character
        if (!preg_match('/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/', $password)) {
            $errors[] = 'at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)';
        }

        // Check against common passwords
        if (in_array(strtolower($password), array_map('strtolower', $this->commonPasswords))) {
            $errors[] = 'a password that is not commonly used';
        }

        // If there are errors, format the message
        if (!empty($errors)) {
            $message = 'The :attribute must contain ' . $this->formatErrorList($errors) . '.';
            $fail($message);
        }
    }

    /**
     * Format the error list into a readable string
     */
    private function formatErrorList(array $errors): string
    {
        if (count($errors) === 1) {
            return $errors[0];
        }

        if (count($errors) === 2) {
            return $errors[0] . ' and ' . $errors[1];
        }

        $lastError = array_pop($errors);
        return implode(', ', $errors) . ', and ' . $lastError;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return 'The :attribute must be a strong password.';
    }
}
