<?php

namespace App\Http\Requests\StoreRequisition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ApproveStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        $storeRequisition = $this->route('storeRequisition');
        return Auth::user()->can('approve', $storeRequisition);
    }

    public function rules(): array
    {
        return [
            'comments' => 'nullable|string|max:1000',
        ];
    }

    public function messages(): array
    {
        return [
            'comments.max' => 'Comments cannot exceed 1000 characters.',
        ];
    }
}