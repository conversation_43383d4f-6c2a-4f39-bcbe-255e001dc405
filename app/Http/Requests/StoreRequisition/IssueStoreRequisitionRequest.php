<?php

namespace App\Http\Requests\StoreRequisition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class IssueStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return Auth::user()->can('issue-store-items') || Auth::user()->can('store-keep');
    }

    public function rules(): array
    {
        return [
            'items' => 'required|array|min:1',
            'items.*.id' => 'required|exists:store_requisition_items,id',
            'items.*.quantity_issued' => 'required|numeric|min:0',
            'issue_notes' => 'nullable|string|max:1000',
        ];
    }

    public function messages(): array
    {
        return [
            'items.required' => 'Items are required.',
            'items.min' => 'At least one item is required.',
            'items.*.id.required' => 'Item ID is required.',
            'items.*.quantity_issued.required' => 'Quantity issued is required.',
            'items.*.quantity_issued.min' => 'Quantity issued must be 0 or greater.',
            'issue_notes.max' => 'Issue notes cannot exceed 1000 characters.',
        ];
    }
}