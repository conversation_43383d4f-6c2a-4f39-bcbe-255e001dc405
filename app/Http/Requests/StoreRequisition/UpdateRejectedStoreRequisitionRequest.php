<?php

namespace App\Http\Requests\StoreRequisition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateRejectedStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        $storeRequisition = $this->route('storeRequisition');
        return Auth::user()->can('update', $storeRequisition);
    }

    public function rules(): array
    {
        return [
            'purpose' => 'required|string|max:1000',
            'comments' => 'required|string|max:1000',
            'branch_id' => 'required|exists:branches,id',
            'department_id' => 'required|exists:departments,id',
            'items' => 'required|array|min:1|max:50',
            'items.*.inventory_item_id' => 'required|exists:inventory_items,id',
            'items.*.quantity_requested' => 'required|numeric|min:0.01|max:999999.99',
        ];
    }

    public function messages(): array
    {
        return [
            'purpose.required' => 'Purpose is required.',
            'comments.required' => 'Comments are required.',
            'branch_id.required' => 'Branch is required.',
            'department_id.required' => 'Department is required.',
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Maximum 50 items allowed.',
            'items.*.inventory_item_id.required' => 'Inventory item is required.',
            'items.*.quantity_requested.required' => 'Quantity is required.',
            'items.*.quantity_requested.min' => 'Quantity must be greater than 0.',
        ];
    }
}