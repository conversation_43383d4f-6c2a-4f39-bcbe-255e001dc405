<?php

namespace App\Http\Requests\StoreRequisition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return Auth::user()->can('edit-store-requisition') || Auth::user()->can('store-keep');
    }

    public function rules(): array
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();

        return [
            'branch_id' => [
                'required',
                Rule::exists('branches', 'id')->where('organization_id', $organization->id)
            ],
            'department_id' => [
                'required',
                Rule::exists('departments', 'id')->where('organization_id', $organization->id)
            ],
            'purpose' => 'required|string|max:1000',
            'items' => 'required|array|min:1|max:50',
            'items.*.inventory_item_id' => [
                'required',
                Rule::exists('inventory_items', 'id')->where('organization_id', $organization->id)
            ],
            'items.*.quantity_requested' => 'required|numeric|min:0.01|max:999999.99',
            'save_as_draft' => 'sometimes|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'department_id.required' => 'Department is required.',
            'purpose.required' => 'Purpose is required.',
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Maximum 50 items allowed.',
            'items.*.inventory_item_id.required' => 'Inventory item is required.',
            'items.*.quantity_requested.required' => 'Quantity is required.',
            'items.*.quantity_requested.min' => 'Quantity must be greater than 0.',
        ];
    }
}