<?php

namespace App\Http\Requests\StoreRequisition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class RejectStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        $storeRequisition = $this->route('storeRequisition');
        return Auth::user()->can('approve', $storeRequisition);
    }

    public function rules(): array
    {
        return [
            'rejection_reason' => 'required|string|max:1000',
        ];
    }

    public function messages(): array
    {
        return [
            'rejection_reason.required' => 'Rejection reason is required.',
            'rejection_reason.max' => 'Rejection reason cannot exceed 1000 characters.',
        ];
    }
}