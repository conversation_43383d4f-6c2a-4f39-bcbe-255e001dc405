<?php

namespace App\Http\Requests\StoreRequisition;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreStoreRequisitionRequest extends FormRequest
{
    public function authorize(): bool
    {
        return Auth::user()->can('create-store-requisition') || Auth::user()->can('store-keep');
    }

    public function rules(): array
    {
        return [
            'branch_id' => 'required|exists:branches,id',
            'department_id' => 'required|exists:departments,id',
            'purpose' => 'required|string|max:1000',
            'items' => 'required|array|min:1|max:50',
            'items.*.inventory_item_id' => 'required|exists:inventory_items,id',
            'items.*.quantity_requested' => 'required|numeric|min:0.01|max:999999.99',
            'save_as_draft' => 'sometimes|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'department_id.required' => 'Department is required.',
            'purpose.required' => 'Purpose is required.',
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Maximum 50 items allowed.',
            'items.*.inventory_item_id.required' => 'Inventory item is required.',
            'items.*.quantity_requested.required' => 'Quantity is required.',
            'items.*.quantity_requested.min' => 'Quantity must be greater than 0.',
        ];
    }
}