<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;

class StoreRequisitionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Example: Only logged-in users can create requisitions
        return Auth::check();
        // In a real application, you'd likely have more complex authorization:
        // return Auth::user()->can('create-requisitions'); // Check for a permission
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'requisition_form_uuid' => ['required', 'string', 'exists:requisition_form_details,requisition_form_uuid'],
            'purpose' => ['required', 'string', 'max:255'],
            'notes' => ['nullable', 'string'],
            'requisition_items' => ['required', 'array', 'min:1'],
            'requisition_items.*.chart_of_account_id' => ['required', 'integer', 'exists:chart_of_accounts,id'],
            'requisition_items.*.description' => ['required', 'string', 'max:255'],
            'requisition_items.*.quantity' => ['required', 'integer', 'min:1'],
            'requisition_items.*.unit_price' => ['required', 'numeric', 'min:0'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'requisition_form_uuid.required' => 'The requisition form UUID is required.',
            'requisition_form_uuid.string' => 'The requisition form UUID must be a string.',
            'requisition_form_uuid.exists' => 'The requisition form UUID is invalid.',
            'purpose.required' => 'The purpose is required.',
            'purpose.string' => 'The purpose must be a string.',
            'purpose.max' => 'The purpose cannot exceed 255 characters.',
            'notes.string' => 'The notes must be a string.',
            'requisition_items.required' => 'At least one requisition item is required.',
            'requisition_items.array' => 'Requisition items must be an array.',
            'requisition_items.min' => 'At least one requisition item is required.',
            'requisition_items.*.chart_of_account_id.required' => 'Each requisition item must have a chart of account.',
            'requisition_items.*.chart_of_account_id.integer' => 'Each requisition item\'s chart of account ID must be an integer.',
            'requisition_items.*.chart_of_account_id.exists' => 'The selected chart of account does not exist.',
            'requisition_items.*.description.required' => 'Each requisition item must have a description.',
            'requisition_items.*.description.string' => 'Each requisition item\'s description must be a string.',
            'requisition_items.*.description.max' => 'Each requisition item\'s description cannot exceed 255 characters.',
            'requisition_items.*.quantity.required' => 'Each requisition item must have a quantity.',
            'requisition_items.*.quantity.integer' => 'Each requisition item\'s quantity must be an integer.',
            'requisition_items.*.quantity.min' => 'Each requisition item\'s quantity must be at least 1.',
            'requisition_items.*.unit_price.required' => 'Each requisition item must have a unit price.',
            'requisition_items.*.unit_price.numeric' => 'Each requisition item\'s unit price must be a number.',
            'requisition_items.*.unit_price.min' => 'Each requisition item\'s unit price must be greater than or equal to 0.',
        ];
    }
}
