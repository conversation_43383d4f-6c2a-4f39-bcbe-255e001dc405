<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use App\Models\Branch;
use App\Models\Department;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;
use App\Models\ApprovalWorkflow;
use App\Models\ApprovalWorkflowStep;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface;

class ApprovalWorkflowController extends Controller
{
    /**
     * @var ApprovalWorkflowRepositoryInterface
     */
    protected $workflowRepository;

    /**
     * @var ApprovalWorkflowStepRepositoryInterface
     */
    protected $stepRepository;

    /**
     * ApprovalWorkflowController constructor.
     *
     * @param ApprovalWorkflowRepositoryInterface $workflowRepository
     * @param ApprovalWorkflowStepRepositoryInterface $stepRepository
     */
    public function __construct(
        ApprovalWorkflowRepositoryInterface $workflowRepository,
        ApprovalWorkflowStepRepositoryInterface $stepRepository
    ) {
        $this->workflowRepository = $workflowRepository;
        $this->stepRepository = $stepRepository;
    }
    /**
     * Display a listing of the approval workflows.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        $organizationId = null;

        // If user is not a platform admin, get their organization
        if (!$user->is_platform_admin) {
            // Get the organization from the user's roles
            $organizationRole = $user->roles()->where('name', 'Organization Admin')->first();
            if ($organizationRole) {
                $organizationId = $organizationRole->organization_id;
            }
        }

        // Get workflows using the repository
        $workflows = $organizationId
            ? $this->workflowRepository->getByOrganizationId($organizationId)
            : $this->workflowRepository->getAll();

        // Ensure relationships are loaded for each workflow
        foreach ($workflows as $workflow) {
            if (!$workflow->relationLoaded('organization')) {
                $workflow->load('organization:id,name');
            }
            if (!$workflow->relationLoaded('branch')) {
                $workflow->load('branch:id,name');
            }
            if (!$workflow->relationLoaded('department')) {
                $workflow->load('department:id,name');
            }
        }

        return Inertia::render('approval-workflows/approval-workflows-index', [
            'workflows' => $workflows,
            'isPlatformAdmin' => $user->is_platform_admin,
            'organizationId' => $organizationId,
        ]);
    }

    /**
     * Show the form for creating a new approval workflow.
     */
    public function create(Request $request): Response
    {
        $user = $request->user();
        $organizationId = $request->query('organization_id');

        // If user is not a platform admin, get their organization
        if (!$user->is_platform_admin && !$organizationId) {
            $organizationRole = $user->roles()->where('name', 'Organization Admin')->first();
            if ($organizationRole) {
                $organizationId = $organizationRole->organization_id;
            }
        }

        // Get organizations for dropdown
        $organizations = $user->is_platform_admin
            ? Organization::active()->get(['id', 'name'])
            : Organization::where('id', $organizationId)->get(['id', 'name']);

        // Get branches and departments if organization is selected
        $branches = $organizationId
            ? Branch::where('organization_id', $organizationId)->active()->get(['id', 'name'])
            : [];

        $departments = $organizationId
            ? Department::where('organization_id', $organizationId)->get(['id', 'name', 'branch_id'])
            : [];

        // Get roles for the organization
        $roles = $organizationId
            ? Role::where('organization_id', $organizationId)->active()->get(['id', 'name', 'description'])
            : [];

        // Get users for the organization
        $users = $organizationId
            ? User::whereHas('organizations', function ($query) use ($organizationId) {
                $query->where('organizations.id', $organizationId);
              })
              ->where('status', 'active')
              ->get(['id', 'first_name', 'last_name', 'email'])
              ->map(function ($user) {
                  // Add roles information to each user
                  $userRoles = $user->roles()->pluck('name')->implode(', ');
                  return [
                      'id' => $user->id,
                      'name' => $user->first_name . ' ' . $user->last_name,
                      'email' => $user->email,
                      'roles' => $userRoles,
                      'display_name' => $user->first_name . ' ' . $user->last_name . ' - ' . $userRoles,
                  ];
              })
            : [];

        return Inertia::render('approval-workflows/approval-workflows-create', [
            'organizations' => $organizations,
            'branches' => $branches,
            'departments' => $departments,
            'roles' => $roles,
            'users' => $users,
            'selectedOrganizationId' => $organizationId,
        ]);
    }

    /**
     * Store a newly created approval workflow in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'name' => 'required|string|max:255',
            'is_default' => 'boolean',
            'steps' => 'required|array|min:1',
            'steps.*.step_number' => 'required|integer|min:1',
            'steps.*.role_id' => 'required_without:steps.*.approver_user_id|nullable|exists:roles,id',
            'steps.*.approver_user_id' => 'nullable|exists:users,id',
            'steps.*.description' => 'nullable|string|max:255',
        ]);


        // Start a database transaction
        return DB::transaction(function () use ($validated) {
            // Create the workflow
            $workflow = $this->workflowRepository->create([
                'organization_id' => $validated['organization_id'],
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'name' => $validated['name'],
                'is_default' => $validated['is_default'] ?? false,
            ]);

            // If this is set as default, unset any existing defaults with the same scope
            if ($validated['is_default']) {
                $this->workflowRepository->setAsDefault($workflow->id);
            }

            // Create the workflow steps
            foreach ($validated['steps'] as $step) {
                $this->stepRepository->create([
                    'approval_workflow_id' => $workflow->id,
                    'step_number' => $step['step_number'],
                    'role_id' => $step['role_id'],
                    'approver_user_id' => $step['approver_user_id'] ?? null,
                    'description' => $step['description'] ?? null,
                ]);
            }

            return redirect()->route('approval-workflows.index')
                ->with('success', 'Approval workflow created successfully.');
        });
    }

    /**
     * Display the specified approval workflow.
     */
    public function show(Request $request, int $id): Response
    {
        // Get the organization ID from the request
        $organizationId = $request->query('organization_id');

        $workflow = $this->workflowRepository->findById($id);
        if (!$workflow) {
            abort(404);
        }

        // If organization ID is provided, check if the workflow belongs to this organization
        if ($organizationId && $workflow->organization_id != $organizationId) {
            abort(403, 'This workflow does not belong to the specified organization.');
        }

        $workflow->load([
            'organization:id,name',
            'branch:id,name',
            'department:id,name',
        ]);

        $steps = $this->stepRepository->getByWorkflowId($workflow->id);
        foreach ($steps as $step) {
            $step->load('role:id,name,description');
            $step->load('approver:id,first_name,last_name');
        }

        // Format the data to match what the frontend expects
        $formattedWorkflow = $workflow->toArray();
        $formattedWorkflow['steps'] = $steps;

        return Inertia::render('approval-workflows/approval-workflows-show', [
            'workflow' => $formattedWorkflow,
        ]);
    }

    /**
     * Show the form for editing the specified approval workflow.
     */
    public function edit(Request $request, int $id): Response
    {
        // Get the organization ID from the request
        $organizationId = $request->query('organization_id');

        $workflow = $this->workflowRepository->findById($id);
        if (!$workflow) {
            abort(404);
        }

        // If organization ID is provided, check if the workflow belongs to this organization
        if ($organizationId && $workflow->organization_id != $organizationId) {
            abort(403, 'This workflow does not belong to the specified organization.');
        }

        $steps = $this->stepRepository->getByWorkflowId($workflow->id);
        foreach ($steps as $step) {
            $step->load('role:id,name,description');
            $step->load('approver:id,first_name,last_name');
        }

        $organizationId = $workflow->organization_id;

        // Get branches and departments for the organization
        $branches = Branch::where('organization_id', $organizationId)->active()->get(['id', 'name']);
        $departments = Department::where('organization_id', $organizationId)->get(['id', 'name', 'branch_id']);

        // Get roles for the organization
        $roles = Role::where('organization_id', $organizationId)->active()->get(['id', 'name', 'description']);

        // Get users for the organization
        $users = User::whereHas('organizations', function ($query) use ($organizationId) {
            $query->where('organizations.id', $organizationId);
          })
          ->where('status', 'active')
          ->get(['id', 'first_name', 'last_name', 'email'])
          ->map(function ($user) {
              // Add roles information to each user
              $userRoles = $user->roles()->pluck('name')->implode(', ');
              return [
                  'id' => $user->id,
                  'name' => $user->first_name . ' ' . $user->last_name,
                  'email' => $user->email,
                  'roles' => $userRoles,
                  'display_name' => $user->first_name . ' ' . $user->last_name . ' - ' . $userRoles,
              ];
          });

        // Format the data to match what the frontend expects
        $formattedWorkflow = $workflow->toArray();
        $formattedWorkflow['steps'] = $steps;

        return Inertia::render('approval-workflows/approval-workflows-edit', [
            'workflow' => $formattedWorkflow,
            'branches' => $branches,
            'departments' => $departments,
            'roles' => $roles,
            'users' => $users,
        ]);
    }

    /**
     * Update the specified approval workflow in storage.
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        $workflow = $this->workflowRepository->findById($id);
        if (!$workflow) {
            abort(404);
        }

        $validated = $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'name' => 'required|string|max:255',
            'is_default' => 'boolean',
            'steps' => 'required|array|min:1',
            'steps.*.id' => 'nullable|exists:approval_workflow_steps,id',
            'steps.*.step_number' => 'required|integer|min:1',
            'steps.*.role_id' => 'required_without:steps.*.approver_user_id|nullable|exists:roles,id',
            'steps.*.approver_user_id' => 'nullable|exists:users,id',
            'steps.*.description' => 'nullable|string|max:255',
        ]);

        // Start a database transaction
        return DB::transaction(function () use ($validated, $workflow, $id) {
            // If this is set as default, unset any existing defaults with the same scope
            if ($validated['is_default']) {
                $this->workflowRepository->setAsDefault($id);
            }

            // Update the workflow
            $this->workflowRepository->update($id, [
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'name' => $validated['name'],
                'is_default' => $validated['is_default'] ?? false,
            ]);

            // Delete existing steps
            $existingSteps = $this->stepRepository->getByWorkflowId($id);
            foreach ($existingSteps as $step) {
                $this->stepRepository->delete($step->id);
            }

            // Create new steps
            foreach ($validated['steps'] as $step) {
                $this->stepRepository->create([
                    'approval_workflow_id' => $id,
                    'step_number' => $step['step_number'],
                    'role_id' => $step['role_id'],
                    'approver_user_id' => $step['approver_user_id'] ?? null,
                    'description' => $step['description'] ?? null,
                ]);
            }

            return redirect()->route('approval-workflows.show', $id)
                ->with('success', 'Approval workflow updated successfully.');
        });
    }

    /**
     * Remove the specified approval workflow from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        $workflow = $this->workflowRepository->findById($id);
        if (!$workflow) {
            abort(404);
        }

        // Check if the workflow is being used by any requisitions
        $inUse = $workflow->requisitions()->exists();

        if ($inUse) {
            return back()->with('error', 'This approval workflow cannot be deleted because it is being used by one or more requisitions.');
        }

        // Delete the workflow (steps will be deleted by cascade)
        $this->workflowRepository->delete($id);

        return redirect()->route('approval-workflows.index')
            ->with('success', 'Approval workflow deleted successfully.');
    }
}
