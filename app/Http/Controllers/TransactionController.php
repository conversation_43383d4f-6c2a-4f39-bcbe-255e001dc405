<?php

namespace App\Http\Controllers;

use App\Models\ChartOfAccount;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Helpers\DatabaseHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;
use Src\Disbursement\Application\Services\DisbursementService;

class TransactionController extends Controller
{
    /**
     * @var DisbursementService
     */
    protected $disbursementService;

    /**
     * TransactionController constructor.
     *
     * @param DisbursementService $disbursementService
     */
    public function __construct(DisbursementService $disbursementService)
    {
        $this->disbursementService = $disbursementService;
    }

    /**
     * Display a listing of transactions.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();

        // Check if user has permission to view transactions
        if (!$user->hasPermissionTo('view-transactions')) {
            abort(403, 'You do not have permission to view transactions');
        }

        // Get filters from request
        $filters = [
            'search' => $request->input('search'),
            'status' => $request->input('status'),
            'sort' => $request->input('sort', 'created_at'),
            'direction' => $request->input('direction', 'desc'),
            'date_from' => $request->input('date_from'),
            'date_to' => $request->input('date_to'),
        ];

        // Get transactions based on user role
        if ($user->hasPermissionTo('manage-transactions')) {
            // Finance managers can see all transactions
            $transactions = $this->disbursementService->getAllTransactions($filters, 10);
        } else {
            // Regular users can only see their own transactions
            $transactions = $this->disbursementService->getTransactionsForRequester($user->id, $filters, 10);
        }

        return Inertia::render('Transactions/Index', [
            'transactions' => $transactions,
            'filters' => $filters,
            'canManageTransactions' => $user->hasPermissionTo('manage-transactions'),
        ]);
    }

    /**
     * Display the specified transaction.
     *
     * @param int $id
     * @return Response
     */
    public function show(int $id): Response
    {
        $user = Auth::user();
        $transaction = $this->disbursementService->getTransactionWithItems($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        // Load attachments for the transaction
        $transaction->load('attachments.uploader');

        // Check if user has permission to view this transaction
        $canView = $user->hasPermissionTo('manage-transactions') ||
                  $this->disbursementService->isRequester($transaction, $user->id);

        if (!$canView) {
            abort(403, 'You are not authorized to view this transaction');
        }

        // Always load transaction creator, cash float, and attachments
        $transaction->load(['creator', 'cashFloat', 'attachments.uploader']);

        // Initialize history arrays
        $requisitionHistory = [];
        $approvalHistory = [];
        $organizationId = null;

        // Load different relationships based on transaction type
        if ($transaction->requisition_id) {
            // Requisition relationships are already loaded by findWithItems()
            // Get full requisition history (creation + approvals)
            $requisitionHistory = $this->getFullRequisitionHistory($transaction->requisition_id);
            $approvalHistory = $this->disbursementService->getRequisitionApprovalHistory($transaction->requisition_id);
            $organizationId = $transaction->requisition->organization_id;
        } else {
            // For non-requisition transactions, try to get organization from cash float
            if ($transaction->cash_float_id) {
                $organizationId = $transaction->cashFloat?->organization_id;
            }
        }

        // Get chart of accounts for displaying item categories
        $chartOfAccounts = ChartOfAccount::all()->keyBy('id');

        // Get active cash floats for selection during completion
        $cashFloats = collect();
        if ($organizationId) {
            $cashFloats = \App\Models\CashFloat::where('status', 'active')
                ->where('organization_id', $organizationId)
                ->select('id', 'name', 'initial_amount', 'alert_threshold', 'organization_id')
                ->orderBy('name')
                ->get();
        }

        // Check if user can attach files to this transaction
        $canAttachFiles = $user->is_platform_admin ||
                         $this->disbursementService->isRequester($transaction, $user->id) ||
                         $user->hasRole('Finance Manager') ||
                         $user->hasRole('Cashier') ||
                         $user->hasRole('Organization Admin');

        return Inertia::render('Transactions/Show', [
            'transaction' => $transaction,
            'chartOfAccounts' => $chartOfAccounts,
            'requisitionHistory' => $requisitionHistory,
            'approvalHistory' => $approvalHistory,
            'cashFloats' => $cashFloats,
            'canUpdate' => $user->hasPermissionTo('manage-transactions') && !in_array($transaction->status, ['completed']),
            'canProcess' => $user->hasPermissionTo('manage-transactions') && $transaction->status === 'updated',
            'canApprove' => $user->hasPermissionTo('manage-transactions') && $transaction->status === 'opened',
            'canAttachFiles' => $canAttachFiles,
        ]);
    }

    /**
     * Show the form for editing the specified transaction.
     *
     * @param int $id
     * @return Response
     */
    public function edit(int $id): Response
    {
        $user = Auth::user();

        // Check if user has permission to edit transactions
        if (!$user->hasPermissionTo('manage-transactions')) {
            abort(403, 'You do not have permission to edit transactions');
        }

        $transaction = $this->disbursementService->getTransactionWithItems($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        if ($transaction->status === 'completed') {
            return redirect()->route('transactions.show', $id)
                ->with('error', 'Completed transactions cannot be edited');
        }

        return Inertia::render('Transactions/Edit', [
            'transaction' => $transaction,
            'paymentMethods' => [
                ['id' => 'cash', 'name' => 'Cash'],
                ['id' => 'bank', 'name' => 'Bank Transfer'],
                ['id' => 'mpesa', 'name' => 'M-Pesa'],
            ],
        ]);
    }

    /**
     * Approve a transaction with payment details.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function approve(Request $request, int $id): RedirectResponse
    {
        $user = Auth::user();

        // Check if user has permission to approve transactions
        if (!$user->hasPermissionTo('manage-transactions')) {
            abort(403, 'You do not have permission to approve transactions');
        }

        $transaction = $this->disbursementService->getTransaction($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        if ($transaction->status !== 'opened') {
            return redirect()->route('transactions.show', $id)
                ->with('error', 'Only transactions in "opened" status can be approved');
        }

        // Validate the request - aligned with database enum values
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:mpesa,bank,cash',
            'account_details' => 'required|string|max:65535', // TEXT field can be large
            'disbursement_transaction_id' => 'nullable|string|max:255',
            'comments' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Prepare update data
            $updateData = [
                'payment_method' => $request->input('payment_method'),
                'account_details' => $request->input('account_details'),
                'status' => 'updated',
                'updated_by' => $user->id,
            ];

            // Add disbursement transaction ID if provided
            if ($request->filled('disbursement_transaction_id')) {
                $updateData['disbursement_transaction_id'] = $request->input('disbursement_transaction_id');
            }

            // Update the transaction
            $updatedTransaction = $this->disbursementService->updateTransaction($id, $updateData);

            // Fire TransactionPaymentDetailsUpdated event when status becomes 'updated'
            if ($updateData['status'] === 'updated') {
                event(new \App\Events\TransactionPaymentDetailsUpdated($updatedTransaction));
            }

            // Log comments if provided
            if ($request->filled('comments')) {
                Log::info("Transaction {$id} approved by user {$user->id}: " . $request->input('comments'));
            }

            $message = 'Transaction approved successfully and is ready for disbursement';

            return redirect()->route('transactions.show', $id)
                ->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Error approving transaction: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while approving the transaction: ' . $e->getMessage());
        }
    }

    /**
     * Once a user has provided their account details     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        $user = Auth::user();

        // Check if user has permission to update transactions
        if (!$user->hasPermissionTo('manage-transactions')) {
            abort(403, 'You do not have permission to update transactions');
        }

        $transaction = $this->disbursementService->getTransaction($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        if ($transaction->status === 'completed') {
            return redirect()->route('transactions.show', $id)
                ->with('error', 'Completed transactions cannot be updated');
        }

        // Validate the request - aligned with database constraints
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:mpesa,bank,cash',
            'account_details' => 'required|string|max:65535', // TEXT field
            'status' => 'sometimes|in:opened,updated,completed',
            'disbursement_transaction_id' => 'nullable|string|max:255',
            'rejection_reason' => 'nullable|string|max:65535', // TEXT field
            'comments' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Prepare update data
            $updateData = [
                'payment_method' => $request->input('payment_method'),
                'account_details' => $request->input('account_details'),
                'updated_by' => $user->id,
            ];

            // Add status if provided
            if ($request->has('status')) {
                $updateData['status'] = $request->input('status');
            }

            // Add disbursement transaction ID if provided
            if ($request->filled('disbursement_transaction_id')) {
                $updateData['disbursement_transaction_id'] = $request->input('disbursement_transaction_id');
            }

            // Add rejection reason if provided
            if ($request->filled('rejection_reason')) {
                $updateData['rejection_reason'] = $request->input('rejection_reason');
            }

            // Update the transaction
            $this->disbursementService->updateTransaction($id, $updateData);

            // If comments provided, log them
            if ($request->filled('comments')) {
                Log::info("Transaction {$id} updated by user {$user->id}: " . $request->input('comments'));
            }

            return redirect()->route('transactions.show', $id)
                ->with('success', 'Transaction updated successfully');
        } catch (\Exception $e) {
            Log::error('Error updating transaction: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while updating the transaction: ' . $e->getMessage());
        }
    }

    /**
     * Complete the transaction process.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function complete(Request $request, int $id): RedirectResponse
    {
        $user = Auth::user();

        // Check if user has permission to complete transactions
        if (!$user->hasPermissionTo('manage-transactions')) {
            abort(403, 'You do not have permission to complete transactions');
        }

        $transaction = $this->disbursementService->getTransaction($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        if ($transaction->status !== 'updated') {
            return redirect()->route('transactions.show', $id)
                ->with('error', 'Only transactions in "updated" status can be completed');
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'disbursement_transaction_id' => 'required|string|max:255',
            'cash_float_id' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if ($value !== null && $value !== 'none' && !\App\Models\CashFloat::where('id', $value)->exists()) {
                        $fail('The selected cash float is invalid.');
                    }
                },
            ],
            'transaction_cost' => 'nullable|numeric|min:0',
            'comments' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Update transaction with additional fields
            $updateData = [
                'disbursement_transaction_id' => $request->input('disbursement_transaction_id'),
                'status' => 'completed',
                'updated_by' => $user->id,
            ];

            // Add cash float ID if provided and not "none"
            if ($request->filled('cash_float_id') && $request->input('cash_float_id') !== 'none') {
                $updateData['cash_float_id'] = $request->input('cash_float_id');
            }

            // Add transaction cost if provided
            if ($request->filled('transaction_cost')) {
                $updateData['transaction_cost'] = $request->input('transaction_cost');
            }

            // Complete the transaction
            $updatedTransaction = $this->disbursementService->updateTransaction($id, $updateData);

            // Fire TransactionDisbursementCompleted event when status becomes 'completed'
            if ($updateData['status'] === 'completed') {
                event(new \App\Events\TransactionDisbursementCompleted($updatedTransaction));
            }

            // Log comments if provided
            if ($request->filled('comments')) {
                Log::info("Transaction {$id} completed by user {$user->id}: " . $request->input('comments'));
            }

            return redirect()->route('transactions.show', $id)
                ->with('success', 'Transaction completed successfully');
        } catch (\Exception $e) {
            Log::error('Error completing transaction: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while completing the transaction: ' . $e->getMessage());
        }
    }

    /**
     * Reject a transaction.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function reject(Request $request, int $id): RedirectResponse
    {
        $user = Auth::user();

        // Check if user has permission to reject transactions
        if (!$user->hasPermissionTo('manage-transactions')) {
            abort(403, 'You do not have permission to reject transactions');
        }

        $transaction = $this->disbursementService->getTransaction($id);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        if ($transaction->status === 'completed') {
            return redirect()->route('transactions.show', $id)
                ->with('error', 'Completed transactions cannot be rejected');
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:65535',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            // Update the transaction with rejection reason
            $updateData = [
                'rejection_reason' => $request->input('rejection_reason'),
                'status' => 'opened', // Reset to opened status for re-approval
                'updated_by' => $user->id,
            ];

            $this->disbursementService->updateTransaction($id, $updateData);

            return redirect()->route('transactions.show', $id)
                ->with('success', 'Transaction rejected successfully');
        } catch (\Exception $e) {
            Log::error('Error rejecting transaction: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while rejecting the transaction: ' . $e->getMessage());
        }
    }

    /**
     * Get full requisition history including creation and approval steps.
     *
     * @param int $requisitionId
     * @return array
     */
    private function getFullRequisitionHistory(int $requisitionId): array
    {
        // Get requisition history (creation, etc.)
        $requisitionHistory = DB::table('requisition_histories')
            ->where('requisition_id', $requisitionId)
            ->join('users', 'requisition_histories.user_id', '=', 'users.id')
            ->select(
                'requisition_histories.id',
                'requisition_histories.action',
                'requisition_histories.comments',
                'requisition_histories.created_at',
                DB::raw(DatabaseHelper::fullName('users') . ' as user_name'),
                'users.id as user_id',
                DB::raw("'requisition' as type")
            )
            ->orderBy('requisition_histories.created_at', 'asc')
            ->get()
            ->toArray();

        return $requisitionHistory;
    }
}