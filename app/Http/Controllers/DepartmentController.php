<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Department;
use App\Models\Organization;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DepartmentController extends Controller
{
    /**
     * Display a listing of the departments.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // If platform admin, show all departments
        if ($user->is_platform_admin) {
            $departments = Department::with(['organization', 'branch', 'headOfDepartment'])->get();

            return Inertia::render('departments/departments-index', [
                'departments' => $departments,
                'isPlatformAdmin' => true,
            ]);
        }

        // Check if user has permission to view departments (supports both hardcoded roles and custom permissions)
        if (!$this->hasDepartmentPermission($user, ['view-departments', 'manage-departments'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to view departments.',
                'user' => $user,
            ]);
        }

        // Get organization from user's roles
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account.',
                'user' => $user,
            ]);
        }

        $departments = Department::where('organization_id', $organization->id)
            ->with(['organization', 'branch', 'headOfDepartment'])
            ->get();

        return Inertia::render('departments/departments-index', [
            'departments' => $departments,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Show the form for creating a new department.
     */
    public function create(Request $request): Response
    {
        $user = $request->user();

        // Check if user has permission to create departments
        if (!$this->hasDepartmentPermission($user, ['manage-departments', 'create-departments'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to create departments.',
                'user' => $user,
            ]);
        }

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();
            $users = User::all();

            return Inertia::render('departments/departments-create', [
                'organizations' => $organizations,
                'branches' => $branches,
                'users' => $users,
                'isPlatformAdmin' => true,
            ]);
        }

        // Get organization from user's roles
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account.',
                'user' => $user,
            ]);
        }

        $branches = Branch::where('organization_id', $organization->id)->get();
        $users = User::whereHas('roles', function ($query) use ($organization) {
            $query->where('organization_id', $organization->id);
        })->get();

        return Inertia::render('departments/departments-create', [
            'organization' => $organization,
            'branches' => $branches,
            'users' => $users,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Store a newly created department in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'required|exists:branches,id',
            'hod_user_id' => 'nullable|exists:users,id',
        ]);

        // If organization admin, ensure the department is associated with their organization
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationId = $user->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            $validated['organization_id'] = $organizationId;

            // Verify branch belongs to organization
            $branch = Branch::find($validated['branch_id']);
            if (!$branch || $branch->organization_id !== $organizationId) {
                return redirect()->back()->withErrors(['branch_id' => 'Invalid branch selected.']);
            }
        }

        // Convert '0' to null for hod_user_id
        if (isset($validated['hod_user_id']) && $validated['hod_user_id'] === '0') {
            $validated['hod_user_id'] = null;
        }

        // Create the department
        $department = Department::create($validated);

        // If HOD is assigned, create or update the HOD role
        if (isset($validated['hod_user_id']) && $validated['hod_user_id'] !== null) {
            $hodUser = User::find($validated['hod_user_id']);

            // Check if a HOD role already exists for this department
            $hodRole = Role::where('name', 'HOD')
                ->where('department_id', $department->id)
                ->first();

            if (!$hodRole) {
                // Create a new HOD role for this department
                $hodRole = Role::create([
                    'name' => 'HOD',
                    'guard_name' => 'web',
                    'organization_id' => $department->organization_id,
                    'branch_id' => $department->branch_id,
                    'department_id' => $department->id,
                    'description' => 'Head of Department for ' . $department->name,
                    'is_active' => true,
                ]);

                // Copy permissions from the template HOD role
                $templateHodRole = Role::where('name', 'HOD')
                    ->whereNull('department_id')
                    ->first();

                if ($templateHodRole) {
                    $permissions = $templateHodRole->permissions;
                    $hodRole->syncPermissions($permissions);
                }
            }

            // Assign the HOD role to the user
            $hodUser->assignRole($hodRole);

            // Add the user to the department
            if (!$hodUser->departments()->where('department_id', $department->id)->exists()) {
                $hodUser->departments()->attach($department->id);
            }
        }

        return redirect()->route('departments.index')
            ->with('success', 'Department created successfully.');
    }

    /**
     * Display the specified department.
     */
    public function show(Department $department): Response
    {
        $department->load(['organization', 'branch', 'headOfDepartment', 'users.roles']);
        return Inertia::render('departments/departments-show', [
            'department' => $department->toArray(),
        ]);
    }

    /**
     * Show the form for editing the specified department.
     */
    public function edit(Request $request, Department $department): Response
    {
        $user = $request->user();
        $department->load([
            'organization' => function($query) {
                $query->select('id', 'name');
            },
            'branch' => function($query) {
                $query->select('id', 'name', 'organization_id');
            },
            'headOfDepartment' => function($query) {
                $query->select('id', 'first_name', 'last_name');
            }
        ]);

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();
            $users = User::all();

            return Inertia::render('departments/departments-edit', [
                'department' => $department,
                'organizations' => $organizations,
                'branches' => $branches,
                'users' => $users,
                'isPlatformAdmin' => true,
            ]);
        }

        // Organization admin
        $organizationId = $user->roles()
            ->where('name', 'Organization Admin')
            ->first()
            ->organization_id;

        // Check if the department belongs to the organization
        if ($department->organization_id !== $organizationId) {
            abort(403, 'Unauthorized action.');
        }

        $organization = Organization::find($organizationId);
        $branches = Branch::where('organization_id', $organizationId)->get();
        $users = User::whereHas('roles', function ($query) use ($organizationId) {
            $query->where('organization_id', $organizationId);
        })->get();

        return Inertia::render('departments/departments-edit', [
            'department' => $department,
            'organization' => $organization,
            'branches' => $branches,
            'users' => $users,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Update the specified department in storage.
     */
    public function update(Request $request, Department $department): RedirectResponse
    {
        $user = $request->user();

        // Check if the user can edit this department
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationId = $user->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            // If the department is not associated with the user's organization
            if ($department->organization_id !== $organizationId) {
                abort(403, 'Unauthorized action.');
            }
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'required|exists:branches,id',
            'hod_user_id' => 'nullable|exists:users,id',
        ]);

        // If organization admin, ensure the department stays associated with their organization
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationId = $user->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            $validated['organization_id'] = $organizationId;

            // Verify branch belongs to organization
            $branch = Branch::find($validated['branch_id']);
            if (!$branch || $branch->organization_id !== $organizationId) {
                return redirect()->back()->withErrors(['branch_id' => 'Invalid branch selected.']);
            }
        }

        // Store the old HOD user ID for comparison
        $oldHodUserId = $department->hod_user_id;

        // Convert '0' to null for hod_user_id
        if (isset($validated['hod_user_id']) && $validated['hod_user_id'] === '0') {
            $validated['hod_user_id'] = null;
        }

        // Update the department
        $department->update($validated);

        // If HOD has changed, update roles
        if ($oldHodUserId !== ($validated['hod_user_id'] ?? null)) {
            // If there was an old HOD, remove their HOD role for this department
            if ($oldHodUserId) {
                $oldHodUser = User::find($oldHodUserId);
                if ($oldHodUser) {
                    $hodRole = Role::where('name', 'HOD')
                        ->where('department_id', $department->id)
                        ->first();

                    if ($hodRole) {
                        $oldHodUser->removeRole($hodRole);
                    }
                }
            }

            // If there's a new HOD, assign them the HOD role
            if (isset($validated['hod_user_id'])) {
                $hodUser = User::find($validated['hod_user_id']);

                // Check if a HOD role already exists for this department
                $hodRole = Role::where('name', 'HOD')
                    ->where('department_id', $department->id)
                    ->first();

                if (!$hodRole) {
                    // Create a new HOD role for this department
                    $hodRole = Role::create([
                        'name' => 'HOD',
                        'guard_name' => 'web',
                        'organization_id' => $department->organization_id,
                        'branch_id' => $department->branch_id,
                        'department_id' => $department->id,
                        'description' => 'Head of Department for ' . $department->name,
                        'is_active' => true,
                    ]);

                    // Copy permissions from the template HOD role
                    $templateHodRole = Role::where('name', 'HOD')
                        ->whereNull('department_id')
                        ->first();

                    if ($templateHodRole) {
                        $permissions = $templateHodRole->permissions;
                        $hodRole->syncPermissions($permissions);
                    }
                }

                // Assign the HOD role to the user
                $hodUser->assignRole($hodRole);

                // Add the user to the department
                if (!$hodUser->departments()->where('department_id', $department->id)->exists()) {
                    $hodUser->departments()->attach($department->id);
                }
            }
        }

        return redirect()->route('departments.index')
            ->with('success', 'Department updated successfully.');
    }

    /**
     * Remove the specified department from storage.
     */
    public function destroy(Request $request, Department $department): RedirectResponse
    {
        $user = $request->user();

        // Check if the user can delete this department
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationId = $user->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            // If the department is not associated with the user's organization
            if ($department->organization_id !== $organizationId) {
                abort(403, 'Unauthorized action.');
            }
        }

        // Check if the department has users
        $usersCount = $department->users()->count();
        if ($usersCount > 0) {
            return redirect()->route('departments.index')
                ->with('error', 'Cannot delete department because it has ' . $usersCount . ' user(s) assigned to it.');
        }

        // Delete department-specific roles
        Role::where('department_id', $department->id)->delete();

        $department->delete();

        return redirect()->route('departments.index')
            ->with('success', 'Department deleted successfully.');
    }

    /**
     * Check if user has any of the specified department permissions.
      */
    private function hasDepartmentPermission(User $user, array $permissions): bool
    {
        // First check hardcoded roles for backward compatibility
        if ($user->hasRole('Organization Admin') ||
            $user->is_platform_admin) {
            return true;
        }

        // Then check for specific permissions (for custom roles)
        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        // Also check for comprehensive management permissions
        $managementPermissions = [
            'manage-users',
            'manage-roles',
            'manage-organizations'
        ];

        foreach ($managementPermissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get user's organization based on their roles.
      */
    private function getUserOrganization(User $user): ?Organization
    {
        // First try Organization Admin role (backward compatibility)
        $orgAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        if ($orgAdminRole && $orgAdminRole->organization_id) {
            return Organization::find($orgAdminRole->organization_id);
        }

        // Then try to get organization from any role with department management permissions (for custom roles)
        $rolesWithOrganization = $user->roles()
            ->whereNotNull('organization_id')
            ->with('permissions')
            ->get();

        foreach ($rolesWithOrganization as $role) {
            $permissions = $role->permissions->pluck('name')->toArray();

            $hasDepartmentPermission = array_intersect($permissions, [
                'manage-departments',
                'view-departments',
                'create-departments',
                'manage-users',
                'manage-roles',
                'manage-organizations'
            ]);

            if (!empty($hasDepartmentPermission)) {
                return Organization::find($role->organization_id);
            }
        }

        return null;
    }
}
