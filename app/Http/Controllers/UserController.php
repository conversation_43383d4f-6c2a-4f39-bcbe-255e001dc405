<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\Organization;
use App\Models\Role;
use App\Models\User;
use App\Rules\StrongPassword;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\DB;
use App\Models\UserOrganization;
use App\Models\UserBranch;
use App\Models\UserDepartment;
use App\Models\Branch;
use App\Notifications\UserOnboardingNotification;
use EragLaravelDisposableEmail\Rules\DisposableEmailRule;
use Src\UserManagement\Application\Services\UserCreationService;
use Src\UserManagement\Application\DTOs\CreateUserDTO;

class UserController extends Controller
{
    public function __construct(
        private readonly UserCreationService $userCreationService
    ) {}

    /**
     * Display a listing of the users.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // If platform admin, show all users
        if ($user->is_platform_admin) {
            $users = User::all();
            $users->each(function ($user) {
                $user->roles = $user->roles->map(function ($role) {
                    return ['id' => $role->id, 'name' => $role->name];
                });
            });
            $organizations = Organization::all();

            return Inertia::render('users/users-index', [
                'users' => $users,
                'organizations' => $organizations,
                'isPlatformAdmin' => true,
            ]);
        }

        // Check if user has permission to view users (supports both hardcoded roles and custom permissions)
        if (!$this->hasUserPermission($user, ['view-users', 'manage-users'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to view users.',
                'user' => $user,
            ]);
        }

        // Get organization from user's roles
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account.',
                'user' => $user,
            ]);
        }

        // Get users that belong to this organization through the user_organizations table
        $users = User::whereHas('organizations', function ($query) use ($organization) {
            $query->where('organizations.id', $organization->id);
        })->get();

        $users->each(function ($user) {
            $user->roles = $user->roles->map(function ($role) {
                return ['id' => $role->id, 'name' => $role->name];
            });
        });
        return Inertia::render('users/users-index', [
            'users' => $users,
            'organization' => $organization,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(Request $request): Response
    {
        $user = $request->user();

        // Check if user has permission to create users
        if (!$this->hasUserPermission($user, ['manage-users', 'create-users'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to create users.',
                'user' => $user,
            ]);
        }

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $roles = Role::all();
            $branches = Branch::all();
            $departments = Department::all();

            return Inertia::render('users/users-create', [
                'organizations' => $organizations,
                'roles' => $roles,
                'branches' => $branches,
                'departments' => $departments,
                'isPlatformAdmin' => true,
            ]);
        }

        // Get organization from user's roles
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account.',
                'user' => $user,
            ]);
        }

        // Get roles but filter HOD roles to show only generic ones (not department-specific)
        $roles = Role::where('organization_id', $organization->id)
            ->where(function($query) {
                $query->where('name', '!=', 'HOD')
                      ->orWhere(function($subQuery) {
                          $subQuery->where('name', 'HOD')
                                   ->whereNull('department_id');
                      });
            })
            ->get();

        $branches = Branch::where('organization_id', $organization->id)->get();
        $departments = Department::where('organization_id', $organization->id)->get();

        return Inertia::render('users/users-create', [
            'organization' => $organization,
            'roles' => $roles,
            'branches' => $branches,
            'departments' => $departments,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request) : RedirectResponse
    {

        // Ensure role_ids is always an array
        if (!is_array($request->input('role_ids'))) {
            $request->merge([
                'role_ids' => [$request->input('role_ids')]
            ]);
        }

        // Build email validation rules
        $emailRules = [
            'required',
            'string',
            'email',
            'max:255',
            'unique:users',
        ];

        // Add disposable email validation if package is available
        if (class_exists('EragLaravelDisposableEmail\Rules\DisposableEmailRule')) {
            $emailRules[] = new DisposableEmailRule();
        }

        $validated = $request->validate([
            'username' => 'required|string|max:255|unique:users',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => $emailRules,
            'phone' => 'nullable|string|max:20',
            'password' => ['required', new StrongPassword()],
            'is_platform_admin' => 'boolean',
            'status' => 'required|in:active,invited,inactive',
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
            'branch_ids' => 'nullable|array',
            'branch_ids.*' => 'exists:branches,id',
            'department_ids' => 'nullable|array',
            'department_ids.*' => 'exists:departments,id',
        ], [
            // Custom error messages
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
        ]);


        // if($validated['status'] === 'active') {
        //     $validated['email_verified_at'] = now();
        // }

        // Store the plain password for the notification
        $plainPassword = $validated['password'];

        // Create user using the centralized service
        try {
            // Get the first role for the DTO (we'll handle multiple roles after creation)
            $primaryRoleId = $validated['role_ids'][0] ?? null;

            $primaryRole = $primaryRoleId ? Role::find($primaryRoleId) : null;

            $userDTO = CreateUserDTO::fromArray([
                'username' => $validated['username'],
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'password' => $validated['password'],
                'is_platform_admin' => $validated['is_platform_admin'] ?? false,
                'status' => $validated['status'],
                'role_name' => $primaryRole?->name,
                'role_id' => $primaryRoleId,
                'branch_id' => $validated['branch_ids'][0] ?? null,
                'department_id' => $validated['department_ids'][0] ?? null,
                'send_notification' => false, // We'll handle notification separately
                'auto_verify_email' => true,
            ]);

            $user = $this->userCreationService->createUser($userDTO, Auth::user());

            // Handle primary role if it's HOD
            if ($primaryRole && $primaryRole->name === 'HOD' && isset($validated['department_ids'])) {
                // Get organization ID from the first department
                $firstDepartment = Department::find($validated['department_ids'][0]);
                $organizationId = $firstDepartment ? $firstDepartment->organization_id : null;

                if ($organizationId) {
                    foreach ($validated['department_ids'] as $departmentId) {
                        $this->assignHodRoleForDepartment($user, $organizationId, $departmentId);
                    }
                }
            }

            // Handle additional roles if multiple were selected
            if (count($validated['role_ids']) > 1) {
                $additionalRoleIds = array_slice($validated['role_ids'], 1);
                foreach ($additionalRoleIds as $roleId) {
                    $role = Role::find($roleId);
                    if ($role) {
                        // Special handling for HOD role
                        if ($role->name === 'HOD') {
                            // For HOD role, create department-specific roles
                            if (isset($validated['department_ids'])) {
                                foreach ($validated['department_ids'] as $departmentId) {
                                    $this->assignHodRoleForDepartment($user, $role->organization_id, $departmentId);
                                }
                            }
                        } else {
                            $user->assignRole($role);
                        }
                    }
                }
            }

            // Handle additional branches if multiple were selected
            if (isset($validated['branch_ids']) && count($validated['branch_ids']) > 1) {
                $additionalBranchIds = array_slice($validated['branch_ids'], 1);
                foreach ($additionalBranchIds as $branchId) {
                    UserBranch::firstOrCreate([
                        'user_id' => $user->id,
                        'branch_id' => $branchId,
                    ]);
                }
            }

            // Handle additional departments if multiple were selected
            if (isset($validated['department_ids']) && count($validated['department_ids']) > 1) {
                $additionalDepartmentIds = array_slice($validated['department_ids'], 1);
                $user->departments()->attach($additionalDepartmentIds);
            }

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create user: ' . $e->getMessage()])
                ->withInput();
        }

        // Handle HOD role assignment for additional roles
        foreach ($validated['role_ids'] as $roleId) {
            $role = Role::find($roleId);
            if ($role && $role->name === 'HOD' && $role->department_id) {
                $department = Department::find($role->department_id);
                if ($department) {
                    $department->update(['hod_user_id' => $user->id]);
                }
            }
        }

        // Send onboarding notification to the new user
        // Get the current admin user who is creating this user
        $organizationAdmin = Auth::user();

        // Send the notification
        $user->notify(new UserOnboardingNotification($user, $plainPassword, $organizationAdmin));

        return redirect()->route('users.index')->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(Request $request, User $user): Response
    {
        // Check if the current user is an organization admin and can access this user
        $currentUser = $request->user();
        if (!$currentUser->is_platform_admin && $currentUser->hasRole('Organization Admin')) {
            $organizationId = $currentUser->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            // Check if the user belongs to the same organization
            $userBelongsToOrg = $user->organizations()->where('organizations.id', $organizationId)->exists();

            if (!$userBelongsToOrg) {
                abort(403, 'You do not have permission to view this user.');
            }
        }
        $user->load([
            'roles' => function($query) {
                $query->select('roles.id', 'roles.name');
            },
            'departments' => function($query) {
                $query->select('departments.id', 'departments.name');
            }
        ]);

        return Inertia::render('users/users-show', [
            'user' => $user,
        ]);
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(Request $request, User $user): Response
    {
        $currentUser = $request->user();
        $user->load([
            'roles' => function($query) {
                $query->select('roles.id', 'roles.name');
            },
            'departments' => function($query) {
                $query->select('departments.id', 'departments.name');
            }
        ]);

        if ($currentUser->is_platform_admin) {
            $organizations = Organization::all();
            $roles = Role::all();
            $departments = Department::all();

            return Inertia::render('users/users-edit', [
                'user' => $user,
                'organizations' => $organizations,
                'roles' => $roles,
                'departments' => $departments,
                'isPlatformAdmin' => true,
            ]);
        }

        // Organization admin
        $organizationId = $currentUser->roles()
            ->where('name', 'Organization Admin')
            ->first()
            ->organization_id;

        // Check if the user belongs to the same organization
        $userBelongsToOrg = $user->organizations()->where('organizations.id', $organizationId)->exists();

        if (!$userBelongsToOrg) {
            abort(403, 'You do not have permission to edit this user.');
        }

        $organization = Organization::find($organizationId);

        // Get roles but filter HOD roles to show only generic ones (not department-specific)
        $roles = Role::where('organization_id', $organizationId)
            ->where(function($query) {
                $query->where('name', '!=', 'HOD')
                      ->orWhere(function($subQuery) {
                          $subQuery->where('name', 'HOD')
                                   ->whereNull('department_id');
                      });
            })
            ->get();

        $departments = Department::where('organization_id', $organizationId)->get();

        return Inertia::render('users/users-edit', [
            'user' => $user,
            'organization' => $organization,
            'roles' => $roles,
            'departments' => $departments,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        // Check if the current user is an organization admin and can access this user
        $currentUser = $request->user();
        if (!$currentUser->is_platform_admin && $currentUser->hasRole('Organization Admin')) {
            $organizationId = $currentUser->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            // Check if the user belongs to the same organization
            $userBelongsToOrg = $user->organizations()->where('organizations.id', $organizationId)->exists();

            if (!$userBelongsToOrg) {
                abort(403, 'You do not have permission to update this user.');
            }
        }

        // Prevent password updates by non-owners
        if ($request->has('password') && !empty($request->input('password'))) {
            if ($currentUser->id !== $user->id) {
                return redirect()->back()
                    ->withErrors(['password' => 'You can only change your own password. Users must change their passwords through their account settings.'])
                    ->withInput();
            }
        }
        // Build email validation rules
        $emailRules = [
            'required',
            'string',
            'email',
            'max:255',
            Rule::unique('users')->ignore($user->id),
        ];

        // Add disposable email validation if package is available
        if (class_exists('EragLaravelDisposableEmail\Rules\DisposableEmailRule')) {
            $emailRules[] = new DisposableEmailRule();
        }

        // Build validation rules - include password validation only if user is editing their own profile
        $validationRules = [
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => $emailRules,
            'phone' => 'nullable|string|max:20',
            'is_platform_admin' => 'boolean',
            'status' => 'required|in:active,invited,inactive',
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
            'department_ids' => 'nullable|array',
            'department_ids.*' => 'exists:departments,id',
        ];

        // Only allow password validation if user is editing their own profile
        if ($currentUser->id === $user->id) {
            $validationRules['password'] = ['nullable', new StrongPassword()];
        }

        $validated = $request->validate($validationRules, [
            // Custom error messages
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
        ]);



        // Update user data
        $userData = [
            'username' => $validated['username'],
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'is_platform_admin' => $validated['is_platform_admin'] ?? false,
            'status' => $validated['status'],
        ];

        // Only update password if provided and user is editing their own profile
        if ($currentUser->id === $user->id && isset($validated['password']) && !empty($validated['password'])) {
            $userData['password'] = Hash::make($validated['password']);
        }

        $user->update($userData);

        // Update roles
        $user->roles()->detach();
        foreach ($validated['role_ids'] as $roleId) {


            $role = Role::find($roleId);

            // Special handling for Organization Admin role
            if ($role && $role->name === 'Organization Admin') {
                // If this is a new Organization Admin assignment, ensure it's tied to the correct organization
                $organizationId = null;

                // If user is platform admin, they might be creating an org admin for any organization
                if ($request->user()->is_platform_admin && isset($validated['organization_id'])) {
                    $organizationId = $validated['organization_id'];
                } else if (!$request->user()->is_platform_admin && $request->user()->hasRole('Organization Admin')) {
                    // If user is org admin, they can only create org admins for their own organization
                    $organizationId = $request->user()->roles()
                        ->where('name', 'Organization Admin')
                        ->first()
                        ->organization_id;
                }

                if ($organizationId) {
                    // Check if an Organization Admin role already exists for this organization
                    $orgAdminRole = Role::where('name', 'Organization Admin')
                        ->where('organization_id', $organizationId)
                        ->first();

                    if (!$orgAdminRole) {
                        // Create a new Organization Admin role for this organization
                        $organization = Organization::find($organizationId);
                        $orgAdminRole = Role::create([
                            'name' => 'Organization Admin',
                            'organization_id' => $organizationId,
                            'description' => 'Administrator for ' . ($organization ? $organization->name : 'an organization'),
                            'guard_name' => 'web'
                        ]);

                        // Copy permissions from the template role
                        $templateRole = Role::where('name', 'Organization Admin')
                            ->whereNull('organization_id')
                            ->first();

                        if ($templateRole) {
                            $orgAdminRole->givePermissionTo($templateRole->permissions);
                        }
                    }

                    // Assign the organization-specific admin role
                    $user->assignRole($orgAdminRole);
                } else {
                    // If no organization specified, just assign the role as is
                    $user->assignRole($role);
                }
            } else {
                // Special handling for HOD role
                if ($role->name === 'HOD') {
                    // For HOD role, we need to assign the department-specific HOD role
                    // Get the user's departments
                    $userDepartments = isset($validated['department_ids']) ? $validated['department_ids'] : $user->departments->pluck('id')->toArray();

                    foreach ($userDepartments as $departmentId) {
                        // Find or create HOD role for this specific department
                        $departmentHodRole = Role::where('name', 'HOD')
                            ->where('organization_id', $role->organization_id)
                            ->where('department_id', $departmentId)
                            ->first();

                        if (!$departmentHodRole) {
                            // Create HOD role for this department
                            $department = Department::find($departmentId);
                            if ($department) {
                                $departmentHodRole = new Role();
                                $departmentHodRole->name = 'HOD';
                                $departmentHodRole->guard_name = 'web';
                                $departmentHodRole->description = 'Head of ' . $department->name;
                                $departmentHodRole->organization_id = $role->organization_id;
                                $departmentHodRole->department_id = $departmentId;
                                $departmentHodRole->is_active = true;
                                $departmentHodRole->save();

                                // Copy permissions from template HOD role
                                $hodTemplate = Role::where('name', 'HOD')->whereNull('organization_id')->first();
                                if ($hodTemplate) {
                                    $departmentHodRole->syncPermissions($hodTemplate->permissions);
                                }
                            }
                        }

                        if ($departmentHodRole) {
                            // Assign the department-specific HOD role
                            $user->assignRole($departmentHodRole);

                            // Update the department to have this user as HOD
                            $department = Department::find($departmentId);
                            if ($department) {
                                $department->update(['hod_user_id' => $user->id]);
                            }
                        }
                    }
                } else {
                    // For other roles, just assign them directly
                    $user->assignRole($role);
                }
            }
        }

        // Update departments
        if (isset($validated['department_ids'])) {
            $user->departments()->sync($validated['department_ids']);
        } else {
            $user->departments()->detach();
        }

        return redirect()->route('users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(Request $request, User $user): RedirectResponse
    {
        // Check if the current user is an organization admin and can access this user
        $currentUser = $request->user();
        if (!$currentUser->is_platform_admin && $currentUser->hasRole('Organization Admin')) {
            $organizationId = $currentUser->roles()
                ->where('name', 'Organization Admin')
                ->first()
                ->organization_id;

            // Check if the user belongs to the same organization
            $userBelongsToOrg = $user->organizations()->where('organizations.id', $organizationId)->exists();

            if (!$userBelongsToOrg) {
                abort(403, 'You do not have permission to delete this user.');
            }
        }
        // Prevent deleting yourself
        if (Auth::id() === $user->id) {
            return redirect()->route('users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        // Remove HOD references
        Department::where('hod_user_id', $user->id)->update(['hod_user_id' => null]);

        $user->delete();

        return redirect()->route('users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Helper method to assign HOD role for a specific department
     */
    private function assignHodRoleForDepartment(User $user, int $organizationId, int $departmentId): void
    {
        // Find or create HOD role for this specific department
        $departmentHodRole = Role::where('name', 'HOD')
            ->where('organization_id', $organizationId)
            ->where('department_id', $departmentId)
            ->first();

        if (!$departmentHodRole) {
            // Create HOD role for this department
            $department = Department::find($departmentId);
            if ($department) {
                $departmentHodRole = new Role();
                $departmentHodRole->name = 'HOD';
                $departmentHodRole->guard_name = 'web';
                $departmentHodRole->description = 'Head of ' . $department->name;
                $departmentHodRole->organization_id = $organizationId;
                $departmentHodRole->department_id = $departmentId;
                $departmentHodRole->is_active = true;
                $departmentHodRole->save();

                // Copy permissions from template HOD role
                $hodTemplate = Role::where('name', 'HOD')->whereNull('organization_id')->first();
                if ($hodTemplate) {
                    $departmentHodRole->syncPermissions($hodTemplate->permissions);
                }
            }
        }

        if ($departmentHodRole) {
            // Assign the department-specific HOD role
            $user->assignRole($departmentHodRole);

            // Update the department to have this user as HOD
            $department = Department::find($departmentId);
            if ($department) {
                $department->update(['hod_user_id' => $user->id]);
            }
        }
    }

    /**
     * Check if user has any of the specified user permissions.
     */
    private function hasUserPermission(User $user, array $permissions): bool
    {
        // First check hardcoded roles for backward compatibility
        if ($user->hasRole('Organization Admin') ||
            $user->is_platform_admin) {
            return true;
        }

        // Then check for specific permissions (for custom roles)
        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        // Also check for comprehensive management permissions
        $managementPermissions = [
            'manage-roles',
            'manage-departments',
            'manage-organizations'
        ];

        foreach ($managementPermissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get user's organization based on their roles.
      */
    private function getUserOrganization(User $user): ?Organization
    {
        // First try Organization Admin role (backward compatibility)
        $orgAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        if ($orgAdminRole && $orgAdminRole->organization_id) {
            return Organization::find($orgAdminRole->organization_id);
        }

        // Then try to get organization from any role with user management permissions (for custom roles)
        $rolesWithOrganization = $user->roles()
            ->whereNotNull('organization_id')
            ->with('permissions')
            ->get();

        foreach ($rolesWithOrganization as $role) {
            $permissions = $role->permissions->pluck('name')->toArray();

            $hasUserPermission = array_intersect($permissions, [
                'manage-users',
                'view-users',
                'create-users',
                'manage-roles',
                'manage-departments',
                'manage-organizations'
            ]);

            if (!empty($hasUserPermission)) {
                return Organization::find($role->organization_id);
            }
        }

        return null;
    }
}
