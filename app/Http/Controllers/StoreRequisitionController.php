<?php

namespace App\Http\Controllers;

use App\Models\StoreRequisition;
use App\Models\InventoryItem;
use App\Models\Branch;
use App\Models\Department;
use App\Models\StoreRequisitionItem;
use App\Services\StoreRequisition\StoreRequisitionService;
use App\Services\StoreRequisition\StoreRequisitionIssueService;
use App\Services\StoreRequisition\StoreRequisitionQueryService;
use App\Services\StoreRequisition\StoreRequisitionReportService;
use App\Services\StoreRequisition\StoreRequisitionHelperService;
use App\Http\Requests\StoreRequisition\StoreStoreRequisitionRequest;
use App\Http\Requests\StoreRequisition\UpdateStoreRequisitionRequest;
use App\Http\Requests\StoreRequisition\ApproveStoreRequisitionRequest;
use App\Http\Requests\StoreRequisition\RejectStoreRequisitionRequest;
use App\Http\Requests\StoreRequisition\IssueStoreRequisitionRequest;
use App\Http\Requests\StoreRequisition\UpdateRejectedStoreRequisitionRequest;
use App\Events\StoreRequisitionSubmitted;
use App\Events\StoreRequisitionApproved;
use App\Events\StoreRequisitionRejected;
use App\Events\StoreRequisitionReturnedForRevision;
use App\Events\StoreRequisitionIssued;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class StoreRequisitionController extends Controller
{
    public function __construct(
        private StoreRequisitionService $storeRequisitionService,
        private StoreRequisitionIssueService $issueService,
        private StoreRequisitionQueryService $queryService,
        private StoreRequisitionReportService $reportService,
        private StoreRequisitionHelperService $helperService
    ) {}

    public function index()
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();
        
        if (!$organization) {
            abort(400, 'User must belong to an organization to view store requisitions');
        }

        $myRequisitions = $this->queryService->getMyRequisitions();
        $pendingApprovals = $this->queryService->getPendingApprovals();
        $allRequisitions = $this->queryService->getAllRequisitions();

        $this->helperService->loadRequesterPermissions([$myRequisitions, $pendingApprovals, $allRequisitions]);

        return Inertia::render('StoreRequisitions/StoreRequisitionManagement', [
            'my_requisitions' => $myRequisitions,
            'pending_approvals' => $pendingApprovals,
            'all_requisitions' => $allRequisitions,
            'user' => [
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $this->helperService->getUserPermissions($user),
                'roles' => $this->helperService->getUserRoles($user),
            ],
        ]);
    }

    public function approvals()
    {
        if (!Auth::user()->can('approve-store-requisition') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to view store requisition approvals');
        }

        $user = Auth::user();
        $organization = $user->organizations()->first();
        
        if (!$organization) {
            abort(400, 'User must belong to an organization to view store requisitions');
        }

        $pendingApprovals = $this->queryService->getPendingApprovals();

        return Inertia::render('StoreRequisitions/StoreRequisitionApprovals', [
            'pending_approvals' => $pendingApprovals,
            'user' => [
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
            ],
        ]);
    }

    public function create(Request $request)
    {
        if (!Auth::user()->can('create-store-requisition') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to create store requisitions');
        }

        $user = Auth::user();
        $requestedDepartmentId = $request->input('department');
        $organization = $user->organizations()->first();
        
        if (!$organization) {
            abort(400, 'User must belong to an organization to create store requisitions');
        }

        $userDepartments = $this->helperService->getUserDepartments();
        if ($userDepartments->isEmpty()) {
            return redirect()->route('store-requisitions.index')
                ->with('error', 'You must be assigned to at least one department to create store requisitions. Please contact your administrator.');
        }

        $departmentId = $this->determineDepartment($userDepartments, $requestedDepartmentId);
        $department = $userDepartments->firstWhere('id', $departmentId);

        $inventoryItems = $this->helperService->getOrganizationInventoryItems();
        if ($inventoryItems->isEmpty()) {
            return redirect()->route('store-requisitions.index')
                ->with('error', 'Please contact your administrator for assistance.');
        }

        return Inertia::render('StoreRequisitions/CreateStoreRequisition', [
            'inventory_items' => $inventoryItems,
            'branches' => $this->helperService->getUserBranches(),
            'departments' => $userDepartments,
            'user_branch_id' => $department?->branch_id,
            'user_department_id' => $departmentId,
            'user_departments' => $userDepartments,
            'department_id' => $departmentId,
            'department_name' => $department?->name,
        ]);
    }

    public function store(StoreStoreRequisitionRequest $request)
    {
        try {
            $requisition = $this->storeRequisitionService->createRequisition($request->validated());
            
            $isDraft = $request->validated()['save_as_draft'] ?? false;
            $successMessage = $isDraft
                ? 'Store requisition saved as draft successfully!'
                : 'Store requisition created and submitted for approval successfully!';

            return redirect()->route('store-requisitions.index')->with('success', $successMessage);
        } catch (\Exception $e) {
            Log::error('Failed to create store requisition: ' . $e->getMessage());
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create requisition. Please try again.'])
                ->withInput();
        }
    }

    public function show(StoreRequisition $storeRequisition)
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();
        
        if (!$organization || $storeRequisition->organization_id !== $organization->id) {
            abort(403, 'Unauthorized to view this store requisition');
        }

        if ($storeRequisition->status === StoreRequisition::STATUS_DRAFT) {
            if ($storeRequisition->requester_user_id !== $user->id) {
                abort(403, 'Draft requisitions can only be viewed by their requester');
            }
        } else {
            $canView = $user->can('view-store-requisitions') ||
                       $user->can('store-keep') ||
                       $storeRequisition->requester_user_id === $user->id;

            if (!$canView) {
                abort(403, 'Unauthorized to view this store requisition');
            }
        }

        $storeRequisition->load(['requester', 'approver', 'issuer', 'items.inventoryItem', 'department', 'branch', 'histories.user']);

        if ($storeRequisition->requester) {
            try {
                $storeRequisition->requester->permissions = $storeRequisition->requester->permission_names;
            } catch (\Exception $e) {
                Log::error('Error loading permissions for requester ' . $storeRequisition->requester->id . ': ' . $e->getMessage());
                $storeRequisition->requester->permissions = [];
            }
        }

        return Inertia::render('StoreRequisitions/ShowStoreRequisition', [
            'store_requisition' => $storeRequisition,
            'histories' => $storeRequisition->histories()->with('user')->orderBy('created_at', 'asc')->get(),
            'user' => [
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $this->helperService->getUserPermissions($user),
                'roles' => $this->helperService->getUserRoles($user),
            ],
        ]);
    }

    public function edit(StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('edit-store-requisition') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to edit store requisitions');
        }

        $user = Auth::user();

        if (!$this->helperService->validateUserOrganizationAccess($storeRequisition->organization_id)) {
            abort(403, 'Unauthorized to edit this store requisition');
        }

        if (!$user->can('update', $storeRequisition)) {
            abort(403, 'You are not authorized to edit this store requisition');
        }

        $storeRequisition->load(['items.inventoryItem', 'department', 'branch']);

        return Inertia::render('StoreRequisitions/EditStoreRequisition', [
            'store_requisition' => $storeRequisition,
            'inventory_items' => $this->helperService->getOrganizationInventoryItems(),
            'branches' => $this->helperService->getOrganizationBranches(),
            'departments' => $this->helperService->getOrganizationDepartments(),
            'user_branch_id' => $storeRequisition->branch_id,
            'user_department_id' => $storeRequisition->department_id,
            'user_departments' => $this->helperService->getUserDepartments(),
        ]);
    }

    public function update(UpdateStoreRequisitionRequest $request, StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('update', $storeRequisition)) {
            abort(403, 'You are not authorized to edit this store requisition');
        }

        try {
            $this->storeRequisitionService->updateRequisition($storeRequisition, $request->validated());
            
            $isDraft = $request->validated()['save_as_draft'] ?? false;
            $successMessage = $isDraft
                ? 'Store requisition updated and saved as draft successfully'
                : 'Store requisition updated and submitted for approval successfully';

            return redirect()->route('store-requisitions.show', $storeRequisition)->with('success', $successMessage);
        } catch (\Exception $e) {
            Log::error('Failed to update store requisition: ' . $e->getMessage());
            return redirect()->back()
                ->withErrors(['error' => 'Failed to update requisition. Please try again.'])
                ->withInput();
        }
    }

    public function submit(StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('create-store-requisition') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to submit store requisitions');
        }

        if ($storeRequisition->requester_user_id !== Auth::id()) {
            abort(403, 'You can only submit your own requisitions');
        }

        if (!$storeRequisition->canBeSubmitted()) {
            return response()->json(['error' => 'Only draft requisitions can be submitted'], 400);
        }

        $this->storeRequisitionService->submitRequisition($storeRequisition);

        // Dispatch event for notifications
        StoreRequisitionSubmitted::dispatch($storeRequisition);

        return redirect()->route('store-requisitions.show', $storeRequisition)
            ->with('success', 'Store requisition submitted for approval successfully');
    }

    public function approve(ApproveStoreRequisitionRequest $request, StoreRequisition $storeRequisition)
    {
        if (!$storeRequisition->canBeApproved()) {
            return response()->json(['error' => 'Only pending requisitions can be approved'], 400);
        }

        $this->storeRequisitionService->approveRequisition($storeRequisition, $request->validated()['comments'] ?? null);

        // Dispatch event for notifications
        StoreRequisitionApproved::dispatch($storeRequisition, Auth::user());

        return redirect()->back()->with('success', 'Store requisition approved successfully');
    }

    public function reject(RejectStoreRequisitionRequest $request, StoreRequisition $storeRequisition)
    {
        if (!$storeRequisition->canBeApproved()) {
            return response()->json(['error' => 'Only pending requisitions can be rejected'], 400);
        }

        $this->storeRequisitionService->rejectRequisition($storeRequisition, $request->validated()['rejection_reason']);

        // Dispatch event for notifications
        StoreRequisitionRejected::dispatch($storeRequisition, Auth::user(), $request->validated()['rejection_reason']);

        return redirect()->back()->with('success', 'Store requisition rejected successfully');
    }

    public function issue(IssueStoreRequisitionRequest $request, StoreRequisition $storeRequisition)
    {
        if (!$storeRequisition->canBeIssued() && $storeRequisition->status !== StoreRequisition::STATUS_PARTIALLY_ISSUED) {
            return redirect()->back()->with('error', 'Only approved or partially issued requisitions can be issued');
        }

        try {
            $this->issueService->issueItems(
                $storeRequisition, 
                $request->validated()['items'], 
                $request->validated()['issue_notes'] ?? null
            );

            // Dispatch event for notifications
            StoreRequisitionIssued::dispatch($storeRequisition, Auth::user(), $storeRequisition->status !== StoreRequisition::STATUS_ISSUED);

            $message = $this->issueService->getIssueSuccessMessage($storeRequisition, $request->validated()['items']);
            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Failed to issue store requisition items: ' . $e->getMessage());
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function issueIndex()
    {
        if (!Auth::user()->can('issue-store-items') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to view issue management');
        }

        return Inertia::render('StoreRequisitions/IssueManagement', [
            'approved_requisitions' => $this->queryService->getApprovedRequisitions(),
            'partially_issued_requisitions' => $this->queryService->getPartiallyIssuedRequisitions(),
            'issued_requisitions' => $this->queryService->getIssuedRequisitions(),
            'user' => [
                'id' => Auth::id(),
                'name' => Auth::user()->first_name . ' ' . Auth::user()->last_name,
                'permissions' => Auth::user()->getAllPermissions()->pluck('name')->toArray(),
            ],
        ]);
    }

    public function validateStock(Request $request)
    {
        if (!Auth::user()->can('issue-store-items') && !Auth::user()->can('store-keep')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'requisition_id' => 'required|exists:store_requisitions,id',
            'items' => 'required|array|min:1',
            'items.*.id' => 'required|exists:store_requisition_items,id',
            'items.*.quantity_issued' => 'required|numeric|min:0',
        ]);

        $storeRequisition = StoreRequisition::findOrFail($validated['requisition_id']);
        $result = $this->issueService->validateStockForApi($storeRequisition, $validated['items']);

        return response()->json($result);
    }

    public function issueStats()
    {
        if (!Auth::user()->can('issue-store-items') && !Auth::user()->can('store-keep')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($this->queryService->getIssueStats());
    }

    public function pickingList(StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('issue-store-items') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to generate picking lists');
        }

        if (!$storeRequisition->canBeIssued()) {
            return response()->json(['error' => 'Only approved requisitions can have picking lists generated'], 400);
        }

        return response()->json($this->reportService->generatePickingList($storeRequisition));
    }

    public function goodsIssueNote(StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('issue-store-items') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to generate goods issue notes');
        }

        if (!in_array($storeRequisition->status, [StoreRequisition::STATUS_ISSUED, StoreRequisition::STATUS_PARTIALLY_ISSUED])) {
            return response()->json(['error' => 'Only issued requisitions can have goods issue notes generated'], 400);
        }

        return response()->json($this->reportService->generateGoodsIssueNote($storeRequisition));
    }

    public function pendingItems()
    {
        if (!Auth::user()->can('view-store-requisitions') && !Auth::user()->can('store-keep')) {
            abort(403, 'Unauthorized to view pending items');
        }

        return response()->json($this->queryService->getPendingItems());
    }

    public function returnForRevision(Request $request, StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('returnForRevision', $storeRequisition)) {
            abort(403, 'Unauthorized to return this store requisition for revision');
        }

        $validated = $request->validate([
            'comments' => 'required|string|max:1000',
        ]);

        if (!$storeRequisition->canBeReturnedForRevision()) {
            return response()->json(['error' => 'Only pending requisitions can be returned for revision'], 400);
        }

        $this->storeRequisitionService->returnForRevision($storeRequisition, $validated['comments']);

        // Dispatch event for notifications
        StoreRequisitionReturnedForRevision::dispatch($storeRequisition, Auth::user(), $validated['comments']);

        return response()->json([
            'message' => 'Store requisition returned for revision',
            'store_requisition' => $storeRequisition
        ]);
    }

    public function editRejected(StoreRequisition $storeRequisition)
    {
        if (!Auth::user()->can('update', $storeRequisition)) {
            abort(403, 'Unauthorized to edit this store requisition');
        }

        if (!$storeRequisition->canBeEdited()) {
            abort(403, 'This store requisition cannot be edited in its current status');
        }

        $user = Auth::user();
        $storeRequisition->load([
            'items.inventoryItem',
            'department',
            'branch',
            'requester',
            'histories.user'
        ]);

        $latestHistory = $storeRequisition->histories()
            ->whereIn('action', ['rejected', 'returned_for_revision'])
            ->first();

        return Inertia::render('StoreRequisitions/EditRejected', [
            'storeRequisition' => $storeRequisition,
            'rejectionComments' => $latestHistory?->comments,
            'inventoryItems' => $this->helperService->getOrganizationInventoryItems(),
            'userDepartments' => $this->helperService->getUserDepartments(),
            'userBranches' => $user->organizations->first()->branches ?? collect(),
        ]);
    }

    public function updateRejected(UpdateRejectedStoreRequisitionRequest $request, StoreRequisition $storeRequisition)
    {
        if (!$storeRequisition->canBeEdited()) {
            return response()->json(['error' => 'This store requisition cannot be edited in its current status'], 400);
        }

        try {
            DB::beginTransaction();

            $originalData = [
                'purpose' => $storeRequisition->purpose,
                'branch_id' => $storeRequisition->branch_id,
                'department_id' => $storeRequisition->department_id,
                'items' => $storeRequisition->items->map(function ($item) {
                    return [
                        'inventory_item_id' => $item->inventory_item_id,
                        'quantity_requested' => $item->quantity_requested,
                    ];
                })->toArray(),
            ];

            $validated = $request->validated();

            $storeRequisition->update([
                'purpose' => $validated['purpose'],
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
                'rejection_reason' => null,
                'approver_user_id' => null,
                'requested_at' => now(),
            ]);

            $storeRequisition->items()->delete();

            foreach ($validated['items'] as $itemData) {
                StoreRequisitionItem::create([
                    'store_requisition_id' => $storeRequisition->id,
                    'inventory_item_id' => $itemData['inventory_item_id'],
                    'quantity_requested' => $itemData['quantity_requested'],
                ]);
            }

            $changes = [
                'original' => $originalData,
                'updated' => [
                    'purpose' => $validated['purpose'],
                    'branch_id' => $validated['branch_id'],
                    'department_id' => $validated['department_id'],
                    'items' => $validated['items'],
                ],
            ];

            \App\Models\StoreRequisitionHistory::createRecord(
                $storeRequisition->id,
                Auth::id(),
                'edited_and_resubmitted',
                $validated['comments'] . ' (Store requisition was edited and resubmitted for approval)',
                $changes
            );

            DB::commit();

            return redirect()->route('store-requisitions.show', $storeRequisition)
                ->with('success', 'Store requisition updated and resubmitted successfully');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update rejected store requisition: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update store requisition. Please try again.'], 500);
        }
    }

    private function determineDepartment($userDepartments, $requestedDepartmentId)
    {
        if ($requestedDepartmentId && $userDepartments->contains('id', $requestedDepartmentId)) {
            return $requestedDepartmentId;
        }
        return $userDepartments->first()->id;
    }
}