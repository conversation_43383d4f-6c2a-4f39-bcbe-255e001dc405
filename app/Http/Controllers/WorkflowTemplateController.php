<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Src\WorkflowTemplate\Application\Services\WorkflowTemplateService;
use App\Models\Department;

class WorkflowTemplateController extends Controller
{
    public function __construct(
        private WorkflowTemplateService $templateService
    ) {}

    /**
     * Display template selection page.
     */
    public function index(Request $request): Response
    {
        $organizationId = $request->query('organization_id');

        // Get templates grouped by category
        $templatesByCategory = $this->templateService->getTemplatesByCategory();

        return Inertia::render('workflow-templates/workflow-templates-select', [
            'templatesByCategory' => $templatesByCategory,
            'selectedOrganizationId' => $organizationId,
        ]);
    }

    /**
     * Preview a template with context.
     */
    public function preview(Request $request, int $templateId)
    {
        $context = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'custom_name' => 'nullable|string|max:255',
        ]);

        try {
            $template = $this->templateService->getTemplate($templateId);
            if (!$template) {
                abort(404, 'Template not found');
            }

            // Check if template requires department context
            $requiresDepartment = $this->templateRequiresDepartment($template);

            // If template requires department but none provided, redirect to department selection
            if ($requiresDepartment && empty($context['department_id'])) {
                return $this->showDepartmentSelection($templateId, $context);
            }

            $previewData = $this->templateService->previewWorkflowFromTemplate($templateId, $context);
            $validation = $this->templateService->validateTemplateData($templateId, $context);

            // Get departments for the organization to help with context
            $departments = Department::where('organization_id', $context['organization_id'])->get();

            return Inertia::render('workflow-templates/workflow-template-preview', [
                'template' => $template->toArray(),
                'previewData' => $previewData,
                'validation' => $validation,
                'context' => $context,
                'departments' => $departments,
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Create workflow from template.
     */
    public function createFromTemplate(Request $request, int $templateId): RedirectResponse
    {
        $context = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'custom_name' => 'nullable|string|max:255',
            'is_default' => 'boolean',
        ]);

        try {
            $result = $this->templateService->createWorkflowFromTemplate($templateId, $context);

            return redirect()->route('approval-workflows.show', $result['workflow']->id)
                ->with('success', 'Workflow created successfully from template.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get template data for AJAX requests.
     */
    public function show(int $templateId)
    {
        $template = $this->templateService->getTemplate($templateId);

        if (!$template) {
            abort(404, 'Template not found');
        }

        return response()->json($template->toArray());
    }

    /**
     * Get categories for AJAX requests.
     */
    public function categories()
    {
        return response()->json($this->templateService->getCategories());
    }

    /**
     * Check if template requires department context.
     */
    private function templateRequiresDepartment($template): bool
    {
        $templateData = $template->toArray();

        if (!isset($templateData['template_data']['steps'])) {
            return false;
        }

        // Check if any step requires department-specific roles
        foreach ($templateData['template_data']['steps'] as $step) {
            $description = $step['description'] ?? '';
            if (strpos($description, 'Department Head') !== false ||
                strpos($description, 'HOD') !== false ||
                strpos($description, 'Direct Supervisor') !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Show department selection page.
     */
    private function showDepartmentSelection(int $templateId, array $context)
    {
        $template = $this->templateService->getTemplate($templateId);
        $departments = Department::where('organization_id', $context['organization_id'])->get();

        return Inertia::render('workflow-templates/workflow-template-department-select', [
            'template' => $template->toArray(),
            'departments' => $departments,
            'context' => $context,
        ]);
    }
}
