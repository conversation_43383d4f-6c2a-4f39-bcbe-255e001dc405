<?php

namespace App\Http\Controllers;

use App\Models\ChartOfAccount;
use App\Models\Organization;
use App\Models\Branch;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;
use Src\ChartOfAccounts\Application\Services\ChartOfAccountService;

class ChartOfAccountController extends Controller
{
    /**
     * @var ChartOfAccountService
     */
    protected $chartOfAccountService;

    /**
     * ChartOfAccountController constructor.
     *
     * @param ChartOfAccountService $chartOfAccountService
     */
    public function __construct(ChartOfAccountService $chartOfAccountService)
    {
        $this->chartOfAccountService = $chartOfAccountService;
    }

    /**
     * Display a listing of the chart of accounts.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        $filters = $request->only(['search', 'account_type', 'is_active', 'sort', 'direction']);

        // If platform admin, show all chart of accounts
        if ($user->is_platform_admin) {
            $chartOfAccounts = $this->chartOfAccountService->getAllChartOfAccounts($filters);

            return Inertia::render('chart-of-accounts/index', [
                'chartOfAccounts' => $chartOfAccounts,
                'filters' => $filters,
                'isPlatformAdmin' => true,
            ]);
        }

        // Check if user has permission to view chart of accounts (permission-based only)
        if (!$user->hasAnyPermission([
            'view-chart-of-accounts', 
            'manage-chart-of-accounts',
            'view-finances', 
            'manage-finances'
        ])) {
            abort(403, 'You are not authorized to view chart of accounts.');
        }

        // Get user's organization ID
        $organizationId = null;

        // Get organization ID from request or user's roles (permission-based approach)
        if ($request->has('organization_id')) {
            $requestedOrgId = $request->get('organization_id');
            
            // Check if user has any role associated with the requested organization
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();
            if (in_array($requestedOrgId, $userOrgIds)) {
                $organizationId = $requestedOrgId;
            }
        }

        // If no organization found from request, get from user's roles
        if (!$organizationId) {
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();
            if (!empty($userOrgIds)) {
                $organizationId = $userOrgIds[0]; // Use first organization
            }
        }

        if (!$organizationId) {
            abort(403, 'You are not associated with any organization.');
        }

        $chartOfAccounts = $this->chartOfAccountService->getChartOfAccountsByOrganizationId($organizationId, $filters);
        $organization = Organization::find($organizationId);

        return Inertia::render('chart-of-accounts/index', [
            'chartOfAccounts' => $chartOfAccounts,
            'organization' => $organization,
            'filters' => $filters,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Show the form for creating a new chart of account.
     */
    public function create(Request $request): Response
    {
        $user = $request->user();

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();

            // For platform admin, we need to ensure standard account types exist for each organization
            // Get all organizations
            $orgIds = Organization::pluck('id')->toArray();

            // Ensure standard account types exist for each organization
            foreach ($orgIds as $orgId) {
                $this->ensureStandardAccountTypes(collect(), $orgId);
            }

            // Get parent accounts
            $parentAccounts = ChartOfAccount::whereNull('parent_id')->get();

            return Inertia::render('chart-of-accounts/create', [
                'organizations' => $organizations,
                'branches' => $branches,
                'parentAccounts' => $parentAccounts,
                'isPlatformAdmin' => true,
            ]);
        }

        // Check if user has permission to create chart of accounts (permission-based only)
        if (!$user->hasAnyPermission([
            'create-chart-of-accounts', 
            'manage-chart-of-accounts',
            'manage-finances'
        ])) {
            abort(403, 'You are not authorized to create chart of accounts.');
        }

        // Get user's organization ID from their roles
        $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();
        $organizationId = !empty($userOrgIds) ? $userOrgIds[0] : null;

        if (!$organizationId) {
            abort(403, 'You are not associated with any organization.');
        }

        $organization = Organization::find($organizationId);
        $branches = Branch::where('organization_id', $organizationId)->get();

        // Get parent accounts for this organization
        $parentAccounts = $this->chartOfAccountService->getParentAccountsByOrganizationId($organizationId);

        // Ensure we have the standard account types as parent accounts for this organization
        $this->ensureStandardAccountTypes($parentAccounts, $organizationId);

        return Inertia::render('chart-of-accounts/create', [
            'organization' => $organization,
            'branches' => $branches,
            'parentAccounts' => $parentAccounts,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Store a newly created chart of account in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Validate the request
        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            // Code is now optional and will be generated automatically
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'spending_limit' => 'nullable|numeric|min:0',
            'limit_period' => 'nullable|in:daily,weekly,monthly,quarterly,annually',
            'is_active' => 'boolean',
            'parent_id' => 'nullable',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
        ]);

        // Check permissions and get organization ID for non-platform admins
        if (!$user->is_platform_admin) {
            // Check if user has permission to create chart of accounts
            if (!$user->hasAnyPermission([
                'create-chart-of-accounts', 
                'manage-chart-of-accounts',
                'manage-finances'
            ])) {
                abort(403, 'You are not authorized to create chart of accounts.');
            }

            // Get user's organization ID from their roles
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();
            $organizationId = !empty($userOrgIds) ? $userOrgIds[0] : null;

            if (!$organizationId) {
                abort(403, 'You are not associated with any organization.');
            }

            if ($validated['organization_id'] != $organizationId) {
                abort(403, 'You are not authorized to create chart of accounts for this organization.');
            }

            // If branch is specified, ensure it belongs to the organization
            if (!empty($validated['branch_id']) && $validated['branch_id'] !== 'none') {
                $branch = Branch::find($validated['branch_id']);
                if ($branch && $branch->organization_id != $organizationId) {
                    abort(403, 'The selected branch does not belong to your organization.');
                }
            }

            // If parent is specified, ensure it belongs to the organization
            if (!empty($validated['parent_id']) && !is_string($validated['parent_id'])) {
                $parent = ChartOfAccount::find($validated['parent_id']);
                if ($parent && $parent->organization_id != $organizationId) {
                    abort(403, 'The selected parent account does not belong to your organization.');
                }
            }
        }

        // We no longer need to handle type_ prefixed parent_ids
        // as we're now creating standard account types directly in the database

        // Convert empty strings and "none" values to NULL
        if (empty($validated['parent_id']) || $validated['parent_id'] === 'none') {
            $validated['parent_id'] = null;
        }

        if (empty($validated['branch_id']) || $validated['branch_id'] === 'none') {
            $validated['branch_id'] = null;
        }

        if (empty($validated['limit_period']) || $validated['limit_period'] === 'none') {
            $validated['limit_period'] = null;
        }

        // Create the chart of account
        try {
            $chartOfAccount = $this->chartOfAccountService->createChartOfAccount($validated);
            return redirect()->route('chart-of-accounts.index')
                ->with('success', 'Chart of account created successfully.');
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle constraint violations gracefully
            if (str_contains($e->getMessage(), 'UNIQUE constraint failed')) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['code' => 'A chart of account with this code already exists for this organization. Please try again or use a different code.']);
            }

            // Re-throw other database exceptions
            throw $e;
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => 'An error occurred while creating the chart of account: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified chart of account.
     */
    public function show(int $id): Response
    {
        $chartOfAccount = $this->chartOfAccountService->getChartOfAccountById($id);

        if (!$chartOfAccount) {
            abort(404, 'Chart of account not found.');
        }

        $user = Auth::user();

        // Check permissions for non-platform admins
        if (!$user->is_platform_admin) {
            // Check if user has permission to view chart of accounts
            if (!$user->hasAnyPermission([
                'view-chart-of-accounts', 
                'manage-chart-of-accounts',
                'view-finances', 
                'manage-finances'
            ])) {
                abort(403, 'You are not authorized to view chart of accounts.');
            }

            // Get user's organization IDs from their roles
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();

            if (empty($userOrgIds) || !in_array($chartOfAccount->organization_id, $userOrgIds)) {
                abort(403, 'You are not authorized to view this chart of account.');
            }
        }

        return Inertia::render('chart-of-accounts/show', [
            'chartOfAccount' => $chartOfAccount,
        ]);
    }

    /**
     * Show the form for editing the specified chart of account.
     */
    public function edit(Request $request, int $id): Response
    {
        $chartOfAccount = $this->chartOfAccountService->getChartOfAccountById($id);

        if (!$chartOfAccount) {
            abort(404, 'Chart of account not found.');
        }

        $user = $request->user();

        // Check permissions for non-platform admins
        if (!$user->is_platform_admin) {
            // Check if user has permission to edit chart of accounts
            if (!$user->hasAnyPermission([
                'edit-chart-of-accounts', 
                'manage-chart-of-accounts',
                'manage-finances'
            ])) {
                abort(403, 'You are not authorized to edit chart of accounts.');
            }

            // Get user's organization IDs from their roles
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();

            if (empty($userOrgIds) || !in_array($chartOfAccount->organization_id, $userOrgIds)) {
                abort(403, 'You are not authorized to edit this chart of account.');
            }
        }

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();

            // For platform admin, we need to ensure standard account types exist for each organization
            // Get all organizations
            $orgIds = Organization::pluck('id')->toArray();

            // Ensure standard account types exist for each organization
            foreach ($orgIds as $orgId) {
                $this->ensureStandardAccountTypes(collect(), $orgId);
            }

            // Get parent accounts
            $parentAccounts = ChartOfAccount::where('id', '!=', $id)
                ->where('id', '!=', $id)
                ->get();

            return Inertia::render('chart-of-accounts/edit', [
                'chartOfAccount' => $chartOfAccount,
                'organizations' => $organizations,
                'branches' => $branches,
                'parentAccounts' => $parentAccounts,
                'isPlatformAdmin' => true,
            ]);
        }

        // Non-platform admin - get organization ID from user's roles
        $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();
        $organizationId = !empty($userOrgIds) ? $userOrgIds[0] : null;

        $organization = Organization::find($organizationId);
        $branches = Branch::where('organization_id', $organizationId)->get();
        $parentAccounts = ChartOfAccount::where('organization_id', $organizationId)
            ->where('id', '!=', $id)
            ->get();

        // Ensure we have the standard account types as parent accounts for this organization
        $this->ensureStandardAccountTypes($parentAccounts, $organizationId);

        return Inertia::render('chart-of-accounts/edit', [
            'chartOfAccount' => $chartOfAccount,
            'organization' => $organization,
            'branches' => $branches,
            'parentAccounts' => $parentAccounts,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Update the specified chart of account in storage.
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        $chartOfAccount = $this->chartOfAccountService->getChartOfAccountById($id);

        if (!$chartOfAccount) {
            abort(404, 'Chart of account not found.');
        }

        $user = $request->user();

        // Check permissions for non-platform admins
        if (!$user->is_platform_admin) {
            // Check if user has permission to update chart of accounts
            if (!$user->hasAnyPermission([
                'edit-chart-of-accounts', 
                'manage-chart-of-accounts',
                'manage-finances'
            ])) {
                abort(403, 'You are not authorized to update chart of accounts.');
            }

            // Get user's organization IDs from their roles
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();

            if (empty($userOrgIds) || !in_array($chartOfAccount->organization_id, $userOrgIds)) {
                abort(403, 'You are not authorized to update this chart of account.');
            }
        }

        // Validate the request
        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            // Code is now optional and will be generated automatically
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'spending_limit' => 'nullable|numeric|min:0',
            'limit_period' => 'nullable|in:daily,weekly,monthly,quarterly,annually',
            'is_active' => 'boolean',
            'parent_id' => 'nullable',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
        ]);

        // Ensure parent_id is not the same as the chart of account's ID
        if (!empty($validated['parent_id']) && $validated['parent_id'] == $id) {
            return redirect()->back()->withErrors(['parent_id' => 'A chart of account cannot be its own parent.']);
        }

        // We no longer need to handle type_ prefixed parent_ids
        // as we're now creating standard account types directly in the database

        // Convert empty strings and "none" values to NULL
        if (empty($validated['parent_id']) || $validated['parent_id'] === 'none') {
            $validated['parent_id'] = null;
        }

        if (empty($validated['branch_id']) || $validated['branch_id'] === 'none') {
            $validated['branch_id'] = null;
        }

        if (empty($validated['limit_period']) || $validated['limit_period'] === 'none') {
            $validated['limit_period'] = null;
        }

        // Update the chart of account
        try {
            $this->chartOfAccountService->updateChartOfAccount($id, $validated);
            return redirect()->route('chart-of-accounts.index')
                ->with('success', 'Chart of account updated successfully.');
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle constraint violations gracefully
            if (str_contains($e->getMessage(), 'UNIQUE constraint failed')) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['code' => 'A chart of account with this code already exists for this organization. Please try again or use a different code.']);
            }

            // Re-throw other database exceptions
            throw $e;
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => 'An error occurred while updating the chart of account: ' . $e->getMessage()]);
        }
    }

    /**
     * Ensure that standard account types exist as parent accounts
     *
     * @param \Illuminate\Database\Eloquent\Collection $parentAccounts
     * @param int|null $organizationId
     */
    private function ensureStandardAccountTypes($parentAccounts, $organizationId = null)
    {
        // If organization ID is provided, use the Organization model's method to ensure accounts exist
        if ($organizationId) {
            $organization = Organization::find($organizationId);
            if ($organization) {
                // Use the centralized method from Organization model
                $organization->setupChartOfAccounts();

                // Refresh the parent accounts collection to include any newly created accounts
                $newAccounts = ChartOfAccount::where('organization_id', $organizationId)
                    ->whereNotNull('parent_id') // These are organization-level accounts with platform parents
                    ->get();

                // Add any new accounts to the collection that aren't already there
                foreach ($newAccounts as $account) {
                    if (!$parentAccounts->contains('id', $account->id)) {
                        $parentAccounts->push($account);
                    }
                }
            }
        }

        return $parentAccounts;
    }

    /**
     * Remove the specified chart of account from storage.
     */
    public function destroy(int $id): RedirectResponse
    {
        $chartOfAccount = $this->chartOfAccountService->getChartOfAccountById($id);

        if (!$chartOfAccount) {
            abort(404, 'Chart of account not found.');
        }

        $user = Auth::user();

        // Check permissions for non-platform admins
        if (!$user->is_platform_admin) {
            // Check if user has permission to delete chart of accounts
            if (!$user->hasAnyPermission([
                'delete-chart-of-accounts', 
                'manage-chart-of-accounts',
                'manage-finances'
            ])) {
                abort(403, 'You are not authorized to delete chart of accounts.');
            }

            // Get user's organization IDs from their roles
            $userOrgIds = $user->roles()->whereNotNull('organization_id')->pluck('organization_id')->toArray();

            if (empty($userOrgIds) || !in_array($chartOfAccount->organization_id, $userOrgIds)) {
                abort(403, 'You are not authorized to delete this chart of account.');
            }
        }

        // Check if the chart of account has children
        if ($chartOfAccount->children()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete chart of account with sub-categories. Please delete the sub-categories first.');
        }

        // Check if the chart of account is used in requisition items
        if ($chartOfAccount->requisitionItems()->exists()) {
            return redirect()->back()->with('error', 'Cannot delete chart of account that is used in requisitions.');
        }

        // Delete the chart of account
        $this->chartOfAccountService->deleteChartOfAccount($id);

        return redirect()->route('chart-of-accounts.index')
            ->with('success', 'Chart of account deleted successfully.');
    }
}
