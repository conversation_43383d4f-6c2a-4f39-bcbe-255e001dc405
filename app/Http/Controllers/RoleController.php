<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class RoleController extends Controller
{
    /**
     * Get the category for a permission based on its name.
     */
    private function getPermissionCategory(string $permissionName): string
    {
        if (strpos($permissionName, 'organization') !== false) {
            return 'Organization Management';
        } elseif (strpos($permissionName, 'branch') !== false) {
            return 'Branch Management';
        } elseif (strpos($permissionName, 'department') !== false) {
            return 'Department Management';
        } elseif (strpos($permissionName, 'user') !== false) {
            return 'User Management';
        } elseif (strpos($permissionName, 'role') !== false) {
            return 'Role Management';
        } elseif (strpos($permissionName, 'requisition') !== false) {
            return 'Requisition Management';
        } elseif (strpos($permissionName, 'float') !== false) {
            return 'Float Management';
        } elseif (strpos($permissionName, 'expense-categor') !== false) {
            return 'Expense Category Management';
        } elseif (strpos($permissionName, 'report') !== false || strpos($permissionName, 'audit') !== false) {
            return 'Reporting & Audit';
        } elseif (strpos($permissionName, 'reconciliation') !== false) {
            return 'Reconciliation Management';
        } else {
            return 'Other Permissions';
        }
    }
    /**
     * Display a listing of the roles.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // If platform admin, show all roles
        if ($user->is_platform_admin) {
            $roles = Role::with(['permissions', 'organization', 'branch', 'department'])->get();

            return Inertia::render('roles/roles-index', [
                'roles' => $roles,
                'isPlatformAdmin' => true,
            ]);
        }

        // Check if user has permission to view roles (supports both hardcoded roles and custom permissions)
        if (!$this->hasRolePermission($user, ['view-roles', 'manage-roles'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to view roles.',
                'user' => $user,
            ]);
        }

        // Get organization from user's roles
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account.',
                'user' => $user,
            ]);
        }

        // Only show roles that belong to this organization
        $roles = Role::where('organization_id', $organization->id)
            ->with(['permissions', 'organization', 'branch', 'department'])
            ->get();

        return Inertia::render('roles/roles-index', [
            'roles' => $roles,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Show the form for creating a new role.
     */
    public function create(Request $request): Response
    {
        $user = $request->user();

        // Check if user has permission to create roles
        if (!$this->hasRolePermission($user, ['manage-roles', 'create-roles'])) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have permission to create roles.',
                'user' => $user,
            ]);
        }

        $permissions = Permission::select('id', 'name', 'description')->get();

        // Group permissions by category
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $category = $this->getPermissionCategory($permission->name);
            if (!isset($groupedPermissions[$category])) {
                $groupedPermissions[$category] = [];
            }
            $groupedPermissions[$category][] = $permission;
        }

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();
            $departments = Department::all();

            return Inertia::render('roles/roles-create', [
                'permissions' => $permissions,
                'groupedPermissions' => $groupedPermissions,
                'organizations' => $organizations,
                'branches' => $branches,
                'departments' => $departments,
                'isPlatformAdmin' => true,
            ]);
        }

        // Get organization from user's roles
        $organization = $this->getUserOrganization($user);

        if (!$organization) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account.',
                'user' => $user,
            ]);
        }

        $branches = Branch::where('organization_id', $organization->id)->get();
        $departments = Department::where('organization_id', $organization->id)->get();

        return Inertia::render('roles/roles-create', [
            'permissions' => $permissions,
            'groupedPermissions' => $groupedPermissions,
            'organization' => $organization,
            'branches' => $branches,
            'departments' => $departments,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'organization_id' => 'nullable|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'permission_ids' => 'required|array',
            'permission_ids.*' => 'exists:permissions,id',
            'is_active' => 'boolean',
        ]);

        // Convert '0' to null for organization_id, branch_id, and department_id
        if (isset($validated['organization_id']) && $validated['organization_id'] === '0') {
            $validated['organization_id'] = null;
        }
        if (isset($validated['branch_id']) && $validated['branch_id'] === '0') {
            $validated['branch_id'] = null;
        }
        if (isset($validated['department_id']) && $validated['department_id'] === '0') {
            $validated['department_id'] = null;
        }

        // If organization admin, ensure the role is associated with their organization
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();

            if (!$organizationAdminRole) {
                return redirect()->back()->withErrors(['error' => 'Organization Admin role not found for this user.']);
            }

            $organizationId = $organizationAdminRole->organization_id;

            $validated['organization_id'] = $organizationId;

            // Verify branch belongs to organization
            if (isset($validated['branch_id'])) {
                $branch = Branch::find($validated['branch_id']);
                if (!$branch || $branch->organization_id !== $organizationId) {
                    return redirect()->back()->withErrors(['branch_id' => 'Invalid branch selected.']);
                }
            }

            // Verify department belongs to organization
            if (isset($validated['department_id'])) {
                $department = Department::find($validated['department_id']);
                if (!$department || $department->organization_id !== $organizationId) {
                    return redirect()->back()->withErrors(['department_id' => 'Invalid department selected.']);
                }
            }
        }

        // Check if this is an Organization Admin role for an organization that already has one
        if ($validated['name'] === 'Organization Admin' && $validated['organization_id']) {
            $existingRole = Role::where('name', 'Organization Admin')
                ->where('organization_id', $validated['organization_id'])
                ->first();

            if ($existingRole) {
                // Update the existing role instead of creating a new one
                $existingRole->update([
                    'description' => $validated['description'],
                    'branch_id' => $validated['branch_id'],
                    'department_id' => $validated['department_id'],
                    'is_active' => $validated['is_active'] ?? true,
                ]);

                $role = $existingRole;
            } else {
                // Create the role
                $role = Role::create([
                    'name' => $validated['name'],
                    'guard_name' => 'web',
                    'organization_id' => $validated['organization_id'],
                    'branch_id' => $validated['branch_id'],
                    'department_id' => $validated['department_id'],
                    'description' => $validated['description'],
                    'is_active' => $validated['is_active'] ?? true,
                ]);
            }
        } else {
            // Create the role
            $role = Role::create([
                'name' => $validated['name'],
                'guard_name' => 'web',
                'organization_id' => $validated['organization_id'],
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'description' => $validated['description'],
                'is_active' => $validated['is_active'] ?? true,
            ]);
        }

        // Assign permissions
        $permissions = Permission::whereIn('id', $validated['permission_ids'])->get();
        $role->syncPermissions($permissions);

        return redirect()->route('roles.index')
            ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified role.
     */
    public function show(Request $request, Role $role): Response
    {
        // Check if the current user is an organization admin and can access this role
        $user = $request->user();
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();

            if (!$organizationAdminRole) {
                abort(403, 'You do not have the Organization Admin role assigned.');
            }

            $organizationId = $organizationAdminRole->organization_id;

            // Check if the role belongs to the same organization
            if ($role->organization_id !== $organizationId) {
                abort(403, 'You do not have permission to view this role.');
            }
        }
        $role->load([
            'permissions' => function($query) {
                $query->select('permissions.id', 'permissions.name');
            },
            'organization',
            'branch',
            'department'
        ]);

        return Inertia::render('roles/roles-show', [
            'role' => $role,
        ]);
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(Request $request, Role $role): Response
    {
        $user = $request->user();
        $permissions = Permission::select('id', 'name', 'description')->get();

        // Group permissions by category
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $category = $this->getPermissionCategory($permission->name);
            if (!isset($groupedPermissions[$category])) {
                $groupedPermissions[$category] = [];
            }
            $groupedPermissions[$category][] = $permission;
        }

        $role->load(['permissions' => function($query) {
            $query->select('permissions.id', 'permissions.name');
        }]);

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();
            $departments = Department::all();

            return Inertia::render('roles/roles-edit', [
                'role' => $role,
                'permissions' => $permissions,
                'groupedPermissions' => $groupedPermissions,
                'organizations' => $organizations,
                'branches' => $branches,
                'departments' => $departments,
                'isPlatformAdmin' => true,
            ]);
        }

        // Organization admin
        $organizationAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        if (!$organizationAdminRole) {
            // Handle the case where the user doesn't have the Organization Admin role
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have the Organization Admin role assigned.',
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'email' => $user->email,
                ],
            ]);
        }

        $organizationId = $organizationAdminRole->organization_id;

        // Check if the role belongs to the organization
        if ($role->organization_id && $role->organization_id !== $organizationId) {
            abort(403, 'Unauthorized action.');
        }

        $organization = Organization::find($organizationId);
        $branches = Branch::where('organization_id', $organizationId)->get();
        $departments = Department::where('organization_id', $organizationId)->get();

        return Inertia::render('roles/roles-edit', [
            'role' => $role,
            'permissions' => $permissions,
            'groupedPermissions' => $groupedPermissions,
            'organization' => $organization,
            'branches' => $branches,
            'departments' => $departments,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, Role $role): RedirectResponse
    {
        $user = $request->user();

        // Check if the user can edit this role
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();

            if (!$organizationAdminRole) {
                abort(403, 'You do not have the Organization Admin role assigned.');
            }

            $organizationId = $organizationAdminRole->organization_id;

            // If the role is not associated with the user's organization
            if ($role->organization_id !== $organizationId) {
                abort(403, 'You do not have permission to update this role.');
            }
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'organization_id' => 'nullable|exists:organizations,id',
            'branch_id' => 'nullable|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'permission_ids' => 'required|array',
            'permission_ids.*' => 'exists:permissions,id',
            'is_active' => 'boolean',
        ]);

        // Convert '0' to null for organization_id, branch_id, and department_id
        if (isset($validated['organization_id']) && $validated['organization_id'] === '0') {
            $validated['organization_id'] = null;
        }
        if (isset($validated['branch_id']) && $validated['branch_id'] === '0') {
            $validated['branch_id'] = null;
        }
        if (isset($validated['department_id']) && $validated['department_id'] === '0') {
            $validated['department_id'] = null;
        }

        // If organization admin, ensure the role stays associated with their organization
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();

            if (!$organizationAdminRole) {
                abort(403, 'You do not have the Organization Admin role assigned.');
            }

            $organizationId = $organizationAdminRole->organization_id;

            $validated['organization_id'] = $organizationId;

            // Verify branch belongs to organization
            if (isset($validated['branch_id'])) {
                $branch = Branch::find($validated['branch_id']);
                if (!$branch || $branch->organization_id !== $organizationId) {
                    return redirect()->back()->withErrors(['branch_id' => 'Invalid branch selected.']);
                }
            }

            // Verify department belongs to organization
            if (isset($validated['department_id'])) {
                $department = Department::find($validated['department_id']);
                if (!$department || $department->organization_id !== $organizationId) {
                    return redirect()->back()->withErrors(['department_id' => 'Invalid department selected.']);
                }
            }
        }

        // Special handling for Organization Admin role
        if ($role->name === 'Organization Admin' && $validated['name'] === 'Organization Admin') {
            // If organization_id is changing, we need to check if there's already an org admin role for the new org
            if ($role->organization_id != $validated['organization_id'] && $validated['organization_id']) {
                $existingRole = Role::where('name', 'Organization Admin')
                    ->where('organization_id', $validated['organization_id'])
                    ->where('id', '!=', $role->id)
                    ->first();

                if ($existingRole) {
                    return redirect()->back()->withErrors(['organization_id' => 'An Organization Admin role already exists for this organization.']);
                }
            }

            // Update the role
            $role->update([
                'name' => $validated['name'],
                'organization_id' => $validated['organization_id'],
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'description' => $validated['description'],
                'is_active' => $validated['is_active'] ?? true,
            ]);
        } else {
            // For other roles, just update normally
            $role->update([
                'name' => $validated['name'],
                'organization_id' => $validated['organization_id'],
                'branch_id' => $validated['branch_id'],
                'department_id' => $validated['department_id'],
                'description' => $validated['description'],
                'is_active' => $validated['is_active'] ?? true,
            ]);
        }

        // Update permissions
        $permissions = Permission::whereIn('id', $validated['permission_ids'])->get();
        $role->syncPermissions($permissions);

        return redirect()->route('roles.index')
            ->with('success', 'Role updated successfully.');
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy(Request $request, Role $role): RedirectResponse
    {
        $user = $request->user();

        // Check if the user can delete this role
        if (!$user->is_platform_admin && $user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();

            if (!$organizationAdminRole) {
                abort(403, 'You do not have the Organization Admin role assigned.');
            }

            $organizationId = $organizationAdminRole->organization_id;

            // If the role is not associated with the user's organization
            if ($role->organization_id !== $organizationId) {
                abort(403, 'You do not have permission to delete this role.');
            }
        }

        // Check if the role is in use
        $usersWithRole = $role->users()->count();
        if ($usersWithRole > 0) {
            return redirect()->route('roles.index')
                ->with('error', 'Cannot delete role because it is assigned to ' . $usersWithRole . ' user(s).');
        }

        $role->delete();

        return redirect()->route('roles.index')
            ->with('success', 'Role deleted successfully.');
    }

    /**
     * Check if user has any of the specified role permissions.
      */
    private function hasRolePermission(User $user, array $permissions): bool
    {
        // First check hardcoded roles for backward compatibility
        if ($user->hasRole('Organization Admin') ||
            $user->is_platform_admin) {
            return true;
        }

        // Then check for specific permissions (for custom roles)
        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        // Also check for comprehensive management permissions
        $managementPermissions = [
            'manage-users',
            'manage-departments',
            'manage-organizations'
        ];

        foreach ($managementPermissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get user's organization based on their roles.
      */
    private function getUserOrganization(User $user): ?Organization
    {
        // First try Organization Admin role (backward compatibility)
        $orgAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        if ($orgAdminRole && $orgAdminRole->organization_id) {
            return Organization::find($orgAdminRole->organization_id);
        }

        // Then try to get organization from any role with role management permissions (for custom roles)
        $rolesWithOrganization = $user->roles()
            ->whereNotNull('organization_id')
            ->with('permissions')
            ->get();

        foreach ($rolesWithOrganization as $role) {
            $permissions = $role->permissions->pluck('name')->toArray();

            $hasRolePermission = array_intersect($permissions, [
                'manage-roles',
                'view-roles',
                'manage-users',
                'manage-departments',
                'manage-organizations'
            ]);

            if (!empty($hasRolePermission)) {
                return Organization::find($role->organization_id);
            }
        }

        return null;
    }
}
