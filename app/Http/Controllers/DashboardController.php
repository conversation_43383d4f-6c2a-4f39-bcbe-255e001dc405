<?php

namespace App\Http\Controllers;

use App\Http\Controllers\FlexibleDashboardController;
use App\Models\Branch;
use App\Models\ChartOfAccount;
use App\Models\Department;
use App\Models\Organization;
use App\Models\Requisition;
use App\Models\Transaction;
use App\Models\Role;
use App\Models\User;
use App\Models\CashFloat;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the appropriate dashboard based on user role.
     *
     * @return \Inertia\Response|\Illuminate\Http\RedirectResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Check for role and department switching parameters
        $switchRole = $request->input('role');
        $switchDepartment = $request->input('department');
        $switchRoleId = $request->input('role_id');

        // Get user's available roles and departments for switching
        $availableContexts = $this->getUserAvailableContexts($user);

        // If switching parameters are provided, validate and use them
        if ($switchRole || $switchDepartment || $switchRoleId) {
            return $this->handleDashboardSwitch($request, $user, $switchRole, $switchDepartment, $availableContexts);
        }

        // Default behavior - show highest priority dashboard
        return $this->getDefaultDashboard($user, $request, $availableContexts);
    }
    
    /**
     * Get user's available cashfloats 
     */
    private function getUserAvailableCashFloats(User $user): array
    {
        // fetch cashfloats with the same organization id as the user
        $organizationIds = $user->organizations->pluck('id');
        
        return CashFloat::whereIn('organization_id', $organizationIds)
            ->where('status', 'active')
            ->pluck('name', 'id')
            ->toArray();
    }
    /**
     * Get user's available dashboard contexts (roles and departments).
     */
    private function getUserAvailableContexts(User $user): array
    {
        $contexts = [];

        $predefinedRoles = ['Organization Admin', 'Finance Manager', 'HOD'];

        // Platform Admin
        if ($user->is_platform_admin) {
            $contexts[] = [
                'type' => 'role',
                'key' => 'platform_admin',
                'label' => 'Platform Administrator',
                'organization' => null,
                'department' => null,
                'priority' => 1
            ];
        }

        // Organization Admin roles
        foreach ($user->roles()->where('name', 'Organization Admin')->get() as $role) {
            $org = Organization::find($role->organization_id);
            $contexts[] = [
                'type' => 'role',
                'key' => 'organization_admin',
                'label' => 'Organization Admin',
                'organization' => $org,
                'department' => null,
                'priority' => 2,
                'role_id' => $role->id
            ];
        }
        
        // Finance Manager roles
        foreach ($user->roles()->where('name', 'Finance Manager')->get() as $role) {
            $org = Organization::find($role->organization_id);
            $contexts[] = [
                'type' => 'role',
                'key' => 'finance_manager',
                'label' => 'Finance Manager',
                'organization' => $org,
                'department' => null,
                'priority' => 3,
                'role_id' => $role->id
            ];
        }
        
        // Store Keeper role (check for store-keep permission)
        if ($user->can('store-keep')) {
            // Get user's organizations for store keeper context
            foreach ($user->organizations as $org) {
                $contexts[] = [
                    'type' => 'role',
                    'key' => 'store_keeper',
                    'label' => 'Store Keeper',
                    'organization' => $org,
                    'department' => null,
                    'priority' => 3, // Higher priority than HOD but lower than Finance Manager
                ];
            }
        }

        // HOD roles (departments where user is HOD)
        $hodDepartments = Department::where('hod_user_id', $user->id)->with(['organization', 'branch'])->get();
        foreach ($hodDepartments as $dept) {
            $contexts[] = [
                'type' => 'role',
                'key' => 'hod',
                'label' => 'Head of Department',
                'organization' => $dept->organization,
                'department' => $dept,
                'priority' => 5, // Adjusted priority
                'department_id' => $dept->id
            ];
        }
        
        // Department memberships (as regular employee)
        foreach ($user->departments()->with(['organization', 'branch'])->get() as $dept) {
            // Skip if user is already HOD of this department
            if ($dept->hod_user_id === $user->id) {
                continue;
            }

            $contexts[] = [
                'type' => 'department',
                'key' => 'employee',
                'label' => 'Employee',
                'organization' => $dept->organization,
                'department' => $dept,
                'priority' => 6, // Adjusted priority to be lower than store keeper
                'department_id' => $dept->id
            ];
        }

        // Custom roles 
        foreach ($user->roles()->whereNotIn('name', $predefinedRoles)->get() as $role) {
            $org = $role->organization_id ? Organization::find($role->organization_id) : null;
            $dept = $role->department_id ? Department::find($role->department_id) : null;

            $contexts[] = [
                'type' => 'role',
                'key' => 'custom_role',
                'label' => $role->name,
                'organization' => $org,
                'department' => $dept,
                'priority' => 6, 
                'role_id' => $role->id,
                'role_name' => $role->name,
                'permissions' => $role->permissions->pluck('name')->toArray()
            ];
        }

        return $contexts;
    }
    
    /**
     * Handle dashboard switching requests.
     */
    private function handleDashboardSwitch(Request $request, User $user, ?string $switchRole, ?string $switchDepartment, array $availableContexts)
    {
        // Find the requested context
        $targetContext = null;
        $switchOrganizationId = $request->input('organization_id');
        $switchRoleId = $request->input('role_id');

        foreach ($availableContexts as $context) {
            $matchesRole = !$switchRole || $context['key'] === $switchRole;
            $matchesDepartment = !$switchDepartment ||
                ($context['department'] && $context['department']->id == $switchDepartment);
            $matchesOrganization = !$switchOrganizationId ||
                ($context['organization'] && $context['organization']->id == $switchOrganizationId);

            $matchesRoleId = !$switchRoleId ||
                (isset($context['role_id']) && $context['role_id'] == $switchRoleId);

            if ($matchesRole && $matchesDepartment && $matchesOrganization && $matchesRoleId) {
                $targetContext = $context;
                break;
            }
        }

        if (!$targetContext) {
            abort(403, 'You do not have access to the requested dashboard context.');
        }

        return $this->renderDashboardForContext($request, $user, $targetContext, $availableContexts);
    }
    
    /**
     * Get the default dashboard (highest priority available).
     */
    private function getDefaultDashboard(User $user, Request $request, array $availableContexts)
    {
        if (empty($availableContexts)) {
            return $this->defaultDashboard();
        }
        
        // Sort by priority and get the highest priority context
        usort($availableContexts, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });
        
        $defaultContext = $availableContexts[0];
        
        // Handle organization setup check for org admins
        if ($defaultContext['key'] === 'organization_admin') {
            $organization = $defaultContext['organization'];

            if (!$organization) {
                return $this->defaultDashboard();
            }

            $organizationId = $organization->id;
            $hasDepartments = Department::where('organization_id', $organizationId)->exists();

            if (!$hasDepartments) {
                return redirect()->route('organization-setup.index');
            }
        }
        
        return $this->renderDashboardForContext($request, $user, $defaultContext, $availableContexts);
    }
    
    /**
     * Render dashboard for a specific context.
     */
    private function renderDashboardForContext(Request $request, User $user, array $context, array $availableContexts)
    {
        switch ($context['key']) {
            case 'platform_admin':
                return $this->platformAdminDashboard($availableContexts);

            case 'organization_admin':
                return $this->organizationAdminDashboard($user, $context, $availableContexts);

            case 'finance_manager':
                return $this->financeManagerDashboard($request, $context, $availableContexts);

            case 'hod':
                return $this->hodDashboard($user, $context['department'], $availableContexts);

            case 'store_keeper':
                return $this->storeKeeperDashboard($request, $user, $context, $availableContexts);

            case 'custom_role':
                return app(FlexibleDashboardController::class)->index($request, $context, $availableContexts);

            case 'employee':
            default:
                return $this->employeeDashboard($request, $context['department'], $availableContexts);
        }
    }

    /**
     * Display dashboard for platform admin.
     */
    private function platformAdminDashboard(array $availableContexts = []): Response
    {
        $stats = [
            'organizations' => Organization::count(),
            'users' => User::count(),
            'roles' => Role::count(),
            'departments' => Department::count(),
        ];

        $recentUsers = User::latest()->take(5)->get();
        $recentOrganizations = Organization::latest()->take(5)->get();

        return Inertia::render('Dashboard/PlatformAdmin', [
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'recentOrganizations' => $recentOrganizations,
            'availableContexts' => $availableContexts,
        ]);
    }

    /**
     * Display dashboard for organization admin.
     */
    private function organizationAdminDashboard(User $user, array $context, array $availableContexts = []): Response
    {
        $organization = $context['organization'];

        if (!$organization) {
            return $this->defaultDashboard();
        }

        $organizationId = $organization->id;
        $cashfloats = $this->getUserAvailableCashFloats($user);


        $stats = [
            'branches' => Branch::where('organization_id', $organizationId)->count(),
            'departments' => Department::where('organization_id', $organizationId)->count(),
            'users' => User::whereHas('roles', function ($query) use ($organizationId) {
                $query->where('organization_id', $organizationId);
            })->count(),
            'roles' => Role::where('organization_id', $organizationId)->count(),
            'workflows' => DB::table('approval_workflows')->where('organization_id', $organizationId)->count(),
            'chartOfAccounts' => ChartOfAccount::where('organization_id', $organizationId)->count(),
            
        ];

        $recentDepartments = Department::where('organization_id', $organizationId)
            ->latest()
            ->take(5)
            ->get();

        $recentUsers = User::whereHas('roles', function ($query) use ($organizationId) {
            $query->where('organization_id', $organizationId);
        })
            ->latest()
            ->take(5)
            ->get();

        return Inertia::render('Dashboard/OrganizationAdmin', [
            'stats' => $stats,
            'organization' => $organization,
            'recentDepartments' => $recentDepartments,
            'recentUsers' => $recentUsers,
            'availableContexts' => $availableContexts,
            'cashFloats' => $cashfloats,
        ]);
    }

    /**
     * Display dashboard for Head of Department.
     */
    private function hodDashboard(User $user, ?Department $selectedDepartment = null, array $availableContexts = []): Response
    {
        // Find all departments where user is HOD
        $departments = Department::where('hod_user_id', $user->id)
            ->with(['organization', 'branch'])
            ->get();

        if ($departments->isEmpty()) {
            return $this->defaultDashboard();
        }

        // Use selected department or get the first department by default
        $department = $selectedDepartment ?: $departments->first();
        $organizationId = $department->organization_id;

        // Get department members
        $departmentMembers = User::whereHas('departments', function ($query) use ($department) {
            $query->where('departments.id', $department->id);
        })->get();

        // Get pending requisitions for this department 
        $pendingRequisitions = Requisition::where('department_id', $department->id)
            ->where('status', 'pending_approval')
            ->where('requester_user_id', '!=', $user->id) 
            ->with('requester:id,first_name,last_name,email')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get HOD's own requisitions
        $hodRequisitions = Requisition::where('department_id', $department->id)
            ->where('requester_user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get requisition statistics
        $stats = [
            'departmentMembers' => $departmentMembers->count(),
            'pendingRequisitions' => Requisition::where('department_id', $department->id)
                ->where('status', 'pending_approval')
                ->where('requester_user_id', '!=', $user->id) // Exclude HOD's own requisitions
                ->count(),
            'approvedRequisitions' => Requisition::where('department_id', $department->id)
                ->where('status', 'approved')
                ->count(),
            'rejectedRequisitions' => Requisition::where('department_id', $department->id)
                ->where('status', 'rejected')
                ->count(),
            'hodRequisitions' => Requisition::where('department_id', $department->id)
                ->where('requester_user_id', $user->id)
                ->count(),
        ];

        return Inertia::render('Dashboard/Hod', [
            'stats' => $stats,
            'department' => $department,
            'departments' => $departments, // Pass all departments for multi-department support
            'departmentMembers' => $departmentMembers,
            'pendingRequisitions' => $pendingRequisitions,
            'hodRequisitions' => $hodRequisitions,
            'organization' => $department->organization,
            'availableContexts' => $availableContexts,
            'user' => [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
            ],
        ]);
    }

    /**
     * Switch to a different department dashboard for HOD.
     */
    public function switchDepartment(Request $request, Department $department): Response
    {
        $user = $request->user();

        // Verify user is HOD for this department
        if ($department->hod_user_id !== $user->id) {
            abort(403, 'You are not authorized to view this department dashboard');
        }

        // Load department with relationships
        $department->load(['organization', 'branch']);

        // Get department members
        $departmentMembers = User::whereHas('departments', function ($query) use ($department) {
            $query->where('departments.id', $department->id);
        })->get();

        // Get all departments where user is HOD
        $departments = Department::where('hod_user_id', $user->id)
            ->with(['organization', 'branch'])
            ->get();

        // Get pending requisitions for this department (from other users)
        $pendingRequisitions = Requisition::where('department_id', $department->id)
            ->where('status', 'pending_approval')
            ->where('requester_user_id', '!=', $user->id) // Exclude HOD's own requisitions
            ->with('requester:id,first_name,last_name,email')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get HOD's own requisitions
        $hodRequisitions = Requisition::where('department_id', $department->id)
            ->where('requester_user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get requisition statistics
        $stats = [
            'departmentMembers' => $departmentMembers->count(),
            'pendingRequisitions' => Requisition::where('department_id', $department->id)
                ->where('status', 'pending_approval')
                ->where('requester_user_id', '!=', $user->id) // Exclude HOD's own requisitions
                ->count(),
            'approvedRequisitions' => Requisition::where('department_id', $department->id)
                ->where('status', 'approved')
                ->count(),
            'rejectedRequisitions' => Requisition::where('department_id', $department->id)
                ->where('status', 'rejected')
                ->count(),
            'hodRequisitions' => Requisition::where('department_id', $department->id)
                ->where('requester_user_id', $user->id)
                ->count(),
        ];

        return Inertia::render('hod-dashboard-index', [
            'stats' => $stats,
            'department' => $department,
            'departments' => $departments,
            'departmentMembers' => $departmentMembers,
            'pendingRequisitions' => $pendingRequisitions,
            'hodRequisitions' => $hodRequisitions,
            'organization' => $department->organization,
            'user' => [
                'id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'email' => $user->email,
            ],
        ]);
    }

    /**
     * Switch department for regular employee dashboard.
     */
    public function switchEmployeeDepartment(Request $request, Department $department): Response
    {
        $user = $request->user();

        // Verify user belongs to this department
        $belongsToDepartment = $user->departments()->where('department_user.department_id', $department->id)->exists();

        if (!$belongsToDepartment) {
            abort(403, 'You are not authorized to view this department dashboard');
        }

        return redirect()->route('dashboard', ['department' => $department->id]);
    }

    /**
     * Display dashboard for employees in a specific department context.
     */
    private function employeeDashboard(Request $request, ?Department $selectedDepartment = null, array $availableContexts = []): Response
    {
        $user = $request->user();
        
        // Get all departments the user belongs to
        $userDepartments = $user->departments()->with(['organization', 'branch'])->get();
        
        // Use selected department or first available department
        $department = $selectedDepartment;
        if (!$department && $userDepartments->isNotEmpty()) {
            $department = $userDepartments->first();
        }
        
        // If department specified but user doesn't belong to it, use first available
        if ($department && !$userDepartments->contains('id', $department->id)) {
            $department = $userDepartments->first();
        }
        
        return $this->renderEmployeeDashboard($user, $department, $userDepartments, $availableContexts);
    }

    /**
     * Display default dashboard for regular users/employees.
     */
    private function defaultDashboard(): Response
    {
        $user = auth()->user();
        $request = request();

        // Get all departments the user belongs to
        $userDepartments = $user->departments()->with(['organization', 'branch'])->get();

        // Get the selected department from the request
        $departmentId = $request->input('department');

        // Get the department object if it exists and the user belongs to it
        $department = null;
        if ($departmentId) {
            // Check if the user belongs to this department
            $departmentExists = $userDepartments->contains('id', $departmentId);

            if ($departmentExists) {
                $department = Department::with(['organization', 'branch'])->find($departmentId);
            } else if ($userDepartments->isNotEmpty()) {
                // If the requested department doesn't exist or user doesn't belong to it,
                // fall back to the first department
                $department = $userDepartments->first();
                $department->load(['organization', 'branch']);
            }
        } else if ($userDepartments->isNotEmpty()) {
            // If no department is specified in the request, use the first department
            $department = $userDepartments->first();
        }

        return $this->renderEmployeeDashboard($user, $department, $userDepartments, []);
    }

    /**
     * Shared method to render employee dashboard.
     */
    private function renderEmployeeDashboard(User $user, ?Department $department, $userDepartments, array $availableContexts): Response
    {

        // Get organization from the department
        $organization = $department ? $department->organization : null;

        // Get user's requisitions for the selected department
        $requisitions = collect([]);
        if ($department) {
            $requisitions = Requisition::where('requester_user_id', $user->id)
                ->where('department_id', $department->id)
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        }


        // Only load pending approvals for users with approver permissions
        $pendingApprovals = collect();
        if ($user->hasPermissionTo('approver')) {
            $pendingApprovals = Requisition::where('status', 'pending_approval')
                ->where(function($q) use ($user) {
                    $q->whereHas('currentApprovalStep', function ($query) use ($user) {
                        $query->where('approver_user_id', $user->id);
                    });

                    $q->orWhereHas('currentApprovalStep', function ($query) use ($user) {
                        $query->whereNull('approver_user_id')
                            ->whereHas('role', function ($roleQuery) use ($user) {
                                $roleQuery->whereHas('users', function ($userQuery) use ($user) {
                                    $userQuery->where('users.id', $user->id);
                                });
                            });
                    });
                })
                ->whereNotExists(function ($query) use ($user) {
                    $query->select(DB::raw(1))
                        ->from('requisition_approvals')
                        ->whereRaw('requisition_approvals.requisition_id = requisitions.id')
                        ->whereRaw('requisition_approvals.approval_workflow_step_id = requisitions.current_approval_step_id')
                        ->where('requisition_approvals.approver_user_id', $user->id)
                        ->where('requisition_approvals.action', 'approved');
                })
                ->with(['department', 'requester', 'currentApprovalStep', 'currentApprovalStep.role'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        }

        // Get requisition stats
        $requisitionStats = [
            'total' => 0,
            'pending' => 0,
            'approved' => 0,
            'rejected' => 0,
            'pendingApprovals' => 0
        ];

        if ($department) {
            $requisitionStats = [
                'total' => Requisition::where('requester_user_id', $user->id)
                    ->where('department_id', $department->id)
                    ->count(),
                'pending' => Requisition::where('requester_user_id', $user->id)
                    ->where('department_id', $department->id)
                    ->where('status', 'pending_approval')
                    ->count(),
                'approved' => Requisition::where('requester_user_id', $user->id)
                    ->where('department_id', $department->id)
                    ->where('status', 'approved')
                    ->count(),
                'rejected' => Requisition::where('requester_user_id', $user->id)
                    ->where('department_id', $department->id)
                    ->where('status', 'rejected')
                    ->count(),
                'pendingApprovals' => $user->hasPermissionTo('approver') ?
                    Requisition::where('status', 'pending_approval')
                    ->where(function($q) use ($user) {
                        $q->whereHas('currentApprovalStep', function ($query) use ($user) {
                            $query->where('approver_user_id', $user->id);
                        });

                        $q->orWhereHas('currentApprovalStep', function ($query) use ($user) {
                            $query->whereNull('approver_user_id')
                                ->whereHas('role', function ($roleQuery) use ($user) {
                                    $roleQuery->whereHas('users', function ($userQuery) use ($user) {
                                        $userQuery->where('users.id', $user->id);
                                    });
                                });
                        });
                    })
                    // Exclude requisitions that this user has already approved at the current step
                    ->whereNotExists(function ($query) use ($user) {
                        $query->select(DB::raw(1))
                            ->from('requisition_approvals')
                            ->whereRaw('requisition_approvals.requisition_id = requisitions.id')
                            ->whereRaw('requisition_approvals.approval_workflow_step_id = requisitions.current_approval_step_id')
                            ->where('requisition_approvals.approver_user_id', $user->id)
                            ->where('requisition_approvals.action', 'approved');
                    })->count() : 0,
            ];
        }

        // Get user roles
        $userRoles = [];
        foreach ($user->roles as $role) {
            $userRoles[] = [
                'role' => $role->name,
                'organization' => $department && $department->organization ? $department->organization->name : '',
                'branch' => $department && $department->branch ? $department->branch->name : '',
                'department' => $department ? $department->name : '',
            ];
        }

        return Inertia::render('Dashboard/Employee', [
            'requisitions' => $requisitions,
            'pendingApprovals' => $pendingApprovals,
            'requisitionStats' => $requisitionStats,
            'organization' => $organization,
            'department' => $department,
            'userDepartments' => $userDepartments,
            'userRoles' => $userRoles,
            'availableContexts' => $availableContexts,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
            ],
        ]);
    }

    /**
     * Display dashboard for finance manager.
     */
   /**
 * Display dashboard for finance manager.
 */
public function financeManagerDashboard(Request $request, array $context, array $availableContexts = [])
{
    $user = $request->user();
    
    // Get the organization from the context
    $organization = $context['organization'];

    if (!$organization) {
        return Inertia::render('Dashboard/Error', [
            'message' => 'The organization associated with your Finance Manager role could not be found.',
            'user' => $user,
        ]);
    }

    // Get pending transactions (instead of pending requisitions)
    $pendingTransactions = Transaction::with([
            'cashFloat.organization',
            'cashFloat.department',
            'cashFloat.branch',
            'cashFloat.user',
            'creator',
            'requisition.requester',
            'requisition.department'
        ])
        ->where(function ($query) use ($organization) {
            // Transactions with cash floats in this organization
            $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                $subQuery->where('organization_id', $organization->id);
            })
            ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                $subQuery->where('organization_id', $organization->id);
            });
        })
        ->whereIn('status', ['opened', 'updated']) 
        ->latest()
        ->take(5)
        ->get();

    // Get recent transactions 
    $recentTransactions = Transaction::with([
            'cashFloat.organization',
            'cashFloat.department',
            'cashFloat.branch',
            'cashFloat.user',
            'creator',
            'requisition.requester',
            'requisition.department'
        ])
        ->where(function ($query) use ($organization) {
            $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                $subQuery->where('organization_id', $organization->id);
            })
            ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                $subQuery->where('organization_id', $organization->id);
            });
        })
        ->latest()
        ->take(5)
        ->get();

    $cashFloats = CashFloat::with(['department', 'branch', 'user', 'transactions' => function ($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        }])
        ->where('organization_id', $organization->id)
        ->where('status', 'active')
        ->get();

    // Calculate detailed float statistics using the model's current_balance attribute
    $totalFloat = $cashFloats->sum('initial_amount');
    $totalRemaining = $cashFloats->sum('current_balance'); // Use current_balance instead of remaining_amount
    $totalUsed = $totalFloat - $totalRemaining;
    
    // Get low float alerts using current_balance
    $lowFloatDepartments = $cashFloats->filter(function ($float) {
        return $float->alert_threshold && $float->current_balance <= $float->alert_threshold;
    });

    // Get float status for each department with corrected calculations
    $floatStatus = $cashFloats->map(function ($float) {
        $monthlyTransactions = $float->transactions->count();
        $monthlyTotal = $float->transactions->sum('total_amount'); 
        $currentBalance = $float->current_balance; 
        
        return [
            'id' => $float->id,
            'name' => $float->name,
            'department' => $float->department?->name ?? ($float->branch?->name ?? ($float->user?->name ?? 'Unassigned')),
            'allocated' => $float->initial_amount,
            'used' => $float->initial_amount - $currentBalance, 
            'remaining' => $currentBalance, 
            'alert_threshold' => $float->alert_threshold,
            'is_low' => $float->isBelowAlertThreshold(), 
            'last_transaction_at' => $float->last_transaction_at,
            'monthly_transactions' => $monthlyTransactions,
            'monthly_total' => $monthlyTotal,
            'utilization_percentage' => $float->initial_amount > 0 
                ? round(($float->initial_amount - $currentBalance) / $float->initial_amount * 100, 2)
                : 0,
        ];
    });

    // Get transaction and requisition statistics
    $dashboardStats = [
        'pendingTransactions' => Transaction::where(function ($query) use ($organization) {
                $query->whereHas('cashFloat', function ($subQuery) use ($organization) {
                    $subQuery->where('organization_id', $organization->id);
                })
                ->orWhereHas('requisition', function ($subQuery) use ($organization) {
                    $subQuery->where('organization_id', $organization->id);
                });
            })
            ->whereIn('status', ['opened', 'updated'])
            ->count(),
        'approvedRequisitions' => Requisition::where('organization_id', $organization->id)
            ->where('status', 'approved')
            ->whereMonth('created_at', now()->month)
            ->count(),
        'rejectedRequisitions' => Requisition::where('organization_id', $organization->id)
            ->where('status', 'rejected')
            ->whereMonth('created_at', now()->month)
            ->count(),
        'totalFloat' => $totalFloat,
        'totalRemaining' => $totalRemaining, 
        'totalUsed' => $totalUsed,
        'lowFloatDepartments' => $lowFloatDepartments->count(),
        'lowFloatAlerts' => $lowFloatDepartments->map(function ($float) {
            return [
                'id' => $float->id,
                'name' => $float->name,
                'department' => $float->department?->name ?? ($float->branch?->name ?? ($float->user?->name ?? 'Unassigned')),
                'remaining' => $float->current_balance, 
                'threshold' => $float->alert_threshold,
            ];
        }),
        'utilizationRate' => $totalFloat > 0 
            ? round(($totalUsed / $totalFloat) * 100, 2)
            : 0,
    ];

    return Inertia::render('Dashboard/FinanceManager', [
        'stats' => $dashboardStats,
        'pendingTransactions' => $pendingTransactions,
        'recentTransactions' => $recentTransactions,
        'floatStatus' => $floatStatus,
        'cashFloats' => $cashFloats->map(function ($float) {
            return [
                'id' => $float->id,
                'name' => $float->name,
                'current_balance' => $float->current_balance, 
                'initial_amount' => $float->initial_amount,
                'status' => $float->status,
                'is_low' => $float->isBelowAlertThreshold(),
                'department' => $float->department ? [
                    'id' => $float->department->id,
                    'name' => $float->department->name,
                ] : null,
                'branch' => $float->branch ? [
                    'id' => $float->branch->id,
                    'name' => $float->branch->name,
                ] : null,
                'user' => $float->user ? [
                    'id' => $float->user->id,
                    'name' => $float->user->name,
                ] : null,
            ];
        }),
        'organization' => $organization,
        'user' => $user,
        'availableContexts' => $availableContexts,

    ]);
}

    /**
     * Display dashboard for store keeper.
     */
    private function storeKeeperDashboard(Request $request, User $user, array $context, array $availableContexts = [])
    {
        // Redirect to the dedicated store keeper dashboard
        return redirect()->route('store-keeper.dashboard');
    }
}