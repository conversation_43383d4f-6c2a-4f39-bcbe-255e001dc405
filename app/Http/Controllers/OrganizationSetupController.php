<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Department;
use App\Models\DepartmentTemplate;
use App\Models\Organization;
use App\Models\Role;
use App\Models\RoleTemplate;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Permission;

class OrganizationSetupController extends Controller
{
    /**
     * Show the organization setup wizard.
     *
     * @return \Inertia\Response|\Illuminate\Http\RedirectResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Get the organization admin role for this user
        $orgAdminRole = $user->roles()->where('name', 'Organization Admin')->first();

        // If user doesn't have an Organization Admin role, redirect to dashboard
        if (!$orgAdminRole) {
            return redirect()->route('dashboard')
                ->with('error', 'You do not have permission to access the organization setup.');
        }

        $organization = Organization::find($orgAdminRole->organization_id);

        // If organization doesn't exist, redirect to dashboard
        if (!$organization) {
            return redirect()->route('dashboard')
                ->with('error', 'Organization not found.');
        }

        // Check if the organization already has departments
        $hasDepartments = Department::where('organization_id', $organization->id)->exists();

        // If the organization already has departments, redirect to dashboard
        if ($hasDepartments) {
            return redirect()->route('dashboard');
        }

        // Get all department templates
        $departmentTemplates = DepartmentTemplate::where('is_default', true)->get();

        // Get role templates grouped by department template
        $roleTemplates = RoleTemplate::where('is_default', true)->get()
            ->groupBy('department_template_id');

        // Get general role templates (not tied to a specific department)
        $generalRoleTemplates = RoleTemplate::whereNull('department_template_id')
            ->where('is_default', true)
            ->get();

        return Inertia::render('organization-setup', [
            'organization' => $organization,
            'departmentTemplates' => $departmentTemplates,
            'roleTemplates' => $roleTemplates,
            'generalRoleTemplates' => $generalRoleTemplates,
        ]);
    }

    /**
     * Process the organization setup form.
     */
    public function store(Request $request): RedirectResponse
    {
        \Log::info('Organization setup form submitted', $request->all());

        // Log the raw request data
        \Log::info('Raw request data', $request->all());

        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'department_templates' => 'required|array',
            'department_templates.*' => 'exists:department_templates,id',
            'role_templates' => 'required|array',
            'role_templates.*' => 'exists:role_templates,id',
        ]);

        // Log the validated data
        \Log::info('Validated data', $validated);

        $organizationId = $validated['organization_id'];
        $organization = Organization::findOrFail($organizationId);
        $user = $request->user();

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Check if the organization has any branches
            $branch = Branch::where('organization_id', $organizationId)->first();

            // If no branch exists, create a default branch
            if (!$branch) {
                $branch = Branch::create([
                    'organization_id' => $organizationId,
                    'name' => 'Main Branch',
                    'is_active' => true,
                ]);

                \Log::info('Created default branch for organization', ['branch_id' => $branch->id]);
            }

            // Create departments from selected templates
            $departmentMap = []; // Map template IDs to created department IDs
            foreach ($validated['department_templates'] as $templateId) {
                $template = DepartmentTemplate::findOrFail($templateId);

                // Create the department with the branch_id
                $department = Department::create([
                    'organization_id' => $organizationId,
                    'branch_id' => $branch->id, // Use the branch ID
                    'name' => $template->name,
                    'hod_user_id' => null, // Will be assigned later
                ]);

                \Log::info('Created department', ['department_id' => $department->id, 'branch_id' => $branch->id]);

                $departmentMap[$templateId] = $department->id;
            }

            // Create roles from selected templates
            foreach ($validated['role_templates'] as $templateId) {
                $template = RoleTemplate::findOrFail($templateId);

                // Determine department_id based on the template's department_template_id
                $departmentId = null;
                if ($template->department_template_id && isset($departmentMap[$template->department_template_id])) {
                    $departmentId = $departmentMap[$template->department_template_id];
                }

                // Create the role
                $role = Role::create([
                    'name' => $template->name,
                    'guard_name' => 'web', // Required by Spatie
                    'description' => $template->description,
                    'organization_id' => $organizationId,
                    'branch_id' => $branch->id, // Use the same branch
                    'department_id' => $departmentId,
                    'is_active' => true,
                ]);

                \Log::info('Created role', ['role_id' => $role->id, 'branch_id' => $branch->id, 'department_id' => $departmentId]);

                // Assign permissions to the role
                if ($template->permissions) {
                    foreach ($template->permissions as $permissionName) {
                        $permission = Permission::where('name', $permissionName)->first();
                        if ($permission) {
                            $role->givePermissionTo($permission);
                        }
                    }
                }
            }

            DB::commit();

            \Log::info('Organization setup completed successfully');
            return redirect()->route('dashboard')->with('success', 'Organization setup completed successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error during organization setup: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
            ]);
            return back()->with('error', 'An error occurred during setup: ' . $e->getMessage());
        }
    }
}
