<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Department;
use App\Models\Organization;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

/**
 * This file contains the fixes for the RoleController
 * These are the methods that need to be updated in the RoleController.php file
 */
class RoleControllerFixes extends Controller
{
    /**
     * Helper method to categorize permissions
     */
    private function getPermissionCategory(string $permissionName): string
    {
        if (strpos($permissionName, 'user') !== false) {
            return 'User Management';
        } elseif (strpos($permissionName, 'role') !== false) {
            return 'Role Management';
        } elseif (strpos($permissionName, 'department') !== false) {
            return 'Department Management';
        } elseif (strpos($permissionName, 'branch') !== false) {
            return 'Branch Management';
        } elseif (strpos($permissionName, 'organization') !== false) {
            return 'Organization Management';
        } elseif (strpos($permissionName, 'requisition') !== false) {
            return 'Requisition Management';
        } else {
            return 'Other';
        }
    }

    /**
     * Fix for the index method
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // If platform admin, show all roles
        if ($user->is_platform_admin) {
            $roles = Role::with(['permissions', 'organization', 'branch', 'department'])->get();

            return Inertia::render('roles/roles-index', [
                'roles' => $roles,
                'isPlatformAdmin' => true,
            ]);
        }

        // If organization admin, show only roles in their organization
        if ($user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();

            if (! $organizationAdminRole) {
                // This shouldn't happen since hasRole('Organization Admin') was true,
                // but we'll handle it just in case
                return Inertia::render('roles/roles-index', [
                    'roles' => [],
                    'isPlatformAdmin' => false,
                    'error' => 'Organization Admin role not found for this user.',
                ]);
            }

            $organizationId = $organizationAdminRole->organization_id;

            // Only show roles that belong to this organization
            $roles = Role::where('organization_id', $organizationId)
                ->with(['permissions', 'organization', 'branch', 'department'])
                ->get();

            return Inertia::render('roles/roles-index', [
                'roles' => $roles,
                'isPlatformAdmin' => false,
                'organizationId' => $organizationId,
            ]);
        }

        // Default case - shouldn't happen due to middleware
        return Inertia::render('roles/roles-index', [
            'roles' => [],
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Fix for the create method
     */
    public function create(Request $request): Response
    {
        $user = $request->user();
        $permissions = Permission::select('id', 'name', 'description')->get();

        // Group permissions by category
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $category = $this->getPermissionCategory($permission->name);
            if (! isset($groupedPermissions[$category])) {
                $groupedPermissions[$category] = [];
            }
            $groupedPermissions[$category][] = $permission;
        }

        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            $branches = Branch::all();
            $departments = Department::all();

            return Inertia::render('roles/roles-create', [
                'permissions' => $permissions,
                'groupedPermissions' => $groupedPermissions,
                'organizations' => $organizations,
                'branches' => $branches,
                'departments' => $departments,
                'isPlatformAdmin' => true,
            ]);
        }

        // Organization admin
        $organizationAdminRole = $user->roles()
            ->where('name', 'Organization Admin')
            ->first();

        if (! $organizationAdminRole) {
            // Handle the case where the user doesn't have the Organization Admin role
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have the Organization Admin role assigned.',
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'email' => $user->email,
                ],
            ]);
        }

        $organizationId = $organizationAdminRole->organization_id;

        $organization = Organization::find($organizationId);
        $branches = Branch::where('organization_id', $organizationId)->get();
        $departments = Department::where('organization_id', $organizationId)->get();

        return Inertia::render('roles/roles-create', [
            'permissions' => $permissions,
            'groupedPermissions' => $groupedPermissions,
            'organization' => $organization,
            'branches' => $branches,
            'departments' => $departments,
            'isPlatformAdmin' => false,
        ]);
    }
}
