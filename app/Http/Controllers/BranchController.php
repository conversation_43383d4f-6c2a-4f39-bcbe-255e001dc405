<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Organization;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class BranchController extends Controller
{
    /**
     * Display a listing of the branches.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // If platform admin, show all branches
        if ($user->is_platform_admin) {
            $branches = Branch::with('organization')->get();

            return Inertia::render('branches/branches-index', [
                'branches' => $branches,
                'isPlatformAdmin' => true,
                'canCreateBranch' => true,
            ]);
        }

        // Get user's organization ID 
        $organizationId = null;
        
        // First try to get from Organization Admin role
        if ($user->hasRole('Organization Admin')) {
            $orgAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();
            if ($orgAdminRole) {
                $organizationId = $orgAdminRole->organization_id;
            }
        }
        
        if (!$organizationId && $user->organization_id) {
            $organizationId = $user->organization_id;
        }
        
        if (!$organizationId) {
            $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
            if ($roleWithOrg) {
                $organizationId = $roleWithOrg->organization_id;
            }
        }

        // Check if user has branch management permissions
        $canManageBranches = $user->hasRole('Organization Admin') || 
                           $user->is_platform_admin ||
                           $user->can('manage-branches') ||
                           $user->can('view-branches') ||
                           $user->can('create-branches') ||
                           $user->can('edit-branches');

        if (!$canManageBranches) {
            abort(403, 'You do not have permission to view branches.');
        }

        if ($organizationId) {
            $branches = Branch::where('organization_id', $organizationId)
                ->with('organization')
                ->get();

            return Inertia::render('branches/branches-index', [
                'branches' => $branches,
                'isPlatformAdmin' => false,
                'organizationId' => $organizationId,
                'canCreateBranch' => $user->can('create-branches') || $user->can('manage-branches') || $user->hasRole('Organization Admin'),
            ]);
        }

        // If no organization found, return empty list
        return Inertia::render('branches/branches-index', [
            'branches' => [],
            'isPlatformAdmin' => false,
            'canCreateBranch' => false,
        ]);
    }

    /**
     * Show the form for creating a new branch.
     */
    public function create()
    {
        $user = auth()->user();
        
        // Check if user is platform admin
        if ($user->is_platform_admin) {
            $organizations = Organization::all();
            return Inertia::render('branches/branches-create', [
                'organizations' => $organizations,
                'isPlatformAdmin' => true,
            ]);
        }
        
        // Check if user has branch creation permissions
        $canCreateBranches = $user->hasRole('Organization Admin') || 
                           $user->can('create-branches') ||
                           $user->can('manage-branches');
            
        if (!$canCreateBranches) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'You do not have the required permissions to create branches.',
                'user' => $user,
            ]);
        }
        
        // Get organization ID  
        $organizationId = null;
        
        // First try to get from Organization Admin role
        if ($user->hasRole('Organization Admin')) {
            $organizationAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();
            if ($organizationAdminRole) {
                $organizationId = $organizationAdminRole->organization_id;
            }
        }
        
        // If no organization from role, try to get from user's organization_id field
        if (!$organizationId && $user->organization_id) {
            $organizationId = $user->organization_id;
        }
        
        // If still no organization, try to get from any role that has organization_id
        if (!$organizationId) {
            $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
            if ($roleWithOrg) {
                $organizationId = $roleWithOrg->organization_id;
            }
        }
        
        if (!$organizationId) {
            return Inertia::render('Dashboard/Error', [
                'message' => 'No organization found for your account. Please contact an administrator.',
                'user' => $user,
            ]);
        }
        
        $organization = Organization::find($organizationId);
        
        return Inertia::render('branches/branches-create', [
            'organization' => $organization,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Store a newly created branch in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'organization_id' => 'required|exists:organizations,id',
            'address' => 'nullable|string|max:500',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        // If not platform admin, ensure the branch is associated with their organization
        if (!$user->is_platform_admin) {
            $organizationId = null;
            
            // First try to get from Organization Admin role
            if ($user->hasRole('Organization Admin')) {
                $orgAdminRole = $user->roles()
                    ->where('name', 'Organization Admin')
                    ->first();
                if ($orgAdminRole) {
                    $organizationId = $orgAdminRole->organization_id;
                }
            }
            
            // If no organization from role, try to get from user's organization_id field
            if (!$organizationId && $user->organization_id) {
                $organizationId = $user->organization_id;
            }
            
            // If still no organization, try to get from any role that has organization_id
            if (!$organizationId) {
                $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
                if ($roleWithOrg) {
                    $organizationId = $roleWithOrg->organization_id;
                }
            }

            if ($organizationId) {
                $validated['organization_id'] = $organizationId;
            }
        }

        // Create the branch
        Branch::create($validated);

        return redirect()->route('branches.index')
            ->with('success', 'Branch created successfully.');
    }

    /**
     * Display the specified branch.
     */
    public function show(Branch $branch): Response
    {
        $branch->load(['organization', 'departments']);

        return Inertia::render('branches/branches-show', [
            'branch' => $branch,
        ]);
    }

    /**
     * Show the form for editing the specified branch.
     */
    public function edit(Request $request, Branch $branch): Response
    {
        $user = $request->user();
        $branch->load('organization');

        if ($user->is_platform_admin) {
            $organizations = Organization::all();

            return Inertia::render('branches/branches-edit', [
                'branch' => $branch,
                'organizations' => $organizations,
                'isPlatformAdmin' => true,
            ]);
        }

        // Get user's organization ID  
        $organizationId = null;
        
        // First try to get from Organization Admin role
        if ($user->hasRole('Organization Admin')) {
            $orgAdminRole = $user->roles()
                ->where('name', 'Organization Admin')
                ->first();
            if ($orgAdminRole) {
                $organizationId = $orgAdminRole->organization_id;
            }
        }
        
        // If no organization from role, try to get from user's organization_id field
        if (!$organizationId && $user->organization_id) {
            $organizationId = $user->organization_id;
        }
        
        // If still no organization, try to get from any role that has organization_id
        if (!$organizationId) {
            $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
            if ($roleWithOrg) {
                $organizationId = $roleWithOrg->organization_id;
            }
        }

        // Check if user has permission to edit branches
        $canEditBranches = $user->hasRole('Organization Admin') || 
                         $user->can('edit-branches') ||
                         $user->can('manage-branches');

        if (!$canEditBranches) {
            abort(403, 'You do not have permission to edit branches.');
        }

        // Check if the branch belongs to the user's organization
        if ($organizationId && $branch->organization_id !== $organizationId) {
            abort(403, 'Unauthorized action.');
        }

        $organization = Organization::find($organizationId);

        return Inertia::render('branches/branches-edit', [
            'branch' => $branch,
            'organization' => $organization,
            'isPlatformAdmin' => false,
        ]);
    }

    /**
     * Update the specified branch in storage.
     */
    public function update(Request $request, Branch $branch): RedirectResponse
    {
        $user = $request->user();

        // Check if the user can edit this branch
        if (!$user->is_platform_admin) {
            // Get user's organization ID  
            $organizationId = null;
            
            // First try to get from Organization Admin role
            if ($user->hasRole('Organization Admin')) {
                $orgAdminRole = $user->roles()
                    ->where('name', 'Organization Admin')
                    ->first();
                if ($orgAdminRole) {
                    $organizationId = $orgAdminRole->organization_id;
                }
            }
            
            // If no organization from role, try to get from user's organization_id field
            if (!$organizationId && $user->organization_id) {
                $organizationId = $user->organization_id;
            }
            
            // If still no organization, try to get from any role that has organization_id
            if (!$organizationId) {
                $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
                if ($roleWithOrg) {
                    $organizationId = $roleWithOrg->organization_id;
                }
            }

            // Check if user has permission to edit branches
            $canEditBranches = $user->hasRole('Organization Admin') || 
                             $user->can('edit-branch') ||
                             $user->can('manage-branches');

            if (!$canEditBranches) {
                abort(403, 'You do not have permission to edit branches.');
            }

            // If the branch is not associated with the user's organization
            if ($organizationId && $branch->organization_id !== $organizationId) {
                abort(403, 'Unauthorized action.');
            }
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'organization_id' => 'required|exists:organizations,id',
            'address' => 'nullable|string|max:500',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        // If not platform admin, ensure the branch stays associated with their organization
        if (!$user->is_platform_admin) {
            $organizationId = null;
            
            // First try to get from Organization Admin role
            if ($user->hasRole('Organization Admin')) {
                $orgAdminRole = $user->roles()
                    ->where('name', 'Organization Admin')
                    ->first();
                if ($orgAdminRole) {
                    $organizationId = $orgAdminRole->organization_id;
                }
            }
            
            // If no organization from role, try to get from user's organization_id field
            if (!$organizationId && $user->organization_id) {
                $organizationId = $user->organization_id;
            }
            
            // If still no organization, try to get from any role that has organization_id
            if (!$organizationId) {
                $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
                if ($roleWithOrg) {
                    $organizationId = $roleWithOrg->organization_id;
                }
            }

            if ($organizationId) {
                $validated['organization_id'] = $organizationId;
            }
        }

        // Update the branch
        $branch->update($validated);

        return redirect()->route('branches.index')
            ->with('success', 'Branch updated successfully.');
    }

    /**
     * Remove the specified branch from storage.
     */
    public function destroy(Request $request, Branch $branch): RedirectResponse
    {
        $user = $request->user();

        // Check if the user can delete this branch
        if (!$user->is_platform_admin) {
            // Get user's organization ID  
            $organizationId = null;
            
            // First try to get from Organization Admin role
            if ($user->hasRole('Organization Admin')) {
                $orgAdminRole = $user->roles()
                    ->where('name', 'Organization Admin')
                    ->first();
                if ($orgAdminRole) {
                    $organizationId = $orgAdminRole->organization_id;
                }
            }
            
            // If no organization from role, try to get from user's organization_id field
            if (!$organizationId && $user->organization_id) {
                $organizationId = $user->organization_id;
            }
            
            // If still no organization, try to get from any role that has organization_id
            if (!$organizationId) {
                $roleWithOrg = $user->roles()->whereNotNull('organization_id')->first();
                if ($roleWithOrg) {
                    $organizationId = $roleWithOrg->organization_id;
                }
            }

            // Check if user has permission to delete branches
            $canDeleteBranches = $user->hasRole('Organization Admin') || 
                               $user->can('delete-branch') ||
                               $user->can('manage-branches');

            if (!$canDeleteBranches) {
                abort(403, 'You do not have permission to delete branches.');
            }

            // If the branch is not associated with the user's organization
            if ($organizationId && $branch->organization_id !== $organizationId) {
                abort(403, 'Unauthorized action.');
            }
        }

        // Check if the branch has departments
        $departmentsCount = $branch->departments()->count();
        if ($departmentsCount > 0) {
            return redirect()->route('branches.index')
                ->with('error', 'Cannot delete branch because it has ' . $departmentsCount . ' department(s).');
        }

        $branch->delete();

        return redirect()->route('branches.index')
            ->with('success', 'Branch deleted successfully.');
    }
}
