<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRequisitionRequest;
use App\Models\Department;
use App\Models\ChartOfAccount;
use App\Models\RequisitionFormDetails;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;
use Src\Requisition\Application\Services\RequisitionService;
use App\Events\RequisitionSubmitted;
use App\Events\RequisitionStepApproved;
use App\Events\RequisitionCompleted;
use App\Events\RequisitionDeclined;
use App\Events\RequisitionRevisionRequested;

class RequisitionController extends Controller
{
    /**
     * @var RequisitionService
     */
    protected $requisitionService;

    /**
     * RequisitionController constructor.
     *
     * @param RequisitionService $requisitionService
     */
    public function __construct(RequisitionService $requisitionService)
    {
        $this->requisitionService = $requisitionService;
    }

    /**
     * Display a history of the user's requisitions with optional department filtering.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function history(Request $request): Response
    {
        $user = Auth::user();
        $departmentId = $request->input('department');
        $userId = $request->input('user');

        // Get all departments the user belongs to
        $userDepartments = $user->departments()->get();

        // Validate that the requested department belongs to the user
        $department = null;
        if ($departmentId) {
            $departmentExists = $userDepartments->contains('id', $departmentId);

            if ($departmentExists) {
                $department = Department::find($departmentId);
            }
        }

        // Get requisitions using the service
        $requisitions = $this->requisitionService->getUserRequisitionHistory(
            $userId && $user->is_platform_admin ? $userId : $user->id,
            $department ? $department->id : null,
            10
        );

        return Inertia::render('Requisitions/History', [
            'requisitions' => $requisitions,
            'department' => $department,
            'userDepartments' => $userDepartments,
            'filters' => [
                'department' => $departmentId,
                'user' => $userId,
            ],
        ]);
    }

    /**
     * Initialize a new requisition form and return the UUID.
     * This method is called when the user first accesses the "Create Requisition" page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function create(Request $request): Response
    {
        $uuid = Uuid::uuid4();
        $user = auth()->user();

        // Get the requested department ID from the query parameters
        $requestedDepartmentId = $request->input('department');

        // Get all departments the user belongs to
        $userDepartments = $user->departments()->get();

        // Get organization ID from user_organizations relationship
        // $organizationId = $user->organizations()->first()->id;
        $organization = $user->organizations()->first();
        if (!$organization) {
            abort(400, 'User must belong to an organization to create requisitions');
        }
        $organizationId = $organization->id;

        // Get branch ID from user_branches relationship
        // Some organizations might not have branches, so we need to handle null case
        $branch = $user->branches()->first();
        $branchId = $branch ? $branch->id : null;

        // Determine which department to use
        $departmentId = null;
        $departmentName = null;

        if ($requestedDepartmentId) {
            // Check if the requested department belongs to the user
            $departmentExists = $userDepartments->contains('id', $requestedDepartmentId);

            if ($departmentExists) {
                $departmentId = $requestedDepartmentId;
                $department = $userDepartments->firstWhere('id', $departmentId);
                $departmentName = $department ? $department->name : null;
            }
        }

        // If no valid department was found from the request, use the first department
        if (!$departmentId && $userDepartments->isNotEmpty()) {
            $department = $userDepartments->first();
            $departmentId = $department->id;
            $departmentName = $department->name;
        }

        // Create a new requisition record with the UUID and user information
        $requisitionFormDetails = new RequisitionFormDetails();
        $requisitionFormDetails->requisition_form_uuid = $uuid;
        $requisitionFormDetails->organization_id = $organizationId;

        if ($branchId) {
            $requisitionFormDetails->branch_id = $branchId;
        }

        if ($departmentId) {
            $requisitionFormDetails->department_id = $departmentId;
        }

        $requisitionFormDetails->requester_user_id = $user->id;
        $requisitionFormDetails->status = 'draft';
        $requisitionFormDetails->save();

        // Get chart of accounts based on user role
        $listOfChartOfAccounts = $this->getFilteredChartOfAccounts($user, $organizationId);

        return Inertia::render('Requisitions/CreateRequisition', [
            'requisition_form_uuid' => $uuid->toString(),
            'list_of_chart_of_accounts' => $listOfChartOfAccounts,
            'department_id' => $departmentId,
            'department_name' => $departmentName,
            'user_departments' => $userDepartments,
        ]);
    }

    /**
     * Store a newly created requisition.
     *
     * @param  \App\Http\Requests\StoreRequisitionRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreRequisitionRequest $request): RedirectResponse
    {
        try {
            // The request is already validated by the StoreRequisitionRequest class
            $requisition = $this->requisitionService->createRequisition($request->validated());

            // Get the latest requisition data with updated total amount
            $requisition->refresh();

            // Trigger notification event for requisition submission
            if ($requisition) {
                // Get first approval step from workflow relationship
                $firstStep = optional($requisition->approvalWorkflow)->steps()->first();

                // Dispatch the event to trigger notifications
                event(new RequisitionSubmitted($requisition, $firstStep));
            }

            return redirect()->route('requisitions.history', [
                'department' => $requisition->department_id
            ])->with('success', 'Requisition created successfully and sent for approval.');
        } catch (\Exception $e) {
            \Log::error('Error creating requisition: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while creating the requisition: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified requisition.
     *
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function show(int $id): Response
    {
        $requisition = $this->requisitionService->getRequisitionById($id);

        if (!$requisition) {
            abort(404, 'Requisition not found');
        }

        $user = Auth::user();

        // Check if the user is the requester or has permission to view this requisition
        $isRequester = $requisition->requester_user_id === $user->id;
        $isHOD = Department::where('id', $requisition->department_id)
            ->where('hod_user_id', $user->id)
            ->exists();

        // Check if user is an approver for this requisition
        $isApprover = false;
        if ($user->hasPermissionTo('approver')) {
            // Check if user is directly assigned as an approver in the current step
            if ($requisition->current_approval_step_id) {
                $isDirectApprover = DB::table('approval_workflow_steps')
                    ->where('id', $requisition->current_approval_step_id)
                    ->where('approver_user_id', $user->id)
                    ->exists();

                if ($isDirectApprover) {
                    $isApprover = true;
                } else {
                    // Check if user has a role that is assigned to the current step
                    $stepRoleId = DB::table('approval_workflow_steps')
                        ->where('id', $requisition->current_approval_step_id)
                        ->whereNull('approver_user_id')
                        ->value('role_id');

                    if ($stepRoleId && $user->roles()->where('id', $stepRoleId)->exists()) {
                        $isApprover = true;
                    }
                }
            }

            // Check if user has been involved in any approval action for this requisition
            if (!$isApprover) {
                $hasApprovalHistory = DB::table('requisition_approvals')
                    ->where('requisition_id', $requisition->id)
                    ->where('approver_user_id', $user->id)
                    ->exists();

                if ($hasApprovalHistory) {
                    $isApprover = true;
                }
            }
        }

        if (!$isRequester && !$isHOD && !$isApprover) {
            abort(403, 'Unauthorized action.');
        }

        // Load relationships
        $requisition->load(['department', 'requester', 'attachments.uploader']);

        // For approved requisitions, check if it has been moved to disbursement
        if ($requisition->status === 'approved') {
            $requisition->load('transactions.attachments.uploader');

            // If there are transactions, don't load items as they've been moved to transaction_items
            if ($requisition->transactions->isEmpty()) {
                $requisition->load('items');
            }
        } else {
            // For pending, rejected, or returned requisitions, load items
            $requisition->load('items');
        }

        // Get requisition history
        $history = $this->requisitionService->getRequisitionHistory($id);

        // Get chart of accounts for displaying item categories
        $chartOfAccounts = ChartOfAccount::all()->keyBy('id');

        return Inertia::render('Requisitions/ShowRequisition', [
            'requisition' => $requisition,
            'history' => $history,
            'chartOfAccounts' => $chartOfAccounts,
            'canApprove' => ($isHOD || $isApprover) && $requisition->status === 'pending_approval' && !$isRequester,
            'canReject' => ($isHOD || $isApprover) && $requisition->status === 'pending_approval' && !$isRequester,
            'canEdit' => $isRequester && $requisition->status === 'rejected',
            'canAttachFiles' => $isRequester || $isHOD || $isApprover,
            'attachmentUIState' => $requisition->getAttachmentUIState(),
        ]);
    }

    /**
     * Get attachment UI state for the requisition.
     */
    public function getAttachmentUIState(Requisition $requisition): JsonResponse
    {
        $this->authorize('view', $requisition);

        $uiState = $requisition->getAttachmentUIState();

        // Add user permission check
        $uiState['user_can_attach'] = auth()->user()->can('attachFiles', $requisition);

        return response()->json($uiState);
    }

    /**
     * Approve a requisition.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve(Request $request, int $id): RedirectResponse
    {
        try {
            $request->validate([
                'comments' => 'required|string',
            ]);

            $requisition = $this->requisitionService->getRequisitionById($id);
            if (!$requisition) {
                abort(404, 'Requisition not found');
            }

            $user = Auth::user();

            // Check if user is authorized to approve
            $isHOD = Department::where('id', $requisition->department_id)
                ->where('hod_user_id', $user->id)
                ->exists();

            $isApprover = false;
            if ($user->hasPermissionTo('approver')) {
                // Check if user is directly assigned as an approver in the current step
                if ($requisition->current_approval_step_id) {
                    $isDirectApprover = DB::table('approval_workflow_steps')
                        ->where('id', $requisition->current_approval_step_id)
                        ->where('approver_user_id', $user->id)
                        ->exists();

                    if ($isDirectApprover) {
                        $isApprover = true;
                    } else {
                        // Check if user has a role that is assigned to the current step
                        $stepRoleId = DB::table('approval_workflow_steps')
                            ->where('id', $requisition->current_approval_step_id)
                            ->whereNull('approver_user_id')
                            ->value('role_id');

                        if ($stepRoleId && $user->roles()->where('id', $stepRoleId)->exists()) {
                            $isApprover = true;
                        }
                    }
                }
            }

            // Prevent approving own requisition
            $isRequester = $requisition->requester_user_id === $user->id;

            if ((!$isHOD && !$isApprover) || $isRequester) {
                abort(403, 'You are not authorized to approve this requisition');
            }

            // Get current step before approval
            $currentStep = null;
            if ($requisition->current_approval_step_id) {
                $currentStep = \App\Models\ApprovalWorkflowStep::find($requisition->current_approval_step_id);
            }

            $this->requisitionService->approveRequisition($id, $request->input('comments'));

            // Reload requisition to get updated status and step
            $updatedRequisition = $this->requisitionService->getRequisitionById($id);

            // Trigger appropriate notification events
            // Note: RequisitionCompleted event is now fired from moveToDisbursal() as TransactionCreated
            if ($updatedRequisition->status === 'approved') {
                // Requisition is fully approved - TransactionCreated event will be fired from moveToDisbursal()
                // No need to fire RequisitionCompleted here anymore
            } elseif ($updatedRequisition->current_approval_step_id && $currentStep) {
                // Move to next step
                $nextStep = \App\Models\ApprovalWorkflowStep::find($updatedRequisition->current_approval_step_id);
                if ($nextStep) {
                    event(new RequisitionStepApproved($updatedRequisition, $nextStep, $currentStep));
                }
            }

            // Redirect to approvals page with approved status to show the approved requisition
            $departmentId = $requisition->department_id;
            return redirect()->route('requisitions.approvals', [
                'department' => $departmentId,
                'status' => 'approved'
            ])->with('success', 'Requisition approved successfully');
        } catch (\Exception $e) {
            \Log::error('Error approving requisition: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while approving the requisition: ' . $e->getMessage());
        }
    }

    /**
     * Reject a requisition.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(Request $request, int $id): RedirectResponse
    {
        try {
            $request->validate([
                'comments' => 'required|string',
            ]);

            $requisition = $this->requisitionService->getRequisitionById($id);
            if (!$requisition) {
                abort(404, 'Requisition not found');
            }

            $user = Auth::user();

            // Check if user is authorized to reject
            $isHOD = Department::where('id', $requisition->department_id)
                ->where('hod_user_id', $user->id)
                ->exists();

            $isApprover = false;
            if ($user->hasPermissionTo('approver')) {
                // Check if user is directly assigned as an approver in the current step
                if ($requisition->current_approval_step_id) {
                    $isDirectApprover = DB::table('approval_workflow_steps')
                        ->where('id', $requisition->current_approval_step_id)
                        ->where('approver_user_id', $user->id)
                        ->exists();

                    if ($isDirectApprover) {
                        $isApprover = true;
                    } else {
                        // Check if user has a role that is assigned to the current step
                        $stepRoleId = DB::table('approval_workflow_steps')
                            ->where('id', $requisition->current_approval_step_id)
                            ->whereNull('approver_user_id')
                            ->value('role_id');

                        if ($stepRoleId && $user->roles()->where('id', $stepRoleId)->exists()) {
                            $isApprover = true;
                        }
                    }
                }
            }

            // Prevent rejecting own requisition
            $isRequester = $requisition->requester_user_id === $user->id;

            if ((!$isHOD && !$isApprover) || $isRequester) {
                abort(403, 'You are not authorized to reject this requisition');
            }

            // Get current step before rejection
            $currentStep = null;
            if ($requisition->current_approval_step_id) {
                $currentStep = \App\Models\ApprovalWorkflowStep::find($requisition->current_approval_step_id);
            }

            $this->requisitionService->rejectRequisition($id, $request->input('comments'));

            // Trigger notification event for requisition rejection
            if ($currentStep) {
                event(new RequisitionDeclined($requisition, $currentStep, $request->input('comments')));
            }

            // Redirect to approvals page with rejected status to show the rejected requisition
            $departmentId = $requisition->department_id;
            return redirect()->route('requisitions.approvals', [
                'department' => $departmentId,
                'status' => 'rejected'
            ])->with('success', 'Requisition rejected successfully');
        } catch (\Exception $e) {
            \Log::error('Error rejecting requisition: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while rejecting the requisition: ' . $e->getMessage());
        }
    }

    /**
     * Display a listing of requisitions that need approval.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response|\Illuminate\Http\RedirectResponse
     */
    public function approvals(Request $request)
    {
        $user = Auth::user();
        $departmentId = $request->input('department');
        $requisitionId = $request->input('requisition');

        // Check if user has the approver permission
        $hasApproverPermission = $user->hasPermissionTo('approver');

        // Get departments where user is HOD
        $hodDepartments = Department::where('hod_user_id', $user->id)->get();

        // If no department is selected and user is HOD of some departments,
        // redirect to first department
        if (!$departmentId && !$hodDepartments->isEmpty()) {
            return redirect()->route('requisitions.approvals', [
                'department' => $hodDepartments->first()->id
            ]);
        }

        // Get the selected department
        $department = null;
        if ($departmentId) {
            $department = Department::find($departmentId);
            if (!$department) {
                return redirect()->route('dashboard')
                    ->with('error', 'Department not found');
            }
        }

        // If user is neither HOD nor has approver permission, they can't access this page
        if ($hodDepartments->isEmpty() && !$hasApproverPermission) {
            return redirect()->route('dashboard')
                ->with('error', 'You do not have permission to view this page');
        }

        $selectedRequisition = null;
        if ($requisitionId && $department) {
            // If user is an HOD, use the standard method
            if (!empty($hodDepartments) && $hodDepartments->contains('id', $department->id)) {
                $selectedRequisition = $this->requisitionService->getRequisitionForApprovalReview(
                    $requisitionId,
                    $department->id,
                    $user->id
                );
            }
            // If user is an approver but not HOD
            elseif ($hasApproverPermission) {
                $requisition = $this->requisitionService->getRequisitionById($requisitionId);

                if ($requisition && $requisition->department_id == $department->id) {
                    // Check if user is an approver for this requisition
                    $isApprover = false;

                    // Check if user is directly assigned as an approver in the current step
                    if ($requisition->current_approval_step_id) {
                        $isDirectApprover = DB::table('approval_workflow_steps')
                            ->where('id', $requisition->current_approval_step_id)
                            ->where('approver_user_id', $user->id)
                            ->exists();

                        if ($isDirectApprover) {
                            $isApprover = true;
                        } else {
                            // Check if user has a role that is assigned to the current step
                            $stepRoleId = DB::table('approval_workflow_steps')
                                ->where('id', $requisition->current_approval_step_id)
                                ->whereNull('approver_user_id')
                                ->value('role_id');

                            if ($stepRoleId && $user->roles()->where('id', $stepRoleId)->exists()) {
                                $isApprover = true;
                            }
                        }
                    }

                    // Check if user has been involved in any approval action for this requisition
                    if (!$isApprover) {
                        $hasApprovalHistory = DB::table('requisition_approvals')
                            ->where('requisition_id', $requisition->id)
                            ->where('approver_user_id', $user->id)
                            ->exists();

                        if ($hasApprovalHistory) {
                            $isApprover = true;
                        }
                    }

                    if ($isApprover) {
                        // Load relationships including attachments for approvers to view
                        $requisition->load(['requester:id,first_name,last_name,email', 'items', 'department', 'attachments.uploader']);

                        $selectedRequisition = $requisition;
                    }
                }
            }

            if (!$selectedRequisition) {
                // Instead of redirecting, just clear the requisition selection
                $requisitionId = null;
            }
        }

        // Get filters from request
        $filters = [
            'search' => $request->input('search'),
            'status' => $request->input('status', 'pending_approval'),
            'sort' => $request->input('sort', 'created_at'),
            'direction' => $request->input('direction', 'desc'),
        ];

        // Get requisitions using the service
        $requisitions = new \Illuminate\Pagination\LengthAwarePaginator([], 0, 10);
        if (!empty($hodDepartments) && $department && $hodDepartments->contains('id', $department->id)) {
            // If user is HOD for this department, use the standard method
            $requisitions = $this->requisitionService->getRequisitionsForApproval(
                $user->id,
                $department->id,
                $filters,
                10
            );
        } elseif ($hasApproverPermission && $department) {
            // If user is an approver but not HOD, get requisitions they can approve
            $requisitions = $this->requisitionService->getRequisitionsForUserApproval(
                $user->id,
                $filters,
                10,
                $department->id
            );
        }

        // Get chart of accounts for displaying item categories
        $chartOfAccounts = ChartOfAccount::all()->keyBy('id');

        return Inertia::render('Requisitions/Approvals', [
            'requisitions' => $requisitions,
            'department' => $department,
            'userDepartments' => $hodDepartments,
            'selectedRequisition' => $selectedRequisition,
            'chartOfAccounts' => $chartOfAccounts,
            'filters' => [
                'department' => $departmentId,
                'search' => $filters['search'],
                'status' => $filters['status'],
                'sort' => $filters['sort'],
                'direction' => $filters['direction'],
            ],
        ]);
    }

    /**
     * Return a requisition for revision.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function returnForRevision(Request $request, int $id): RedirectResponse
    {
        try {
            $request->validate([
                'comments' => 'required|string|max:500',
            ]);

            // Get requisition before returning for revision
            $requisition = $this->requisitionService->getRequisitionById($id);

            $this->requisitionService->returnRequisitionForRevision($id, $request->input('comments'));

            // Trigger notification event for requisition returned for revision
            if ($requisition) {
                // Get current step
                $currentStep = null;
                if ($requisition->current_approval_step_id) {
                    $currentStep = \App\Models\ApprovalWorkflowStep::find($requisition->current_approval_step_id);
                }

                if ($currentStep) {
                    event(new RequisitionRevisionRequested($requisition, $currentStep, $request->input('comments')));
                }
            }

            return redirect()->route('requisitions.approvals', [
                'department' => $request->input('department_id')
            ])->with('success', 'Requisition returned for revision successfully');
        } catch (\Exception $e) {
            \Log::error('Error returning requisition for revision: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while returning the requisition for revision: ' . $e->getMessage());
        }
    }

    /**
     * Resubmit a requisition that was returned for revision.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resubmit(Request $request, int $id): RedirectResponse
    {
        try {
            $validated = $request->validate([
                'purpose' => 'required|string',
                'notes' => 'nullable|string',
                'comments' => 'required|string|max:500',
            ]);

            $this->requisitionService->resubmitRequisition(
                $id,
                [
                    'purpose' => $validated['purpose'],
                    'notes' => $validated['notes'] ?? null,
                ],
                $validated['comments']
            );

            // Get updated requisition and trigger submission event
            $updatedRequisition = $this->requisitionService->getRequisitionById($id);
            if ($updatedRequisition && $updatedRequisition->current_approval_step_id) {
                $currentStep = \App\Models\ApprovalWorkflowStep::find($updatedRequisition->current_approval_step_id);
                if ($currentStep) {
                    event(new RequisitionSubmitted($updatedRequisition, $currentStep));
                }
            }

            return redirect()->route('requisitions.show', ['id' => $id])
                ->with('success', 'Requisition resubmitted successfully');
        } catch (\Exception $e) {
            \Log::error('Error resubmitting requisition: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while resubmitting the requisition: ' . $e->getMessage());
        }
    }

    /**
     * Display requisitions that need the user's approval.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function myApprovals(Request $request): Response
    {
        $user = Auth::user();

        // Check if user has the approver permission
        if (!$user->hasPermissionTo('approver')) {
            abort(403, 'You do not have permission to view this page.');
        }

        // Get filters from request
        $filters = [
            'search' => $request->input('search'),
            'sort' => $request->input('sort', 'created_at'),
            'direction' => $request->input('direction', 'desc'),
        ];

        // Get requisitions where the user is an approver
        $requisitions = $this->requisitionService->getRequisitionsForUserApproval(
            $user->id,
            $filters,
            10
        );

        // Get chart of accounts for displaying item categories
        $chartOfAccounts = ChartOfAccount::all()->keyBy('id');

        return Inertia::render('Requisitions/MyApprovals', [
            'requisitions' => $requisitions,
            'chartOfAccounts' => $chartOfAccounts,
            'filters' => $filters,
        ]);
    }

    /**
     * Display all requisitions where the user is or was an approver.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function myApprovalHistory(Request $request): Response
    {
        $user = Auth::user();

        // Check if user has the approver permission
        if (!$user->hasPermissionTo('approver')) {
            abort(403, 'You do not have permission to view this page.');
        }

        // Get filters from request
        $filters = [
            'search' => $request->input('search'),
            'sort' => $request->input('sort', 'created_at'),
            'direction' => $request->input('direction', 'desc'),
            'status' => $request->input('status'),
        ];

        // Get all requisitions where the user is or was an approver
        $requisitions = $this->requisitionService->getAllUserApprovalRequisitions(
            $user->id,
            $filters,
            10
        );

        // Get chart of accounts for displaying item categories
        $chartOfAccounts = ChartOfAccount::all()->keyBy('id');

        return Inertia::render('Requisitions/MyApprovalHistory', [
            'requisitions' => $requisitions,
            'chartOfAccounts' => $chartOfAccounts,
            'filters' => $filters,
        ]);
    }

    /**
     * Show the form for editing a rejected requisition.
     *
     * @param  int  $id
     * @return \Inertia\Response
     */
    public function editRejected(int $id): Response
    {
        $user = Auth::user();
        $requisition = $this->requisitionService->getRequisitionById($id);

        if (!$requisition) {
            abort(404, 'Requisition not found');
        }

        // Check if user is the requester
        if ($requisition->requester_user_id !== $user->id) {
            abort(403, 'You are not authorized to edit this requisition');
        }

        // Check if requisition is rejected
        if ($requisition->status !== 'rejected') {
            abort(403, 'Only rejected requisitions can be edited');
        }

        // Load relationships
        $requisition->load(['department', 'items', 'approvals']);

        // Get rejection comments
        $rejectionRecord = $requisition->approvals()
            ->where('action', 'rejected')
            ->orderBy('created_at', 'desc')
            ->first();

        $rejectionComments = $rejectionRecord ? $rejectionRecord->comments : null;

        // Get chart of accounts based on user role
        $chartOfAccounts = $this->getFilteredChartOfAccounts($user, $requisition->organization_id);

        // Get user departments for department selection
        $userDepartments = $user->departments;

        return Inertia::render('Requisitions/EditRejected', [
            'requisition' => $requisition,
            'rejectionComments' => $rejectionComments,
            'chartOfAccounts' => $chartOfAccounts,
            'userDepartments' => $userDepartments,
        ]);
    }

    /**
     * Update a rejected requisition and resubmit it.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateRejected(Request $request, int $id): RedirectResponse
    {
        try {
            $validated = $request->validate([
                'purpose' => 'required|string',
                'notes' => 'nullable|string',
                'comments' => 'required|string|max:500',
                'requisition_items' => 'required|array|min:1',
                'requisition_items.*.description' => 'required|string',
                'requisition_items.*.quantity' => 'required|numeric|min:1',
                'requisition_items.*.unit_price' => 'required|numeric|min:0',
                'requisition_items.*.chart_of_account_id' => 'required|exists:chart_of_accounts,id',
            ]);

            $this->requisitionService->editRejectedRequisition(
                $id,
                [
                    'purpose' => $validated['purpose'],
                    'notes' => $validated['notes'] ?? null,
                    'requisition_items' => $validated['requisition_items'],
                ],
                $validated['comments']
            );

            // Get updated requisition and trigger submission event
            $updatedRequisition = $this->requisitionService->getRequisitionById($id);
            if ($updatedRequisition && $updatedRequisition->current_approval_step_id) {
                $currentStep = \App\Models\ApprovalWorkflowStep::find($updatedRequisition->current_approval_step_id);
                if ($currentStep) {
                    event(new RequisitionSubmitted($updatedRequisition, $currentStep));
                }
            }

            return redirect()->route('requisitions.show', $id)
                ->with('success', 'Requisition updated and resubmitted successfully');
        } catch (\Exception $e) {
            \Log::error('Error updating rejected requisition: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while updating the requisition: ' . $e->getMessage());
        }
    }

    /**
     * Get filtered chart of accounts based on user role
     *
     * @param \App\Models\User $user
     * @param int $organizationId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getFilteredChartOfAccounts($user, int $organizationId)
    {
        // Check if user is finance manager, organization admin, or platform admin
        if ($user->is_platform_admin ||
            $user->hasRole('Finance Manager') ||
            $user->hasRole('Finance Admin') ||
            $user->hasRole('Organization Admin')) {

            // Return all chart of accounts for the organization
            return ChartOfAccount::getFinanceFilteredAccounts($organizationId);
        }

        // For regular employees, return only descendant accounts (leaf nodes)
        return ChartOfAccount::getEmployeeFilteredAccounts($organizationId);
    }
}
