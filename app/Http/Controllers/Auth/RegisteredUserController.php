<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Models\Organization;
use App\Providers\RouteServiceProvider;
use App\Rules\StrongPassword;
use App\Services\EmailVerificationService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Route;
use App\Models\UserOrganization;
use App\Models\UserBranch;
use App\Models\UserDepartment;
use App\Models\Branch;
use App\Models\Department;
use EragLaravelDisposableEmail\Rules\DisposableEmailRule;

class RegisteredUserController extends Controller
{
    protected $verificationService;

    public function __construct(EmailVerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }

    /**
     * Show the registration page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/register', [
            'canResetPassword' => Route::has('password.request'),
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * Handle an incoming registration request.
     * Store registration data temporarily and send verification code.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // Build email validation rules
        $emailRules = [
            'required',
            'string',
            'lowercase',
            'email',
            'max:255',
            'unique:' . User::class,
        ];

        // Add disposable email validation if package is available
        if (class_exists('EragLaravelDisposableEmail\Rules\DisposableEmailRule')) {
            $emailRules[] = new DisposableEmailRule();
        }

        $validated = $request->validate([
            // User details validation
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => $emailRules,
            'phone' => 'nullable|string|max:20',
            'password' => ['required', 'confirmed', new StrongPassword()],
            'org_name' => 'required|string|max:255',
            'org_status' => 'nullable|in:active,inactive',
        ], [
            // Custom error messages
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
        ]);

        try {
            // Store registration data temporarily and send verification code
            $this->verificationService->sendVerificationCode(
                $validated['email'],
                $validated,
                $request->ip()
            );

            // Store pending verification data in session
            Session::put('pending_email_verification', [
                'email' => $validated['email'],
                'registration_data' => $validated,
            ]);

            return redirect()->route('email.verification.code.show')
                ->with('status', 'A verification code has been sent to your email address.');
        } catch (\Exception $e) {
            return back()->withErrors(['email' => $e->getMessage()])->withInput();
        }
    }
}
