<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\EmailVerificationService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class EmailVerificationCodeController extends Controller
{
    protected $verificationService;

    public function __construct(EmailVerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }

    /**
     * Show the email verification code form
     */
    public function show(Request $request): Response|RedirectResponse
    {
        // Check if we have pending verification data in session
        $pendingVerification = Session::get('pending_email_verification');

        if (!$pendingVerification) {
            return redirect()->route('register')->withErrors([
                'email' => 'No pending email verification found. Please register again.'
            ]);
        }

        $canRequestNew = $this->verificationService->canRequestNewCode($pendingVerification['email']);
        $remainingAttempts = $this->verificationService->getRemainingAttempts($pendingVerification['email']);

        return Inertia::render('auth/verify-email-code', [
            'email' => $pendingVerification['email'],
            'canRequestNew' => $canRequestNew,
            'remainingAttempts' => $remainingAttempts,
            'status' => Session::get('status'),
        ]);
    }

    /**
     * Verify the email code
     */
    public function verify(Request $request): RedirectResponse
    {
        $request->validate([
            'code' => 'required|string|size:6|regex:/^[0-9]{6}$/',
        ], [
            'code.required' => 'Verification code is required.',
            'code.size' => 'Verification code must be exactly 6 digits.',
            'code.regex' => 'Verification code must contain only numbers.',
        ]);

        $pendingVerification = Session::get('pending_email_verification');

        if (!$pendingVerification) {
            return redirect()->route('register')->withErrors([
                'email' => 'No pending email verification found. Please register again.'
            ]);
        }

        try {
            $result = $this->verificationService->verifyCodeAndCreateUser(
                $pendingVerification['email'],
                $request->code
            );

            if ($result['success']) {
                // Clear pending verification from session
                Session::forget('pending_email_verification');

                // Log the user in
                Auth::login($result['user']);

                // Redirect to organization setup wizard for new organizations
                return redirect()->route('organization-setup.index')->with('success', $result['message']);
            } else {
                return back()->withErrors(['code' => $result['message']]);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['code' => 'An error occurred during verification. Please try again.']);
        }
    }

    /**
     * Resend verification code
     */
    public function resend(Request $request): RedirectResponse
    {
        $pendingVerification = Session::get('pending_email_verification');

        if (!$pendingVerification) {
            return redirect()->route('register')->withErrors([
                'email' => 'No pending email verification found. Please register again.'
            ]);
        }

        try {
            if (!$this->verificationService->canRequestNewCode($pendingVerification['email'])) {
                return back()->withErrors([
                    'code' => 'Too many verification code requests. Please try again later.'
                ]);
            }

            $this->verificationService->sendVerificationCode(
                $pendingVerification['email'],
                $pendingVerification['registration_data'],
                $request->ip()
            );

            return back()->with('status', 'A new verification code has been sent to your email.');

        } catch (\Exception $e) {
            return back()->withErrors(['code' => $e->getMessage()]);
        }
    }
}
