<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

class AppController extends Controller
{
    /**
     * Display the welcome page.
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        return Inertia::render('welcome', [
            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'laravelVersion' => Application::VERSION,
            'phpVersion' => PHP_VERSION,
        ]);
    }

    /**
     * Get the count of pending approvals for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPendingApprovalsCount(Request $request)
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo('approver')) {
            return response()->json(['count' => 0]);
        }

        $requisitionService = app(\Src\Requisition\Application\Services\RequisitionService::class);
        $count = $requisitionService->getPendingApprovalsCount($user->id);

        return response()->json(['count' => $count]);
    }

    /**
     * Get the counts of requisitions by status for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRequisitionStatusCounts(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'pending' => 0,
                'approved' => 0,
                'rejected' => 0
            ]);
        }

        $requisitionService = app(\Src\Requisition\Application\Services\RequisitionService::class);

        // Get counts for requisitions created by the user
        $pendingCount = $requisitionService->getRequisitionCountByStatus($user->id, 'pending_approval');
        $approvedCount = $requisitionService->getRequisitionCountByStatus($user->id, 'approved');
        $rejectedCount = $requisitionService->getRequisitionCountByStatus($user->id, 'rejected');

        // Get counts for requisitions where the user is an approver
        $pendingApprovalCount = 0;
        if ($user->hasPermissionTo('approver')) {
            $pendingApprovalCount = $requisitionService->getPendingApprovalsCount($user->id);
        }

        return response()->json([
            'pending' => $pendingCount,
            'approved' => $approvedCount,
            'rejected' => $rejectedCount,
            'pendingApproval' => $pendingApprovalCount
        ]);
    }
}
