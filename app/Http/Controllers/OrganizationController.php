<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use EragLaravelDisposableEmail\Rules\DisposableEmailRule;
use Src\UserManagement\Application\Services\UserCreationService;
use Src\UserManagement\Application\DTOs\CreateOrganizationAdminDTO;

class OrganizationController extends Controller
{
    public function __construct(
        private readonly UserCreationService $userCreationService
    ) {}

    /**
     * Display a listing of the organizations.
     */
    public function index(): Response
    {
        $organizations = Organization::all();

        return Inertia::render('organizations/organizations-index', [
            'organizations' => $organizations,
        ]);
    }

    /**
     * Show the form for creating a new organization.
     */
    public function create(): Response
    {
        return Inertia::render('organizations/organizations-create');
    }

    /**
     * Store a newly created organization in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        // Build email validation rules
        $contactEmailRules = [
            'required',
            'email',
            'max:255',
        ];

        $adminEmailRules = [
            'required',
            'email',
            'unique:users,email',
        ];

        // Add disposable email validation if package is available
        if (class_exists('EragLaravelDisposableEmail\Rules\DisposableEmailRule')) {
            $contactEmailRules[] = new DisposableEmailRule();
            $adminEmailRules[] = new DisposableEmailRule();
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'contact_email' => $contactEmailRules,
            'contact_phone' => 'required|string|max:20',
            'address' => 'nullable|string|max:500',
            'mpesa_account_details' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',

            // Admin user details
            'admin_first_name' => 'nullable|string|max:255',
            'admin_last_name' => 'nullable|string|max:255',
            'admin_email' => $adminEmailRules,
            'admin_phone' => 'nullable|string|max:20',
            'admin_username' => 'required|string|unique:users,username|max:255',
            'admin_password' => 'required|string|min:8',
        ], [
            // Custom error messages
            'contact_email.required' => 'Organization contact email is required.',
            'contact_email.email' => 'Please enter a valid contact email address.',
            'admin_email.required' => 'Admin email address is required.',
            'admin_email.email' => 'Please enter a valid admin email address.',
            'admin_email.unique' => 'This admin email address is already registered.',
        ]);

        // Create the organization
        $organization = Organization::create([
            'name' => $validated['name'],
            'contact_email' => $validated['contact_email'],
            'contact_phone' => $validated['contact_phone'],
            'address' => $validated['address'],
            'mpesa_account_details' => $validated['mpesa_account_details'],
            'status' => $validated['status'],
        ]);

        // Create the organization admin user using the centralized service
        try {
            $adminDTO = CreateOrganizationAdminDTO::fromArray([
                'admin_username' => $validated['admin_username'],
                'admin_first_name' => $validated['admin_first_name'],
                'admin_last_name' => $validated['admin_last_name'],
                'admin_email' => $validated['admin_email'],
                'admin_phone' => $validated['admin_phone'],
                'admin_password' => $validated['admin_password'],
                'organization_id' => $organization->id,
                'send_notification' => false, // Don't send notification for organization creation
                'auto_verify_email' => true,
                'create_default_structures' => false, // Don't create default structures - let wizard handle this
            ]);

            $this->userCreationService->createOrganizationAdmin($adminDTO);
        } catch (\Exception $e) {
            // If user creation fails, delete the organization and return error
            $organization->delete();
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create organization admin: ' . $e->getMessage()])
                ->withInput();
        }

        // Note: Chart of accounts are automatically created via Organization model event

        // Redirect to the organization setup wizard
        return redirect()->route('organization-setup.index')
            ->with('success', 'Organization created successfully. Please complete the setup process.');
    }

    /**
     * Display the specified organization.
     */
    public function show(Organization $organization): Response
    {
        $organization->load(['branches', 'departments', 'approvalWorkflows']);

        $admins = User::whereHas('roles', function ($query) use ($organization) {
            $query->where('name', 'Organization Admin')
                ->where('organization_id', $organization->id);
        })->get();

        return Inertia::render('organizations/organizations-show', [
            'organization' => $organization,
            'admins' => $admins,
            'workflows' => $organization->approvalWorkflows()->with('steps.role')->get(),
        ]);
    }

    /**
     * Show the form for editing the specified organization.
     */
    public function edit(Organization $organization): Response
    {
        $admins = User::whereHas('roles', function ($query) use ($organization) {
            $query->where('name', 'Organization Admin')
                ->where('organization_id', $organization->id);
        })->get();

        return Inertia::render('organizations/organizations-edit', [
            'organization' => $organization,
            'admins' => $admins,
        ]);
    }

    /**
     * Update the specified organization in storage.
     */
    public function update(Request $request, Organization $organization): RedirectResponse
    {
        // Build email validation rules
        $contactEmailRules = [
            'required',
            'email',
            'max:255',
        ];

        // Add disposable email validation if package is available
        if (class_exists('EragLaravelDisposableEmail\Rules\DisposableEmailRule')) {
            $contactEmailRules[] = new DisposableEmailRule();
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'contact_email' => $contactEmailRules,
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'mpesa_account_details' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
        ], [
            // Custom error messages
            'contact_email.required' => 'Organization contact email is required.',
            'contact_email.email' => 'Please enter a valid contact email address.',
        ]);

        $organization->update($validated);

        return redirect()->route('organizations.index')
            ->with('success', 'Organization updated successfully.');
    }

    /**
     * Remove the specified organization from storage.
     */
    public function destroy(Organization $organization): RedirectResponse
    {
        // This is a dangerous operation that would cascade to branches, departments, etc.
        // Consider implementing a soft delete or archiving mechanism instead

        // Delete associated workflows first
        $organization->approvalWorkflows()->delete();

        $organization->delete();

        return redirect()->route('organizations.index')
            ->with('success', 'Organization deleted successfully.');
    }


}