<?php

namespace App\Http\Controllers;

use App\Models\Attachment;
use App\Models\Requisition;
use App\Models\Transaction;
use Src\Attachment\Application\Services\AttachmentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Inertia\Inertia;

class AttachmentController extends Controller
{
    use AuthorizesRequests;

    protected AttachmentService $attachmentService;

    public function __construct(AttachmentService $attachmentService)
    {
        $this->attachmentService = $attachmentService;
    }

    /**
     * Upload files to a requisition.
     */
    public function uploadToRequisition(Request $request, int $requisitionId): JsonResponse|RedirectResponse
    {
        $requisition = Requisition::findOrFail($requisitionId);

        // Check if user can attach files to this requisition
        $this->authorize('attachFiles', $requisition);

        $request->validate([
            'files' => 'required|array|max:5',
            'files.*' => 'required|file|max:10240', // 10MB max per file
            'descriptions' => 'nullable|array',
            'descriptions.*' => 'nullable|string|max:500',
            'uploaded_at_step' => 'nullable|string|max:100',
        ]);

        try {
            $files = $request->file('files');
            $descriptions = $request->input('descriptions', []);
            $uploadedAtStep = $request->input('uploaded_at_step', $requisition->status);

            // Validate each file
            foreach ($files as $file) {
                $errors = $this->attachmentService->validateFile($file);
                if (!empty($errors)) {
                    throw ValidationException::withMessages(['files' => $errors]);
                }
            }

            // Upload files
            $attachments = $this->attachmentService->uploadMultipleFiles(
                $files,
                $requisition,
                $descriptions,
                true, // is_evidence
                $uploadedAtStep
            );

            // Load relationships for response
            foreach ($attachments as $attachment) {
                $attachment->load('uploader');
            }

            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return redirect()->back()->with('success', 'Files uploaded successfully');
            }

            return response()->json([
                'message' => 'Files uploaded successfully',
                'attachments' => $attachments,
            ], 201);

        } catch (\Exception $e) {
            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return redirect()->back()->withErrors(['upload' => 'Failed to upload files: ' . $e->getMessage()]);
            }

            return response()->json([
                'message' => 'Failed to upload files',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload files to a transaction.
     */
    public function uploadToTransaction(Request $request, int $transactionId): JsonResponse|RedirectResponse
    {
        $transaction = Transaction::findOrFail($transactionId);

        // Check if user can attach files to this transaction
        $this->authorize('attachFiles', $transaction);

        $request->validate([
            'files' => 'required|array|max:5',
            'files.*' => 'required|file|max:10240', // 10MB max per file
            'descriptions' => 'nullable|array',
            'descriptions.*' => 'nullable|string|max:500',
            'uploaded_at_step' => 'nullable|string|max:100',
        ]);

        try {
            $files = $request->file('files');
            $descriptions = $request->input('descriptions', []);
            $uploadedAtStep = $request->input('uploaded_at_step', $transaction->status);

            // Validate each file
            foreach ($files as $file) {
                $errors = $this->attachmentService->validateFile($file);
                if (!empty($errors)) {
                    throw ValidationException::withMessages(['files' => $errors]);
                }
            }

            // Upload files
            $attachments = $this->attachmentService->uploadMultipleFiles(
                $files,
                $transaction,
                $descriptions,
                true, // is_evidence
                $uploadedAtStep
            );

            // Load relationships for response
            foreach ($attachments as $attachment) {
                $attachment->load('uploader');
            }

            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return redirect()->back()->with('success', 'Files uploaded successfully');
            }

            return response()->json([
                'message' => 'Files uploaded successfully',
                'attachments' => $attachments,
            ], 201);

        } catch (\Exception $e) {
            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return redirect()->back()->withErrors(['upload' => 'Failed to upload files: ' . $e->getMessage()]);
            }

            return response()->json([
                'message' => 'Failed to upload files',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download an attachment.
     */
    public function download(int $attachmentId): Response
    {
        $attachment = Attachment::findOrFail($attachmentId);

        // Check if user can download this attachment
        $this->authorize('download', $attachment);

        $fileContent = $this->attachmentService->getFileContent($attachment->id);

        if (!$fileContent) {
            abort(404, 'File not found');
        }

        return response($fileContent)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Disposition', 'attachment; filename="' . $attachment->original_name . '"')
            ->header('Content-Length', $attachment->file_size);
    }

    /**
     * View an attachment inline.
     */
    public function view(int $attachmentId): Response
    {
        $attachment = Attachment::findOrFail($attachmentId);

        // Authorization
        $this->authorize('download', $attachment);

        $fileContent = $this->attachmentService->getFileContent($attachment->id);

        if (!$fileContent) {
            abort(404, 'File not found');
        }

        $response = response($fileContent)
            ->header('Content-Type', $attachment->mime_type)
            ->header('Content-Length', $attachment->file_size)
            ->header('X-Frame-Options', 'SAMEORIGIN')
            ->header('Cache-Control', 'private, max-age=3600')
            ->header('X-Content-Type-Options', 'nosniff');

        // For PDFs, ensure inline viewing without filename to prevent download
        if ($attachment->mime_type === 'application/pdf') {
            $response->header('Content-Disposition', 'inline');
        } else {
            $response->header('Content-Disposition', 'inline; filename="' . $attachment->original_name . '"');
        }

        return $response;
    }


    /**
     * Delete an attachment.
     */
    public function destroy(int $attachmentId): JsonResponse|RedirectResponse
    {
        $attachment = Attachment::findOrFail($attachmentId);

        // Check if user can delete this attachment
        $this->authorize('delete', $attachment);

        try {
            $deleted = $this->attachmentService->deleteAttachment($attachment->id);

            if ($deleted) {
                // Check if this is an Inertia request
                if (request()->header('X-Inertia')) {
                    return redirect()->back()->with('success', 'Attachment deleted successfully');
                }

                return response()->json([
                    'message' => 'Attachment deleted successfully',
                ]);
            } else {
                // Check if this is an Inertia request
                if (request()->header('X-Inertia')) {
                    return redirect()->back()->withErrors(['delete' => 'Failed to delete attachment']);
                }

                return response()->json([
                    'message' => 'Failed to delete attachment',
                ], 500);
            }
        } catch (\Exception $e) {
            // Check if this is an Inertia request
            if (request()->header('X-Inertia')) {
                return redirect()->back()->withErrors(['delete' => 'Failed to delete attachment: ' . $e->getMessage()]);
            }

            return response()->json([
                'message' => 'Failed to delete attachment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get attachments for a requisition.
     */
    public function getRequisitionAttachments(int $requisitionId): JsonResponse
    {
        $requisition = Requisition::findOrFail($requisitionId);

        // Check if user can view this requisition
        $this->authorize('view', $requisition);

        $attachments = $requisition->attachments()->with('uploader')->get();

        return response()->json([
            'attachments' => $attachments,
        ]);
    }

    /**
     * Get attachments for a transaction.
     */
    public function getTransactionAttachments(int $transactionId): JsonResponse
    {
        $transaction = Transaction::findOrFail($transactionId);

        // Check if user can view this transaction
        $this->authorize('view', $transaction);

        $attachments = $transaction->attachments()->with('uploader')->get();

        return response()->json([
            'attachments' => $attachments,
        ]);
    }
}
