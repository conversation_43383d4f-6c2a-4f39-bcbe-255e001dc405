<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class NotificationController extends Controller
{
    public function index(Request $request): Response
    {
        $user = $request->user();

        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->orderBy('id', 'desc')
            ->paginate(15);

        $unreadCount = $user->unreadNotifications()->count();

        return Inertia::render('notifications/notifications-index', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
        ]);
    }

    public function markAsRead(Request $request, string $id): RedirectResponse
    {
        $notification = $request->user()->notifications()->findOrFail($id);
        $notification->markAsRead();

        // Redirect to action URL if it exists
        $actionUrl = $notification->data['action_url'] ?? null;
        if ($actionUrl) {
            return redirect($actionUrl);
        }

        return back();
    }

    public function markAsReadAjax(Request $request, string $id)
    {
        $notification = $request->user()->notifications()->findOrFail($id);
        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
            'unread_count' => $request->user()->unreadNotifications()->count()
        ]);
    }

    public function markAllAsRead(Request $request): RedirectResponse
    {
        $request->user()->unreadNotifications->markAsRead();

        return back()->with('success', 'All notifications marked as read.');
    }

    public function getUnreadCount(Request $request)
    {
        return response()->json([
            'count' => $request->user()->unreadNotifications()->count()
        ]);
    }

    public function destroy(Request $request, string $id): RedirectResponse
    {
        $notification = $request->user()->notifications()->findOrFail($id);
        $notification->delete();

        return back()->with('success', 'Notification deleted.');
    }

    /**
     * Delete all notifications for the authenticated user
     */
    public function deleteAll(Request $request): RedirectResponse
    {
        $user = $request->user();
        $deletedCount = $user->notifications()->delete();

        return back()->with('success', "All {$deletedCount} notifications deleted.");
    }

    /**
     * Get latest notifications for enhanced polling
     */
    public function getLatest(Request $request)
    {
        $user = $request->user();
        $since = $request->get('since');
        $limit = $request->get('limit', 10);

        // Validate timestamp
        if ($since && !strtotime($since)) {
            return response()->json(['error' => 'Invalid timestamp'], 400);
        }

        // Cache key for performance (1-second cache to reduce duplicates)
        $cacheKey = "notifications_latest_{$user->id}_" . md5($since ?? '');

        $result = Cache::remember($cacheKey, 1, function() use ($user, $since, $limit) {
            $query = $user->notifications()->orderBy('created_at', 'desc');

            // Only get notifications since last check AND not shown as toast yet
            $query->where('shown_as_toast', false);
            
            if ($since) {
                // Parse the timestamp with timezone handling
                $sinceCarbon = \Carbon\Carbon::parse($since)->utc();
                $query->where('created_at', '>', $sinceCarbon);
            }

            $notifications = $query->limit($limit)->get();

            // Mark notifications as shown as toast
            if ($notifications->count() > 0) {
                $notificationIds = $notifications->pluck('id');
                \DB::table('notifications')
                    ->whereIn('id', $notificationIds)
                    ->update(['shown_as_toast' => true]);
            }

            return [
                'notifications' => $notifications->map(function($notification) {
                    return [
                        'id' => $notification->id,
                        'type' => $notification->type,
                        'data' => $notification->data,
                        'read_at' => $notification->read_at,
                        'created_at' => $notification->created_at->toISOString(),
                        'toast' => $this->getToastData($notification)
                    ];
                }),
                'unread_count' => $user->unreadNotifications()->count(),
                'timestamp' => now()->utc()->toISOString(),
                'has_new' => $notifications->count() > 0
            ];
        });

        return response()->json($result);
    }

    /**
     * Get toast-specific data for a notification
     */
    private function getToastData($notification)
    {
        $data = $notification->data;
        $message = $data['message'] ?? '';
        
        // Make message more concise for toast
        $conciseMessage = $this->makeConciseMessage($message, $data['type'] ?? 'info');

        return [
            'title' => $data['title'] ?? 'Notification',
            'message' => $conciseMessage,
            'type' => $this->mapToToastType($data['type'] ?? 'info'),
            'duration' => $this->getToastDuration($data['type'] ?? 'info'),
            'action_url' => $data['action_url'] ?? null,
            'dismissible' => true
        ];
    }

    /**
     * Make notification message more concise for toast display
     */
    private function makeConciseMessage($message, $type)
    {
        // Remove time information and make more concise
        $patterns = [
            '/\s+at\s+\d{1,2}:\d{2}\s+(AM|PM)$/' => '', // Remove time at end
            '/\s+\(\s*KSH\s+[\d,]+\.?\d*\s*\)/' => '', // Simplify amount display
            '/Your requisition\s+#/' => 'Requisition #',
            '/has been submitted successfully and is now pending approval/' => 'submitted for approval',
            '/requires your approval/' => 'needs approval',
            '/has been approved/' => 'approved',
            '/has been rejected/' => 'rejected',
            '/has been moved to transaction/' => 'moved to disbursement',
            '/is ready for disbursement/' => 'ready for disbursement',
            '/disbursement has been completed/' => 'disbursed',
            '/Please upload evidence/' => 'Upload evidence required',
        ];

        $conciseMessage = $message;
        foreach ($patterns as $pattern => $replacement) {
            $conciseMessage = preg_replace($pattern, $replacement, $conciseMessage);
        }

        // Limit length to 100 characters
        if (strlen($conciseMessage) > 100) {
            $conciseMessage = substr($conciseMessage, 0, 97) . '...';
        }

        return trim($conciseMessage);
    }

    /**
     * Map notification types to toast types
     */
    private function mapToToastType($notificationType)
    {
        $mapping = [
            'requisition_submission_confirmation' => 'success',
            'requisition_fully_approved' => 'success',
            'disbursement_completed' => 'success',
            'disbursement_completed_for_finance' => 'success',
            'requisition_rejected' => 'error',
            'requisition_returned_for_revision' => 'warning',
            'requisition_pending_approval' => 'info',
            'transaction_ready_for_disbursement' => 'info',
            'evidence_upload_reminder' => 'warning',
            'requisition_moved_to_transaction' => 'success',
            'requisition_step_approved' => 'info',
            'low_cash_float_alert' => 'warning',
        ];

        return $mapping[$notificationType] ?? 'info';
    }

    /**
     * Get toast duration based on notification type
     */
    private function getToastDuration($notificationType)
    {
        $durations = [
            // Existing petty cash durations
            'requisition_submission_confirmation' => 6000,
            'requisition_rejected' => 10000,
            'requisition_returned_for_revision' => 8000,
            'requisition_pending_approval' => 10000,
            'transaction_ready_for_disbursement' => 8000,
            'requisition_fully_approved' => 8000,
            'disbursement_completed' => 8000,
            'disbursement_completed_for_finance' => 8000,
            'evidence_upload_reminder' => 8000,
            'requisition_moved_to_transaction' => 8000,
            'requisition_step_approved' => 8000,
            'low_cash_float_alert' => 10000,

            // New store requisition durations
            'store_requisition_pending_approval' => 10000,
            'store_requisition_approved' => 8000,
            'store_requisition_rejected' => 10000,
            'store_requisition_returned_for_revision' => 8000,
            'store_requisition_issued' => 8000,
            'store_requisition_partially_issued' => 8000,

            // New inventory durations
            'inventory_low_stock_alert' => 10000,
            'inventory_out_of_stock_alert' => 12000,
            'inventory_stock_movement' => 6000,
            'inventory_replenishment_required' => 10000,
        ];

        return $durations[$notificationType] ?? 5000;
    }
}