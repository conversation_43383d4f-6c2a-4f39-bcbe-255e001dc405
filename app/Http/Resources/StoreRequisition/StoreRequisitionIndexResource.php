<?php

namespace App\Http\Resources\StoreRequisition;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Services\StoreRequisition\StoreRequisitionQueryService;
use App\Services\StoreRequisition\StoreRequisitionHelperService;

class StoreRequisitionIndexResource extends JsonResource
{
    private StoreRequisitionQueryService $queryService;
    private StoreRequisitionHelperService $helperService;

    public function __construct(
        StoreRequisitionQueryService $queryService,
        StoreRequisitionHelperService $helperService
    ) {
        $this->queryService = $queryService;
        $this->helperService = $helperService;
    }

    public function toArray($request): array
    {
        $user = auth()->user();
        
        $myRequisitions = $this->queryService->getMyRequisitions();
        $pendingApprovals = $this->queryService->getPendingApprovals();
        $allRequisitions = $this->queryService->getAllRequisitions();

        $this->helperService->loadRequesterPermissions([$myRequisitions, $pendingApprovals, $allRequisitions]);

        return [
            'my_requisitions' => $myRequisitions,
            'pending_approvals' => $pendingApprovals,
            'all_requisitions' => $allRequisitions,
            'user' => [
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'permissions' => $this->helperService->getUserPermissions($user),
                'roles' => $this->helperService->getUserRoles($user),
            ],
        ];
    }

    public static function make(StoreRequisitionQueryService $queryService, StoreRequisitionHelperService $helperService): self
    {
        return new self($queryService, $helperService);
    }
}