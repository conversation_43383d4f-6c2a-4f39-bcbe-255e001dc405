<?php

namespace App\Http\Resources\StoreRequisition;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Services\StoreRequisition\StoreRequisitionHelperService;

class StoreRequisitionCreateResource extends JsonResource
{
    private StoreRequisitionHelperService $helperService;
    private ?int $requestedDepartmentId;

    public function __construct(
        StoreRequisitionHelperService $helperService,
        ?int $requestedDepartmentId = null
    ) {
        $this->helperService = $helperService;
        $this->requestedDepartmentId = $requestedDepartmentId;
    }

    public function toArray($request): array
    {
        $user = auth()->user();
        $userDepartments = $this->helperService->getUserDepartments();
        
        if ($userDepartments->isEmpty()) {
            throw new \Exception('You must be assigned to at least one department to create store requisitions. Please contact your administrator.');
        }

        $departmentId = $this->determineDepartment($userDepartments, $this->requestedDepartmentId);
        $department = $userDepartments->firstWhere('id', $departmentId);

        $inventoryItems = $this->helperService->getOrganizationInventoryItems();
        if ($inventoryItems->isEmpty()) {
            throw new \Exception('Please contact your administrator for assistance.');
        }

        return [
            'inventory_items' => $inventoryItems,
            'branches' => $this->helperService->getUserBranches(),
            'departments' => $userDepartments,
            'user_branch_id' => $department?->branch_id,
            'user_department_id' => $departmentId,
            'user_departments' => $userDepartments,
            'department_id' => $departmentId,
            'department_name' => $department?->name,
        ];
    }

    public static function make(StoreRequisitionHelperService $helperService, ?int $requestedDepartmentId = null): self
    {
        return new self($helperService, $requestedDepartmentId);
    }

    private function determineDepartment($userDepartments, $requestedDepartmentId)
    {
        if ($requestedDepartmentId && $userDepartments->contains('id', $requestedDepartmentId)) {
            return $requestedDepartmentId;
        }
        return $userDepartments->first()->id;
    }
}