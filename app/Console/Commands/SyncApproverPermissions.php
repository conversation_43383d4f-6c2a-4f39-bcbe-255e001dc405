<?php

namespace App\Console\Commands;

use App\Services\ApproverPermissionService;
use Illuminate\Console\Command;

class SyncApproverPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-approver-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync approver permissions for users in approval workflows';

    /**
     * The approver permission service instance.
     *
     * @var \App\Services\ApproverPermissionService
     */
    protected $approverPermissionService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\ApproverPermissionService  $approverPermissionService
     * @return void
     */
    public function __construct(ApproverPermissionService $approverPermissionService)
    {
        parent::__construct();
        $this->approverPermissionService = $approverPermissionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Syncing approver permissions for users in approval workflows...');

        $results = $this->approverPermissionService->syncAllApproverPermissions();

        $this->info("Processed {$results['direct_users']} users directly assigned as approvers");
        $this->info("Processed {$results['role_users']} users with roles in approval workflows");
        $this->info('Approver permissions have been synced successfully!');

        return Command::SUCCESS;
    }
}
