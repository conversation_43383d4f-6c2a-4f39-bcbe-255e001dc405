<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\UpdateRolesWithApproverPermissionSeeder;

class UpdateApproverPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-approver-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update roles and users with the approver permission';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating roles and users with the approver permission...');
        
        $seeder = new UpdateRolesWithApproverPermissionSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Approver permissions have been updated successfully!');
        
        return Command::SUCCESS;
    }
}
