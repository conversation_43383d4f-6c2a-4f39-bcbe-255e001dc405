<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ProcessNotificationQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:process-queue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process the notification queue to handle delayed evidence upload reminders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing notification queue...');

        // Process the queue once
        $exitCode = $this->call('queue:work', [
            '--once' => true,
            '--timeout' => 60
        ]);

        if ($exitCode === 0) {
            $this->info('Queue processed successfully!');
        } else {
            $this->error('Queue processing failed.');
        }

        return $exitCode;
    }
}
