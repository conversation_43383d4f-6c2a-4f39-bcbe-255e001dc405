<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ChartOfAccount;

class FixChartOfAccountsHierarchy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'coa:fix-hierarchy {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix chart of accounts hierarchy by setting correct parent_id and codes for organization accounts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        // Get all platform-level accounts
        $platformAccounts = ChartOfAccount::whereNull('organization_id')->get();

        if ($platformAccounts->isEmpty()) {
            $this->error('No platform-level accounts found. Please run the COASeeder first.');
            return 1;
        }

        $this->info('Platform-level accounts found:');
        foreach ($platformAccounts as $account) {
            $this->line("  - {$account->account_type}: {$account->name} (ID: {$account->id}, Code: {$account->code})");
        }
        $this->newLine();

        // Get all organization-specific accounts that need fixing (missing parent_id)
        $orgAccountsNeedingParentFix = ChartOfAccount::whereNotNull('organization_id')
            ->whereNull('parent_id')
            ->get();

        // Get all organizations that don't have any chart of accounts
        $organizationsWithoutAccounts = \App\Models\Organization::whereDoesntHave('chartOfAccounts')->get();

        if ($orgAccountsNeedingParentFix->isEmpty() && $organizationsWithoutAccounts->isEmpty()) {
            $this->info('No organization accounts need fixing.');
            return 0;
        }

        $fixedCount = 0;

        // Handle organizations without any chart of accounts
        if ($organizationsWithoutAccounts->isNotEmpty()) {
            $this->info('Organizations without chart of accounts:');
            foreach ($organizationsWithoutAccounts as $org) {
                $this->line("  - Organization ID {$org->id}: {$org->name}");

                if (!$dryRun) {
                    $org->setupChartOfAccounts();
                    $this->line("    ✓ Created chart of accounts");
                    $fixedCount += 5; // 5 standard account types
                } else {
                    $this->line("    Will create 5 chart of accounts");
                }
            }
            $this->newLine();
        }

        // Handle organization accounts that need parent_id fixing
        if ($orgAccountsNeedingParentFix->isNotEmpty()) {
            $this->info('Organization accounts that need parent_id fixing:');

            foreach ($orgAccountsNeedingParentFix as $orgAccount) {
                // Find the corresponding platform account
                $platformAccount = $platformAccounts->where('account_type', $orgAccount->account_type)->first();

                if (!$platformAccount) {
                    $this->warn("  - No platform account found for type '{$orgAccount->account_type}' (Account ID: {$orgAccount->id})");
                    continue;
                }

                // Generate the correct code
                $expectedCode = strtoupper(substr($orgAccount->account_type, 0, 3)) . '-' . $orgAccount->organization_id . '-000';

                $this->line("  - Account ID {$orgAccount->id} (Org: {$orgAccount->organization_id}):");
                $this->line("    Current: parent_id={$orgAccount->parent_id}, code='{$orgAccount->code}'");
                $this->line("    Will set: parent_id={$platformAccount->id}, code='{$expectedCode}'");

                if (!$dryRun) {
                    $orgAccount->update([
                        'parent_id' => $platformAccount->id,
                        'code' => $expectedCode,
                    ]);
                    $fixedCount++;
                }
            }
        }

        $this->newLine();

        if ($dryRun) {
            $totalToFix = $organizationsWithoutAccounts->count() * 5 + $orgAccountsNeedingParentFix->count();
            $this->info("DRY RUN: Would fix {$totalToFix} organization accounts/issues.");
            $this->info('Run without --dry-run to apply changes.');
        } else {
            $this->info("Successfully fixed {$fixedCount} organization accounts/issues.");
        }

        return 0;
    }
}
