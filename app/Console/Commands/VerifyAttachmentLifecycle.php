<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Requisition;
use App\Models\Transaction;
use App\Models\Attachment;
use App\Models\User;

class VerifyAttachmentLifecycle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attachments:verify-lifecycle {--requisition-id= : Specific requisition ID to check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify that attachment functionality works throughout the requisition-to-transaction lifecycle';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Verifying Attachment Lifecycle');
        $this->info('============================');
        $this->newLine();

        $requisitionId = $this->option('requisition-id');

        if ($requisitionId) {
            $this->verifySpecificRequisition($requisitionId);
        } else {
            $this->verifyAllRequisitions();
        }

        return 0;
    }

    private function verifySpecificRequisition($requisitionId)
    {
        $requisition = Requisition::with(['attachments', 'transactions.attachments', 'requester'])->find($requisitionId);

        if (!$requisition) {
            $this->error("Requisition {$requisitionId} not found.");
            return;
        }

        $this->info("Verifying Requisition: {$requisition->requisition_number}");
        $this->info("Status: {$requisition->status}");
        $this->info("Requester: {$requisition->requester->name}");
        $this->newLine();

        $this->verifyRequisitionAttachments($requisition);
        $this->verifyTransactionAttachments($requisition);
        $this->verifyAttachmentPermissions($requisition);
    }

    private function verifyAllRequisitions()
    {
        $requisitions = Requisition::with(['attachments', 'transactions.attachments', 'requester'])->get();

        $this->info("Found {$requisitions->count()} requisitions to verify");
        $this->newLine();

        $stats = [
            'total_requisitions' => $requisitions->count(),
            'with_attachments' => 0,
            'with_transactions' => 0,
            'attachment_transfer_issues' => 0,
            'permission_issues' => 0,
        ];

        foreach ($requisitions as $requisition) {
            if ($requisition->attachments->count() > 0) {
                $stats['with_attachments']++;
            }

            if ($requisition->transactions->count() > 0) {
                $stats['with_transactions']++;

                // Check if attachments were properly transferred
                $requisitionAttachmentCount = $requisition->attachments->count();
                $transactionAttachmentCount = $requisition->transactions->sum(function ($transaction) {
                    return $transaction->attachments->count();
                });

                if ($requisitionAttachmentCount > 0 && $transactionAttachmentCount === 0) {
                    $stats['attachment_transfer_issues']++;
                    $this->warn("Requisition {$requisition->requisition_number}: Has {$requisitionAttachmentCount} attachments but transactions have 0");
                }
            }
        }

        $this->displayStats($stats);
    }

    private function verifyRequisitionAttachments(Requisition $requisition)
    {
        $attachments = $requisition->attachments;

        $this->info("Requisition Attachments: {$attachments->count()}");

        foreach ($attachments as $attachment) {
            $this->line("  - {$attachment->original_name} (Evidence: " . ($attachment->is_evidence ? 'Yes' : 'No') . ", Step: {$attachment->uploaded_at_step})");
        }

        $this->newLine();
    }

    private function verifyTransactionAttachments(Requisition $requisition)
    {
        $transactions = $requisition->transactions;

        $this->info("Transactions: {$transactions->count()}");

        foreach ($transactions as $transaction) {
            $this->line("  Transaction ID {$transaction->id} (Status: {$transaction->status}):");
            $this->line("    Attachments: {$transaction->attachments->count()}");

            foreach ($transaction->attachments as $attachment) {
                $this->line("    - {$attachment->original_name} (Evidence: " . ($attachment->is_evidence ? 'Yes' : 'No') . ", Step: {$attachment->uploaded_at_step})");
            }
        }

        $this->newLine();
    }

    private function verifyAttachmentPermissions(Requisition $requisition)
    {
        $requester = $requisition->requester;

        $this->info("Verifying Attachment Permissions:");

        // Check requisition attachment permission
        $canAttachToRequisition = $requester->can('attachFiles', $requisition);
        $requisitionStatus = $canAttachToRequisition ? '✅ Yes' : '❌ No';

        // Add context for approved requisitions with transactions
        if (!$canAttachToRequisition && $requisition->status === 'approved' && $requisition->transactions()->exists()) {
            $requisitionStatus .= ' (Correctly blocked - should use transaction)';
        }

        $this->line("  Requester can attach to requisition ({$requisition->status}): {$requisitionStatus}");

        // Check transaction attachment permissions
        foreach ($requisition->transactions as $transaction) {
            $canAttachToTransaction = $requester->can('attachFiles', $transaction);
            $this->line("  Requester can attach to transaction {$transaction->id} ({$transaction->status}): " . ($canAttachToTransaction ? '✅ Yes' : '❌ No'));
        }

        // Verify workflow logic
        if ($requisition->status === 'approved' && $requisition->transactions()->exists()) {
            $expectedRequisitionAccess = false; // Should not be able to attach to approved requisition
            $expectedTransactionAccess = true;  // Should be able to attach to transaction

            $actualRequisitionAccess = $requester->can('attachFiles', $requisition);
            $actualTransactionAccess = $requisition->transactions->every(function ($transaction) use ($requester) {
                return $requester->can('attachFiles', $transaction);
            });

            if ($actualRequisitionAccess === $expectedRequisitionAccess && $actualTransactionAccess === $expectedTransactionAccess) {
                $this->line("  ✅ Workflow logic correct: Attachment capability properly shifted to transaction");
            } else {
                $this->line("  ❌ Workflow logic incorrect: Attachment capability not properly managed");
            }
        }

        $this->newLine();
    }

    private function displayStats(array $stats)
    {
        $this->newLine();
        $this->info('Summary Statistics:');
        $this->info('==================');
        $this->line("Total Requisitions: {$stats['total_requisitions']}");
        $this->line("Requisitions with Attachments: {$stats['with_attachments']}");
        $this->line("Requisitions with Transactions: {$stats['with_transactions']}");

        if ($stats['attachment_transfer_issues'] > 0) {
            $this->warn("Attachment Transfer Issues: {$stats['attachment_transfer_issues']}");
        } else {
            $this->info("Attachment Transfer Issues: 0 ✅");
        }

        if ($stats['permission_issues'] > 0) {
            $this->warn("Permission Issues: {$stats['permission_issues']}");
        } else {
            $this->info("Permission Issues: 0 ✅");
        }
    }
}
