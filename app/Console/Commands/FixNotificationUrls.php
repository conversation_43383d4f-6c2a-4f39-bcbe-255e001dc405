<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixNotificationUrls extends Command
{
    protected $signature = 'notifications:fix-urls';
    protected $description = 'Fix all notification URLs to use relative paths to prevent CORS issues';

    public function handle()
    {
        $this->info('Fixing notification URLs...');
        
        $notificationFiles = File::glob(app_path('Notifications/*.php'));
        $fixed = 0;
        
        foreach ($notificationFiles as $file) {
            $content = File::get($file);
            $originalContent = $content;
            
            // Add the trait import if not present
            if (!str_contains($content, 'use App\Traits\GeneratesRelativeUrls;')) {
                $content = str_replace(
                    'use Illuminate\Bus\Queueable;',
                    "use App\Traits\GeneratesRelativeUrls;\nuse Illuminate\Bus\Queueable;",
                    $content
                );
            }
            
            // Add trait to class if not present
            if (!str_contains($content, 'GeneratesRelativeUrls')) {
                $content = preg_replace(
                    '/use Queueable;/',
                    'use Queueable, GeneratesRelativeUrls;',
                    $content
                );
            }
            
            // Replace route() calls in action_url with relativeRoute()
            $content = preg_replace(
                "/'action_url'\s*=>\s*route\('([^']+)',\s*([^)]+)\)/",
                "'action_url' => \$this->relativeRoute('$1', $2)",
                $content
            );
            
            // Replace simple route() calls with relativeRoute()
            $content = preg_replace(
                '/\$actionUrl\s*=\s*route\(\'([^\']+)\',\s*([^)]+)\);/',
                '$actionUrl = $this->relativeRoute(\'$1\', $2);',
                $content
            );
            
            if ($content !== $originalContent) {
                File::put($file, $content);
                $this->info('Fixed: ' . basename($file));
                $fixed++;
            }
        }
        
        $this->info("Fixed {$fixed} notification files.");
        
        return 0;
    }
}