<?php

namespace App\Console\Commands;

use App\Jobs\CheckLowStockItems;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CronInventoryCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:cron-check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run inventory low stock check (designed for cron jobs)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            Log::info('Starting scheduled inventory check via cron');
            
            // Run the job synchronously
            (new CheckLowStockItems())->handle();
            
            Log::info('Completed scheduled inventory check via cron');
            
            return 0;
        } catch (\Exception $e) {
            Log::error('Error in scheduled inventory check: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
}
