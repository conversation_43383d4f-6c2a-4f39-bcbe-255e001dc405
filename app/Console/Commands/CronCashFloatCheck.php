<?php

namespace App\Console\Commands;

use App\Jobs\CheckLowCashFloats;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CronCashFloatCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cash-float:cron-check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run cash float check (designed for cron jobs)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            Log::info('Starting scheduled cash float check via cron');
            
            // Run the job synchronously
            (new CheckLowCashFloats())->handle();
            
            Log::info('Completed scheduled cash float check via cron');
            
            return 0;
        } catch (\Exception $e) {
            Log::error('Error in scheduled cash float check: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
}