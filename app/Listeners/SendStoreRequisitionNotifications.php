<?php

namespace App\Listeners;

use App\Events\StoreRequisitionSubmitted;
use App\Events\StoreRequisitionApproved;
use App\Events\StoreRequisitionRejected;
use App\Events\StoreRequisitionReturnedForRevision;
use App\Events\StoreRequisitionIssued;
use App\Notifications\StoreRequisitionPendingApproval;
use App\Notifications\StoreRequisitionApproved as StoreRequisitionApprovedNotification;
use App\Notifications\StoreRequisitionRejected as StoreRequisitionRejectedNotification;
use App\Notifications\StoreRequisitionReturnedForRevision as StoreRequisitionReturnedNotification;
use App\Notifications\StoreRequisitionIssued as StoreRequisitionIssuedNotification;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SendStoreRequisitionNotifications
{
    public function handle($event)
    {
        if ($event instanceof StoreRequisitionSubmitted) {
            $this->handleSubmitted($event);
        } elseif ($event instanceof StoreRequisitionApproved) {
            $this->handleApproved($event);
        } elseif ($event instanceof StoreRequisitionRejected) {
            $this->handleRejected($event);
        } elseif ($event instanceof StoreRequisitionReturnedForRevision) {
            $this->handleReturnedForRevision($event);
        } elseif ($event instanceof StoreRequisitionIssued) {
            $this->handleIssued($event);
        }
    }

    private function handleSubmitted(StoreRequisitionSubmitted $event)
    {
        $storeRequisition = $event->storeRequisition;
        $requester = $storeRequisition->requester;

        Log::info("StoreRequisitionSubmitted event received", [
            'store_requisition_id' => $storeRequisition->id,
            'requester_id' => $storeRequisition->requester_user_id,
            'requester_is_store_keeper' => $requester->can('store-keep'),
        ]);

        // Get authorized approvers based on requester role
        $approvers = $this->getAuthorizedApprovers($storeRequisition);

        Log::info("Found {$approvers->count()} approvers for store requisition {$storeRequisition->id}");

        foreach ($approvers as $approver) {
            Log::info("Sending pending approval notification to approver", [
                'approver_id' => $approver->id,
                'store_requisition_id' => $storeRequisition->id,
            ]);
            $approver->notify(new StoreRequisitionPendingApproval($storeRequisition));
        }

        // Note: No additional overseer notifications needed here because:
        // - For employee/HOD requisitions: Store keepers are the approvers (already notified above)
        // - For store keeper requisitions: Overseers are the approvers (already notified above)
    }

    private function handleApproved(StoreRequisitionApproved $event)
    {
        $storeRequisition = $event->storeRequisition;
        $approver = $event->approver;
        $requester = $storeRequisition->requester;

        Log::info("StoreRequisitionApproved event received", [
            'store_requisition_id' => $storeRequisition->id,
            'approver_id' => $approver->id,
            'requester_is_store_keeper' => $requester->can('store-keep'),
        ]);

        // Notify requester
        $storeRequisition->requester->notify(
            new StoreRequisitionApprovedNotification($storeRequisition)
        );

        // Notify store keepers for issuing (if approver is not a store keeper)
        if (!$approver->can('store-keep')) {
            $storeKeepers = $this->getStoreKeepers($storeRequisition->organization_id);
            foreach ($storeKeepers as $storeKeeper) {
                if ($storeKeeper->id !== $storeRequisition->requester_user_id) {
                    $storeKeeper->notify(
                        new StoreRequisitionApprovedNotification($storeRequisition)
                    );
                }
            }
        }

        // Note: No additional overseer notifications needed here because:
        // - For employee/HOD requisitions: Overseers don't need approval notifications (per new rules)
        // - For store keeper requisitions: The approver IS an overseer (no need for duplicate notifications)
    }

    private function handleRejected(StoreRequisitionRejected $event)
    {
        $storeRequisition = $event->storeRequisition;
        $rejectionReason = $event->rejectionReason;
        $rejector = $event->rejector;
        $requester = $storeRequisition->requester;

        Log::info("StoreRequisitionRejected event received", [
            'store_requisition_id' => $storeRequisition->id,
            'rejector_id' => $rejector->id,
            'requester_is_store_keeper' => $requester->can('store-keep'),
        ]);

        // Notify requester
        $storeRequisition->requester->notify(
            new StoreRequisitionRejectedNotification($storeRequisition, $rejectionReason)
        );

        // Note: No additional overseer notifications needed here because:
        // - For employee/HOD requisitions: Overseers don't need rejection notifications (per new rules)
        // - For store keeper requisitions: The rejector IS an overseer (no need for duplicate notifications)
    }

    private function handleReturnedForRevision(StoreRequisitionReturnedForRevision $event)
    {
        $storeRequisition = $event->storeRequisition;
        $comments = $event->comments;

        Log::info("StoreRequisitionReturnedForRevision event received", [
            'store_requisition_id' => $storeRequisition->id,
            'returner_id' => $event->returner->id,
        ]);

        // Notify requester
        $storeRequisition->requester->notify(
            new StoreRequisitionReturnedNotification($storeRequisition, $comments)
        );
    }

    private function handleIssued(StoreRequisitionIssued $event)
    {
        $storeRequisition = $event->storeRequisition;
        $issuer = $event->issuer;
        $isPartialIssue = $event->isPartialIssue;
        $requester = $storeRequisition->requester;

        Log::info("StoreRequisitionIssued event received", [
            'store_requisition_id' => $storeRequisition->id,
            'issuer_id' => $issuer->id,
            'is_partial_issue' => $isPartialIssue,
            'requester_is_store_keeper' => $requester->can('store-keep'),
        ]);

        // Notify requester
        $storeRequisition->requester->notify(
            new StoreRequisitionIssuedNotification($storeRequisition, $isPartialIssue)
        );

        // Notify approver if different from issuer
        if ($storeRequisition->approved_by && $storeRequisition->approved_by !== $issuer->id) {
            $approver = User::find($storeRequisition->approved_by);
            if ($approver) {
                $approver->notify(
                    new StoreRequisitionIssuedNotification($storeRequisition, $isPartialIssue)
                );
            }
        }

        // NEW RULE: Notify overseers for ALL issued requisitions (both employee/HOD and store keeper)
        // But exclude the overseer who created the requisition from receiving their own issued notification
        $overseers = $this->getOverseers($storeRequisition->organization_id);
        foreach ($overseers as $overseer) {
            // Exclude: issuer, requester, and approver to avoid duplicate notifications
            if ($overseer->id !== $issuer->id &&
                $overseer->id !== $storeRequisition->requester_user_id &&
                $overseer->id !== $storeRequisition->approved_by) {
                $overseer->notify(
                    new StoreRequisitionIssuedNotification($storeRequisition, $isPartialIssue)
                );
                Log::info("Sent issued notification to overseer {$overseer->id} for requisition {$storeRequisition->id}");
            }
        }
    }

    private function getAuthorizedApprovers($storeRequisition)
    {
        $requester = $storeRequisition->requester;

        // If requester is an employee, store keepers can approve
        if (!$requester->can('store-keep')) {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'Store Keeper');
            })->whereHas('organizations', function ($query) use ($storeRequisition) {
                $query->where('organizations.id', $storeRequisition->organization_id);
            })->get();
        }

        // If requester is a store keeper, overseers can approve
        return User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Finance Manager', 'Organization Admin']);
        })->whereHas('organizations', function ($query) use ($storeRequisition) {
            $query->where('organizations.id', $storeRequisition->organization_id);
        })->get();
    }

    private function getStoreKeepers($organizationId)
    {
        return User::whereHas('roles', function ($query) {
            $query->where('name', 'Store Keeper');
        })->whereHas('organizations', function ($query) use ($organizationId) {
            $query->where('organizations.id', $organizationId);
        })->get();
    }

    private function getOverseers($organizationId)
    {
        return User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Finance Manager', 'Organization Admin']);
        })->whereHas('organizations', function ($query) use ($organizationId) {
            $query->where('organizations.id', $organizationId);
        })->get();
    }
}
