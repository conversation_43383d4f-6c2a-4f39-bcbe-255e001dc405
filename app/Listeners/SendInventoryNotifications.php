<?php

namespace App\Listeners;

use App\Events\InventoryLowStockDetected;
use App\Events\InventoryOutOfStockDetected;
use App\Notifications\InventoryLowStockAlert;
use App\Notifications\InventoryOutOfStockAlert;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SendInventoryNotifications
{
    public function handle($event)
    {
        if ($event instanceof InventoryLowStockDetected) {
            $this->handleLowStockDetected($event);
        } elseif ($event instanceof InventoryOutOfStockDetected) {
            $this->handleOutOfStockDetected($event);
        }
    }

    private function handleLowStockDetected(InventoryLowStockDetected $event)
    {
        $inventoryItems = $event->inventoryItems;

        Log::info("InventoryLowStockDetected event received", [
            'items_count' => $inventoryItems->count(),
        ]);

        // Group items by organization
        $itemsByOrganization = $inventoryItems->groupBy('organization_id');

        foreach ($itemsByOrganization as $organizationId => $orgItems) {
            // Get store keepers for this organization
            $storeKeepers = User::whereHas('roles', function ($query) {
                $query->where('name', 'Store Keeper');
            })->whereHas('organizations', function ($query) use ($organizationId) {
                $query->where('organizations.id', $organizationId);
            })->get();

            foreach ($storeKeepers as $storeKeeper) {
                $storeKeeper->notify(new InventoryLowStockAlert($orgItems));
                Log::info("Sent low stock alert to store keeper {$storeKeeper->id} for organization {$organizationId}");
            }
        }
    }

    private function handleOutOfStockDetected(InventoryOutOfStockDetected $event)
    {
        $inventoryItems = $event->inventoryItems;

        Log::info("InventoryOutOfStockDetected event received", [
            'items_count' => $inventoryItems->count(),
        ]);

        // Group items by organization
        $itemsByOrganization = $inventoryItems->groupBy('organization_id');

        foreach ($itemsByOrganization as $organizationId => $orgItems) {
            // Get store keepers for this organization
            $storeKeepers = User::whereHas('roles', function ($query) {
                $query->where('name', 'Store Keeper');
            })->whereHas('organizations', function ($query) use ($organizationId) {
                $query->where('organizations.id', $organizationId);
            })->get();

            // Also notify organization admins for out of stock (high priority)
            $orgAdmins = User::whereHas('roles', function ($query) {
                $query->where('name', 'Organization Admin');
            })->whereHas('organizations', function ($query) use ($organizationId) {
                $query->where('organizations.id', $organizationId);
            })->get();

            $recipients = $storeKeepers->merge($orgAdmins);

            foreach ($recipients as $recipient) {
                $recipient->notify(new InventoryOutOfStockAlert($orgItems));
                Log::info("Sent out of stock alert to user {$recipient->id} for organization {$organizationId}");
            }
        }
    }
}
