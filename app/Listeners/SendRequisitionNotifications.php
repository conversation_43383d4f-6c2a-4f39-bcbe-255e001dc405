<?php

namespace App\Listeners;

use App\Events\RequisitionSubmitted;
use App\Events\RequisitionStepApproved;
use App\Events\RequisitionCompleted;
use App\Events\RequisitionDeclined;
use App\Events\RequisitionRevisionRequested;
use App\Events\TransactionCreated;
use App\Events\TransactionPaymentDetailsUpdated;
use App\Events\TransactionDisbursementCompleted;
use App\Notifications\RequisitionPendingApproval;
use App\Notifications\RequisitionRejected;
use App\Notifications\RequisitionFullyApproved;
use App\Notifications\RequisitionReturnedForRevision;
use App\Notifications\RequisitionMovedToTransaction;
use App\Notifications\TransactionReadyForDisbursement;
use App\Notifications\DisbursementCompleted;
use App\Notifications\DisbursementCompletedForFinance;
use App\Notifications\RequisitionSubmissionConfirmation;
// Removed SendEvidenceUploadReminder - evidence reminder now included in DisbursementCompleted
use App\Models\User;
use App\Models\ApprovalWorkflowStep;
use App\Models\Requisition;

class SendRequisitionNotifications
{
    public function handle($event)
    {
        if ($event instanceof RequisitionSubmitted) {
            $this->handleRequisitionSubmitted($event);
        } elseif ($event instanceof RequisitionStepApproved) {
            $this->handleRequisitionStepApproved($event);
        } elseif ($event instanceof RequisitionCompleted) {
            $this->handleRequisitionCompleted($event);
        } elseif ($event instanceof RequisitionDeclined) {
            $this->handleRequisitionDeclined($event);
        } elseif ($event instanceof RequisitionRevisionRequested) {
            $this->handleRequisitionRevisionRequested($event);
        } elseif ($event instanceof TransactionCreated) {
            $this->handleTransactionCreated($event);
        } elseif ($event instanceof TransactionPaymentDetailsUpdated) {
            $this->handleTransactionPaymentDetailsUpdated($event);
        } elseif ($event instanceof TransactionDisbursementCompleted) {
            $this->handleTransactionDisbursementCompleted($event);
        }
    }

    /**
     * Get approvers for a specific workflow step.
     *
     * @param ApprovalWorkflowStep $step
     * @param Requisition $requisition
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getApproversForStep(ApprovalWorkflowStep $step, Requisition $requisition)
    {
        // If specific approver is assigned
        if ($step->approver_user_id) {
            \Log::info("Using specific approver user ID {$step->approver_user_id} for step {$step->id}");
            return User::where('id', $step->approver_user_id)->get();
        }

        // Get users with the required role in the organization
        if ($step->role_id) {
            \Log::info("Getting users with role ID {$step->role_id} for organization {$requisition->organization_id}");
            return User::whereHas('roles', function($query) use ($step) {
                $query->where('id', $step->role_id);
            })->whereHas('organizations', function($query) use ($requisition) {
                $query->where('organizations.id', $requisition->organization_id);
            })->get();
        }

        \Log::warning("No approver_user_id or role_id set for step {$step->id}");
        return collect();
    }

    private function handleRequisitionSubmitted(RequisitionSubmitted $event)
    {
        \Log::info("RequisitionSubmitted event received", [
            'requisition_id' => $event->requisition->id,
            'requester_id' => $event->requisition->requester_id,
            'first_step_id' => $event->firstStep->id
        ]);

        // 1. Send submission confirmation to the requester
        if ($event->requisition->requester) {
            \Log::info("Sending submission confirmation to requester", [
                'requester_id' => $event->requisition->requester->id,
                'requester_email' => $event->requisition->requester->email
            ]);
            $event->requisition->requester->notify(new RequisitionSubmissionConfirmation($event->requisition, $event->firstStep));
        }

        // 2. Send pending approval notification ONLY to approvers in the first step
        \Log::info("Getting approvers for step", [
            'step_id' => $event->firstStep->id,
            'step_number' => $event->firstStep->step_number,
            'role_id' => $event->firstStep->role_id,
            'approver_user_id' => $event->firstStep->approver_user_id,
            'organization_id' => $event->requisition->organization_id,
            'branch_id' => $event->requisition->branch_id
        ]);

        $approvers = $this->getApproversForStep($event->firstStep, $event->requisition);

        \Log::info("Found approvers for first step", [
            'step_id' => $event->firstStep->id,
            'approver_count' => $approvers->count(),
            'approver_ids' => $approvers->pluck('id')->toArray(),
            'approver_emails' => $approvers->pluck('email')->toArray()
        ]);

        foreach ($approvers as $approver) {
            \Log::info("Sending pending approval notification to approver", [
                'approver_id' => $approver->id,
                'approver_email' => $approver->email
            ]);
            $approver->notify(new RequisitionPendingApproval($event->requisition, $event->firstStep));
        }
    }

    private function handleRequisitionStepApproved(RequisitionStepApproved $event)
    {
        // Only notify approvers for the next step if there is one
        // This ensures notifications are sent sequentially as each step is approved
        if ($event->nextStep) {
            \Log::info("Getting approvers for step", [
                'step_id' => $event->nextStep->id,
                'step_number' => $event->nextStep->step_number,
                'role_id' => $event->nextStep->role_id,
                'approver_user_id' => $event->nextStep->approver_user_id,
                'organization_id' => $event->requisition->organization_id,
                'branch_id' => $event->requisition->branch_id
            ]);

            $approvers = $this->getApproversForStep($event->nextStep, $event->requisition);

            foreach ($approvers as $approver) {
                \Log::info("Sending notification to approver", [
                    'approver_id' => $approver->id,
                    'requisition_id' => $event->requisition->id,
                    'step_id' => $event->nextStep->id,
                    'timestamp' => now()->toDateTimeString()
                ]);
                $approver->notify(new RequisitionPendingApproval($event->requisition, $event->nextStep));
            }
        }
    }

    private function handleRequisitionCompleted(RequisitionCompleted $event)
    {
        // Notify the requisition creator that their requisition is fully approved
        if ($event->requisition->requester) {
            $event->requisition->requester->notify(new RequisitionFullyApproved($event->requisition));
        }
    }

    private function handleRequisitionDeclined(RequisitionDeclined $event)
    {
        // Notify the requisition creator that their requisition was rejected
        if ($event->requisition->requester) {
            $event->requisition->requester->notify(new RequisitionRejected($event->requisition, $event->workflowStep, $event->comments));
        }
    }

    private function handleRequisitionRevisionRequested(RequisitionRevisionRequested $event)
    {
        // Notify the requisition creator that revision is requested
        if ($event->requisition->requester) {
            $event->requisition->requester->notify(new RequisitionReturnedForRevision($event->requisition, $event->workflowStep, $event->comments));
        }
    }

    private function handleTransactionCreated(TransactionCreated $event)
    {
        // Notify the requisition creator that they need to provide payment details
        if ($event->requisition->requester) {
            \Log::info("Sending RequisitionMovedToTransaction notification to requester", [
                'requester_id' => $event->requisition->requester->id,
                'requester_email' => $event->requisition->requester->email,
                'transaction_id' => $event->transaction->id,
                'requisition_id' => $event->requisition->id
            ]);
            $event->requisition->requester->notify(new RequisitionMovedToTransaction($event->transaction));
        }
    }

    private function handleTransactionPaymentDetailsUpdated(TransactionPaymentDetailsUpdated $event)
    {
        // Notify finance managers that transaction is ready for disbursement
        // Get users with Finance Manager role for the organization
        $financeManagers = User::whereHas('roles', function($query) use ($event) {
            $query->where('name', 'Finance Manager')
                  ->where('organization_id', $event->transaction->requisition->organization_id);
        })->get();

        \Log::info("Found finance managers for notification", [
            'count' => $financeManagers->count(),
            'user_ids' => $financeManagers->pluck('id')->toArray(),
            'user_emails' => $financeManagers->pluck('email')->toArray(),
            'transaction_id' => $event->transaction->id,
            'requisition_id' => $event->transaction->requisition_id,
            'organization_id' => $event->transaction->requisition->organization_id,
            'branch_id' => $event->transaction->requisition->branch_id
        ]);

        foreach ($financeManagers as $manager) {
            \Log::info("Sending TransactionReadyForDisbursement notification to finance manager", [
                'manager_id' => $manager->id,
                'manager_email' => $manager->email,
                'transaction_id' => $event->transaction->id
            ]);
            $manager->notify(new TransactionReadyForDisbursement($event->transaction));
        }
    }

    private function handleTransactionDisbursementCompleted(TransactionDisbursementCompleted $event)
    {
        // Notify the requester that disbursement is completed
        if ($event->transaction->requisition->requester) {
            \Log::info("Sending DisbursementCompleted notification to requester", [
                'requester_id' => $event->transaction->requisition->requester->id,
                'requester_email' => $event->transaction->requisition->requester->email,
                'transaction_id' => $event->transaction->id
            ]);
            $event->transaction->requisition->requester->notify(new DisbursementCompleted($event->transaction));
        }

        // Notify finance managers that disbursement has been completed (confirmation)
        // Only notify Finance Managers from the same organization as the requester
        $financeManagers = User::whereHas('roles', function($query) use ($event) {
            $query->where('name', 'Finance Manager')
                  ->where('organization_id', $event->transaction->requisition->organization_id);
        })->get();

        \Log::info("Found finance managers for disbursement completion notification", [
            'count' => $financeManagers->count(),
            'user_ids' => $financeManagers->pluck('id')->toArray(),
            'user_emails' => $financeManagers->pluck('email')->toArray(),
            'organization_id' => $event->transaction->requisition->organization_id,
            'transaction_id' => $event->transaction->id
        ]);

        foreach ($financeManagers as $manager) {
            \Log::info("Sending DisbursementCompletedForFinance notification to finance manager", [
                'manager_id' => $manager->id,
                'manager_email' => $manager->email,
                'transaction_id' => $event->transaction->id,
                'organization_id' => $event->transaction->requisition->organization_id
            ]);
            $manager->notify(new DisbursementCompletedForFinance($event->transaction));
        }

        // Evidence reminder is now included in the DisbursementCompleted notification
        // No separate evidence reminder needed
    }
}
