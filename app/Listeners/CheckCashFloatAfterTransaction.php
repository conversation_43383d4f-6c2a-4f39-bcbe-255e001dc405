<?php

namespace App\Listeners;

use App\Events\TransactionDisbursementCompleted;
use App\Jobs\CheckLowCashFloats;
use App\Models\CashFloat;
use App\Notifications\LowCashFloatAlert;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class CheckCashFloatAfterTransaction
{
    /**
     * Handle the event.
     */
    public function handle(TransactionDisbursementCompleted $event): void
    {
        // Only check if the transaction affects a cash float
        if (!$event->transaction->cash_float_id) {
            return;
        }

        Log::info("Checking cash float after transaction completion", [
            'transaction_id' => $event->transaction->id,
            'cash_float_id' => $event->transaction->cash_float_id
        ]);

        try {
            $cashFloat = CashFloat::with(['organization', 'department', 'branch', 'user'])
                ->find($event->transaction->cash_float_id);

            if (!$cashFloat || $cashFloat->status !== 'active') {
                return;
            }

            // Check if this specific cash float needs a low balance notification
            if ($cashFloat->shouldSendLowBalanceNotification()) {
                Log::info("Triggering immediate low cash float alert", [
                    'cash_float_id' => $cashFloat->id,
                    'current_balance' => $cashFloat->current_balance,
                    'alert_threshold' => $cashFloat->alert_threshold
                ]);

                // Get finance managers for the organization
                $financeManagers = User::whereHas('roles', function ($query) use ($cashFloat) {
                    $query->where('name', 'Finance Manager')
                          ->where('organization_id', $cashFloat->organization_id);
                })->get();

                // Send notifications
                foreach ($financeManagers as $financeManager) {
                    $financeManager->notify(new LowCashFloatAlert($cashFloat));
                    Log::info("Sent immediate low cash float alert to finance manager {$financeManager->id} for cash float {$cashFloat->id}");
                }

                // Notify assigned user if different from finance managers
                if ($cashFloat->user && !$financeManagers->contains('id', $cashFloat->user->id)) {
                    $cashFloat->user->notify(new LowCashFloatAlert($cashFloat));
                    Log::info("Sent immediate low cash float alert to assigned user {$cashFloat->user->id} for cash float {$cashFloat->id}");
                }

                // Mark that alert has been sent to prevent duplicates
                $cashFloat->markAlertSent();
            }
        } catch (\Exception $e) {
            Log::error("Error checking cash float after transaction", [
                'transaction_id' => $event->transaction->id,
                'cash_float_id' => $event->transaction->cash_float_id,
                'error' => $e->getMessage()
            ]);
        }
    }
}