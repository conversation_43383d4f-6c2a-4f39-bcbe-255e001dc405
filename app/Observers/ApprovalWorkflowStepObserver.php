<?php

namespace App\Observers;

use App\Models\ApprovalWorkflowStep;
use App\Services\ApproverPermissionService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class ApprovalWorkflowStepObserver
{
    /**
     * The approver permission service instance.
     *
     * @var \App\Services\ApproverPermissionService
     */
    protected $approverPermissionService;

    /**
     * Create a new observer instance.
     *
     * @param  \App\Services\ApproverPermissionService  $approverPermissionService
     * @return void
     */
    public function __construct(?ApproverPermissionService $approverPermissionService = null)
    {
        $this->approverPermissionService = $approverPermissionService ?? App::make(ApproverPermissionService::class);
    }

    /**
     * Handle the ApprovalWorkflowStep "created" event.
     */
    public function created(ApprovalWorkflowStep $step): void
    {
        $this->assignApproverPermission($step);
    }

    /**
     * Handle the ApprovalWorkflowStep "updated" event.
     */
    public function updated(ApprovalWorkflowStep $step): void
    {
        $this->assignApproverPermission($step);
    }

    /**
     * Assign the approver permission to users in the workflow step
     */
    private function assignApproverPermission(ApprovalWorkflowStep $step): void
    {
        try {
            // If a specific user is assigned as approver
            if ($step->approver_user_id) {
                $this->approverPermissionService->assignApproverPermissionToUser($step->approver_user_id);
            }

            // If a role is assigned to the step
            if ($step->role_id) {
                $this->approverPermissionService->assignApproverPermissionToUsersWithRole($step->role_id);
            }
        } catch (\Exception $e) {
            Log::error("Error assigning approver permission in observer: " . $e->getMessage());
        }
    }
}
