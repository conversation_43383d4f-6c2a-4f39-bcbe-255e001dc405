<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkflowTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'description',
        'template_data',
        'validation_rules',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'template_data' => 'array',
        'validation_rules' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get templates ordered by sort order and name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get all available categories.
     */
    public static function getCategories(): array
    {
        return self::distinct('category')
            ->where('is_active', true)
            ->pluck('category')
            ->toArray();
    }

    /**
     * Generate workflow data for the given context.
     */
    public function generateWorkflowData(array $context = []): array
    {
        $templateData = $this->template_data;
        
        // Apply context-specific transformations
        if (isset($context['organization_id'])) {
            $templateData['organization_id'] = $context['organization_id'];
        }
        
        if (isset($context['branch_id'])) {
            $templateData['branch_id'] = $context['branch_id'];
        }
        
        if (isset($context['department_id'])) {
            $templateData['department_id'] = $context['department_id'];
        }

        return $templateData;
    }

    /**
     * Get validation rules for this template.
     */
    public function getValidationRules(): array
    {
        return $this->validation_rules ?? [];
    }
}
