<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MpesaTransaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'organization_id',
        'branch_id',
        'transaction_type',
        'related_id',
        'related_type',
        'mpesa_transaction_id',
        'mpesa_receipt_number',
        'checkout_request_id',
        'merchant_request_id',
        'amount',
        'recipient_phone',
        'phone_number',
        'reference',
        'description',
        'status',
        'result_code',
        'result_desc',
        'confirmed_amount',
        'confirmed_phone',
        'transaction_date',
        'request_payload',
        'response_payload',
        'transaction_time',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'confirmed_amount' => 'decimal:2',
        'request_payload' => 'array',
        'response_payload' => 'array',
        'transaction_time' => 'datetime',
        'transaction_date' => 'datetime',
    ];

    /**
     * Get the organization that owns the M-Pesa transaction.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the branch that owns the M-Pesa transaction.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the related model based on related_type and related_id.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function related()
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include successful transactions.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * Scope a query to only include failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope a query to only include initiated transactions.
     */
    public function scopeInitiated($query)
    {
        return $query->where('status', 'initiated');
    }

    /**
     * Scope a query to only include pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Get the transactions associated with this M-Pesa transaction.
     */
    public function transactions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Transaction::class, 'mpesa_transaction_id');
    }
}
