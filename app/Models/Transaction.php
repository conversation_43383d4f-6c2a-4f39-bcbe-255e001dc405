<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Transaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'requisition_id',
        'cash_float_id',
        'transaction_type',
        'status',
        'payment_method',
        'mpesa_transaction_id',
        'account_details',
        'disbursement_transaction_id',
        'approvers_details',
        'total_amount',
        'transaction_cost',
        'created_by',
        'updated_by',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total_amount' => 'decimal:2',
        'approvers_details' => 'array',
    ];

    /**
     * Get the requisition that owns the transaction.
     */
    public function requisition(): BelongsTo
    {
        return $this->belongsTo(Requisition::class, 'requisition_id');
    }

    /**
     * Get the items for the transaction.
     */
    public function items(): HasMany
    {
        return $this->hasMany(TransactionItem::class, 'transaction_id');
    }

    /**
     * Get the user who created the transaction.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the transaction.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the M-Pesa transaction associated with this transaction.
     */
    public function mpesaTransaction(): BelongsTo
    {
        return $this->belongsTo(MpesaTransaction::class, 'mpesa_transaction_id');
    }

    /**
     * Get the cash float that owns the transaction.
     */
    public function cashFloat(): BelongsTo
    {
        return $this->belongsTo(CashFloat::class, 'cash_float_id');
        }

    /*
     Get all attachments for the transaction.
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Get evidence attachments only.
     */
    public function evidenceAttachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable')->where('is_evidence', true);
    }
}
