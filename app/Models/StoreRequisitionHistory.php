<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoreRequisitionHistory extends Model
{
    protected $fillable = [
        'store_requisition_id',
        'user_id',
        'action',
        'comments',
        'changes',
    ];

    protected $casts = [
        'changes' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function storeRequisition(): BelongsTo
    {
        return $this->belongsTo(StoreRequisition::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a history record for store requisition changes.
     */
    public static function createRecord(
        int $storeRequisitionId,
        int $userId,
        string $action,
        ?string $comments = null,
        ?array $changes = null
    ): self {
        // Validate action is in allowed list
        $allowedActions = [
            'created_as_draft', 'created_and_submitted', 'edited',
            'edited_and_submitted', 'edited_and_resubmitted',
            'submitted_for_approval', 'approved', 'rejected',
            'returned_for_revision', 'issued'
        ];

        if (!in_array($action, $allowedActions)) {
            throw new \InvalidArgumentException("Invalid action: {$action}");
        }

        // Limit comment length if provided
        if ($comments && strlen($comments) > 1000) {
            $comments = substr($comments, 0, 1000);
        }

        return self::create([
            'store_requisition_id' => $storeRequisitionId,
            'user_id' => $userId,
            'action' => $action,
            'comments' => $comments,
            'changes' => $changes,
        ]);
    }

    /**
     * Get formatted action name for display
     */
    public function getFormattedActionAttribute(): string
    {
        return match($this->action) {
            'created_as_draft' => 'Created as Draft',
            'created_and_submitted' => 'Created and Submitted',
            'edited' => 'Edited',
            'edited_and_submitted' => 'Edited and Submitted',
            'edited_and_resubmitted' => 'Edited and Resubmitted',
            'submitted_for_approval' => 'Submitted for Approval',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'returned_for_revision' => 'Returned for Revision',
            'issued' => 'Items Issued',
            default => ucfirst(str_replace('_', ' ', $this->action))
        };
    }
}
