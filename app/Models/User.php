<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'first_name',
        'last_name',
        'email',
        'phone',
        'avatar',
        'password',
        'is_platform_admin',
        'status',
        'last_login_at',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
            'is_platform_admin' => 'boolean',
        ];
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the user's organization ID.
     */
    public function getOrganizationIdAttribute(): ?int
    {
        return $this->organizations()->first()?->id;
    }

    /**
     * Get user permissions as array for frontend using direct database query
     */
    public function getPermissionNamesAttribute(): array
    {
        try {
            // Use direct database query to avoid Spatie collection conflicts
            $permissions = \DB::table('model_has_permissions')
                ->join('permissions', 'model_has_permissions.permission_id', '=', 'permissions.id')
                ->where('model_has_permissions.model_id', $this->id)
                ->where('model_has_permissions.model_type', 'App\\Models\\User')
                ->pluck('permissions.name')
                ->toArray();

            // Also get permissions from roles
            $rolePermissions = \DB::table('model_has_roles')
                ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                ->join('role_has_permissions', 'roles.id', '=', 'role_has_permissions.role_id')
                ->join('permissions', 'role_has_permissions.permission_id', '=', 'permissions.id')
                ->where('model_has_roles.model_id', $this->id)
                ->where('model_has_roles.model_type', 'App\\Models\\User')
                ->pluck('permissions.name')
                ->toArray();

            return array_values(array_unique(array_merge($permissions, $rolePermissions)));
        } catch (\Exception $e) {
            \Log::error('Error getting user permission names: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * The departments that belong to the user.
     */
    public function departments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, 'user_department');
    }

    /**
     * The organizations that belong to the user.
     */
    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'user_organizations');
    }

    /**
     * The branches that belong to the user.
     */
    public function branches(): BelongsToMany
    {
        return $this->belongsToMany(Branch::class, 'user_branches');
    }

    /**
     * Get the cash floats issued to the user.
     */
    public function cashFloats(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CashFloat::class);
    }
}
