<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CashFloat extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'branch_id',
        'department_id',
        'user_id',
        'name',
        'initial_amount',
        'issued_at',
        'alert_threshold',
        'last_alert_sent_at',
        'status',
    ];

    protected $casts = [
        'last_alert_sent_at' => 'datetime',
    ];

    protected $appends = [
        'current_balance',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'cash_float_id');
    }

    public function getCurrentBalanceAttribute()
    {
        Log::debug("Calculating current_balance for CashFloat ID: {$this->id}");
        
        // CASH IN: Only float_issuance increases the balance
        // Exclude the first float_issuance (initial funding) as it's already in initial_amount
        $firstIssuance = $this->transactions()
            ->where('transaction_type', 'float_issuance')
            ->oldest()
            ->first();

        $floatIssuances = $this->transactions()
            ->whereIn('transaction_type', ['float_issuance', 'reimbursement'])
            ->when($firstIssuance, function ($query) use ($firstIssuance) {
                return $query->where('id', '!=', $firstIssuance->id);
            })
            ->sum('total_amount');

        // CASH OUT: All other transaction types linked to this float decrease the balance
        // Note: Only transactions with cash_float_id set affect this float's balance
        
        // Subtract disbursements with transaction costs
        $disbursements = $this->transactions()
            ->where('transaction_type', 'disbursement')
            ->get()
            ->sum(function ($transaction) {
                return $transaction->total_amount + ($transaction->transaction_cost ?? 0);
            });

        // Subtract expenses with transaction costs (only those linked to this float)
        $expenses = $this->transactions()
            ->where('transaction_type', 'expense')
            ->get()
            ->sum(function ($transaction) {
                return $transaction->total_amount + ($transaction->transaction_cost ?? 0);
            });

        // Subtract float returns with transaction costs
        $floatReturns = $this->transactions()
            ->where('transaction_type', 'float_return')
            ->get()
            ->sum(function ($transaction) {
                return $transaction->total_amount + ($transaction->transaction_cost ?? 0);
            });

        // Handle reimbursement and other transaction types
        $otherCashOut = $this->transactions()
            ->where('transaction_type', 'other')
            ->get()
            ->sum(function ($transaction) {
                return $transaction->total_amount + ($transaction->transaction_cost ?? 0);
            });

        $calculatedBalance = $this->initial_amount + $floatIssuances - $disbursements - $expenses - $floatReturns - $otherCashOut;
        Log::debug("Calculated current_balance for CashFloat ID: {$this->id} is {$calculatedBalance}");
        return $calculatedBalance;
    }

    public function isBelowAlertThreshold(): bool
    {
        if ($this->alert_threshold === null) return false;
        return $this->current_balance < $this->alert_threshold;
    }

    /**
     * Determine if a low balance notification should be sent.
     *
     * @return bool
     */
    public function shouldSendLowBalanceNotification(): bool
    {
        Log::debug("SFN Start: Checking CashFloat ID: {$this->id}, Name: {$this->name}, Balance: {$this->current_balance}, Threshold: {$this->alert_threshold}");

        // Check if alert_threshold is set
        if ($this->alert_threshold === null) {
            Log::debug("SFN Skip: No alert threshold set for CashFloat ID: {$this->id}");
            return false;
        }

        // Only send notification if current balance is below alert threshold
        if ($this->current_balance >= $this->alert_threshold) {
            Log::debug("SFN Skip: Balance {$this->current_balance} is not below threshold {$this->alert_threshold} for CashFloat ID: {$this->id}");
            return false;
        }
        
        Log::debug("SFN ConditionMet: Balance {$this->current_balance} IS below threshold {$this->alert_threshold} for CashFloat ID: {$this->id}");

        // NEW: Prevent duplicate alerts - don't send if alert was sent in last hour
        if ($this->last_alert_sent_at && $this->last_alert_sent_at->diffInMinutes(now()) < 60) {
            Log::debug("SFN Skip: Recent alert already sent (within last hour) for CashFloat ID: {$this->id}. Last alert: {$this->last_alert_sent_at}");
            return false;
        }
        
        Log::info("SFN Proceed: Should send low balance notification for CashFloat ID: {$this->id}. Balance: {$this->current_balance}, Threshold: {$this->alert_threshold}");
        return true;
    }

    /**
     * Check if an alert can be sent for this cash float
     * Uses both cache and database to ensure reliable throttling
     */
    public function canSendAlert(): bool
    {
        $cacheKey = "cash_float_alert_{$this->id}";
        
        // First check cache to avoid unnecessary DB queries
        if (Cache::has($cacheKey)) {
            return false;
        }
        
        // Then verify with database as backup
        if ($this->last_alert_sent_at && 
            $this->last_alert_sent_at->addHours(2)->isFuture()) {
            // Update cache to maintain consistency
            Cache::put($cacheKey, true, now()->addHours(2));
            return false;
        }
        
        return true;
    }

    /**
     * Mark that an alert has been sent
     */
    public function markAlertSent(): void
    {
        $cacheKey = "cash_float_alert_{$this->id}";
        
        // Update both cache and database
        Cache::put($cacheKey, true, now()->addHours(2));
        $this->update(['last_alert_sent_at' => now()]);
    }

    /**
     * Check if the float is below threshold
     */
    public function isBelowThreshold(): bool
    {
        return $this->current_balance < $this->alert_threshold;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}