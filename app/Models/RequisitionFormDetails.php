<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequisitionFormDetails extends Model
{
    use HasFactory;

    protected $table = 'requisition_form_details'; // Explicitly set the table name
    protected $primaryKey = 'requisition_form_uuid'; // Set the primary key, if it's not 'id'
    public $incrementing = false; // If the primary key is not auto-incrementing
    protected $keyType = 'string'; // Define the data type of the primary key
    public $timestamps = true; // Keep timestamps


    protected $fillable = [
        'requisition_form_uuid',
        'organization_id',
        'branch_id',
        'department_id',
        'requester_user_id',
        'status',
    ];

    // Define relationships if needed
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function requester()
    {
        return $this->belongsTo(User::class, 'requester_user_id');
    }
}
