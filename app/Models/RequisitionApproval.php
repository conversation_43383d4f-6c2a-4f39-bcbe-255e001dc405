<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequisitionApproval extends Model
{
    use HasFactory;

    protected $fillable = [
        'requisition_id',
        'approval_workflow_step_id',
        'approver_user_id',
        'action',
        'comments',
        'action_at',
    ];

    protected $casts = [
        'action_at' => 'datetime',
    ];

    /**
     * Get the requisition that owns the approval.
     */
    public function requisition(): BelongsTo
    {
        return $this->belongsTo(Requisition::class, 'requisition_id');
    }

    /**
     * Get the workflow step that this approval is for.
     */
    public function workflowStep(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflowStep::class, 'approval_workflow_step_id');
    }

    /**
     * Get the user who performed the approval action.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    /**
     * Scope a query to only include approvals with a specific action.
     */
    public function scopeWithAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope a query to only include approvals for a specific requisition.
     */
    public function scopeForRequisition($query, $requisitionId)
    {
        return $query->where('requisition_id', $requisitionId);
    }
}
