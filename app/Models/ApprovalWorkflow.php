<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApprovalWorkflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'branch_id',
        'department_id',
        'name',
        'is_default',
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * Get the organization that owns the approval workflow.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * Get the branch that owns the approval workflow.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    /**
     * Get the department that owns the approval workflow.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    /**
     * Get the steps for the approval workflow.
     */
    public function steps(): HasMany
    {
        return $this->hasMany(ApprovalWorkflowStep::class, 'approval_workflow_id')->orderBy('step_number');
    }

    /**
     * Get the requisitions for the approval workflow.
     */
    public function requisitions(): HasMany
    {
        return $this->hasMany(Requisition::class, 'approval_workflow_id');
    }

    /**
     * Scope a query to only include default workflows.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Find the appropriate workflow for a requisition based on its context.
     */
    public static function findWorkflowForRequisition(Requisition $requisition)
    {
        // Try to find a department-specific workflow
        if ($requisition->department_id) {
            $workflow = self::where('organization_id', $requisition->organization_id)
                ->where('department_id', $requisition->department_id)
                ->where('is_default', true)
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Try to find a branch-specific workflow
        if ($requisition->branch_id) {
            $workflow = self::where('organization_id', $requisition->organization_id)
                ->where('branch_id', $requisition->branch_id)
                ->whereNull('department_id')
                ->where('is_default', true)
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Fall back to organization-level workflow
        return self::where('organization_id', $requisition->organization_id)
            ->whereNull('branch_id')
            ->whereNull('department_id')
            ->where('is_default', true)
            ->first();
    }
}
