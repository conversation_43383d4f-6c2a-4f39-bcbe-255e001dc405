<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailVerificationCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'code',
        'registration_data',
        'expires_at',
        'used',
        'ip_address',
    ];

    protected $casts = [
        'registration_data' => 'array',
        'expires_at' => 'datetime',
        'used' => 'boolean',
    ];

    /**
     * Check if the verification code is valid (not expired and not used)
     */
    public function isValid(): bool
    {
        return !$this->used && $this->expires_at->isFuture();
    }

    /**
     * Mark the verification code as used
     */
    public function markAsUsed(): void
    {
        $this->update(['used' => true]);
    }

    /**
     * Scope to get valid codes
     */
    public function scopeValid($query)
    {
        return $query->where('used', false)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get expired codes
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Generate a random 6-digit verification code
     */
    public static function generateCode(): string
    {
        return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Create a new verification code for an email
     */
    public static function createForEmail(string $email, ?array $registrationData = null, ?string $ipAddress = null): self
    {
        // Invalidate any existing codes for this email
        static::where('email', $email)->update(['used' => true]);

        return static::create([
            'email' => $email,
            'code' => static::generateCode(),
            'registration_data' => $registrationData,
            'expires_at' => now()->addMinutes(15), // 15 minute expiration
            'ip_address' => $ipAddress,
        ]);
    }

    /**
     * Find a valid verification code
     */
    public static function findValidCode(string $email, string $code): ?self
    {
        return static::where('email', $email)
                    ->where('code', $code)
                    ->valid()
                    ->first();
    }

    /**
     * Clean up expired codes (can be called by a scheduled job)
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }
}
