<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Validation\ValidationException;

class ApprovalWorkflowStep extends Model
{
    use HasFactory;

    protected $fillable = [
        'approval_workflow_id',
        'step_number',
        'role_id',
        'approver_user_id',
        'description',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::saving(function (ApprovalWorkflowStep $step) {
            // Ensure either role_id or approver_user_id is provided, but not both
            if (empty($step->role_id) && empty($step->approver_user_id)) {
                throw ValidationException::withMessages([
                    'step' => 'Either a role or a specific approver must be assigned to each workflow step.'
                ]);
            }

            if (!empty($step->role_id) && !empty($step->approver_user_id)) {
                throw ValidationException::withMessages([
                    'step' => 'A workflow step cannot have both a role and a specific approver assigned.'
                ]);
            }
        });
    }

    /**
     * Get the workflow that owns the step.
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'approval_workflow_id');
    }

    /**
     * Get the role that is responsible for this step.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id');
    }

    /**
     * Get the specific user assigned as approver for this step.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    /**
     * Get the approvals for this step.
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(RequisitionApproval::class, 'approval_workflow_step_id');
    }

    /**
     * Get the next step in the workflow.
     */
    public function getNextStep()
    {
        return self::where('approval_workflow_id', $this->approval_workflow_id)
            ->where('step_number', '>', $this->step_number)
            ->orderBy('step_number')
            ->first();
    }

    /**
     * Get the previous step in the workflow.
     */
    public function getPreviousStep()
    {
        return self::where('approval_workflow_id', $this->approval_workflow_id)
            ->where('step_number', '<', $this->step_number)
            ->orderBy('step_number', 'desc')
            ->first();
    }
}
