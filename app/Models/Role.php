<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Exceptions\RoleAlreadyExists;
use <PERSON>tie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    protected $fillable = [
        'name',
        'guard_name',
        'organization_id',
        'branch_id',
        'department_id',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Override the create method to allow duplicate role names with different organization_ids and department_ids
     * and to use firstOrCreate for roles that already exist within an organization and department
     */
    public static function create(array $attributes = [])
    {
        $attributes['guard_name'] = $attributes['guard_name'] ?? config('auth.defaults.guard');

        // Check if role already exists within the organization and department
        $existingRole = static::where('name', $attributes['name'])
            ->where('guard_name', $attributes['guard_name'])
            ->where('organization_id', $attributes['organization_id'] ?? null)
            ->where('department_id', $attributes['department_id'] ?? null)
            ->first();

        if ($existingRole) {
            // Return the existing role instead of throwing an exception
            return $existingRole;
        }

        return static::query()->create($attributes);
    }

    /**
     * First or create a role with the given attributes
     */
    public static function firstOrCreate(array $attributes = [])
    {
        $attributes['guard_name'] = $attributes['guard_name'] ?? config('auth.defaults.guard');

        return static::query()->firstOrCreate(
            [
                'name' => $attributes['name'],
                'guard_name' => $attributes['guard_name'],
                'organization_id' => $attributes['organization_id'] ?? null,
                'department_id' => $attributes['department_id'] ?? null
            ],
            $attributes
        );
    }

    /**
     * Get the organization that owns the role.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the branch that owns the role.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the department that owns the role.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Scope a query to only include active roles.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}