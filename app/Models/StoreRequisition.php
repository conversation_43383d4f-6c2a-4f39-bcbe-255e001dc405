<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StoreRequisition extends Model
{
    use HasFactory;
    
    public const STATUS_DRAFT = 'draft';
    public const STATUS_PENDING_APPROVAL = 'pending_approval';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';
    public const STATUS_RETURNED_FOR_REVISION = 'returned_for_revision';
    public const STATUS_ISSUED = 'issued';
    public const STATUS_PARTIALLY_ISSUED = 'partially_issued';

    protected $fillable = [
        'organization_id',
        'branch_id',
        'department_id',
        'requester_user_id',
        'approver_user_id',
        'issuer_user_id',
        'purpose',
        'status',
        'rejection_reason',
        'requested_at',
        'approved_at',
        'issued_at',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'issued_at' => 'datetime',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_user_id');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    public function issuer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'issuer_user_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(StoreRequisitionItem::class);
    }

    public function canBeSubmitted(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    public function canBeApproved(): bool
    {
        return $this->status === self::STATUS_PENDING_APPROVAL;
    }

    public function canBeIssued(): bool
    {
        return in_array($this->status, [self::STATUS_APPROVED, self::STATUS_PARTIALLY_ISSUED]);
    }

    public function isFullyIssued(): bool
    {
        return $this->status === self::STATUS_ISSUED;
    }

    public function isPending(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PENDING_APPROVAL]);
    }

    /**
     * Check if the store requisition can be edited.
     * Follows the same pattern as petty cash requisitions.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, [
            self::STATUS_DRAFT,
            self::STATUS_REJECTED,
            self::STATUS_RETURNED_FOR_REVISION
        ]);
    }

    /**
     * Check if the store requisition can be returned for revision.
     */
    public function canBeReturnedForRevision(): bool
    {
        return $this->status === self::STATUS_PENDING_APPROVAL;
    }

    /**
     * Get the history records for this store requisition.
     */
    public function histories(): HasMany
    {
        return $this->hasMany(StoreRequisitionHistory::class)->orderBy('created_at', 'desc');
    }
}
