<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Requisition extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'branch_id',
        'department_id',
        'requester_user_id',
        'requisition_number',
        'purpose',
        'status',
        'notes',
        'total_amount',
        'approval_workflow_id',
        'current_approval_step_id',
        'current_workflow_step',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'status' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    // --- Relationships ---

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_user_id');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function rejector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    public function items(): HasMany
    {
        return $this->hasMany(RequisitionItem::class, 'requisition_id');
    }

    public function history(): HasMany
    {
        return $this->hasMany(RequisitionHistory::class, 'requisition_id');
    }

    /**
     * Get the approval workflow for the requisition.
     */
    public function approvalWorkflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'approval_workflow_id');
    }

    /**
     * Get the workflow for the requisition (alias for approvalWorkflow).
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'approval_workflow_id');
    }

    /**
     * Get the current approval step for the requisition.
     */
    public function currentApprovalStep(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflowStep::class, 'current_approval_step_id');
    }

    /**
     * Get the current workflow step based on step number.
     */
    public function getCurrentStepAttribute()
    {
        return $this->workflow->steps()
            ->where('step_number', $this->current_workflow_step)
            ->first();
    }

    /**
     * Get the approval actions for the requisition.
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(RequisitionApproval::class, 'requisition_id');
    }

    /**
     * Get the transactions for the requisition.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'requisition_id');
    }

    /**
     * Get all attachments for the requisition.
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Check if this requisition should show upload button or redirect to transaction.
     *
     * @return array{show_upload: bool, redirect_to_transaction: bool, transaction_id: int|null}
     */
    public function getAttachmentUIState(): array
    {
        // If approved and has transactions, should redirect to transaction
        if ($this->status === 'approved' && $this->transactions()->exists()) {
            $transaction = $this->transactions()->first();
            return [
                'show_upload' => false,
                'redirect_to_transaction' => true,
                'transaction_id' => $transaction->id,
            ];
        }

        // For all other cases, show upload button (if user has permission)
        return [
            'show_upload' => true,
            'redirect_to_transaction' => false,
            'transaction_id' => null,
        ];
    }

    /**
     * Get evidence attachments only.
     */
    public function evidenceAttachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable')->where('is_evidence', true);
    }

    // --- Boot Method to Automatically Update Total Amount and Generate Requisition Number ---
    protected static function booted()
    {
        static::saving(function ($requisition) {
            $requisition->total_amount = $requisition->items()->sum('total_price');
        });

        static::creating(function ($requisition) {
            // Generate a unique requisition number when creating
            if (!$requisition->requisition_number) {
                $requisition->requisition_number = 'REQ-' . date('Ymd') . '-' . rand(1000, 9999);
            }
        });
    }
}
