<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Organization extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'contact_email',
        'contact_phone',
        'address',
        'mpesa_account_details',
        'status',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (Organization $organization) {
            // Automatically create chart of accounts when organization is created
            $organization->setupChartOfAccounts();
        });
    }

    /**
     * Get the branches for the organization.
     */
    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get the departments for the organization.
     */
    public function departments(): HasMany
    {
        return $this->hasMany(Department::class);
    }

    /**
     * Get the roles for the organization.
     */
    public function roles(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Role::class);
    }

    /**
     * Get the approval workflows for the organization.
     */
    public function approvalWorkflows(): HasMany
    {
        return $this->hasMany(ApprovalWorkflow::class);
    }

    /**
     * Get the workflows for the organization.
     */
    public function workflows(): HasMany
    {
        return $this->hasMany(ApprovalWorkflow::class);
    }

    /**
     * Get the cash floats for the organization.
     */
    public function cashFloats(): HasMany
    {
        return $this->hasMany(CashFloat::class);
    }


    /**
     * Scope a query to only include active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive organizations.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * The users that belong to the organization.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_organizations');
    }

    /**
     * Get the chart of accounts for the organization.
     */
    public function chartOfAccounts(): HasMany
    {
        return $this->hasMany(ChartOfAccount::class);
    }

    /**
     * Setup standard chart of accounts for the organization.
     */
    public function setupChartOfAccounts(): void
    {
        // Use database transaction to prevent race conditions
        DB::transaction(function () {
            // Standard account types
            $standardTypes = [
                'asset' => 'Asset',
                'liability' => 'Liability',
                'equity' => 'Equity',
                'revenue' => 'Revenue',
                'expense' => 'Expense'
            ];

            foreach ($standardTypes as $type => $name) {
                // Find the platform-level parent account
                $platformAccount = ChartOfAccount::whereNull('organization_id')
                    ->where('account_type', $type)
                    ->first();

                if ($platformAccount) {
                    $code = strtoupper(substr($type, 0, 3)) . '-' . $this->id . '-000';

                    // Use updateOrCreate to handle duplicates gracefully
                    try {
                        ChartOfAccount::updateOrCreate(
                            [
                                'organization_id' => $this->id,
                                'code' => $code,
                            ],
                            [
                                'name' => $name,
                                'account_type' => $type,
                                'parent_id' => $platformAccount->id,
                                'is_active' => true,
                                'description' => $name . ' accounts'
                            ]
                        );

                        Log::info("Chart of account created/updated for organization {$this->id}", [
                            'organization_id' => $this->id,
                            'account_type' => $type,
                            'code' => $code
                        ]);
                    } catch (\Illuminate\Database\QueryException $e) {
                        // Handle constraint violations gracefully
                        if (str_contains($e->getMessage(), 'UNIQUE constraint failed')) {
                            Log::warning("Chart of account already exists for organization {$this->id}", [
                                'organization_id' => $this->id,
                                'account_type' => $type,
                                'code' => $code,
                                'error' => $e->getMessage()
                            ]);

                            // Try to find and update the existing record
                            $existingAccount = ChartOfAccount::where('organization_id', $this->id)
                                ->where('code', $code)
                                ->first();

                            if ($existingAccount) {
                                $existingAccount->update([
                                    'name' => $name,
                                    'account_type' => $type,
                                    'parent_id' => $platformAccount->id,
                                    'is_active' => true,
                                    'description' => $name . ' accounts'
                                ]);

                                Log::info("Updated existing chart of account for organization {$this->id}", [
                                    'organization_id' => $this->id,
                                    'account_type' => $type,
                                    'code' => $code
                                ]);
                            }
                        } else {
                            // Re-throw other database exceptions
                            throw $e;
                        }
                    }
                }
            }
        });
    }
}
