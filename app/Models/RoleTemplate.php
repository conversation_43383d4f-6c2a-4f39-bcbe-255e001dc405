<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoleTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_default',
        'department_template_id',
        'permissions',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'permissions' => 'array',
    ];

    /**
     * Get the department template that this role template belongs to.
     */
    public function departmentTemplate(): BelongsTo
    {
        return $this->belongsTo(DepartmentTemplate::class);
    }
}
