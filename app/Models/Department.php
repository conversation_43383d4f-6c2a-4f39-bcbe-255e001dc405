<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'branch_id',
        'name',
        'hod_user_id',
    ];

    /**
     * Get the organization that owns the department.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the branch that owns the department.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the head of department user.
     */
    public function headOfDepartment(): BelongsTo
    {
        return $this->belongsTo(User::class, 'hod_user_id');
    }

    /**
     * The users that belong to the department.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_department');
    }

    /**
     * Get the roles for the department.
     */
    public function roles(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Role::class);
    }

    /**
     * Get the cash floats for the department.
     */
    public function cashFloats(): HasMany
    {
        return $this->hasMany(CashFloat::class);
    }
}
