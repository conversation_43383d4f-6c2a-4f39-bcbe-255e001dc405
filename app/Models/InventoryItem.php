<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InventoryItem extends Model
{
    protected $fillable = [
        'organization_id',
        'branch_id',
        'sku',
        'name',
        'description',
        'unit_of_measure',
        'quantity_on_hand',
        'reorder_level',
        'last_low_stock_alert_sent_at',
    ];

    protected $casts = [
        'quantity_on_hand' => 'decimal:2',
        'reorder_level' => 'decimal:2',
        'last_low_stock_alert_sent_at' => 'datetime',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(InventoryTransaction::class)->orderBy('transaction_date', 'desc');
    }

    public function requisitionItems(): HasMany
    {
        return $this->hasMany(StoreRequisitionItem::class);
    }

    public function isLowStock(): bool
    {
        return $this->quantity_on_hand <= $this->reorder_level && $this->reorder_level > 0;
    }

    public function isOutOfStock(): bool
    {
        return $this->quantity_on_hand <= 0;
    }

    /**
     * Determine if a low stock notification should be sent.
     * Following the same pattern as CashFloat model.
     */
    public function shouldSendLowStockNotification(): bool
    {
        // Check if reorder_level is set
        if ($this->reorder_level === null || $this->reorder_level <= 0) {
            return false;
        }

        // Only send notification if current stock is below reorder level
        if ($this->quantity_on_hand > $this->reorder_level) {
            return false;
        }

        // Prevent duplicate alerts - don't send if alert was sent in last 24 hours
        if ($this->last_low_stock_alert_sent_at && $this->last_low_stock_alert_sent_at->diffInHours(now()) < 24) {
            return false;
        }

        return true;
    }

    /**
     * Mark that a low stock alert has been sent
     */
    public function markLowStockAlertSent(): void
    {
        $this->update(['last_low_stock_alert_sent_at' => now()]);
    }
}
