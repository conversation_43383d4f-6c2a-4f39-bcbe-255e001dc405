<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;

class DatabaseHelper
{
    /**
     * Get database-agnostic string concatenation expression.
     * 
     * @param array $columns Array of column names to concatenate
     * @param string $separator Separator between columns (default: ' ')
     * @return string Raw SQL expression for concatenation
     */
    public static function concat(array $columns, string $separator = ' '): string
    {
        $driver = DB::connection()->getDriverName();
        
        switch ($driver) {
            case 'sqlite':
                // SQLite uses || operator for concatenation
                return implode(" || '{$separator}' || ", $columns);
                
            case 'mysql':
            case 'mariadb':
            case 'pgsql':
                // MySQL, MariaDB, and PostgreSQL use CONCAT function
                $separatedColumns = [];
                for ($i = 0; $i < count($columns); $i++) {
                    $separatedColumns[] = $columns[$i];
                    if ($i < count($columns) - 1) {
                        $separatedColumns[] = "'{$separator}'";
                    }
                }
                return 'CONCAT(' . implode(', ', $separatedColumns) . ')';
                
            case 'sqlsrv':
                // SQL Server uses + operator for concatenation
                return implode(" + '{$separator}' + ", $columns);
                
            default:
                // Fallback to CONCAT for unknown drivers
                $separatedColumns = [];
                for ($i = 0; $i < count($columns); $i++) {
                    $separatedColumns[] = $columns[$i];
                    if ($i < count($columns) - 1) {
                        $separatedColumns[] = "'{$separator}'";
                    }
                }
                return 'CONCAT(' . implode(', ', $separatedColumns) . ')';
        }
    }
    
    /**
     * Get database-agnostic full name concatenation for users table.
     * 
     * @param string $tableAlias Optional table alias (e.g., 'users', 'u')
     * @return string Raw SQL expression for full name
     */
    public static function fullName(string $tableAlias = ''): string
    {
        $prefix = $tableAlias ? $tableAlias . '.' : '';
        return self::concat([
            $prefix . 'first_name',
            $prefix . 'last_name'
        ]);
    }
    
    /**
     * Check if current database driver is SQLite.
     * 
     * @return bool
     */
    public static function isSqlite(): bool
    {
        return DB::connection()->getDriverName() === 'sqlite';
    }
    
    /**
     * Check if current database driver is MySQL/MariaDB.
     * 
     * @return bool
     */
    public static function isMysql(): bool
    {
        $driver = DB::connection()->getDriverName();
        return in_array($driver, ['mysql', 'mariadb']);
    }
    
    /**
     * Check if current database driver is PostgreSQL.
     * 
     * @return bool
     */
    public static function isPostgres(): bool
    {
        return DB::connection()->getDriverName() === 'pgsql';
    }
    
    /**
     * Get database-agnostic LIMIT clause.
     * 
     * @param int $limit
     * @param int $offset
     * @return string
     */
    public static function limit(int $limit, int $offset = 0): string
    {
        $driver = DB::connection()->getDriverName();
        
        switch ($driver) {
            case 'sqlsrv':
                // SQL Server uses OFFSET...FETCH
                return "OFFSET {$offset} ROWS FETCH NEXT {$limit} ROWS ONLY";
            default:
                // Most databases use LIMIT...OFFSET
                return $offset > 0 ? "LIMIT {$limit} OFFSET {$offset}" : "LIMIT {$limit}";
        }
    }
    
    /**
     * Get database-agnostic date formatting.
     * 
     * @param string $column
     * @param string $format
     * @return string
     */
    public static function dateFormat(string $column, string $format = 'Y-m-d'): string
    {
        $driver = DB::connection()->getDriverName();
        
        switch ($driver) {
            case 'sqlite':
                // SQLite uses strftime
                $sqliteFormat = str_replace(['Y', 'm', 'd'], ['%Y', '%m', '%d'], $format);
                return "strftime('{$sqliteFormat}', {$column})";
                
            case 'mysql':
            case 'mariadb':
                // MySQL uses DATE_FORMAT
                $mysqlFormat = str_replace(['Y', 'm', 'd'], ['%Y', '%m', '%d'], $format);
                return "DATE_FORMAT({$column}, '{$mysqlFormat}')";
                
            case 'pgsql':
                // PostgreSQL uses TO_CHAR
                $pgFormat = str_replace(['Y', 'm', 'd'], ['YYYY', 'MM', 'DD'], $format);
                return "TO_CHAR({$column}, '{$pgFormat}')";
                
            case 'sqlsrv':
                // SQL Server uses FORMAT
                $sqlServerFormat = str_replace(['Y', 'm', 'd'], ['yyyy', 'MM', 'dd'], $format);
                return "FORMAT({$column}, '{$sqlServerFormat}')";
                
            default:
                // Fallback to standard DATE function
                return "DATE({$column})";
        }
    }
}
