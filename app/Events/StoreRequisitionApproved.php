<?php

namespace App\Events;

use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StoreRequisitionApproved
{
    use Dispatchable, SerializesModels;

    public $storeRequisition;
    public $approver;

    public function __construct(StoreRequisition $storeRequisition, User $approver)
    {
        $this->storeRequisition = $storeRequisition;
        $this->approver = $approver;
    }
}
