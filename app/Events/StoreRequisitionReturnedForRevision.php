<?php

namespace App\Events;

use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StoreRequisitionReturnedForRevision
{
    use Dispatchable, SerializesModels;

    public $storeRequisition;
    public $returner;
    public $comments;

    public function __construct(StoreRequisition $storeRequisition, User $returner, $comments = null)
    {
        $this->storeRequisition = $storeRequisition;
        $this->returner = $returner;
        $this->comments = $comments;
    }
}
