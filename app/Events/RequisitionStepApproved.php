<?php

namespace App\Events;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RequisitionStepApproved
{
    use Dispatchable, SerializesModels;

    public $requisition;
    public $nextStep;
    public $approvedStep;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $nextStep, ApprovalWorkflowStep $approvedStep)
    {
        $this->requisition = $requisition;
        $this->nextStep = $nextStep;
        $this->approvedStep = $approvedStep;
    }
}
