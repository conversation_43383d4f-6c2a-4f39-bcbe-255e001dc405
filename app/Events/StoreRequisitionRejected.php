<?php

namespace App\Events;

use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StoreRequisitionRejected
{
    use Dispatchable, SerializesModels;

    public $storeRequisition;
    public $rejector;
    public $rejectionReason;

    public function __construct(StoreRequisition $storeRequisition, User $rejector, $rejectionReason = null)
    {
        $this->storeRequisition = $storeRequisition;
        $this->rejector = $rejector;
        $this->rejectionReason = $rejectionReason;
    }
}
