<?php

namespace App\Events;

use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StoreRequisitionIssued
{
    use Dispatchable, SerializesModels;

    public $storeRequisition;
    public $issuer;
    public $isPartialIssue;

    public function __construct(StoreRequisition $storeRequisition, User $issuer, $isPartialIssue = false)
    {
        $this->storeRequisition = $storeRequisition;
        $this->issuer = $issuer;
        $this->isPartialIssue = $isPartialIssue;
    }
}
