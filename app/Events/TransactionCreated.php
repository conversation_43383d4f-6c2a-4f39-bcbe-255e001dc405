<?php

namespace App\Events;

use App\Models\Requisition;
use App\Models\Transaction;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TransactionCreated
{
    use Dispatchable, SerializesModels;

    public $requisition;
    public $transaction;

    public function __construct(Requisition $requisition, Transaction $transaction)
    {
        $this->requisition = $requisition;
        $this->transaction = $transaction;
    }
}
