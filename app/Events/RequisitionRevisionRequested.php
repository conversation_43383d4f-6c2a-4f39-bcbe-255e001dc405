<?php

namespace App\Events;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RequisitionRevisionRequested
{
    use Dispatchable, SerializesModels;

    public $requisition;
    public $workflowStep;
    public $comments;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $workflowStep, $comments = null)
    {
        $this->requisition = $requisition;
        $this->workflowStep = $workflowStep;
        $this->comments = $comments;
    }
}
