<?php

namespace App\Events;

use App\Models\Requisition;
use App\Models\ApprovalWorkflowStep;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RequisitionSubmitted
{
    use Dispatchable, SerializesModels;

    public $requisition;
    public $firstStep;

    public function __construct(Requisition $requisition, ApprovalWorkflowStep $firstStep)
    {
        $this->requisition = $requisition;
        $this->firstStep = $firstStep;
    }
}