<?php

namespace App\Traits;

trait GeneratesRelativeUrls
{
    /**
     * Generate a URL for a route
     *
     * @param string $routeName
     * @param mixed $parameters
     * @param bool $absolute
     * @return string
     */
    public function relativeRoute(string $routeName, $parameters = [], bool $absolute = false): string
    {
        $url = route($routeName, $parameters, $absolute);
        return $absolute ? $url : parse_url($url, PHP_URL_PATH);
    }

    /**
     * Generate a URL with query parameters
     *
     * @param string $routeName
     * @param mixed $parameters
     * @param bool $absolute
     * @return string
     */
    public function relativeRouteWithQuery(string $routeName, $parameters = [], bool $absolute = false): string
    {
        $url = route($routeName, $parameters, $absolute);
        if (!$absolute) {
            $parsed = parse_url($url);
            $url = $parsed['path'];
            if (isset($parsed['query'])) {
                $url .= '?' . $parsed['query'];
            }
        }
        return $url;
    }
}