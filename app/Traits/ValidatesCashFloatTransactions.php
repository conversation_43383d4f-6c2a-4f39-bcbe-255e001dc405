<?php

namespace App\Traits;

use App\Models\CashFloat;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

trait ValidatesCashFloatTransactions
{
    /**
     * Validate if a transaction can be processed against a cash float
     *
     * @param int $cashFloatId
     * @param float $amount
     * @return array{valid: bool, message: string, remaining_amount: float|null, warnings: array}
     */
    protected function validateCashFloatTransaction(int $cashFloatId, float $amount): array
    {
        try {
            $cashFloat = CashFloat::findOrFail($cashFloatId);
            $warnings = [];

            // Check if cash float is active
            if ($cashFloat->status !== 'active') {
                return [
                    'valid' => false,
                    'message' => 'Cash float is not active',
                    'remaining_amount' => null,
                    'warnings' => []
                ];
            }

            // Validate amount is positive
            if ($amount <= 0) {
                return [
                    'valid' => false,
                    'message' => 'Transaction amount must be greater than zero',
                    'remaining_amount' => null,
                    'warnings' => []
                ];
            }

            // Check if transaction would exceed remaining amount
            if ($amount > $cashFloat->remaining_amount) {
                return [
                    'valid' => false,
                    'message' => sprintf(
                        'Transaction amount ($%.2f) exceeds remaining float amount ($%.2f)',
                        $amount,
                        $cashFloat->remaining_amount
                    ),
                    'remaining_amount' => $cashFloat->remaining_amount,
                    'warnings' => []
                ];
            }

            // Calculate new remaining amount
            $newRemainingAmount = $cashFloat->remaining_amount - $amount;

            // Check if transaction would trigger alert threshold
            if ($cashFloat->alert_threshold && $newRemainingAmount <= $cashFloat->alert_threshold) {
                $warnings[] = sprintf(
                    'Warning: Transaction will bring float below alert threshold (Current: $%.2f, Threshold: $%.2f)',
                    $newRemainingAmount,
                    $cashFloat->alert_threshold
                );
            }

            // Check if transaction would bring float below 10% of initial amount
            $tenPercentThreshold = $cashFloat->initial_amount * 0.1;
            if ($newRemainingAmount <= $tenPercentThreshold) {
                $warnings[] = sprintf(
                    'Warning: Transaction will bring float below 10%% of initial amount (Current: $%.2f, 10%%: $%.2f)',
                    $newRemainingAmount,
                    $tenPercentThreshold
                );
            }

            return [
                'valid' => true,
                'message' => 'Transaction is valid',
                'remaining_amount' => $newRemainingAmount,
                'warnings' => $warnings
            ];
        } catch (ModelNotFoundException $e) {
            Log::error('Cash float not found', [
                'cash_float_id' => $cashFloatId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'valid' => false,
                'message' => 'Cash float not found',
                'remaining_amount' => null,
                'warnings' => []
            ];
        }
    }

    /**
     * Process a transaction against a cash float
     *
     * @param int $cashFloatId
     * @param float $amount
     * @param array $transactionData
     * @return array{transaction: Transaction, warnings: array}
     * @throws \Exception
     */
    protected function processCashFloatTransaction(int $cashFloatId, float $amount, array $transactionData): array
    {
        $validation = $this->validateCashFloatTransaction($cashFloatId, $amount);

        if (!$validation['valid']) {
            throw new \Exception($validation['message']);
        }

        return DB::transaction(function () use ($cashFloatId, $amount, $transactionData, $validation) {
            // Create the transaction
            $transaction = Transaction::create([
                ...$transactionData,
                'amount' => $amount,
                'cash_float_id' => $cashFloatId,
                'status' => 'completed',
                'transaction_date' => now(),
            ]);

            // Update the cash float
            $cashFloat = CashFloat::findOrFail($cashFloatId);
            $cashFloat->remaining_amount = $validation['remaining_amount'];
            
            // Update last transaction date
            $cashFloat->last_transaction_at = now();
            
            // If below alert threshold, mark as low
            if ($cashFloat->alert_threshold && $validation['remaining_amount'] <= $cashFloat->alert_threshold) {
                $cashFloat->is_low = true;
            }
            
            $cashFloat->save();

            // Log the transaction
            Log::info('Cash float transaction processed', [
                'cash_float_id' => $cashFloatId,
                'transaction_id' => $transaction->id,
                'amount' => $amount,
                'remaining_amount' => $validation['remaining_amount'],
                'warnings' => $validation['warnings']
            ]);

            return [
                'transaction' => $transaction,
                'warnings' => $validation['warnings']
            ];
        });
    }
} 