<?php

namespace App\Policies;

use App\Models\Transaction;
use App\Models\User;

class TransactionPolicy
{
    /**
     * Determine whether the user can view the transaction.
     */
    public function view(User $user, Transaction $transaction): bool
    {
        // Platform admin can view all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization as the parent requisition
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($transaction->requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Requester can view transactions for their own requisition
        if ($transaction->requisition->requester_user_id === $user->id) {
            return true;
        }

        // HOD can view transactions for requisitions from their department
        if ($user->hasRole('HOD')) {
            $userDepartments = $user->departments->pluck('id')->toArray();
            if (in_array($transaction->requisition->department_id, $userDepartments)) {
                return true;
            }
        }

        // Finance roles can view transactions
        if ($user->hasRole('Finance Manager') || $user->hasRole('Cashier')) {
            return true;
        }

        // Approvers can view transactions
        if ($user->hasRole('Approver') || $user->hasPermissionTo('approver')) {
            return true;
        }

        // Organization admin can view all transactions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can attach files to the transaction.
     *
     * Key rule: Once a requisition is approved and converted to a transaction,
     * this becomes the PRIMARY place for requestors to attach evidence.
     * Requestors can attach evidence throughout the entire transaction lifecycle.
     */
    public function attachFiles(User $user, Transaction $transaction): bool
    {
        // Platform admin can attach files to any transaction at any stage
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization as the parent requisition
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($transaction->requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // MAIN RULE: Requester can ALWAYS attach evidence to transactions for their own requisition
        // This includes pending, processing, approved, completed, etc.
        if ($transaction->requisition->requester_user_id === $user->id) {
            return true;
        }

        // Finance roles can attach files for administrative/financial documentation
        // (receipts, payment confirmations, etc.)
        if ($user->hasRole('Finance Manager') || $user->hasRole('Cashier')) {
            return true;
        }

        // Organization admin can attach files for administrative purposes
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        // HOD can attach files for departmental administrative purposes
        // (but this is more for admin docs, not evidence)
        if ($user->hasRole('HOD')) {
            $userDepartments = $user->departments->pluck('id')->toArray();
            if (in_array($transaction->requisition->department_id, $userDepartments)) {
                return true;
            }
        }

        // NOTE: Approvers are intentionally NOT given attachment permissions
        // They should review and approve/reject, but not add evidence

        return false;
    }

    /**
     * Determine whether the user can update the transaction.
     */
    public function update(User $user, Transaction $transaction): bool
    {
        // Platform admin can update all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization as the parent requisition
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($transaction->requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Finance roles can update transactions
        if ($user->hasRole('Finance Manager') || $user->hasRole('Cashier')) {
            return true;
        }

        // Organization admin can update transactions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the transaction.
     */
    public function delete(User $user, Transaction $transaction): bool
    {
        // Platform admin can delete all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization as the parent requisition
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($transaction->requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Only finance manager and organization admin can delete transactions
        if ($user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
            // Only allow deletion if transaction is not completed
            return $transaction->status !== 'completed';
        }

        return false;
    }
}
