<?php

namespace App\Policies;

use App\Models\Requisition;
use App\Models\User;
use App\Models\Department;

class RequisitionPolicy
{
    /**
     * Determine whether the user can view the requisition.
     */
    public function view(User $user, Requisition $requisition): bool
    {
        // Platform admin can view all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Requester can always view their own requisition
        if ($requisition->requester_user_id === $user->id) {
            return true;
        }

        // HOD can view requisitions from their department
        if ($user->hasRole('HOD')) {
            $userDepartments = $user->departments->pluck('id')->toArray();
            if (in_array($requisition->department_id, $userDepartments)) {
                return true;
            }
        }

        // Approvers can view requisitions they can approve
        if ($user->hasRole('Approver') || $user->hasPermissionTo('approver')) {
            return true; // Simplified - in real app, check approval workflow
        }

        // Organization admin can view all requisitions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can attach files to the requisition.
     *
     * Key rule: Once a requisition is approved and converted to a transaction,
     * attachment capability shifts to the transaction. Requestors should attach
     * evidence to the transaction, not the original requisition.
     */
    public function attachFiles(User $user, Requisition $requisition): bool
    {
        // Platform admin can attach files to any requisition
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // MAIN RULE: Requester can attach evidence ONLY until requisition is approved
        // Once approved and converted to transaction, they should use the transaction
        if ($requisition->requester_user_id === $user->id) {
            // If requisition is approved and has transactions, redirect to transaction
            if ($requisition->status === 'approved' && $requisition->transactions()->exists()) {
                return false; // Should attach to transaction instead
            }

            // For all other statuses (draft, pending_approval, rejected), allow attachment
            return in_array($requisition->status, ['draft', 'pending_approval', 'rejected']);
        }

        // HOD can attach files to requisitions from their department
        if ($user->hasRole('HOD')) {
            $userDepartments = $user->departments->pluck('id')->toArray();
            if (in_array($requisition->department_id, $userDepartments)) {
                return true;
            }
        }

        // NOTE: Approvers are intentionally NOT given attachment permissions
        // They should review and approve/reject, but not add evidence

        // Finance roles can attach files for financial documentation
        if ($user->hasRole('Finance Manager') || $user->hasRole('Cashier')) {
            return true;
        }

        // Organization admin can attach files to any requisition in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can edit the requisition.
     */
    public function update(User $user, Requisition $requisition): bool
    {
        // Platform admin can edit all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Requester can edit their own requisition if it's in draft or rejected status
        if ($requisition->requester_user_id === $user->id) {
            return in_array($requisition->status, ['draft', 'rejected']);
        }

        // Organization admin can edit requisitions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the requisition.
     */
    public function delete(User $user, Requisition $requisition): bool
    {
        // Platform admin can delete all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($requisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Requester can delete their own requisition if it's in draft status
        if ($requisition->requester_user_id === $user->id) {
            return $requisition->status === 'draft';
        }

        // Organization admin can delete requisitions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }
}
