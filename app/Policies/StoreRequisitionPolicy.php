<?php

namespace App\Policies;

use App\Models\StoreRequisition;
use App\Models\User;

class StoreRequisitionPolicy
{
    /**
     * Determine whether the user can view the store requisition.
     *
     * View Rules:
     * - Finance Manager & Organization Admin: Can view all requisitions (they are overseers)
     * - HOD: Can only view their own requisitions
     * - Store Keeper: Can view all requisitions (for approval purposes)
     * - Employee: Can only view their own requisitions
     * - Draft requisitions: Only viewable by the requester
     */
    public function view(User $user, StoreRequisition $storeRequisition): bool
    {
        // Platform admin can view all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($storeRequisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // For draft requisitions, only the requester can view them
        if ($storeRequisition->status === StoreRequisition::STATUS_DRAFT) {
            return $storeRequisition->requester_user_id === $user->id;
        }

        // Users can view their own requisitions
        if ($storeRequisition->requester_user_id === $user->id) {
            return true;
        }

        // Role-based viewing for non-draft requisitions
        if ($user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
            // Overseers can view all requisitions
            return true;
        } elseif ($user->can('store-keep')) {
            // Store keepers can view all requisitions (for approval purposes)
            return true;
        } elseif ($user->hasRole('HOD')) {
            // HOD can only view their own requisitions (already handled above)
            return false;
        } else {
            // Employees can only view their own requisitions (already handled above)
            return false;
        }
    }

    /**
     * Determine whether the user can edit the store requisition.
     * Follows the same pattern as petty cash requisitions.
     */
    public function update(User $user, StoreRequisition $storeRequisition): bool
    {
        // Platform admin can edit all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($storeRequisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Check if requisition is in an editable status
        if (!$storeRequisition->canBeEdited()) {
            return false;
        }

        // Requester can edit their own requisition if it's in editable status
        if ($storeRequisition->requester_user_id === $user->id) {
            return true;
        }

        // Organization admin can edit requisitions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the store requisition.
     */
    public function delete(User $user, StoreRequisition $storeRequisition): bool
    {
        // Platform admin can delete all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($storeRequisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Requester can delete their own requisition if it's in draft status
        if ($storeRequisition->requester_user_id === $user->id) {
            return $storeRequisition->status === StoreRequisition::STATUS_DRAFT;
        }

        // Organization admin can delete requisitions in their org
        if ($user->hasRole('Organization Admin')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can approve the store requisition.
     * Implements role-based approval workflow:
     * - Employee requisitions: Only store keepers can approve
     * - Store keeper requisitions: Overseers, Finance Managers, or Organization Admins can approve
     * - Self-approval is always prevented
     */
    public function approve(User $user, StoreRequisition $storeRequisition): bool
    {
        // Platform admin can approve all
        if ($user->is_platform_admin) {
            return true;
        }

        // Must be in same organization
        $userOrganizationIds = $user->organizations->pluck('id')->toArray();
        if (!in_array($storeRequisition->organization_id, $userOrganizationIds)) {
            return false;
        }

        // Cannot approve own requisition
        if ($storeRequisition->requester_user_id === $user->id) {
            return false;
        }

        // Must be in pending approval status
        if (!$storeRequisition->canBeApproved()) {
            return false;
        }

        // Get the requester to determine approval rules
        $requester = $storeRequisition->requester;
        if (!$requester) {
            return false;
        }

        // Determine if requester is a store keeper
        $requesterIsStoreKeeper = $requester->can('store-keep');

        // Role-based approval logic
        if ($requesterIsStoreKeeper) {
            // Store keeper requisitions can ONLY be approved by:
            // - Finance Managers
            // - Organization Admins
            // Note: HOD and other roles should NOT be able to approve store keeper requisitions
            return $user->hasRole('Finance Manager') || $user->hasRole('Organization Admin');
        } else {
            // Employee/HOD requisitions can only be approved by store keepers
            // Explicitly prevent Finance Manager and Organization Admin from approving non-store-keeper requisitions
            if ($user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
                return false;
            }
            return $user->can('store-keep');
        }
    }

    /**
     * Determine whether the user can return the store requisition for revision.
     */
    public function returnForRevision(User $user, StoreRequisition $storeRequisition): bool
    {
        // Same rules as approve, but for returning for revision
        return $this->approve($user, $storeRequisition) && $storeRequisition->canBeReturnedForRevision();
    }
}
