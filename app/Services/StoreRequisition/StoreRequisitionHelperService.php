<?php

namespace App\Services\StoreRequisition;

use App\Models\InventoryItem;
use App\Models\Branch;
use App\Models\Department;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Collection;

class StoreRequisitionHelperService
{
    public function getOrganizationInventoryItems(): Collection
    {
        $organization = Auth::user()->organizations()->first();
        
        if (!$organization) {
            return new Collection();
        }

        return InventoryItem::where('organization_id', $organization->id)
            ->orderBy('name')
            ->get();
    }

    public function getOrganizationBranches(): Collection
    {
        $organization = Auth::user()->organizations()->first();
        
        if (!$organization) {
            return new Collection();
        }

        return Branch::where('organization_id', $organization->id)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    public function getOrganizationDepartments(): Collection
    {
        $organization = Auth::user()->organizations()->first();
        
        if (!$organization) {
            return new Collection();
        }

        return Department::where('organization_id', $organization->id)
            ->orderBy('name')
            ->get();
    }

    public function getUserDepartments(): Collection
    {
        return Auth::user()->departments()->get();
    }

    public function getUserBranches(): Collection
    {
        $userDepartments = $this->getUserDepartments();
        $userBranchIds = $userDepartments->pluck('branch_id')->unique();
        
        return $this->getOrganizationBranches()
            ->whereIn('id', $userBranchIds);
    }

    public function validateUserOrganizationAccess($organizationId): bool
    {
        $userOrganizationIds = Auth::user()->organizations->pluck('id')->toArray();
        return in_array($organizationId, $userOrganizationIds);
    }

    public function getUserPermissions($user): array
    {
        try {
            $permissions = $user->getAllPermissions();
            return is_array($permissions) ? $permissions : $permissions->pluck('name')->toArray();
        } catch (\Exception $e) {
            \Log::error('Error getting user permissions: ' . $e->getMessage());
            return [];
        }
    }

    public function getUserRoles($user): array
    {
        try {
            $roles = $user->getRoleNames();
            return is_array($roles) ? $roles : $roles->toArray();
        } catch (\Exception $e) {
            \Log::error('Error getting user roles: ' . $e->getMessage());
            return [];
        }
    }

    public function loadRequesterPermissions($collections): void
    {
        foreach ($collections as $collection) {
            foreach ($collection as $requisition) {
                if ($requisition->requester) {
                    try {
                        $requisition->requester->permissions = $requisition->requester->permission_names;
                    } catch (\Exception $e) {
                        \Log::error('Error loading permissions for user ' . $requisition->requester->id . ': ' . $e->getMessage());
                        $requisition->requester->permissions = [];
                    }
                }
            }
        }
    }
}