<?php

namespace App\Services\StoreRequisition;

use App\Models\StoreRequisition;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StoreRequisitionIssueService
{
    public function issueItems(StoreRequisition $storeRequisition, array $items, ?string $notes = null): void
    {
        $stockValidation = $this->validateStockAvailability($storeRequisition, $items);
        if (!$stockValidation['valid']) {
            throw new \Exception($stockValidation['message']);
        }

        DB::beginTransaction();
        try {
            $allIssued = true;
            $partiallyIssued = false;
            $issuedItems = [];

            foreach ($items as $itemData) {
                $requisitionItem = $storeRequisition->items()->findOrFail($itemData['id']);
                $quantityIssued = $itemData['quantity_issued'];

                if ($quantityIssued > 0) {
                    $newQuantityIssued = ($requisitionItem->quantity_issued ?? 0) + $quantityIssued;
                    $requisitionItem->update(['quantity_issued' => $newQuantityIssued]);

                    $inventoryItem = $requisitionItem->inventoryItem()->lockForUpdate()->first();
                    $newBalance = $inventoryItem->quantity_on_hand - $quantityIssued;

                    $transaction = $inventoryItem->transactions()->create([
                        'user_id' => Auth::id(),
                        'transaction_type' => 'issuance',
                        'quantity_change' => -$quantityIssued,
                        'related_document_id' => $storeRequisition->id,
                        'related_document_type' => StoreRequisition::class,
                        'transaction_date' => now(),
                        'notes' => "Issued for requisition #{$storeRequisition->id} - {$storeRequisition->purpose}",
                    ]);

                    $inventoryItem->update(['quantity_on_hand' => $newBalance]);

                    $issuedItems[] = [
                        'item' => $requisitionItem,
                        'quantity' => $quantityIssued,
                        'transaction_id' => $transaction->id
                    ];
                }

                if ($newQuantityIssued < $requisitionItem->quantity_requested) {
                    $allIssued = false;
                    if ($newQuantityIssued > 0) {
                        $partiallyIssued = true;
                    }
                }
            }

            $oldStatus = $storeRequisition->status;
            $status = $allIssued
                ? StoreRequisition::STATUS_ISSUED
                : ($partiallyIssued ? StoreRequisition::STATUS_PARTIALLY_ISSUED : StoreRequisition::STATUS_APPROVED);

            $storeRequisition->update([
                'status' => $status,
                'issuer_user_id' => Auth::id(),
                'issued_at' => now(),
            ]);

            $storeRequisition->histories()->create([
                'user_id' => Auth::id(),
                'action' => 'issued',
                'comments' => $notes ?? "Items issued - {$status}",
                'changes' => [
                    'status' => ['from' => $oldStatus, 'to' => $status],
                    'issued_items_count' => count($issuedItems),
                    'total_items_count' => $storeRequisition->items->count(),
                ],
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to issue store requisition items: ' . $e->getMessage());
            throw $e;
        }
    }

    public function getIssueSuccessMessage(StoreRequisition $storeRequisition, array $items): string
    {
        $allIssued = true;
        foreach ($items as $itemData) {
            $requisitionItem = $storeRequisition->items()->findOrFail($itemData['id']);
            $newQuantityIssued = ($requisitionItem->quantity_issued ?? 0) + $itemData['quantity_issued'];
            if ($newQuantityIssued < $requisitionItem->quantity_requested) {
                $allIssued = false;
                break;
            }
        }
        
        return $allIssued ? 'All items issued successfully' : 'Items partially issued successfully';
    }

    public function validateStockAvailability(StoreRequisition $storeRequisition, array $items): array
    {
        foreach ($items as $itemData) {
            $requisitionItem = $storeRequisition->items()->findOrFail($itemData['id']);
            $quantityToIssue = $itemData['quantity_issued'];

            if ($quantityToIssue > 0) {
                $inventoryItem = $requisitionItem->inventoryItem;

                if ($inventoryItem->quantity_on_hand < $quantityToIssue) {
                    return [
                        'valid' => false,
                        'message' => "Insufficient stock for {$inventoryItem->name}. Available: {$inventoryItem->quantity_on_hand}, Requested: {$quantityToIssue}"
                    ];
                }

                $currentlyIssued = $requisitionItem->quantity_issued ?? 0;
                $totalToBeIssued = $currentlyIssued + $quantityToIssue;
                if ($totalToBeIssued > $requisitionItem->quantity_requested) {
                    return [
                        'valid' => false,
                        'message' => "Cannot issue more than requested for {$inventoryItem->name}. Requested: {$requisitionItem->quantity_requested}, Already issued: {$currentlyIssued}, Trying to issue: {$quantityToIssue}"
                    ];
                }
            }
        }

        return ['valid' => true];
    }

    public function validateStockForApi(StoreRequisition $storeRequisition, array $items): array
    {
        $errors = [];
        $warnings = [];

        foreach ($items as $itemData) {
            $requisitionItem = $storeRequisition->items()->findOrFail($itemData['id']);
            $inventoryItem = $requisitionItem->inventoryItem;
            $quantityToIssue = $itemData['quantity_issued'];

            if ($inventoryItem->quantity_on_hand < $quantityToIssue) {
                $errors[] = [
                    'item_id' => $inventoryItem->id,
                    'item_name' => $inventoryItem->name,
                    'available' => $inventoryItem->quantity_on_hand,
                    'requested' => $quantityToIssue,
                    'message' => "Insufficient stock for {$inventoryItem->name}"
                ];
            }

            $remainingStock = $inventoryItem->quantity_on_hand - $quantityToIssue;
            if ($remainingStock <= $inventoryItem->reorder_level && $inventoryItem->reorder_level > 0) {
                $warnings[] = [
                    'item_id' => $inventoryItem->id,
                    'item_name' => $inventoryItem->name,
                    'remaining_stock' => $remainingStock,
                    'reorder_level' => $inventoryItem->reorder_level,
                    'message' => "Issuing will trigger low stock alert for {$inventoryItem->name}"
                ];
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
        ];
    }
}