<?php

namespace App\Services\StoreRequisition;

use App\Models\StoreRequisition;
use Illuminate\Support\Facades\Auth;

class StoreRequisitionReportService
{
    public function generatePickingList(StoreRequisition $storeRequisition): array
    {
        return [
            'requisition_id' => $storeRequisition->id,
            'requester' => $storeRequisition->requester->name,
            'department' => $storeRequisition->department->name,
            'branch' => $storeRequisition->branch->name,
            'purpose' => $storeRequisition->purpose,
            'requested_at' => $storeRequisition->requested_at,
            'items' => $storeRequisition->items->map(function ($item) {
                return [
                    'sku' => $item->inventoryItem->sku,
                    'name' => $item->inventoryItem->name,
                    'quantity_requested' => $item->quantity_requested,
                    'current_stock' => $item->inventoryItem->quantity_on_hand,
                    'location' => $item->inventoryItem->branch?->name ?? 'Main Store',
                    'unit' => $item->inventoryItem->unit_of_measure,
                ];
            }),
            'generated_by' => Auth::user()->name,
            'generated_at' => now(),
        ];
    }

    public function generateGoodsIssueNote(StoreRequisition $storeRequisition): array
    {
        return [
            'issue_note_number' => 'GIN-' . str_pad($storeRequisition->id, 6, '0', STR_PAD_LEFT),
            'requisition_id' => $storeRequisition->id,
            'issued_to' => $storeRequisition->requester->name,
            'department' => $storeRequisition->department->name,
            'branch' => $storeRequisition->branch->name,
            'purpose' => $storeRequisition->purpose,
            'issued_by' => $storeRequisition->issuer->name,
            'issued_at' => $storeRequisition->issued_at,
            'items_issued' => $storeRequisition->items->where('quantity_issued', '>', 0)->map(function ($item) {
                return [
                    'sku' => $item->inventoryItem->sku,
                    'name' => $item->inventoryItem->name,
                    'quantity_requested' => $item->quantity_requested,
                    'quantity_issued' => $item->quantity_issued,
                    'unit' => $item->inventoryItem->unit_of_measure,
                    'remarks' => $item->quantity_issued < $item->quantity_requested ? 'Partial Issue' : 'Full Issue',
                ];
            }),
            'total_items' => $storeRequisition->items->where('quantity_issued', '>', 0)->count(),
            'status' => $storeRequisition->status,
        ];
    }
}