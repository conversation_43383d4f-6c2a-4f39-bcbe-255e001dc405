<?php

namespace App\Services\StoreRequisition;

use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Collection;

class StoreRequisitionQueryService
{
    public function getMyRequisitions(): Collection
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            return collect();
        }

        return StoreRequisition::where('organization_id', $organization->id)
            ->where('requester_user_id', $user->id)
            ->with(['requester', 'approver', 'issuer', 'items.inventoryItem', 'department', 'branch'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getPendingApprovals(): Collection
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization || (!$user->can('store-keep') && !$user->can('approve-store-requisition'))) {
            return new Collection();
        }

        $query = StoreRequisition::where('organization_id', $organization->id)
            ->where('status', StoreRequisition::STATUS_PENDING_APPROVAL)
            ->where('requester_user_id', '!=', $user->id)
            ->with(['requester', 'approver', 'issuer', 'items.inventoryItem', 'department', 'branch']);

        if ($user->can('store-keep')) {
            $query->whereHas('requester', function ($q) {
                $q->whereDoesntHave('permissions', function ($permQuery) {
                    $permQuery->where('name', 'store-keep');
                });
            });
        } elseif ($user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
            $query->whereHas('requester', function ($q) {
                $q->whereHas('permissions', function ($permQuery) {
                    $permQuery->where('name', 'store-keep');
                });
            });
        } else {
            $query->whereRaw('1 = 0');
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    public function getAllRequisitions(): Collection
    {
        $user = Auth::user();
        $organization = $user->organizations()->first();

        if (!$organization) {
            return new Collection();
        }

        if ($user->can('store-keep') || $user->hasRole('Finance Manager') || $user->hasRole('Organization Admin')) {
            return StoreRequisition::where('organization_id', $organization->id)
                ->where(function ($q) use ($user) {
                    $q->where('status', '!=', StoreRequisition::STATUS_DRAFT)
                      ->orWhere('requester_user_id', $user->id);
                })
                ->with(['requester', 'approver', 'issuer', 'items.inventoryItem', 'department', 'branch'])
                ->orderBy('created_at', 'desc')
                ->get();
        } elseif ($user->hasRole('HOD')) {
            return $this->getMyRequisitions();
        }

        return new Collection();
    }

    public function getApprovedRequisitions(): Collection
    {
        $organization = Auth::user()->organizations()->first();

        if (!$organization) {
            return new Collection();
        }

        return StoreRequisition::where('organization_id', $organization->id)
            ->where('status', StoreRequisition::STATUS_APPROVED)
            ->with(['requester', 'department', 'branch', 'items.inventoryItem'])
            ->orderBy('approved_at', 'desc')
            ->get();
    }

    public function getPartiallyIssuedRequisitions(): Collection
    {
        $organization = Auth::user()->organizations()->first();

        if (!$organization) {
            return new Collection();
        }

        return StoreRequisition::where('organization_id', $organization->id)
            ->where('status', StoreRequisition::STATUS_PARTIALLY_ISSUED)
            ->with(['requester', 'department', 'branch', 'items.inventoryItem'])
            ->orderBy('issued_at', 'desc')
            ->get();
    }

    public function getIssuedRequisitions(): Collection
    {
        $organization = Auth::user()->organizations()->first();

        if (!$organization) {
            return new Collection();
        }

        return StoreRequisition::where('organization_id', $organization->id)
            ->where('status', StoreRequisition::STATUS_ISSUED)
            ->with(['requester', 'department', 'branch', 'items.inventoryItem'])
            ->orderBy('issued_at', 'desc')
            ->get();
    }

    public function getIssueStats(): array
    {
        $organization = Auth::user()->organizations()->first();

        if (!$organization) {
            return [
                'pending_issues' => 0,
                'partial_issues' => 0,
                'issued_count' => 0,
            ];
        }

        return [
            'pending_issues' => StoreRequisition::where('organization_id', $organization->id)
                ->where('status', StoreRequisition::STATUS_APPROVED)
                ->count(),
            'partial_issues' => StoreRequisition::where('organization_id', $organization->id)
                ->where('status', StoreRequisition::STATUS_PARTIALLY_ISSUED)
                ->count(),
            'issued_count' => StoreRequisition::where('organization_id', $organization->id)
                ->where('status', StoreRequisition::STATUS_ISSUED)
                ->count(),
        ];
    }

    public function getPendingItems()
    {
        $organization = Auth::user()->organizations()->first();
        
        if (!$organization) {
            return collect();
        }

        return StoreRequisitionItem::whereHas('storeRequisition', function ($query) use ($organization) {
                $query->where('organization_id', $organization->id)
                      ->where('status', StoreRequisition::STATUS_PARTIALLY_ISSUED);
            })
            ->whereColumn('quantity_issued', '<', 'quantity_requested')
            ->with(['storeRequisition.requester', 'inventoryItem'])
            ->get()
            ->map(function ($item) {
                return [
                    'requisition_id' => $item->storeRequisition->id,
                    'requester' => $item->storeRequisition->requester->name,
                    'item_name' => $item->inventoryItem->name,
                    'sku' => $item->inventoryItem->sku,
                    'quantity_pending' => $item->quantity_requested - $item->quantity_issued,
                    'current_stock' => $item->inventoryItem->quantity_on_hand,
                    'can_fulfill' => $item->inventoryItem->quantity_on_hand >= ($item->quantity_requested - $item->quantity_issued),
                    'requested_at' => $item->storeRequisition->requested_at,
                ];
            });
    }
}