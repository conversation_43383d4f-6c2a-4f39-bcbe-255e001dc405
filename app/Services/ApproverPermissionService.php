<?php

namespace App\Services;

use App\Models\ApprovalWorkflowStep;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApproverPermissionService
{
    /**
     * Assign approver permission to a specific user
     *
     * @param int $userId
     * @return bool
     */
    public function assignApproverPermissionToUser(int $userId): bool
    {
        try {
            $user = User::find($userId);
            if ($user && !$user->hasPermissionTo('approver')) {
                $user->givePermissionTo('approver');
                Log::info("Assigned approver permission to user {$user->email} (ID: {$user->id})");
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error("Error assigning approver permission to user ID {$userId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Assign approver permission to all users with a specific role
     *
     * @param int $roleId
     * @return int Number of users updated
     */
    public function assignApproverPermissionToUsersWithRole(int $roleId): int
    {
        try {
            $count = 0;
            $usersWithRole = DB::table('model_has_roles')
                ->where('role_id', $roleId)
                ->where('model_type', 'App\\Models\\User')
                ->pluck('model_id')
                ->toArray();

            foreach ($usersWithRole as $userId) {
                if ($this->assignApproverPermissionToUser($userId)) {
                    $count++;
                }
            }
            
            return $count;
        } catch (\Exception $e) {
            Log::error("Error assigning approver permission to users with role ID {$roleId}: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Assign approver permission to all users in approval workflow steps
     *
     * @return array
     */
    public function syncAllApproverPermissions(): array
    {
        $results = [
            'direct_users' => 0,
            'role_users' => 0
        ];

        try {
            // 1. Process users directly assigned as approvers
            $approverUserIds = ApprovalWorkflowStep::whereNotNull('approver_user_id')
                ->pluck('approver_user_id')
                ->unique()
                ->toArray();
                
            foreach ($approverUserIds as $userId) {
                if ($this->assignApproverPermissionToUser($userId)) {
                    $results['direct_users']++;
                }
            }
            
            // 2. Process users with roles in approval workflows
            $roleIds = ApprovalWorkflowStep::whereNull('approver_user_id')
                ->whereNotNull('role_id')
                ->pluck('role_id')
                ->unique()
                ->toArray();
                
            foreach ($roleIds as $roleId) {
                $results['role_users'] += $this->assignApproverPermissionToUsersWithRole($roleId);
            }
        } catch (\Exception $e) {
            Log::error("Error syncing all approver permissions: " . $e->getMessage());
        }
        
        return $results;
    }
}
