<?php

namespace App\Services;

use App\Models\EmailVerificationCode;
use App\Models\User;
use App\Models\Organization;
use App\Notifications\EmailVerificationCodeNotification;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Auth\Events\Registered;
use Src\UserManagement\Application\Services\UserCreationService;
use Src\UserManagement\Application\DTOs\CreateOrganizationAdminDTO;

class EmailVerificationService
{
    /**
     * Rate limiting constants
     */
    const MAX_ATTEMPTS_PER_HOUR = 5;
    const RATE_LIMIT_KEY_PREFIX = 'email_verification_rate_limit:';

    public function __construct(
        private readonly UserCreationService $userCreationService
    ) {}

    /**
     * Send verification code to email
     */
    public function sendVerificationCode(string $email, array $registrationData, ?string $ipAddress = null): EmailVerificationCode
    {
        // Check rate limiting
        $this->checkRateLimit($email);

        // Create verification code
        $verificationCode = EmailVerificationCode::createForEmail($email, $registrationData, $ipAddress);

        // Send notification
        Notification::route('mail', $email)->notify(new EmailVerificationCodeNotification($verificationCode));

        // Increment rate limit counter
        $this->incrementRateLimit($email);

        return $verificationCode;
    }

    /**
     * Verify the code and complete user registration
     */
    public function verifyCodeAndCreateUser(string $email, string $code): array
    {
        $verificationCode = EmailVerificationCode::findValidCode($email, $code);

        if (!$verificationCode) {
            return [
                'success' => false,
                'message' => 'Invalid or expired verification code.',
                'user' => null
            ];
        }

        try {
            // Mark code as used
            $verificationCode->markAsUsed();

            // Create user from stored registration data
            $user = $this->createUserFromVerificationData($verificationCode);

            // Clear rate limiting for this email
            $this->clearRateLimit($email);

            return [
                'success' => true,
                'message' => 'Email verified successfully. Your account has been created.',
                'user' => $user
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create user after email verification', [
                'email' => $email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create account. Please try again.',
                'user' => null
            ];
        }
    }

    /**
     * Create user from verification data
     */
    protected function createUserFromVerificationData(EmailVerificationCode $verificationCode): User
    {
        $data = $verificationCode->registration_data;

        // Create the organization first
        $organization = Organization::create([
            'name' => $data['org_name'],
            'contact_email' => $data['email'],
            'contact_phone' => $data['phone'] ?? null,
            'status' => 'inactive',
        ]);

        // Always generate username from email address (for self-registration)
        $username = explode('@', $data['email'])[0];

        $adminDTO = CreateOrganizationAdminDTO::fromArray([
            'username' => $username,
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? '',
            'password' => $data['password'],
            'organization_id' => $organization->id,
            'send_notification' => false, // Don't send notification for self-registration
            'auto_verify_email' => true,
            'create_default_structures' => false, // Don't create default structures - let wizard handle this
        ]);

        $user = $this->userCreationService->createOrganizationAdmin($adminDTO);

        // Fire the registered event
        event(new Registered($user));

        return $user;
    }

    /**
     * Check if email has exceeded rate limit
     */
    protected function checkRateLimit(string $email): void
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . $email;
        $attempts = Cache::get($key, 0);

        if ($attempts >= self::MAX_ATTEMPTS_PER_HOUR) {
            throw new \Exception('Too many verification code requests. Please try again later.');
        }
    }

    /**
     * Increment rate limit counter
     */
    protected function incrementRateLimit(string $email): void
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . $email;
        $attempts = Cache::get($key, 0);
        Cache::put($key, $attempts + 1, now()->addHour());
    }

    /**
     * Clear rate limit for email
     */
    protected function clearRateLimit(string $email): void
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . $email;
        Cache::forget($key);
    }

    /**
     * Check if user can request a new code
     */
    public function canRequestNewCode(string $email): bool
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . $email;
        $attempts = Cache::get($key, 0);
        return $attempts < self::MAX_ATTEMPTS_PER_HOUR;
    }

    /**
     * Get remaining attempts for rate limiting
     */
    public function getRemainingAttempts(string $email): int
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . $email;
        $attempts = Cache::get($key, 0);
        return max(0, self::MAX_ATTEMPTS_PER_HOUR - $attempts);
    }
}
