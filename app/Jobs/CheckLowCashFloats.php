<?php

namespace App\Jobs;

use App\Models\CashFloat;
use App\Models\User;
use App\Notifications\LowCashFloatAlert;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CheckLowCashFloats implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting CheckLowCashFloats job");
        
        // Get all active cash floats with alert thresholds set
        $cashFloats = CashFloat::with(['organization', 'department', 'branch', 'user'])
            ->where('status', 'active')
            ->whereNotNull('alert_threshold')
            ->get();
        
        Log::info("Found {$cashFloats->count()} active cash floats with alert thresholds to check.");
        
        // Filter cash floats that need notifications
        $lowCashFloats = $cashFloats->filter(function ($cashFloat) {
            Log::debug("Checking cash float ID: {$cashFloat->id} with shouldSendLowBalanceNotification()");
            return $cashFloat->shouldSendLowBalanceNotification();
        });
        
        Log::info("Found {$lowCashFloats->count()} cash floats that actually require notification (below threshold and no recent alert).");

        foreach ($lowCashFloats as $cashFloat) {
            Log::debug("Processing low cash float ID: {$cashFloat->id} for notification.");
            try {
                // Get finance managers for the organization
                $financeManagers = User::whereHas('roles', function ($query) use ($cashFloat) {
                    $query->where('name', 'Finance Manager')
                          ->where('organization_id', $cashFloat->organization_id);
                })->get();

                Log::debug("Found {$financeManagers->count()} finance managers for organization ID: {$cashFloat->organization_id} (CashFloat ID: {$cashFloat->id}).");

                if ($financeManagers->isEmpty()) {
                    Log::warning("No finance managers found for organization {$cashFloat->organization_id}, skipping finance manager notification for cash float ID: {$cashFloat->id}", [
                        'cash_float_id' => $cashFloat->id,
                        'organization_id' => $cashFloat->organization_id
                    ]);
                    continue;
                }

                // Send notification to all finance managers
                foreach ($financeManagers as $financeManager) {
                    $financeManager->notify(new LowCashFloatAlert($cashFloat));
                    Log::info("Sent low cash float alert to finance manager {$financeManager->id} for cash float {$cashFloat->id}");
                }

                // If the cash float is assigned to a user, notify them as well
                if ($cashFloat->user) {
                    if (!$financeManagers->contains('id', $cashFloat->user->id)) {
                        Log::debug("Attempting to notify assigned user ID: {$cashFloat->user->id} for cash float ID: {$cashFloat->id}");
                        $cashFloat->user->notify(new LowCashFloatAlert($cashFloat));
                        Log::info("Sent low cash float alert to assigned user {$cashFloat->user->id} for cash float {$cashFloat->id}");
                    } else {
                        Log::debug("Assigned user ID: {$cashFloat->user->id} is already a finance manager, not sending duplicate notification for cash float ID: {$cashFloat->id}");
                    }
                } else {
                    Log::debug("No specific user assigned to cash float ID: {$cashFloat->id}, skipping assigned user notification.");
                }

                // Mark that alert has been sent to prevent duplicates
                $cashFloat->markAlertSent();

                Log::info("Successfully processed notifications for low cash float ID: {$cashFloat->id}", [
                    'cash_float_id' => $cashFloat->id,
                    'organization_id' => $cashFloat->organization_id,
                    'current_balance' => $cashFloat->current_balance,
                    'alert_threshold' => $cashFloat->alert_threshold
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to send low cash float notification", [
                    'cash_float_id' => $cashFloat->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        Log::info("Completed CheckLowCashFloats job");
    }
}