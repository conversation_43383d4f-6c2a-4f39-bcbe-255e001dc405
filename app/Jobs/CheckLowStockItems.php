<?php

namespace App\Jobs;

use App\Models\InventoryItem;
use App\Models\User;
use App\Notifications\InventoryLowStockAlert;
use App\Notifications\InventoryOutOfStockAlert;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CheckLowStockItems implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting CheckLowStockItems job");
        
        // Get all inventory items with reorder levels set
        $inventoryItems = InventoryItem::with(['organization', 'branch'])
            ->where('reorder_level', '>', 0)
            ->get();
        
        Log::info("Found {$inventoryItems->count()} inventory items with reorder levels to check.");
        
        // Group items by organization for targeted notifications
        $itemsByOrganization = $inventoryItems->groupBy('organization_id');
        
        foreach ($itemsByOrganization as $organizationId => $orgItems) {
            try {
                // Filter items that need low stock notifications
                $lowStockItems = $orgItems->filter(function ($item) {
                    return $item->shouldSendLowStockNotification();
                });
                
                // Separate out of stock items for urgent notifications
                $outOfStockItems = $lowStockItems->filter(function ($item) {
                    return $item->isOutOfStock();
                });
                
                $regularLowStockItems = $lowStockItems->filter(function ($item) {
                    return !$item->isOutOfStock();
                });
                
                Log::info("Organization {$organizationId}: {$lowStockItems->count()} low stock items ({$outOfStockItems->count()} out of stock, {$regularLowStockItems->count()} low stock)");
                
                if ($lowStockItems->isEmpty()) {
                    continue;
                }
                
                // Get store keepers for this organization
                $storeKeepers = User::whereHas('roles', function ($query) {
                    $query->where('name', 'Store Keeper');
                })->whereHas('organizations', function ($query) use ($organizationId) {
                    $query->where('organizations.id', $organizationId);
                })->get();
                
                Log::info("Found {$storeKeepers->count()} store keepers for organization ID: {$organizationId}");
                
                if ($storeKeepers->isEmpty()) {
                    Log::warning("No store keepers found for organization {$organizationId}, skipping inventory notifications", [
                        'organization_id' => $organizationId,
                        'low_stock_items_count' => $lowStockItems->count()
                    ]);
                    continue;
                }
                
                $notificationsSent = false;

                // Send out of stock alerts (high priority)
                if ($outOfStockItems->isNotEmpty()) {
                    foreach ($storeKeepers as $storeKeeper) {
                        try {
                            $storeKeeper->notify(new InventoryOutOfStockAlert($outOfStockItems));
                            Log::info("Sent out of stock alert to store keeper {$storeKeeper->id} for organization {$organizationId}");
                            $notificationsSent = true;
                        } catch (\Exception $e) {
                            Log::error("Failed to send out of stock alert to store keeper {$storeKeeper->id}", [
                                'error' => $e->getMessage(),
                                'organization_id' => $organizationId
                            ]);
                        }
                    }

                    // Also notify organization admins for out of stock items
                    $orgAdmins = User::whereHas('roles', function ($query) {
                        $query->where('name', 'Organization Admin');
                    })->whereHas('organizations', function ($query) use ($organizationId) {
                        $query->where('organizations.id', $organizationId);
                    })->get();

                    foreach ($orgAdmins as $admin) {
                        try {
                            $admin->notify(new InventoryOutOfStockAlert($outOfStockItems));
                            Log::info("Sent out of stock alert to organization admin {$admin->id} for organization {$organizationId}");
                            $notificationsSent = true;
                        } catch (\Exception $e) {
                            Log::error("Failed to send out of stock alert to organization admin {$admin->id}", [
                                'error' => $e->getMessage(),
                                'organization_id' => $organizationId
                            ]);
                        }
                    }
                }

                // Send regular low stock alerts
                if ($regularLowStockItems->isNotEmpty()) {
                    foreach ($storeKeepers as $storeKeeper) {
                        try {
                            $storeKeeper->notify(new InventoryLowStockAlert($regularLowStockItems));
                            Log::info("Sent low stock alert to store keeper {$storeKeeper->id} for organization {$organizationId}");
                            $notificationsSent = true;
                        } catch (\Exception $e) {
                            Log::error("Failed to send low stock alert to store keeper {$storeKeeper->id}", [
                                'error' => $e->getMessage(),
                                'organization_id' => $organizationId
                            ]);
                        }
                    }
                }

                // Only mark items as alerted if notifications were actually sent successfully
                if ($notificationsSent) {
                    foreach ($lowStockItems as $item) {
                        $item->markLowStockAlertSent();
                    }
                    Log::info("Marked {$lowStockItems->count()} items as alerted for organization {$organizationId}");
                } else {
                    Log::warning("No notifications were sent successfully for organization {$organizationId}, items not marked as alerted");
                }
                
                Log::info("Successfully processed inventory notifications for organization {$organizationId}", [
                    'organization_id' => $organizationId,
                    'low_stock_items_count' => $lowStockItems->count(),
                    'out_of_stock_items_count' => $outOfStockItems->count(),
                    'store_keepers_notified' => $storeKeepers->count()
                ]);
                
            } catch (\Exception $e) {
                Log::error("Failed to send inventory notifications for organization {$organizationId}", [
                    'organization_id' => $organizationId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        Log::info("Completed CheckLowStockItems job");
    }
}
