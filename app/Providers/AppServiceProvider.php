<?php

namespace App\Providers;

use App\Services\ApproverPermissionService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rules\Password;
use App\Models\Attachment;
use App\Models\Requisition;
use App\Models\Transaction;
use App\Policies\AttachmentPolicy;
use App\Policies\RequisitionPolicy;
use App\Policies\TransactionPolicy;
use Src\Disbursement\Application\Services\MockDisburserService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->register(RepositoryServiceProvider::class);
        $this->app->register(EventServiceProvider::class);

        // Register the mock disburser service
        $this->app->singleton(MockDisburserService::class, function ($app) {
            return new MockDisburserService();
        });

        // Register the approver permission service
        $this->app->singleton(ApproverPermissionService::class, function ($app) {
            return new ApproverPermissionService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure strong password defaults
        Password::defaults(function () {
            return Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()
                ->uncompromised();
        });

        // Register policies
        Gate::policy(Attachment::class, AttachmentPolicy::class);
        Gate::policy(Requisition::class, RequisitionPolicy::class);
        Gate::policy(Transaction::class, TransactionPolicy::class);
    }
}
