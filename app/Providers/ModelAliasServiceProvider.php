<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class ModelAliasServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // This provider previously created aliases between Laravel models and domain models
        // Since we've removed the domain models, this is no longer needed
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
