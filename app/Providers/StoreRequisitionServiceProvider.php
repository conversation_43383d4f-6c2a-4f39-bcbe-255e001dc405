<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\StoreRequisition\StoreRequisitionService;
use App\Services\StoreRequisition\StoreRequisitionIssueService;
use App\Services\StoreRequisition\StoreRequisitionQueryService;
use App\Services\StoreRequisition\StoreRequisitionReportService;
use App\Services\StoreRequisition\StoreRequisitionHelperService;

class StoreRequisitionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(StoreRequisitionService::class);
        $this->app->singleton(StoreRequisitionIssueService::class);
        $this->app->singleton(StoreRequisitionQueryService::class);
        $this->app->singleton(StoreRequisitionReportService::class);
        $this->app->singleton(StoreRequisitionHelperService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}