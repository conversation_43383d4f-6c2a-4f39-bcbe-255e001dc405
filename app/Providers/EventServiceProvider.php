<?php

namespace App\Providers;

use App\Models\ApprovalWorkflowStep;
use App\Observers\ApprovalWorkflowStepObserver;
use App\Events\RequisitionSubmitted;
use App\Events\RequisitionStepApproved;
use App\Events\RequisitionCompleted;
use App\Events\RequisitionDeclined;
use App\Events\RequisitionRevisionRequested;
use App\Events\TransactionCreated;
use App\Events\TransactionPaymentDetailsUpdated;
use App\Events\TransactionDisbursementCompleted;
use App\Events\StoreRequisitionSubmitted;
use App\Events\StoreRequisitionApproved;
use App\Events\StoreRequisitionRejected;
use App\Events\StoreRequisitionReturnedForRevision;
use App\Events\StoreRequisitionIssued;
use App\Events\InventoryLowStockDetected;
use App\Events\InventoryOutOfStockDetected;
use App\Listeners\SendRequisitionNotifications;
use App\Listeners\SendStoreRequisitionNotifications;
use App\Listeners\SendInventoryNotifications;
use App\Listeners\SendApprovalRequestNotifications;
use App\Listeners\CheckCashFloatAfterTransaction;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        RequisitionSubmitted::class => [
            SendRequisitionNotifications::class,
        ],
        RequisitionStepApproved::class => [
            SendRequisitionNotifications::class,
        ],
        RequisitionCompleted::class => [
            SendRequisitionNotifications::class,
        ],
        RequisitionDeclined::class => [
            SendRequisitionNotifications::class,
        ],
        RequisitionRevisionRequested::class => [
            SendRequisitionNotifications::class,
        ],
        TransactionCreated::class => [
            SendRequisitionNotifications::class,
        ],
        TransactionPaymentDetailsUpdated::class => [
            SendRequisitionNotifications::class,
        ],
        TransactionDisbursementCompleted::class => [
            SendRequisitionNotifications::class,
            CheckCashFloatAfterTransaction::class,
        ],

        // Store Requisition Events
        StoreRequisitionSubmitted::class => [
            SendStoreRequisitionNotifications::class,
        ],
        StoreRequisitionApproved::class => [
            SendStoreRequisitionNotifications::class,
        ],
        StoreRequisitionRejected::class => [
            SendStoreRequisitionNotifications::class,
        ],
        StoreRequisitionReturnedForRevision::class => [
            SendStoreRequisitionNotifications::class,
        ],
        StoreRequisitionIssued::class => [
            SendStoreRequisitionNotifications::class,
        ],

        // Inventory Events
        InventoryLowStockDetected::class => [
            SendInventoryNotifications::class,
        ],
        InventoryOutOfStockDetected::class => [
            SendInventoryNotifications::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        // Register observers
        ApprovalWorkflowStep::observe(ApprovalWorkflowStepObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
