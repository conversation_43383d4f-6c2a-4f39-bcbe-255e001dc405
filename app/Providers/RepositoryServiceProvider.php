<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Src\Requisition\Domain\Repositories\RequisitionRepositoryInterface;
use Src\Requisition\Infrastructure\Repositories\EloquentRequisitionRepository;
use Src\Requisition\Domain\Repositories\RequisitionItemRepositoryInterface;
use Src\Requisition\Infrastructure\Repositories\EloquentRequisitionItemRepository;
use Src\Requisition\Domain\Repositories\RequisitionHistoryRepositoryInterface;
use Src\Requisition\Infrastructure\Repositories\EloquentRequisitionHistoryRepository;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowRepositoryInterface;
use Src\ApprovalWorkflow\Infrastructure\Repositories\EloquentApprovalWorkflowRepository;
use Src\ApprovalWorkflow\Domain\Repositories\ApprovalWorkflowStepRepositoryInterface;
use Src\ApprovalWorkflow\Infrastructure\Repositories\EloquentApprovalWorkflowStepRepository;
use Src\Disbursement\Domain\Repositories\TransactionRepositoryInterface;
use Src\Disbursement\Infrastructure\Repositories\EloquentTransactionRepository;
use Src\Disbursement\Domain\Repositories\TransactionItemRepositoryInterface;
use Src\Disbursement\Infrastructure\Repositories\EloquentTransactionItemRepository;
use Src\Attachment\Domain\Repositories\AttachmentRepositoryInterface;
use Src\Attachment\Infrastructure\Repositories\EloquentAttachmentRepository;
use Src\WorkflowTemplate\Domain\Repositories\WorkflowTemplateRepositoryInterface;
use Src\WorkflowTemplate\Infrastructure\Repositories\EloquentWorkflowTemplateRepository;
use Src\UserManagement\Domain\Repositories\UserRepositoryInterface;
use Src\UserManagement\Infrastructure\Repositories\EloquentUserRepository;
use Src\UserManagement\Domain\Repositories\UserOrganizationRepositoryInterface;
use Src\UserManagement\Infrastructure\Repositories\EloquentUserOrganizationRepository;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Requisition repositories
        $this->app->bind(
            RequisitionRepositoryInterface::class,
            EloquentRequisitionRepository::class
        );

        $this->app->bind(
            RequisitionItemRepositoryInterface::class,
            EloquentRequisitionItemRepository::class
        );

        $this->app->bind(
            RequisitionHistoryRepositoryInterface::class,
            EloquentRequisitionHistoryRepository::class
        );

        // Register ApprovalWorkflow repositories
        $this->app->bind(
            ApprovalWorkflowRepositoryInterface::class,
            EloquentApprovalWorkflowRepository::class
        );

        $this->app->bind(
            ApprovalWorkflowStepRepositoryInterface::class,
            EloquentApprovalWorkflowStepRepository::class
        );

        // Register Disbursement repositories
        $this->app->bind(
            TransactionRepositoryInterface::class,
            EloquentTransactionRepository::class
        );

        $this->app->bind(
            TransactionItemRepositoryInterface::class,
            EloquentTransactionItemRepository::class
        );

        // Register Attachment repositories
        $this->app->bind(
            AttachmentRepositoryInterface::class,
            EloquentAttachmentRepository::class
        );

        // Register WorkflowTemplate repositories
        $this->app->bind(
            WorkflowTemplateRepositoryInterface::class,
            EloquentWorkflowTemplateRepository::class
        );

        // Register UserManagement repositories
        $this->app->bind(
            UserRepositoryInterface::class,
            EloquentUserRepository::class
        );

        $this->app->bind(
            UserOrganizationRepositoryInterface::class,
            EloquentUserOrganizationRepository::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
