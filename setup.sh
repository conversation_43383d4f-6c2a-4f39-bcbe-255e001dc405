#!/bin/bash

# Print colorful messages
print_success() {
    echo -e "\033[0;32m$1\033[0m"
}

print_error() {
    echo -e "\033[0;31m$1\033[0m"
}

print_info() {
    echo -e "\033[0;34m$1\033[0m"
}

# Check PHP version
print_info "Checking PHP version..."
if ! command -v php &> /dev/null; then
    print_error "PHP 8.2 or higher is required but not installed"
    exit 1
fi

PHP_VERSION=$(php -r "echo PHP_VERSION;")
if [[ $(echo "$PHP_VERSION 8.2" | tr " " "\n" | sort -V | head -n 1) != "8.2" ]]; then
    print_error "PHP 8.2 or higher is required. Current version: $PHP_VERSION"
    exit 1
fi

# Check required PHP extensions
print_info "Checking required PHP extensions..."
REQUIRED_EXTENSIONS=("pdo" "pdo_sqlite" "fileinfo" "json" "mbstring" "openssl" "tokenizer" "xml")
MISSING_EXTENSIONS=()

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if ! php -m | grep -q "$ext"; then
        MISSING_EXTENSIONS+=("$ext")
    fi
done

if [ ${#MISSING_EXTENSIONS[@]} -ne 0 ]; then
    print_error "The following PHP extensions are required but missing: ${MISSING_EXTENSIONS[*]}"
    print_info "Please install them using your package manager. For example:"
    print_info "sudo apt-get install php8.2-${MISSING_EXTENSIONS[0]} php8.2-${MISSING_EXTENSIONS[1]} ..."
    exit 1
fi

# Check Composer version
print_info "Checking Composer..."
if ! command -v composer &> /dev/null; then
    print_error "Composer is required but not installed"
    exit 1
fi

# Check Node.js version
print_info "Checking Node.js..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is required but not installed"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2)
if [[ $(echo "$NODE_VERSION 18.0.0" | tr " " "\n" | sort -V | head -n 1) != "18.0.0" ]]; then
    print_error "Node.js 18.0.0 or higher is required. Current version: $NODE_VERSION"
    exit 1
fi

# Check npm
print_info "Checking npm..."
if ! command -v npm &> /dev/null; then
    print_error "npm is required but not installed"
    exit 1
fi

# Clean installation
print_info "Cleaning previous installation..."
rm -rf vendor node_modules composer.lock package-lock.json

# Install PHP dependencies
print_info "Installing PHP dependencies..."
composer install --no-interaction --prefer-dist --optimize-autoloader

# Install disposable email package if not already installed
print_info "Checking disposable email security package..."
if ! composer show erag/laravel-disposable-email &> /dev/null; then
    print_info "Installing disposable email security package..."
    composer require erag/laravel-disposable-email --no-interaction

    # Install the package configuration
    print_info "Installing disposable email package configuration..."
    php artisan erag:install-disposable-email

    print_success "Disposable email package installed and configured successfully!"
else
    print_success "Disposable email package already installed!"
fi

# Install Node.js dependencies
print_info "Installing Node.js dependencies..."
npm install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_info "Creating .env file..."
    cp .env.example .env
    php artisan key:generate

    # Update database configuration in .env
    print_info "Configuring database in .env..."
    sed -i 's/DB_CONNECTION=.*/DB_CONNECTION=sqlite/g' .env
    sed -i '/DB_HOST=/d' .env
    sed -i '/DB_PORT=/d' .env
    sed -i '/DB_DATABASE=/d' .env
    sed -i '/DB_USERNAME=/d' .env
    sed -i '/DB_PASSWORD=/d' .env
    echo "DB_DATABASE=$(pwd)/database/database.sqlite" >> .env
fi

# Create storage symlink
print_info "Creating storage symlink..."
php artisan storage:link

# Create SQLite database
print_info "Setting up database..."
if [ ! -f database/database.sqlite ]; then
    touch database/database.sqlite
fi

# Run migrations and seeders
print_info "Running migrations and seeders..."

# Publish the Spatie permission package migrations
print_info "Publishing Spatie permission package migrations..."
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider" --force

# First, run the base migrations
php artisan migrate:fresh

# Verify that the roles table unique constraint is updated for multiple organization admins
print_info "Verifying roles table unique constraint..."
if ! php artisan tinker --execute="DB::getSchemaBuilder()->getColumnListing('roles');" | grep -q "roles_name_guard_organization_unique"; then
    print_info "Updating roles table unique constraint..."
    php artisan migrate --path=database/migrations/2025_04_21_115810_update_roles_table_unique_constraint.php
fi

# Run the seeders using the DatabaseSeeder which will run all seeders in the correct order
print_info "Running seeders..."
php artisan db:seed

# Verify that the permissions table has the description field
print_info "Verifying permissions table structure..."
if ! php artisan tinker --execute="Schema::hasColumn('permissions', 'description') ? 'Description column exists' : 'Description column does not exist';" | grep -q "Description column exists"; then
    print_info "Adding description column to permissions table..."
    php artisan migrate --path=database/migrations/2025_04_15_164919_add_description_to_permissions_table.php

    # Re-run the RoleAndPermissionSeeder to update the descriptions
    print_info "Updating permission descriptions..."
    php artisan db:seed --class=RoleAndPermissionSeeder --force
fi

# Sync disposable email blacklist
print_info "Syncing disposable email blacklist..."
php artisan erag:sync-disposable-email-list
print_success "Disposable email blacklist synced successfully!"

# Build assets
print_info "Building assets..."
npm run build

# Set correct permissions
print_info "Setting file permissions..."
chmod -R 777 storage bootstrap/cache

# Clear all caches
print_info "Clearing caches..."
php artisan optimize:clear

# cache issues workaround
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p storage/framework/cache/data

php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Setup cron jobs for production
setup_cron_jobs() {
    print_info "Setting up cron jobs..."
    
    # Get current directory and PHP path
    APP_PATH=$(pwd)
    PHP_PATH=$(which php)
    
    print_info "Application path: $APP_PATH"
    print_info "PHP path: $PHP_PATH"
    
    # Create temporary cron file
    TEMP_CRON=$(mktemp)
    
    # Get existing cron jobs (if any)
    crontab -l 2>/dev/null > "$TEMP_CRON" || true
    
    # Remove any existing Laravel-related cron jobs to avoid duplicates
    sed -i '/artisan schedule:run/d' "$TEMP_CRON"
    sed -i '/artisan queue:work/d' "$TEMP_CRON"
    sed -i '/artisan cash-float:cron-check/d' "$TEMP_CRON"
    
    # Add new cron jobs
    echo "# Laravel Queue Processing (every minute)" >> "$TEMP_CRON"
    echo "* * * * * cd $APP_PATH && $PHP_PATH artisan schedule:run >> /dev/null 2>&1" >> "$TEMP_CRON"
    echo "" >> "$TEMP_CRON"
    echo "# Cash Float Monitoring (every 30 minutes)" >> "$TEMP_CRON"
    echo "*/30 * * * * cd $APP_PATH && $PHP_PATH artisan cash-float:cron-check >/dev/null 2>&1" >> "$TEMP_CRON"
    echo "" >> "$TEMP_CRON"
    
    # Install the new cron jobs
    crontab "$TEMP_CRON"
    
    # Clean up
    rm "$TEMP_CRON"
    
    print_success "Cron jobs installed successfully!"
    print_info "Current cron jobs:"
    crontab -l | grep -E "(artisan|Laravel)" || print_info "No Laravel cron jobs found"
}


print_success "Setup complete! You can now run 'php artisan serve'"
print_info "
Available commands:
- 'php artisan serve'    : Start development server
- 'npm run dev'          : Start Vite development server with hot reload
- 'npm run build'        : Build assets for production
- 'composer test'        : Run tests
- 'npm run lint'         : Run frontend linter
- 'npm run format'       : Format frontend code
"

print_info "
Login credentials:
- Platform Admin: <EMAIL> / password
- Organization Admin: <EMAIL> / password
- Admin (Finance): <EMAIL> / password
- Cashier (Finance): <EMAIL> / password
- Reviewer (HOD): <EMAIL> / password
- Approver (Manager): <EMAIL> / password
- Requestor (Employee): <EMAIL> / password

Registration:
- The registration page is now a two-step process for creating organization admins
- Step 1: Enter personal details
- Step 2: Create a new organization
- Regular users cannot self-register; they must be invited by an organization admin

Multiple Organization Support:
- The system supports multiple organizations, each with its own organization admin(s)
- Each organization can have multiple organization admins
- Organization admins can only manage users and roles within their organization
- Platform admins can manage all organizations and their admins
- Roles are organization-specific and not shared across organizations

Security Features:
- Disposable email validation: Automatically installed and configured during setup
- Blocks 112,000+ temporary email providers during registration
- Email verification: 6-digit codes sent to verify email addresses
- To update disposable email blacklist: php artisan erag:sync-disposable-email-list
"

# Temporary solution
npm install sonner
php artisan migrate:fresh --seed
