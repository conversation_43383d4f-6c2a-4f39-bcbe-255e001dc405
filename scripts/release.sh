#!/bin/bash
set -e

# Function to log with timestamp
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to run command with logging
run_command() {
    log "Running: $1"
    if eval "$1"; then
        log "✓ Success: $1"
    else
        log "✗ Failed: $1"
        exit 1
    fi
}

log "Starting release process..."

# Database operations
run_command "php artisan migrate --force"

# Cache operations (clear first, then rebuild)
run_command "php artisan config:clear"
run_command "php artisan config:cache"

run_command "php artisan route:clear"
run_command "php artisan route:cache"

run_command "php artisan view:clear"
run_command "php artisan view:cache"

# Optional: Queue restart (if using queue workers)
run_command "php artisan queue:work"

log "Release process completed successfully!"
