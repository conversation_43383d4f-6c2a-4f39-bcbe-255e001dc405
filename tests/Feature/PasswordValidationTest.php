<?php

namespace Tests\Feature;

use App\Rules\StrongPassword;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class PasswordValidationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test strong password validation rule
     */
    public function test_strong_password_validation()
    {
        $rule = new StrongPassword();

        // Test valid passwords
        $validPasswords = [
            'MySecure123!',
            'Complex@Pass1',
            'Strong#Password2',
            'Valid$123Pass',
        ];

        foreach ($validPasswords as $password) {
            $validator = Validator::make(['password' => $password], [
                'password' => [$rule]
            ]);
            
            $this->assertTrue($validator->passes(), "Password '{$password}' should be valid");
        }

        // Test invalid passwords
        $invalidPasswords = [
            'short',                    // Too short
            'nouppercase123!',         // No uppercase
            'NOLOWERCASE123!',         // No lowercase
            'NoNumbers!',              // No numbers
            'NoSpecialChars123',       // No special characters
            'password',                // Common password
            'Password123',             // No special characters
            '12345678',                // Common password, no letters
        ];

        foreach ($invalidPasswords as $password) {
            $validator = Validator::make(['password' => $password], [
                'password' => [$rule]
            ]);
            
            $this->assertTrue($validator->fails(), "Password '{$password}' should be invalid");
        }
    }

    /**
     * Test user registration with strong password
     */
    public function test_user_registration_with_strong_password()
    {
        $validData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'StrongPass123!',
            'password_confirmation' => 'StrongPass123!',
            'org_name' => 'Test Organization',
        ];

        $response = $this->post('/register', $validData);

        // Should redirect to email verification
        $response->assertRedirect('/verify-email-code');
    }

    /**
     * Test user registration with weak password
     */
    public function test_user_registration_with_weak_password()
    {
        $invalidData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'weak',
            'password_confirmation' => 'weak',
            'org_name' => 'Test Organization',
        ];

        $response = $this->post('/register', $invalidData);
        
        // Should return validation errors
        $response->assertSessionHasErrors('password');
    }

    /**
     * Test user registration with common password
     */
    public function test_user_registration_with_common_password()
    {
        $invalidData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'org_name' => 'Test Organization',
        ];

        $response = $this->post('/register', $invalidData);
        
        // Should return validation errors
        $response->assertSessionHasErrors('password');
    }
}
