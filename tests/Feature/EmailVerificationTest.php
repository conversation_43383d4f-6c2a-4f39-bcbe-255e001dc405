<?php
/*
namespace Tests\Feature;

use App\Models\EmailVerificationCode;
use App\Models\User;
use App\Services\EmailVerificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class EmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    public function test_registration_redirects_to_verification_page()
    {
        $response = $this->post('/register', [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'TestPassword123!',
            'password_confirmation' => 'TestPassword123!',
            'org_name' => 'Test Organization',
        ]);

        $response->assertRedirect(route('email.verification.code.show'));
        $this->assertTrue(Session::has('pending_email_verification'));
    }

    public function test_verification_code_is_created_and_sent()
    {
        $service = new EmailVerificationService();
        $registrationData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'TestPassword123!',
            'org_name' => 'Test Organization',
        ];

        $verificationCode = $service->sendVerificationCode(
            '<EMAIL>',
            $registrationData,
            '127.0.0.1'
        );

        $this->assertInstanceOf(EmailVerificationCode::class, $verificationCode);
        $this->assertEquals('<EMAIL>', $verificationCode->email);
        $this->assertEquals(6, strlen($verificationCode->code));
        $this->assertTrue($verificationCode->isValid());
        $this->assertEquals($registrationData, $verificationCode->registration_data);
    }

    public function test_valid_code_creates_user_and_organization()
    {
        $service = new EmailVerificationService();
        $registrationData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'TestPassword123!',
            'org_name' => 'Test Organization',
        ];

        // Create verification code
        $verificationCode = $service->sendVerificationCode(
            '<EMAIL>',
            $registrationData,
            '127.0.0.1'
        );

        // Verify the code
        $result = $service->verifyCodeAndCreateUser(
            '<EMAIL>',
            $verificationCode->code
        );

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(User::class, $result['user']);
        $this->assertEquals('testuser', $result['user']->username);
        $this->assertEquals('<EMAIL>', $result['user']->email);

        // Refresh the user to get the latest data
        $result['user']->refresh();
        $this->assertNotNull($result['user']->email_verified_at);

        // Check that verification code is marked as used
        $verificationCode->refresh();
        $this->assertTrue($verificationCode->used);
    }

    public function test_invalid_code_returns_error()
    {
        $service = new EmailVerificationService();
        
        $result = $service->verifyCodeAndCreateUser(
            '<EMAIL>',
            '123456'
        );

        $this->assertFalse($result['success']);
        $this->assertNull($result['user']);
        $this->assertStringContainsString('Invalid or expired', $result['message']);
    }

    public function test_expired_code_returns_error()
    {
        $verificationCode = EmailVerificationCode::create([
            'email' => '<EMAIL>',
            'code' => '123456',
            'registration_data' => ['test' => 'data'],
            'expires_at' => now()->subMinutes(20), // Expired
            'ip_address' => '127.0.0.1',
        ]);

        $service = new EmailVerificationService();
        $result = $service->verifyCodeAndCreateUser(
            '<EMAIL>',
            '123456'
        );

        $this->assertFalse($result['success']);
        $this->assertNull($result['user']);
    }

    public function test_rate_limiting_works()
    {
        $service = new EmailVerificationService();
        $registrationData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'TestPassword123!',
            'org_name' => 'Test Organization',
        ];

        // Send maximum allowed codes
        for ($i = 0; $i < 5; $i++) {
            $service->sendVerificationCode(
                '<EMAIL>',
                $registrationData,
                '127.0.0.1'
            );
        }

        // Next attempt should fail
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Too many verification code requests');
        
        $service->sendVerificationCode(
            '<EMAIL>',
            $registrationData,
            '127.0.0.1'
        );
    }

    public function test_cleanup_removes_expired_codes()
    {
        // Create expired code
        EmailVerificationCode::create([
            'email' => '<EMAIL>',
            'code' => '123456',
            'registration_data' => ['test' => 'data'],
            'expires_at' => now()->subMinutes(20),
            'ip_address' => '127.0.0.1',
        ]);

        // Create valid code
        EmailVerificationCode::create([
            'email' => '<EMAIL>',
            'code' => '654321',
            'registration_data' => ['test' => 'data'],
            'expires_at' => now()->addMinutes(10),
            'ip_address' => '127.0.0.1',
        ]);

        $this->assertEquals(2, EmailVerificationCode::count());

        $deletedCount = EmailVerificationCode::cleanupExpired();

        $this->assertEquals(1, $deletedCount);
        $this->assertEquals(1, EmailVerificationCode::count());
    }
}
*/
