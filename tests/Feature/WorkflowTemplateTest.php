<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\WorkflowTemplate;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Src\WorkflowTemplate\Application\Services\WorkflowTemplateService;

class WorkflowTemplateTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create([
            'is_platform_admin' => true
        ]);

        // Create a test organization
        $this->organization = Organization::factory()->create();
    }

    public function test_can_get_categories()
    {
        WorkflowTemplate::create([
            'name' => 'Template 1',
            'category' => 'Category A',
            'template_data' => ['steps' => []],
            'is_active' => true,
            'sort_order' => 1
        ]);

        WorkflowTemplate::create([
            'name' => 'Template 2',
            'category' => 'Category B',
            'template_data' => ['steps' => []],
            'is_active' => true,
            'sort_order' => 2
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('workflow-templates.categories'));

        $response->assertStatus(200);
        $response->assertJsonCount(2);
        $response->assertJsonFragment(['Category A']);
        $response->assertJsonFragment(['Category B']);
    }

    public function test_role_mapping_works_correctly()
    {
        // Create a department for testing
        $department = \App\Models\Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Department'
        ]);

        // Create roles for the organization
        $hodRole = Role::create([
            'name' => 'HOD',
            'organization_id' => $this->organization->id,
            'department_id' => $department->id,
            'guard_name' => 'web'
        ]);

        $financeRole = Role::create([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id,
            'guard_name' => 'web'
        ]);

        // Create a template with steps that should map to these roles
        $template = WorkflowTemplate::create([
            'name' => 'Test Template',
            'category' => 'Test Category',
            'template_data' => [
                'name' => 'Test Workflow',
                'steps' => [
                    [
                        'step_number' => 1,
                        'role_id' => null,
                        'description' => 'Department Head Approval'
                    ],
                    [
                        'step_number' => 2,
                        'role_id' => null,
                        'description' => 'Finance Manager Approval'
                    ]
                ]
            ],
            'is_active' => true,
            'sort_order' => 1
        ]);

        $service = app(WorkflowTemplateService::class);
        $result = $service->previewWorkflowFromTemplate($template->id, [
            'organization_id' => $this->organization->id,
            'department_id' => $department->id
        ]);

        // Verify that roles were mapped correctly
        $this->assertEquals($hodRole->id, $result['steps'][0]['role_id']);
        $this->assertEquals($financeRole->id, $result['steps'][1]['role_id']);
    }

    public function test_can_create_workflow_from_template()
    {
        // Create a department for testing
        $department = \App\Models\Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Department'
        ]);

        // Create roles for the organization
        $hodRole = Role::create([
            'name' => 'HOD',
            'organization_id' => $this->organization->id,
            'department_id' => $department->id,
            'guard_name' => 'web'
        ]);

        $financeRole = Role::create([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id,
            'guard_name' => 'web'
        ]);

        // Create a template
        $template = WorkflowTemplate::create([
            'name' => 'Test Template',
            'category' => 'Test Category',
            'template_data' => [
                'name' => 'Test Workflow',
                'steps' => [
                    [
                        'step_number' => 1,
                        'role_id' => null,
                        'description' => 'Department Head Approval'
                    ],
                    [
                        'step_number' => 2,
                        'role_id' => null,
                        'description' => 'Finance Manager Approval'
                    ]
                ]
            ],
            'is_active' => true,
            'sort_order' => 1
        ]);

        $response = $this->actingAs($this->user)
            ->post(route('workflow-templates.create-from-template', $template->id), [
                'organization_id' => $this->organization->id,
                'department_id' => $department->id,
                'custom_name' => 'My Custom Workflow',
                'is_default' => false
            ]);

        $response->assertRedirect();

        // Verify workflow was created
        $this->assertDatabaseHas('approval_workflows', [
            'name' => 'My Custom Workflow',
            'organization_id' => $this->organization->id
        ]);

        // Verify steps were created with correct roles
        $this->assertDatabaseHas('approval_workflow_steps', [
            'step_number' => 1,
            'role_id' => $hodRole->id,
            'description' => 'Department Head Approval'
        ]);

        $this->assertDatabaseHas('approval_workflow_steps', [
            'step_number' => 2,
            'role_id' => $financeRole->id,
            'description' => 'Finance Manager Approval'
        ]);
    }

    public function test_validation_handles_missing_department_roles()
    {
        // Create a department WITHOUT an HOD role
        $department = \App\Models\Department::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Finance Department'
        ]);

        // Create only a Finance Manager role (no HOD)
        Role::create([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id,
            'guard_name' => 'web'
        ]);

        // Create a template that requires HOD
        $template = WorkflowTemplate::create([
            'name' => 'Test Template',
            'category' => 'Test Category',
            'template_data' => [
                'name' => 'Test Workflow',
                'steps' => [
                    [
                        'step_number' => 1,
                        'role_id' => null,
                        'description' => 'Department Head Approval'
                    ]
                ]
            ],
            'is_active' => true,
            'sort_order' => 1
        ]);

        $service = app(WorkflowTemplateService::class);

        // Test validation with department that has no HOD
        $validation = $service->validateTemplateData($template->id, [
            'organization_id' => $this->organization->id,
            'department_id' => $department->id
        ]);

        // Should be invalid due to missing HOD role
        $this->assertFalse($validation['valid']);
        $this->assertArrayHasKey('steps.0.role_missing', $validation['errors']);
        $this->assertStringContainsString('No Head of Department role found', $validation['errors']['steps.0.role_missing'][0]);
    }

    public function test_validation_requires_department_for_hod_roles()
    {
        // Create a template that requires HOD
        $template = WorkflowTemplate::create([
            'name' => 'Test Template',
            'category' => 'Test Category',
            'template_data' => [
                'name' => 'Test Workflow',
                'steps' => [
                    [
                        'step_number' => 1,
                        'role_id' => null,
                        'description' => 'Department Head Approval'
                    ]
                ]
            ],
            'is_active' => true,
            'sort_order' => 1
        ]);

        $service = app(WorkflowTemplateService::class);

        // Test validation without department context
        $validation = $service->validateTemplateData($template->id, [
            'organization_id' => $this->organization->id
            // No department_id provided
        ]);

        // Should be invalid due to missing department context
        $this->assertFalse($validation['valid']);
        $this->assertArrayHasKey('steps.0.department_required', $validation['errors']);
        $this->assertStringContainsString('Department selection is required', $validation['errors']['steps.0.department_required'][0]);
    }
}
