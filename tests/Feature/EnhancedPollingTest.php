<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('enhanced polling endpoint returns correct structure', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)
        ->getJson(route('notifications.latest'));

    $response->assertStatus(200)
        ->assertJsonStructure([
            'notifications',
            'unread_count',
            'timestamp',
            'has_new'
        ]);
});

test('enhanced polling returns notifications without timestamp filter', function () {
    $user = User::factory()->create();

    // Create a simple notification directly in the database
    $user->notifications()->create([
        'id' => \Illuminate\Support\Str::uuid(),
        'type' => 'App\\Notifications\\TestNotification',
        'data' => [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'type' => 'requisition_submission_confirmation'
        ],
        'created_at' => now(),
        'updated_at' => now()
    ]);

    $response = $this->actingAs($user)
        ->getJson(route('notifications.latest'));

    $response->assertStatus(200)
        ->assertJson([
            'has_new' => true,
            'unread_count' => 1
        ])
        ->assertJsonCount(1, 'notifications');
});

test('enhanced polling returns empty when no new notifications', function () {
    $user = User::factory()->create();

    // Get current timestamp
    $currentTimestamp = now()->toISOString();

    $response = $this->actingAs($user)
        ->getJson(route('notifications.latest', ['since' => $currentTimestamp]));

    $response->assertStatus(200)
        ->assertJson([
            'has_new' => false,
            'unread_count' => 0
        ])
        ->assertJsonCount(0, 'notifications');
});

test('enhanced polling includes toast data for notifications', function () {
    $user = User::factory()->create();

    // Create a simple notification directly in the database
    $user->notifications()->create([
        'id' => \Illuminate\Support\Str::uuid(),
        'type' => 'App\\Notifications\\TestNotification',
        'data' => [
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'type' => 'requisition_submission_confirmation'
        ],
        'created_at' => now(),
        'updated_at' => now()
    ]);

    $response = $this->actingAs($user)
        ->getJson(route('notifications.latest'));

    $response->assertStatus(200)
        ->assertJsonStructure([
            'notifications' => [
                '*' => [
                    'id',
                    'type',
                    'data',
                    'read_at',
                    'created_at',
                    'toast' => [
                        'title',
                        'message',
                        'type',
                        'duration',
                        'action_url',
                        'dismissible'
                    ]
                ]
            ]
        ]);
});

test('enhanced polling validates timestamp parameter', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)
        ->getJson(route('notifications.latest', ['since' => 'invalid-timestamp']));

    $response->assertStatus(400)
        ->assertJson(['error' => 'Invalid timestamp']);
});
