<?php

namespace Tests\Feature;

use App\Events\StoreRequisitionSubmitted;
use App\Events\StoreRequisitionApproved as StoreRequisitionApprovedEvent;
use App\Events\StoreRequisitionRejected as StoreRequisitionRejectedEvent;
use App\Events\StoreRequisitionIssued as StoreRequisitionIssuedEvent;
use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use App\Notifications\StoreRequisitionPendingApproval;
use App\Notifications\StoreRequisitionApproved;
use App\Notifications\StoreRequisitionRejected;
use App\Notifications\StoreRequisitionIssued;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionNotificationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);
        $this->employee->departments()->attach($this->department->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);
        $this->storeKeeper->departments()->attach($this->department->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);
        $this->overseer->departments()->attach($this->department->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Assign permissions
        $this->employee->givePermissionTo('create-store-requisition');
        $this->storeKeeper->givePermissionTo(['approve-store-requisition', 'issue-store-items', 'store-keep']);
        $this->overseer->givePermissionTo(['approve-store-requisition']);
    }

    public function test_complete_store_requisition_workflow_with_notifications()
    {
        Notification::fake();

        // Step 1: Create and submit store requisition
        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        $storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $this->actingAs($this->employee);

        // Submit the store requisition
        $response = $this->post(route('store-requisitions.submit', $storeRequisition));
        $response->assertRedirect();

        // Note: Approval request notifications depend on complex role-based logic
        // For integration test, we just verify the workflow completes without errors

        // Step 2: Approve the store requisition
        Notification::fake(); // Reset notifications
        $this->actingAs($this->storeKeeper);

        $response = $this->post(route('store-requisitions.approve', $storeRequisition));
        $response->assertRedirect();

        // Verify approval notification was sent to requester
        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionApproved::class
        );

        // Step 3: Issue the store requisition
        Notification::fake(); // Reset notifications
        $storeRequisition->refresh();
        $storeRequisitionItem = $storeRequisition->items->first();

        $response = $this->post(route('store-requisitions.issue', $storeRequisition), [
            'items' => [
                [
                    'id' => $storeRequisitionItem->id,
                    'quantity_issued' => 5,
                ]
            ],
            'issue_notes' => 'Items issued successfully'
        ]);
        $response->assertRedirect();

        // Verify issued notification was sent to requester
        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionIssued::class
        );
    }

    public function test_store_requisition_rejection_workflow_with_notifications()
    {
        Notification::fake();

        // Create and submit store requisition
        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        $storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $this->actingAs($this->storeKeeper);

        $rejectionReason = 'Insufficient stock available';

        // Reject the store requisition
        $response = $this->post(route('store-requisitions.reject', $storeRequisition), [
            'rejection_reason' => $rejectionReason
        ]);
        $response->assertRedirect();

        // Verify rejection notification was sent to requester
        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionRejected::class
        );
    }

    public function test_notification_database_storage_and_retrieval()
    {
        // Create store requisition
        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        // Send notification
        $this->storeKeeper->notify(new StoreRequisitionPendingApproval($storeRequisition));

        // Verify notification was stored in database
        $this->assertDatabaseHas('notifications', [
            'notifiable_id' => $this->storeKeeper->id,
            'notifiable_type' => User::class,
        ]);

        // Retrieve and verify notification data
        $notification = $this->storeKeeper->notifications()->first();
        $data = $notification->data;

        $this->assertEquals('Store Requisition Pending Approval', $data['title']);
        $this->assertEquals('store_requisition_pending_approval', $data['type']);
        $this->assertEquals($storeRequisition->id, $data['store_requisition_id']);
        $this->assertArrayHasKey('action_url', $data);
        $this->assertArrayHasKey('metadata', $data);
    }

    public function test_notification_email_content_and_structure()
    {
        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        // Test pending approval email
        $pendingNotification = new StoreRequisitionPendingApproval($storeRequisition);
        $mailData = $pendingNotification->toMail($this->storeKeeper);

        $this->assertStringContainsString('Store Requisition Pending Your Approval', $mailData->subject);
        $this->assertEquals('emails.store-requisitions.pending-approval', $mailData->markdown);

        // Test approval notification email
        $approvalNotification = new StoreRequisitionApproved($storeRequisition);
        $mailData = $approvalNotification->toMail($this->employee);

        $this->assertStringContainsString('Store Requisition Approved', $mailData->subject);
        $this->assertEquals('emails.store-requisitions.approved', $mailData->markdown);

        // Test rejection notification email
        $rejectionNotification = new StoreRequisitionRejected($storeRequisition, 'Test reason');
        $mailData = $rejectionNotification->toMail($this->employee);

        $this->assertStringContainsString('Store Requisition Rejected', $mailData->subject);
        $this->assertEquals('emails.store-requisitions.rejected', $mailData->markdown);

        // Test issued notification email
        $issuedNotification = new StoreRequisitionIssued($storeRequisition, false);
        $mailData = $issuedNotification->toMail($this->employee);

        $this->assertStringContainsString('Store Requisition Issued', $mailData->subject);
        $this->assertEquals('emails.store-requisitions.issued', $mailData->markdown);
    }

    public function test_notification_action_urls_are_correct()
    {
        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        $notification = new StoreRequisitionPendingApproval($storeRequisition);
        $databaseData = $notification->toDatabase($this->storeKeeper);

        $expectedUrl = '/store-requisitions/' . $storeRequisition->id;
        $this->assertEquals($expectedUrl, $databaseData['action_url']);
    }


}
