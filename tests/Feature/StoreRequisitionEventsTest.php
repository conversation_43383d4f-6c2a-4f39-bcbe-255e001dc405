<?php

namespace Tests\Feature;

use App\Events\StoreRequisitionSubmitted;
use App\Events\StoreRequisitionApproved;
use App\Events\StoreRequisitionRejected;
use App\Events\StoreRequisitionIssued;
use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionEventsTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);
        $this->employee->departments()->attach($this->department->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);
        $this->storeKeeper->departments()->attach($this->department->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);
        $this->overseer->departments()->attach($this->department->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Assign permissions
        $this->employee->givePermissionTo('create-store-requisition');
        $this->storeKeeper->givePermissionTo(['approve-store-requisition', 'issue-store-items', 'store-keep']);
        $this->overseer->givePermissionTo(['approve-store-requisition']);
    }

    public function test_store_requisition_submitted_event_is_dispatched()
    {
        Event::fake();

        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        $storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $this->actingAs($this->employee);

        // Submit the store requisition
        $response = $this->post(route('store-requisitions.submit', $storeRequisition));

        $response->assertRedirect();

        // Assert that the StoreRequisitionSubmitted event was dispatched
        Event::assertDispatched(StoreRequisitionSubmitted::class, function ($event) use ($storeRequisition) {
            return $event->storeRequisition->id === $storeRequisition->id;
        });
    }

    public function test_store_requisition_approved_event_is_dispatched()
    {
        Event::fake();

        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        $storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $this->actingAs($this->storeKeeper);

        // Approve the store requisition
        $response = $this->post(route('store-requisitions.approve', $storeRequisition));

        $response->assertRedirect();

        // Assert that the StoreRequisitionApproved event was dispatched
        Event::assertDispatched(StoreRequisitionApproved::class, function ($event) use ($storeRequisition) {
            return $event->storeRequisition->id === $storeRequisition->id
                && $event->approver->id === $this->storeKeeper->id;
        });
    }

    public function test_store_requisition_rejected_event_is_dispatched()
    {
        Event::fake();

        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        $storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $this->actingAs($this->storeKeeper);

        $rejectionReason = 'Insufficient stock available';

        // Reject the store requisition
        $response = $this->post(route('store-requisitions.reject', $storeRequisition), [
            'rejection_reason' => $rejectionReason
        ]);

        $response->assertRedirect();

        // Assert that the StoreRequisitionRejected event was dispatched
        Event::assertDispatched(StoreRequisitionRejected::class, function ($event) use ($storeRequisition, $rejectionReason) {
            return $event->storeRequisition->id === $storeRequisition->id
                && $event->rejector->id === $this->storeKeeper->id
                && $event->rejectionReason === $rejectionReason;
        });
    }

    public function test_store_requisition_issued_event_is_dispatched()
    {
        Event::fake();

        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_APPROVED,
        ]);

        $storeRequisitionItem = $storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $this->actingAs($this->storeKeeper);

        // Issue the store requisition
        $response = $this->post(route('store-requisitions.issue', $storeRequisition), [
            'items' => [
                [
                    'id' => $storeRequisitionItem->id,
                    'quantity_issued' => 5,
                ]
            ],
            'issue_notes' => 'Items issued successfully'
        ]);

        $response->assertRedirect();

        // Assert that the StoreRequisitionIssued event was dispatched
        Event::assertDispatched(StoreRequisitionIssued::class, function ($event) use ($storeRequisition) {
            return $event->storeRequisition->id === $storeRequisition->id
                && $event->issuer->id === $this->storeKeeper->id;
        });
    }

    public function test_store_requisition_events_contain_correct_data()
    {
        $storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        // Test StoreRequisitionSubmitted event data
        $submittedEvent = new StoreRequisitionSubmitted($storeRequisition);
        $this->assertEquals($storeRequisition->id, $submittedEvent->storeRequisition->id);

        // Test StoreRequisitionApproved event data
        $approvedEvent = new StoreRequisitionApproved($storeRequisition, $this->storeKeeper);
        $this->assertEquals($storeRequisition->id, $approvedEvent->storeRequisition->id);
        $this->assertEquals($this->storeKeeper->id, $approvedEvent->approver->id);

        // Test StoreRequisitionRejected event data
        $reason = 'Test rejection reason';
        $rejectedEvent = new StoreRequisitionRejected($storeRequisition, $this->storeKeeper, $reason);
        $this->assertEquals($storeRequisition->id, $rejectedEvent->storeRequisition->id);
        $this->assertEquals($this->storeKeeper->id, $rejectedEvent->rejector->id);
        $this->assertEquals($reason, $rejectedEvent->rejectionReason);

        // Test StoreRequisitionIssued event data
        $issuedEvent = new StoreRequisitionIssued($storeRequisition, $this->storeKeeper, false);
        $this->assertEquals($storeRequisition->id, $issuedEvent->storeRequisition->id);
        $this->assertEquals($this->storeKeeper->id, $issuedEvent->issuer->id);
        $this->assertFalse($issuedEvent->isPartialIssue);
    }
}
