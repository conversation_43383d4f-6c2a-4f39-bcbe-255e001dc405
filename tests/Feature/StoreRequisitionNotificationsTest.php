<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use App\Notifications\StoreRequisitionPendingApproval;
use App\Notifications\StoreRequisitionApproved;
use App\Notifications\StoreRequisitionRejected;
use App\Notifications\StoreRequisitionIssued;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionNotificationsTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;
    private StoreRequisition $storeRequisition;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);
        $this->employee->departments()->attach($this->department->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);
        $this->storeKeeper->departments()->attach($this->department->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);
        $this->overseer->departments()->attach($this->department->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create store requisition
        $this->storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        $this->storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Assign permissions
        $this->employee->givePermissionTo('create-store-requisition');
        $this->storeKeeper->givePermissionTo(['approve-store-requisition', 'issue-store-items', 'store-keep']);
        $this->overseer->givePermissionTo(['approve-store-requisition']);
    }

    public function test_store_requisition_pending_approval_notification_structure()
    {
        $notification = new StoreRequisitionPendingApproval($this->storeRequisition);

        // Test notification channels
        $this->assertEquals(['database', 'mail'], $notification->via($this->storeKeeper));

        // Test database notification data
        $databaseData = $notification->toDatabase($this->storeKeeper);

        $this->assertArrayHasKey('title', $databaseData);
        $this->assertArrayHasKey('message', $databaseData);
        $this->assertArrayHasKey('type', $databaseData);
        $this->assertArrayHasKey('store_requisition_id', $databaseData);
        $this->assertArrayHasKey('action_url', $databaseData);
        $this->assertArrayHasKey('metadata', $databaseData);

        $this->assertEquals('Store Requisition Pending Approval', $databaseData['title']);
        $this->assertEquals('store_requisition_pending_approval', $databaseData['type']);
        $this->assertEquals($this->storeRequisition->id, $databaseData['store_requisition_id']);

        // Test mail notification
        $mailData = $notification->toMail($this->storeKeeper);
        $this->assertStringContainsString('Store Requisition Pending Your Approval', $mailData->subject);
    }

    public function test_store_requisition_approval_notification_structure()
    {
        $notification = new StoreRequisitionApproved($this->storeRequisition);

        // Test notification channels
        $this->assertEquals(['database', 'mail'], $notification->via($this->employee));

        // Test database notification data
        $databaseData = $notification->toDatabase($this->employee);

        $this->assertArrayHasKey('title', $databaseData);
        $this->assertArrayHasKey('message', $databaseData);
        $this->assertArrayHasKey('type', $databaseData);
        $this->assertArrayHasKey('store_requisition_id', $databaseData);
        $this->assertArrayHasKey('action_url', $databaseData);
        $this->assertArrayHasKey('metadata', $databaseData);

        $this->assertEquals('Store Requisition Approved', $databaseData['title']);
        $this->assertEquals('store_requisition_approved', $databaseData['type']);
        $this->assertEquals($this->storeRequisition->id, $databaseData['store_requisition_id']);

        // Test mail notification
        $mailData = $notification->toMail($this->employee);
        $this->assertStringContainsString('Store Requisition Approved', $mailData->subject);
    }

    public function test_store_requisition_rejection_notification_structure()
    {
        $rejectionReason = 'Insufficient stock available';
        $notification = new StoreRequisitionRejected($this->storeRequisition, $rejectionReason);

        // Test notification channels
        $this->assertEquals(['database', 'mail'], $notification->via($this->employee));

        // Test database notification data
        $databaseData = $notification->toDatabase($this->employee);

        $this->assertArrayHasKey('title', $databaseData);
        $this->assertArrayHasKey('message', $databaseData);
        $this->assertArrayHasKey('type', $databaseData);
        $this->assertArrayHasKey('store_requisition_id', $databaseData);
        $this->assertArrayHasKey('action_url', $databaseData);
        $this->assertArrayHasKey('metadata', $databaseData);

        $this->assertEquals('Store Requisition Rejected', $databaseData['title']);
        $this->assertEquals('store_requisition_rejected', $databaseData['type']);
        $this->assertEquals($this->storeRequisition->id, $databaseData['store_requisition_id']);
        $this->assertStringContainsString($rejectionReason, $databaseData['message']);

        // Test mail notification
        $mailData = $notification->toMail($this->employee);
        $this->assertStringContainsString('Store Requisition Rejected', $mailData->subject);
    }

    public function test_store_requisition_issued_notification_structure()
    {
        $notification = new StoreRequisitionIssued($this->storeRequisition, false);

        // Test notification channels
        $this->assertEquals(['database', 'mail'], $notification->via($this->employee));

        // Test database notification data
        $databaseData = $notification->toDatabase($this->employee);

        $this->assertArrayHasKey('title', $databaseData);
        $this->assertArrayHasKey('message', $databaseData);
        $this->assertArrayHasKey('type', $databaseData);
        $this->assertArrayHasKey('store_requisition_id', $databaseData);
        $this->assertArrayHasKey('action_url', $databaseData);
        $this->assertArrayHasKey('metadata', $databaseData);

        $this->assertEquals('Store Requisition Issued', $databaseData['title']);
        $this->assertEquals('store_requisition_issued', $databaseData['type']);
        $this->assertEquals($this->storeRequisition->id, $databaseData['store_requisition_id']);

        // Test mail notification
        $mailData = $notification->toMail($this->employee);
        $this->assertStringContainsString('Store Requisition Issued', $mailData->subject);
    }

    public function test_store_requisition_notifications_are_sent_to_correct_users()
    {
        Notification::fake();

        // Test pending approval notification is sent to approvers
        $this->storeKeeper->notify(new StoreRequisitionPendingApproval($this->storeRequisition));

        Notification::assertSentTo(
            $this->storeKeeper,
            StoreRequisitionPendingApproval::class
        );

        // Test approval notification is sent to requester
        $this->employee->notify(new StoreRequisitionApproved($this->storeRequisition));

        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionApproved::class
        );

        // Test rejection notification is sent to requester
        $this->employee->notify(new StoreRequisitionRejected($this->storeRequisition, 'Test reason'));

        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionRejected::class
        );

        // Test issued notification is sent to requester
        $this->employee->notify(new StoreRequisitionIssued($this->storeRequisition, false));

        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionIssued::class
        );
    }

    public function test_store_requisition_notification_metadata_contains_correct_information()
    {
        $notification = new StoreRequisitionPendingApproval($this->storeRequisition);
        $databaseData = $notification->toDatabase($this->storeKeeper);

        $metadata = $databaseData['metadata'];

        $this->assertArrayHasKey('purpose', $metadata);
        $this->assertArrayHasKey('department_name', $metadata);
        $this->assertArrayHasKey('requester_name', $metadata);
        $this->assertArrayHasKey('total_items', $metadata);

        $this->assertEquals($this->storeRequisition->purpose, $metadata['purpose']);
        $this->assertEquals($this->department->name, $metadata['department_name']);
        $this->assertEquals($this->employee->full_name, $metadata['requester_name']);
        $this->assertEquals(1, $metadata['total_items']);
    }
}
