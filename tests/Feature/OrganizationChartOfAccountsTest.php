<?php

namespace Tests\Feature;

use App\Models\Organization;
use App\Models\ChartOfAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrganizationChartOfAccountsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create platform-level chart of accounts
        $this->createPlatformAccounts();
    }

    private function createPlatformAccounts(): void
    {
        $platformTypes = [
            ['code' => 'AST-000', 'name' => 'asset', 'account_type' => 'asset'],
            ['code' => 'LBT-000', 'name' => 'liability', 'account_type' => 'liability'],
            ['code' => 'EQT-000', 'name' => 'equity', 'account_type' => 'equity'],
            ['code' => 'REV-000', 'name' => 'revenue', 'account_type' => 'revenue'],
            ['code' => 'EXP-000', 'name' => 'expense', 'account_type' => 'expense'],
        ];

        foreach ($platformTypes as $type) {
            ChartOfAccount::create([
                'organization_id' => null,
                'code' => $type['code'],
                'name' => $type['name'],
                'account_type' => $type['account_type'],
                'is_active' => true,
                'description' => 'Platform-level ' . $type['name'] . ' account',
            ]);
        }
    }

    public function test_organization_automatically_creates_chart_of_accounts_on_creation(): void
    {
        // Create an organization
        $organization = Organization::create([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => 'Test Address',
            'status' => 'active',
        ]);

        // Assert that 5 chart of accounts were created
        $accounts = ChartOfAccount::where('organization_id', $organization->id)->get();
        $this->assertCount(5, $accounts);

        // Assert that all standard account types are present
        $accountTypes = $accounts->pluck('account_type')->sort()->values();
        $expectedTypes = ['asset', 'equity', 'expense', 'liability', 'revenue'];
        $this->assertEquals($expectedTypes, $accountTypes->toArray());

        // Assert proper naming convention
        foreach ($accounts as $account) {
            $expectedPrefix = strtoupper(substr($account->account_type, 0, 3));
            $expectedCode = "{$expectedPrefix}-{$organization->id}-000";
            $this->assertEquals($expectedCode, $account->code);
        }

        // Assert proper parent relationships
        $platformAccounts = ChartOfAccount::whereNull('organization_id')->get();
        foreach ($accounts as $account) {
            $platformParent = $platformAccounts->where('account_type', $account->account_type)->first();
            $this->assertNotNull($platformParent);
            $this->assertEquals($platformParent->id, $account->parent_id);
        }
    }

    public function test_organization_does_not_create_duplicate_accounts(): void
    {
        // Create an organization
        $organization = Organization::create([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => 'Test Address',
            'status' => 'active',
        ]);

        // Manually call setupChartOfAccounts again
        $organization->setupChartOfAccounts();

        // Assert that still only 5 chart of accounts exist (no duplicates)
        $accounts = ChartOfAccount::where('organization_id', $organization->id)->get();
        $this->assertCount(5, $accounts);
    }

    public function test_chart_of_accounts_relationship(): void
    {
        $organization = Organization::create([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => 'Test Address',
            'status' => 'active',
        ]);

        // Test the relationship
        $chartOfAccounts = $organization->chartOfAccounts;
        $this->assertCount(5, $chartOfAccounts);

        // Test that each account belongs to the organization
        foreach ($chartOfAccounts as $account) {
            $this->assertEquals($organization->id, $account->organization_id);
        }
    }

    public function test_ensure_standard_account_types_uses_organization_method(): void
    {
        // Create organization without triggering model events (simulate old data)
        $organization = new Organization([
            'name' => 'Test Organization',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+************',
            'address' => 'Test Address',
            'status' => 'active',
        ]);
        $organization->saveQuietly();

        // Verify no accounts exist initially
        $this->assertCount(0, $organization->chartOfAccounts);

        // Create controller and call ensureStandardAccountTypes
        $chartOfAccountService = app(\Src\ChartOfAccounts\Application\Services\ChartOfAccountService::class);
        $controller = new \App\Http\Controllers\ChartOfAccountController($chartOfAccountService);

        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('ensureStandardAccountTypes');
        $method->setAccessible(true);

        $parentAccounts = collect();
        $method->invoke($controller, $parentAccounts, $organization->id);

        // Verify accounts were created
        $organization->refresh();
        $this->assertCount(5, $organization->chartOfAccounts);

        // Verify parent accounts collection was updated
        $this->assertCount(5, $parentAccounts);
    }
}
