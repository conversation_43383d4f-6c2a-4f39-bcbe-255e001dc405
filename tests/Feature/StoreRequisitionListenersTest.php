<?php

namespace Tests\Feature;

use App\Events\StoreRequisitionSubmitted;
use App\Events\StoreRequisitionApproved as StoreRequisitionApprovedEvent;
use App\Events\StoreRequisitionRejected as StoreRequisitionRejectedEvent;
use App\Events\StoreRequisitionIssued as StoreRequisitionIssuedEvent;
use App\Listeners\SendStoreRequisitionNotifications;
use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use App\Notifications\StoreRequisitionPendingApproval;
use App\Notifications\StoreRequisitionApproved;
use App\Notifications\StoreRequisitionRejected;
use App\Notifications\StoreRequisitionIssued;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionListenersTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;
    private StoreRequisition $storeRequisition;
    private SendStoreRequisitionNotifications $listener;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);
        $this->employee->departments()->attach($this->department->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);
        $this->storeKeeper->departments()->attach($this->department->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);
        $this->overseer->departments()->attach($this->department->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create store requisition
        $this->storeRequisition = StoreRequisition::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);

        $this->storeRequisition->items()->create([
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Create roles
        $employeeRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'Employee']);
        $storeKeeperRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'Store Keeper']);
        $financeManagerRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'Finance Manager']);

        // Assign roles
        $this->employee->assignRole($employeeRole);
        $this->storeKeeper->assignRole($storeKeeperRole);
        $this->overseer->assignRole($financeManagerRole);

        // Assign permissions
        $this->employee->givePermissionTo('create-store-requisition');
        $this->storeKeeper->givePermissionTo(['approve-store-requisition', 'issue-store-items', 'store-keep']);
        $this->overseer->givePermissionTo(['approve-store-requisition']);

        // Create listener instance
        $this->listener = new SendStoreRequisitionNotifications();
    }

    public function test_listener_handles_store_requisition_submitted_event()
    {
        $event = new StoreRequisitionSubmitted($this->storeRequisition);

        // Test that the listener can handle the event without errors
        $this->listener->handle($event);

        // If we reach here, no exceptions were thrown
        $this->assertTrue(true);
    }

    public function test_listener_handles_store_requisition_approved_event()
    {
        Notification::fake();

        $event = new StoreRequisitionApprovedEvent($this->storeRequisition, $this->storeKeeper);

        $this->listener->handle($event);

        // Assert that approval notification was sent to requester
        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionApproved::class
        );
    }

    public function test_listener_handles_store_requisition_rejected_event()
    {
        Notification::fake();

        $rejectionReason = 'Insufficient stock available';
        $event = new StoreRequisitionRejectedEvent($this->storeRequisition, $this->storeKeeper, $rejectionReason);

        $this->listener->handle($event);

        // Assert that rejection notification was sent to requester
        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionRejected::class
        );
    }

    public function test_listener_handles_store_requisition_issued_event()
    {
        Notification::fake();

        $event = new StoreRequisitionIssuedEvent($this->storeRequisition, $this->storeKeeper, false);

        $this->listener->handle($event);

        // Assert that issued notification was sent to requester
        Notification::assertSentTo(
            $this->employee,
            StoreRequisitionIssued::class
        );
    }



    public function test_listener_finds_correct_approvers_for_store_requisition()
    {
        $event = new StoreRequisitionSubmitted($this->storeRequisition);

        // Test that the listener can handle the event without errors
        $this->listener->handle($event);

        // If we reach here, no exceptions were thrown
        $this->assertTrue(true);
    }

    public function test_listener_logs_notification_activities()
    {
        // This test verifies that the listener can handle events without errors

        $submittedEvent = new StoreRequisitionSubmitted($this->storeRequisition);
        $approvedEvent = new StoreRequisitionApprovedEvent($this->storeRequisition, $this->storeKeeper);
        $rejectedEvent = new StoreRequisitionRejectedEvent($this->storeRequisition, $this->storeKeeper, 'Test reason');
        $issuedEvent = new StoreRequisitionIssuedEvent($this->storeRequisition, $this->storeKeeper, false);

        // These should not throw any exceptions
        $this->listener->handle($submittedEvent);
        $this->listener->handle($approvedEvent);
        $this->listener->handle($rejectedEvent);
        $this->listener->handle($issuedEvent);

        $this->assertTrue(true); // If we reach here, no exceptions were thrown
    }

    public function test_listener_handles_unknown_events_gracefully()
    {
        // Create a mock event that the listener doesn't handle
        $unknownEvent = new class {
            public $storeRequisition;

            public function __construct()
            {
                // Empty constructor
            }
        };

        // This should not throw any exceptions
        $this->listener->handle($unknownEvent);

        $this->assertTrue(true); // If we reach here, no exceptions were thrown
    }
}
