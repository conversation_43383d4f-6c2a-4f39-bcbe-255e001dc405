<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionHistory;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionRejectionTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private User $financeManager;
    private User $organizationAdmin;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);

        $this->financeManager = User::factory()->create();
        $this->financeManager->organizations()->attach($this->organization->id);

        $this->organizationAdmin = User::factory()->create();
        $this->organizationAdmin->organizations()->attach($this->organization->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for testing',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Create roles
        $financeManagerRole = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id
        ]);

        $organizationAdminRole = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'Organization Admin',
            'organization_id' => $this->organization->id
        ]);

        // Assign permissions
        $this->employee->givePermissionTo([
            'view-store-requisitions',
            'create-store-requisition',
            'edit-store-requisition'
        ]);
        
        $this->storeKeeper->givePermissionTo(['store-keep']);
        
        $this->overseer->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition'
        ]);
        
        $this->financeManager->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition', 
            'approve-store-requisition'
        ]);

        $this->organizationAdmin->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition', 
            'approve-store-requisition'
        ]);

        // Assign roles
        $this->financeManager->assignRole($financeManagerRole);
        $this->organizationAdmin->assignRole($organizationAdminRole);
    }

    private function createPendingRequisition(User $requester): StoreRequisition
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $requester->id,
            'purpose' => 'Test requisition for rejection testing',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        StoreRequisitionItem::create([
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        return $requisition;
    }

    public function test_store_keeper_can_reject_employee_requisition()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $rejectionReason = 'Insufficient justification for the requested items';

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => $rejectionReason
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Store requisition rejected successfully');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_REJECTED,
            'approver_user_id' => $this->storeKeeper->id,
            'rejection_reason' => $rejectionReason,
        ]);

        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $requisition->id,
            'user_id' => $this->storeKeeper->id,
            'action' => 'rejected',
            'comments' => 'Store requisition rejected: ' . $rejectionReason
        ]);
    }

    public function test_overseer_can_reject_store_keeper_requisition()
    {
        $requisition = $this->createPendingRequisition($this->storeKeeper);
        $rejectionReason = 'Budget constraints for this period';

        $response = $this->actingAs($this->financeManager)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => $rejectionReason
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Store requisition rejected successfully');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_REJECTED,
            'approver_user_id' => $this->financeManager->id,
            'rejection_reason' => $rejectionReason,
        ]);
    }

    public function test_finance_manager_can_reject_store_keeper_requisition()
    {
        $requisition = $this->createPendingRequisition($this->storeKeeper);
        $rejectionReason = 'Does not align with current procurement policies';

        $response = $this->actingAs($this->financeManager)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => $rejectionReason
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_REJECTED,
            'approver_user_id' => $this->financeManager->id,
            'rejection_reason' => $rejectionReason,
        ]);
    }

    public function test_rejection_requires_reason_validation()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                // Missing rejection_reason
            ]);

        $response->assertSessionHasErrors(['rejection_reason']);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'approver_user_id' => null,
        ]);
    }

    public function test_rejection_creates_audit_trail_with_reason()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $rejectionReason = 'Detailed rejection reason for audit trail';

        $this->actingAs($this->storeKeeper)
            ->postJson("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => $rejectionReason
            ]);

        $history = StoreRequisitionHistory::where('store_requisition_id', $requisition->id)
            ->where('action', 'rejected')
            ->first();

        $this->assertNotNull($history);
        $this->assertEquals('Store requisition rejected: ' . $rejectionReason, $history->comments);
        $this->assertEquals($this->storeKeeper->id, $history->user_id);
    }

    public function test_rejection_returns_json_response_with_requisition_data()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Test rejection'
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Store requisition rejected successfully');
    }

    public function test_rejection_updates_status_and_sets_approver()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Test rejection'
            ]);

        $requisition->refresh();

        $this->assertEquals(StoreRequisition::STATUS_REJECTED, $requisition->status);
        $this->assertEquals($this->storeKeeper->id, $requisition->approver_user_id);
        $this->assertEquals('Test rejection', $requisition->rejection_reason);
    }

    public function test_rejection_validates_reason_length_limit()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $longReason = str_repeat('a', 1001); // Exceeds 1000 character limit

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => $longReason
            ]);

        $response->assertSessionHasErrors(['rejection_reason']);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'approver_user_id' => null,
        ]);
    }

    public function test_return_for_revision_functionality()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $comments = 'Please provide more detailed justification for the requested items';

        $response = $this->actingAs($this->storeKeeper)
            ->postJson("/store-requisitions/{$requisition->id}/return-for-revision", [
                'comments' => $comments
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Store requisition returned for revision',
                'store_requisition' => [
                    'id' => $requisition->id,
                    'status' => StoreRequisition::STATUS_RETURNED_FOR_REVISION
                ]
            ]);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_RETURNED_FOR_REVISION,
            'rejection_reason' => $comments,
        ]);
    }

    public function test_return_for_revision_requires_comments()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->storeKeeper)
            ->postJson("/store-requisitions/{$requisition->id}/return-for-revision", [
                // Missing comments
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['comments']);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);
    }

    public function test_return_for_revision_creates_audit_trail()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $comments = 'Returned for additional documentation';

        $this->actingAs($this->storeKeeper)
            ->postJson("/store-requisitions/{$requisition->id}/return-for-revision", [
                'comments' => $comments
            ]);

        $history = StoreRequisitionHistory::where('store_requisition_id', $requisition->id)
            ->where('action', 'returned_for_revision')
            ->first();

        $this->assertNotNull($history);
        $this->assertEquals($comments, $history->comments);
        $this->assertEquals($this->storeKeeper->id, $history->user_id);
    }

    public function test_return_for_revision_updates_status_correctly()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $this->actingAs($this->storeKeeper)
            ->postJson("/store-requisitions/{$requisition->id}/return-for-revision", [
                'comments' => 'Please revise and resubmit'
            ]);

        $requisition->refresh();

        $this->assertEquals(StoreRequisition::STATUS_RETURNED_FOR_REVISION, $requisition->status);
        $this->assertEquals('Please revise and resubmit', $requisition->rejection_reason);
    }

    public function test_rejected_requisition_can_be_edited_and_resubmitted()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // First reject the requisition
        $requisition->update([
            'status' => StoreRequisition::STATUS_REJECTED,
            'approver_user_id' => $this->storeKeeper->id,
            'rejection_reason' => 'Initial rejection',
        ]);

        // Test that it can be edited
        $this->assertTrue($requisition->canBeEdited());

        // Test edit access
        $response = $this->actingAs($this->employee)
            ->get("/store-requisitions/{$requisition->id}/edit-rejected");

        $response->assertStatus(200);
    }

    public function test_returned_requisition_can_be_edited_and_resubmitted()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // First return for revision
        $requisition->update([
            'status' => StoreRequisition::STATUS_RETURNED_FOR_REVISION,
            'rejection_reason' => 'Needs more details',
        ]);

        // Test that it can be edited
        $this->assertTrue($requisition->canBeEdited());

        // Test edit access
        $response = $this->actingAs($this->employee)
            ->get("/store-requisitions/{$requisition->id}/edit-rejected");

        $response->assertStatus(200);
    }

    public function test_unauthorized_user_cannot_reject()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->employee)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Unauthorized rejection attempt'
            ]);

        $response->assertStatus(403);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'approver_user_id' => null,
        ]);
    }

    public function test_unauthorized_user_cannot_return_for_revision()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->employee)
            ->postJson("/store-requisitions/{$requisition->id}/return-for-revision", [
                'comments' => 'Unauthorized return attempt'
            ]);

        $response->assertStatus(403);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        ]);
    }

    public function test_already_processed_requisition_cannot_be_rejected()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // First approve the requisition
        $requisition->update([
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => $this->storeKeeper->id,
            'approved_at' => now(),
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Trying to reject approved requisition'
            ]);

        // Should get 403 because user cannot reject their own requisition
        $response->assertStatus(403);
    }

    public function test_draft_requisition_cannot_be_rejected()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Draft requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Trying to reject draft requisition'
            ]);

        $response->assertStatus(403);
    }

    public function test_rejection_policy_authorization_is_enforced()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test that overseer cannot reject employee requisition
        $response = $this->actingAs($this->overseer)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => 'Unauthorized rejection attempt'
            ]);

        $response->assertStatus(403);
    }
}
