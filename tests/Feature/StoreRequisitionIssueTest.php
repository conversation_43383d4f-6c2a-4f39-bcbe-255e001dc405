<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionHistory;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionIssueTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);

        // Create inventory item with sufficient stock
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for issuing',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Assign permissions
        $this->employee->givePermissionTo([
            'view-store-requisitions',
            'create-store-requisition',
            'edit-store-requisition'
        ]);
        
        $this->storeKeeper->givePermissionTo(['store-keep', 'issue-store-items']);
    }

    private function createApprovedRequisition(User $requester, int $quantityRequested = 10): StoreRequisition
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $requester->id,
            'purpose' => 'Test requisition for issuing',
            'status' => StoreRequisition::STATUS_APPROVED,
            'requested_at' => now(),
            'approved_at' => now(),
            'approver_user_id' => $this->storeKeeper->id,
        ]);

        StoreRequisitionItem::create([
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => $quantityRequested,
            'quantity_issued' => 0,
        ]);

        return $requisition;
    }

    public function test_store_keeper_can_issue_approved_requisition()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 5);
        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 5
                    ]
                ]
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'All items issued successfully');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_ISSUED,
            'issuer_user_id' => $this->storeKeeper->id,
        ]);

        $this->assertDatabaseHas('store_requisition_items', [
            'id' => $requisitionItem->id,
            'quantity_issued' => 5,
        ]);
    }

    public function test_partial_issuing_functionality()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 10);
        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 6 // Partial quantity
                    ]
                ]
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PARTIALLY_ISSUED,
            'issuer_user_id' => $this->storeKeeper->id,
        ]);

        $this->assertDatabaseHas('store_requisition_items', [
            'id' => $requisitionItem->id,
            'quantity_issued' => 6,
        ]);
    }

    public function test_full_issuing_functionality()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 8);
        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 8 // Full quantity
                    ]
                ]
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_ISSUED,
            'issuer_user_id' => $this->storeKeeper->id,
        ]);

        $this->assertDatabaseHas('store_requisition_items', [
            'id' => $requisitionItem->id,
            'quantity_issued' => 8,
        ]);
    }

    public function test_stock_validation_during_issuing()
    {
        // Create requisition with quantity higher than available stock
        $this->inventoryItem->update(['quantity_on_hand' => 5]);
        $requisition = $this->createApprovedRequisition($this->employee, 10);
        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 10 // More than available stock
                    ]
                ]
            ]);

        $response->assertRedirect()
            ->assertSessionHas('error');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_APPROVED, // Status unchanged
        ]);
    }

    public function test_insufficient_stock_prevents_issuing()
    {
        // Set inventory to very low stock
        $this->inventoryItem->update(['quantity_on_hand' => 2]);
        $requisition = $this->createApprovedRequisition($this->employee, 5);
        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 5
                    ]
                ]
            ]);

        $response->assertRedirect()
            ->assertSessionHas('error');

        // Verify no changes were made
        $this->assertDatabaseHas('store_requisition_items', [
            'id' => $requisitionItem->id,
            'quantity_issued' => 0,
        ]);

        $this->assertDatabaseHas('inventory_items', [
            'id' => $this->inventoryItem->id,
            'quantity_on_hand' => 2, // Stock unchanged
        ]);
    }

    public function test_inventory_deduction_after_issuing()
    {
        $initialStock = $this->inventoryItem->quantity_on_hand;
        $requisition = $this->createApprovedRequisition($this->employee, 7);
        $requisitionItem = $requisition->items->first();

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 7
                    ]
                ]
            ]);

        $this->assertDatabaseHas('inventory_items', [
            'id' => $this->inventoryItem->id,
            'quantity_on_hand' => $initialStock - 7,
        ]);
    }

    public function test_issue_creates_inventory_transaction()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 4);
        $requisitionItem = $requisition->items->first();

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 4
                    ]
                ]
            ]);

        $this->assertDatabaseHas('inventory_transactions', [
            'inventory_item_id' => $this->inventoryItem->id,
            'user_id' => $this->storeKeeper->id,
            'transaction_type' => 'issuance',
            'quantity_change' => -4,
            'related_document_id' => $requisition->id,
            'related_document_type' => StoreRequisition::class,
        ]);
    }

    public function test_issue_creates_audit_trail()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 3);
        $requisitionItem = $requisition->items->first();

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 3
                    ]
                ]
            ]);

        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $requisition->id,
            'user_id' => $this->storeKeeper->id,
            'action' => 'issued',
        ]);
    }

    public function test_issue_updates_requisition_status()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 6);
        $requisitionItem = $requisition->items->first();

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 6
                    ]
                ]
            ]);

        $requisition->refresh();

        $this->assertEquals(StoreRequisition::STATUS_ISSUED, $requisition->status);
        $this->assertEquals($this->storeKeeper->id, $requisition->issuer_user_id);
        $this->assertNotNull($requisition->issued_at);
    }

    public function test_unauthorized_user_cannot_issue()
    {
        $requisition = $this->createApprovedRequisition($this->employee, 5);
        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->employee)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 5
                    ]
                ]
            ]);

        $response->assertStatus(403);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_APPROVED, // Status unchanged
            'issuer_user_id' => null,
        ]);
    }

    public function test_non_approved_requisition_cannot_be_issued()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Pending requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        StoreRequisitionItem::create([
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        $requisitionItem = $requisition->items->first();

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/issue", [
                'items' => [
                    [
                        'id' => $requisitionItem->id,
                        'quantity_issued' => 5
                    ]
                ]
            ]);

        $response->assertRedirect()
            ->assertSessionHas('error');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'issuer_user_id' => null,
        ]);
    }
}
