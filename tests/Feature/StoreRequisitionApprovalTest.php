<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionHistory;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionApprovalTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $overseer;
    private User $financeManager;
    private User $organizationAdmin;
    private Organization $organization;
    private Organization $otherOrganization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test organizations
        $this->organization = Organization::factory()->create();
        $this->otherOrganization = Organization::factory()->create();
        
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);

        $this->financeManager = User::factory()->create();
        $this->financeManager->organizations()->attach($this->organization->id);

        $this->organizationAdmin = User::factory()->create();
        $this->organizationAdmin->organizations()->attach($this->organization->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for testing',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'issue-store-items']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Create roles
        $financeManagerRole = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id
        ]);

        $organizationAdminRole = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'Organization Admin',
            'organization_id' => $this->organization->id
        ]);

        // Assign permissions
        $this->employee->givePermissionTo([
            'view-store-requisitions',
            'create-store-requisition',
            'edit-store-requisition'
        ]);
        
        $this->storeKeeper->givePermissionTo(['store-keep']);
        
        $this->overseer->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition'
        ]);
        
        $this->financeManager->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition', 
            'approve-store-requisition'
        ]);

        $this->organizationAdmin->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition', 
            'approve-store-requisition'
        ]);

        // Assign roles
        $this->financeManager->assignRole($financeManagerRole);
        $this->organizationAdmin->assignRole($organizationAdminRole);
    }

    private function createPendingRequisition(User $requester): StoreRequisition
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $requester->id,
            'purpose' => 'Test requisition for approval testing',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        StoreRequisitionItem::create([
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        return $requisition;
    }

    public function test_store_keeper_can_approve_employee_requisition()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Approved by store keeper'
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Store requisition approved successfully');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => $this->storeKeeper->id,
        ]);

        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $requisition->id,
            'user_id' => $this->storeKeeper->id,
            'action' => 'approved',
            'comments' => 'Approved by store keeper'
        ]);
    }

    public function test_overseer_can_approve_store_keeper_requisition()
    {
        $requisition = $this->createPendingRequisition($this->storeKeeper);

        $response = $this->actingAs($this->financeManager)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Approved by finance manager'
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Store requisition approved successfully');

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => $this->financeManager->id,
        ]);
    }

    public function test_finance_manager_can_approve_store_keeper_requisition()
    {
        $requisition = $this->createPendingRequisition($this->storeKeeper);

        $response = $this->actingAs($this->financeManager)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Approved by finance manager'
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => $this->financeManager->id,
        ]);
    }

    public function test_organization_admin_can_approve_store_keeper_requisition()
    {
        $requisition = $this->createPendingRequisition($this->storeKeeper);

        $response = $this->actingAs($this->organizationAdmin)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Approved by organization admin'
            ]);

        $response->assertRedirect();

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => $this->organizationAdmin->id,
        ]);
    }

    public function test_user_cannot_approve_own_requisition()
    {
        $requisition = $this->createPendingRequisition($this->storeKeeper);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Trying to approve own requisition'
            ]);

        $response->assertStatus(403);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'approver_user_id' => null,
        ]);
    }

    public function test_approval_creates_audit_trail_with_comments()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $comments = 'Approved after review of requirements';

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => $comments
            ]);

        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $requisition->id,
            'user_id' => $this->storeKeeper->id,
            'action' => 'approved',
            'comments' => $comments
        ]);
    }

    public function test_approval_updates_status_and_timestamps_correctly()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve");

        $requisition->refresh();

        $this->assertEquals(StoreRequisition::STATUS_APPROVED, $requisition->status);
        $this->assertEquals($this->storeKeeper->id, $requisition->approver_user_id);
        $this->assertNotNull($requisition->approved_at);
    }

    public function test_approval_returns_json_response_with_requisition_data()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Test approval'
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Store requisition approved successfully');
    }

    public function test_unauthorized_user_receives_403_json_response()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->employee)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Unauthorized approval attempt'
            ]);

        $response->assertStatus(403);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'approver_user_id' => null,
        ]);
    }

    public function test_already_approved_requisition_cannot_be_approved_again()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // First approval
        $requisition->update([
            'status' => StoreRequisition::STATUS_APPROVED,
            'approver_user_id' => $this->storeKeeper->id,
            'approved_at' => now(),
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Second approval attempt'
            ]);

        // Should get 403 because user cannot approve their own requisition
        $response->assertStatus(403);
    }

    public function test_rejected_requisition_cannot_be_approved()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $requisition->update([
            'status' => StoreRequisition::STATUS_REJECTED,
            'approver_user_id' => $this->storeKeeper->id,
            'rejection_reason' => 'Test rejection',
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Trying to approve rejected requisition'
            ]);

        // Should get 403 because user cannot approve their own requisition
        $response->assertStatus(403);
    }

    public function test_draft_requisition_cannot_be_approved()
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Draft requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Trying to approve draft requisition'
            ]);

        $response->assertStatus(403);
    }

    public function test_approval_with_comments_stores_in_history()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $comments = 'Approved with detailed comments about the approval decision';

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => $comments
            ]);

        $history = StoreRequisitionHistory::where('store_requisition_id', $requisition->id)
            ->where('action', 'approved')
            ->first();

        $this->assertNotNull($history);
        $this->assertEquals($comments, $history->comments);
        $this->assertEquals($this->storeKeeper->id, $history->user_id);
    }

    public function test_approval_without_comments_uses_default_message()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve");

        $history = StoreRequisitionHistory::where('store_requisition_id', $requisition->id)
            ->where('action', 'approved')
            ->first();

        $this->assertNotNull($history);
        $this->assertEquals('Store requisition approved', $history->comments);
    }

    public function test_approval_validates_comment_length_limit()
    {
        $requisition = $this->createPendingRequisition($this->employee);
        $longComment = str_repeat('a', 1001); // Exceeds 1000 character limit

        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => $longComment
            ]);

        $response->assertSessionHasErrors(['comments']);

        $this->assertDatabaseHas('store_requisitions', [
            'id' => $requisition->id,
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'approver_user_id' => null,
        ]);
    }

    public function test_approval_policy_authorization_is_enforced()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test that overseer cannot approve employee requisition
        $response = $this->actingAs($this->overseer)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Unauthorized approval attempt'
            ]);

        $response->assertStatus(403);
    }

    public function test_approval_sets_approver_user_id_correctly()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Test approval'
            ]);

        $requisition->refresh();
        $this->assertEquals($this->storeKeeper->id, $requisition->approver_user_id);
    }
}
