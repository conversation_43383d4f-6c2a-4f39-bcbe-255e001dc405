<?php

use App\Models\User;
use App\Models\Role;
use App\Models\Organization;
use Illuminate\Support\Facades\Hash;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin cannot change another users password through user update', function () {
    // Create an organization
    $organization = Organization::factory()->create();

    // Create an admin user
    $admin = User::factory()->create(['is_platform_admin' => true]);

    // Create a basic role
    $userRole = Role::create([
        'name' => 'User',
        'guard_name' => 'web'
    ]);

    // Create a regular user
    $user = User::factory()->create([
        'password' => Hash::make('OriginalPassword123!')
    ]);
    $user->assignRole($userRole);

    // Attempt to update the user's password as admin
    $response = $this
        ->actingAs($admin)
        ->put("/users/{$user->id}", [
            'username' => $user->username,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'password' => 'NewPassword456!',
            'is_platform_admin' => false,
            'status' => 'active',
            'role_ids' => [$userRole->id],
            'department_ids' => [],
        ]);

    // Should redirect back with error
    $response->assertRedirect();
    $response->assertSessionHasErrors(['password']);

    // Password should remain unchanged
    $user->refresh();
    expect(Hash::check('OriginalPassword123!', $user->password))->toBeTrue();
    expect(Hash::check('NewPassword456!', $user->password))->toBeFalse();
});

test('organization admin cannot change another users password', function () {
    // Create an organization
    $organization = Organization::factory()->create();

    // Create organization admin role
    $orgAdminRole = Role::create([
        'name' => 'Organization Admin',
        'organization_id' => $organization->id,
        'guard_name' => 'web'
    ]);

    // Create a basic role for the user
    $userRole = Role::create([
        'name' => 'User',
        'organization_id' => $organization->id,
        'guard_name' => 'web'
    ]);

    // Create an organization admin user
    $orgAdmin = User::factory()->create();
    $orgAdmin->assignRole($orgAdminRole);
    $orgAdmin->organizations()->attach($organization->id);

    // Create a regular user in the same organization
    $user = User::factory()->create([
        'password' => Hash::make('original-password')
    ]);
    $user->organizations()->attach($organization->id);
    $user->assignRole($userRole);

    // Attempt to update the user's password as org admin
    $response = $this
        ->actingAs($orgAdmin)
        ->put("/users/{$user->id}", [
            'username' => $user->username,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'password' => 'NewPassword123!',
            'is_platform_admin' => false,
            'status' => 'active',
            'role_ids' => [$userRole->id],
            'department_ids' => [],
        ]);

    // Should redirect back with error
    $response->assertRedirect();
    $response->assertSessionHasErrors(['password']);

    // Password should remain unchanged
    $user->refresh();
    expect(Hash::check('original-password', $user->password))->toBeTrue();
    expect(Hash::check('NewPassword123!', $user->password))->toBeFalse();
});

test('user can update their own profile without password change', function () {
    // Create a basic role
    $userRole = Role::create([
        'name' => 'User',
        'guard_name' => 'web'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('OriginalPassword123!')
    ]);
    $user->assignRole($userRole);

    $response = $this
        ->actingAs($user)
        ->put("/users/{$user->id}", [
            'username' => $user->username,
            'first_name' => 'Updated Name',
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'is_platform_admin' => false,
            'status' => 'active',
            'role_ids' => [$userRole->id],
            'department_ids' => [],
        ]);

    $response->assertRedirect('/users');

    // Profile should be updated
    $user->refresh();
    expect($user->first_name)->toBe('Updated Name');

    // Password should remain unchanged
    expect(Hash::check('OriginalPassword123!', $user->password))->toBeTrue();
});

test('user can update their own password through user edit form', function () {
    // Create a basic role
    $userRole = Role::create([
        'name' => 'User',
        'guard_name' => 'web'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('OriginalPassword123!')
    ]);
    $user->assignRole($userRole);

    $response = $this
        ->actingAs($user)
        ->put("/users/{$user->id}", [
            'username' => $user->username,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'password' => 'NewPassword456!',
            'is_platform_admin' => false,
            'status' => 'active',
            'role_ids' => [$userRole->id],
            'department_ids' => [],
        ]);

    $response->assertRedirect('/users');

    // Password should be updated
    $user->refresh();
    expect(Hash::check('NewPassword456!', $user->password))->toBeTrue();
    expect(Hash::check('OriginalPassword123!', $user->password))->toBeFalse();
});

test('admin can still create new users with passwords', function () {
    // Create an organization
    $organization = Organization::factory()->create();

    // Create an admin user and associate with organization
    $admin = User::factory()->create(['is_platform_admin' => true]);
    $admin->organizations()->attach($organization->id);

    // Create a basic role
    $userRole = Role::create([
        'name' => 'User',
        'guard_name' => 'web'
    ]);

    $response = $this
        ->actingAs($admin)
        ->post('/users', [
            'username' => 'newuser',
            'first_name' => 'New',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'InitialPassword123!',
            'is_platform_admin' => false,
            'status' => 'active',
            'role_ids' => [$userRole->id],
            'department_ids' => [],
        ]);

    $response->assertRedirect('/users');

    // User should be created with the specified password
    $newUser = User::where('email', '<EMAIL>')->first();
    expect($newUser)->not->toBeNull();
    expect(Hash::check('InitialPassword123!', $newUser->password))->toBeTrue();
});

test('user can still change their own password through settings', function () {
    $user = User::factory()->create([
        'password' => Hash::make('OldPassword123!')
    ]);

    $response = $this
        ->actingAs($user)
        ->from('/settings/password')
        ->put('/settings/password', [
            'current_password' => 'OldPassword123!',
            'password' => 'NewPassword456!',
            'password_confirmation' => 'NewPassword456!',
        ]);

    $response->assertRedirect('/settings/password');
    $response->assertSessionHasNoErrors();

    // Password should be updated
    $user->refresh();
    expect(Hash::check('NewPassword456!', $user->password))->toBeTrue();
    expect(Hash::check('OldPassword123!', $user->password))->toBeFalse();
});
