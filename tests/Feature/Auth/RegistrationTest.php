<?php

use App\Models\User;
use App\Models\Organization;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Illuminate\Support\Facades\Route;

uses(RefreshDatabase::class);

// test('registration page can be rendered', function () {
//     $response = $this->get('/register');

//     $response->assertStatus(200);
//     $response->assertInertia(fn (Assert $page) => $page
//         ->component('auth/register')
//         ->has('canResetPassword')
//         ->has('status')
//     );
// });

// test('new users can register', function () {
//     $response = $this->post('/register', [
//         'username' => 'testuser',
//         'email' => '<EMAIL>',
//         'phone' => '+25487289767282',
//         'password' => 'password123',
//         'password_confirmation' => 'password123',
//         'org_name' => 'test_organization',
//     ]);

//     // With email verification, user is not immediately authenticated
//     $this->assertGuest();
//     // Should redirect (either to verification or back with errors)
//     $response->assertRedirect();
//     // Should have pending verification data in session if successful
//     if (!$response->getSession()->has('errors')) {
//         $response->assertSessionHas('pending_email_verification');
//     }
// });

// test('email must be unique', function () {
//     User::factory()->create([
//         'email' => '<EMAIL>'
//     ]);

//     $response = $this->post('/register', [
//         'first_name' => 'John',
//         'last_name' => 'Doe',
//         'email' => '<EMAIL>',
//         'password' => 'password123',
//         'password_confirmation' => 'password123',
//         'org_name' => 'new_organization',
//     ]);

//     $response->assertSessionHasErrors(['email']);
//     $this->assertEquals(0, User::where('email', '<EMAIL>')->count());
// });

test('password must be confirmed', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'differentpassword',
        'org_name' => 'test_organization',
    ]);

    $response->assertSessionHasErrors(['password']);
    $this->assertEquals(0, User::where('email', '<EMAIL>')->count());
});

test('first_name is required', function () {
    $response = $this->post('/register', [
        'first_name' => '',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'StrongPass123!',
        'password_confirmation' => 'StrongPass123!',
        'org_name' => 'test_organization',
    ]);

    $response->assertSessionHasErrors(['first_name']);
});

test('last_name is required', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => '',
        'email' => '<EMAIL>',
        'password' => 'StrongPass123!',
        'password_confirmation' => 'StrongPass123!',
        'org_name' => 'test_organization',
    ]);

    $response->assertSessionHasErrors(['last_name']);
});

test('valid registration should work', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'phone' => '+25487289767282',
        'password' => 'StrongPass123!',
        'password_confirmation' => 'StrongPass123!',
        'org_name' => 'test_organization',
    ]);

    $response->assertRedirect('/verify-email-code');
    $this->assertGuest();
    $response->assertSessionHas('pending_email_verification');
});

test('email is required', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '',
        'password' => 'StrongPass123!',
        'password_confirmation' => 'StrongPass123!',
        'org_name' => 'test_organization',
    ]);

    $response->assertSessionHasErrors(['email']);
});

test('password is required', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => '',
        'password_confirmation' => '',
        'org_name' => 'test_organization',
    ]);

    $response->assertSessionHasErrors(['password']);
});

// TODO: Uncomment when erag/laravel-disposable-email package is added to CI/CD workflow
// test('disposable email addresses are blocked', function () {
//     $response = $this->post('/register', [
//         'first_name' => 'John',
//         'last_name' => 'Doe',
//         'email' => '<EMAIL>', // Known disposable email
//         'phone' => '+25487289767282',
//         'password' => 'password123',
//         'password_confirmation' => 'password123',
//         'org_name' => 'test_organization',
//     ]);

//     $response->assertSessionHasErrors(['email']);
//     $this->assertEquals(0, User::where('email', '<EMAIL>')->count());
// });

// TODO: Uncomment when erag/laravel-disposable-email package is added to CI/CD workflow
// test('legitimate email addresses are allowed', function () {
//     $response = $this->post('/register', [
//         'first_name' => 'John',
//         'last_name' => 'Doe',
//         'email' => '<EMAIL>', // Legitimate email
//         'phone' => '+25487289767282',
//         'password' => 'password123',
//         'password_confirmation' => 'password123',
//         'org_name' => 'test_organization',
//     ]);

//     // The important test is that disposable email validation doesn't block legitimate emails
//     // Route issues in test environment may cause other errors, but that's not related to our validation
//     $response->assertStatus(302); // Should redirect (either success or error redirect)

//     // Verify that if there are validation errors, they're not about disposable emails
//     $errors = $response->getSession()->get('errors');
//     if ($errors && $errors->has('email')) {
//         $emailErrors = $errors->get('email');
//         foreach ($emailErrors as $error) {
//             expect($error)->not->toContain('unauthorized email provider');
//             expect($error)->not->toContain('disposable');
//         }
//     }
// });
