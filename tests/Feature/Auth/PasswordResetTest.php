<?php

use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\Notification;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

// test('reset password link screen can be rendered', function () {
//     $response = $this->get('/forgot-password');

//     $response->assertStatus(200);
// });

test('reset password link can be requested', function () {
    Notification::fake();

    $user = User::factory()->create();

    $this->post('/forgot-password', ['email' => $user->email]);

    Notification::assertSentTo($user, ResetPassword::class);
});

// test('reset password screen can be rendered', function () {
//     Notification::fake();

//     $user = User::factory()->create();

//     $this->post('/forgot-password', ['email' => $user->email]);

//     Notification::assertSentTo($user, ResetPassword::class, function ($notification) {
//         $response = $this->get('/reset-password/'.$notification->token);

//         $response->assertStatus(200);

//         return true;
//     });
// });

test('password can be reset with valid token', function () {
    Notification::fake();

    $user = User::factory()->create();

    $this->post('/forgot-password', ['email' => $user->email]);

    Notification::assertSentTo($user, ResetPassword::class, function ($notification) use ($user) {
        $response = $this->post('/reset-password', [
            'token' => $notification->token,
            'email' => $user->email,
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
        ]);

        $response
            ->assertSessionHasNoErrors()
            ->assertRedirect(route('login'));

        return true;
    });
});