<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreKeeperDashboardControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $storeKeeper;
    private User $employee;
    private User $overseer;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users
        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);

        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        $this->overseer = User::factory()->create();
        $this->overseer->organizations()->attach($this->organization->id);

        // Create inventory items
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions and assign to users
        Permission::firstOrCreate(['name' => 'store-keep']);
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);

        $this->storeKeeper->givePermissionTo(['store-keep']);
        $this->employee->givePermissionTo(['view-store-requisitions', 'create-store-requisition']);
    }

    public function test_store_keeper_can_access_dashboard()
    {
        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('StoreKeeper/Dashboard')
            ->has('stats')
            ->has('pendingApprovals')
            ->has('inventoryAlerts')
            ->has('recentActivity')
        );
    }

    public function test_dashboard_shows_correct_statistics()
    {
        // Create employee requisition for pending approval (store keeper can approve)
        $pendingRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Pending requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        // Create store keeper's own requisitions for my_requisitions count
        $myDraftRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->storeKeeper->id,
            'purpose' => 'My draft requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        $myApprovedRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->storeKeeper->id,
            'purpose' => 'My approved requisition',
            'status' => StoreRequisition::STATUS_APPROVED,
            'requested_at' => now(),
            'approved_at' => now(),
            'approver_user_id' => $this->overseer->id,
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->where('stats.pending_approvals', 1)
            ->where('stats.inventory_items', 1)
            ->where('stats.my_requisitions', 2)
        );
    }

    public function test_dashboard_shows_pending_approvals()
    {
        // Create pending requisitions
        $pendingRequisition1 = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'First pending requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now()->subMinute(),
        ]);

        $pendingRequisition2 = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Second pending requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->has('pendingApprovals', 2)
            ->where('pendingApprovals.0.id', $pendingRequisition2->id) // Most recent first
            ->where('pendingApprovals.1.id', $pendingRequisition1->id)
        );
    }

    public function test_dashboard_shows_inventory_alerts()
    {
        // Create low stock item
        $lowStockItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'LOW-STOCK-001',
            'name' => 'Low Stock Item',
            'description' => 'Item with low stock',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 5,
            'reorder_level' => 10,
        ]);

        // Create out of stock item
        $outOfStockItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'OUT-OF-STOCK-001',
            'name' => 'Out of Stock Item',
            'description' => 'Item with no stock',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 0,
            'reorder_level' => 5,
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->has('inventoryAlerts', 2) // Should have both items
            ->where('inventoryAlerts.0.id', $outOfStockItem->id) // Out of stock is more critical
            ->where('inventoryAlerts.1.id', $lowStockItem->id)
        );
    }

    public function test_dashboard_filters_data_by_organization()
    {
        // Create another organization with its own data
        $otherOrganization = Organization::factory()->create();
        $otherBranch = Branch::factory()->create(['organization_id' => $otherOrganization->id]);
        $otherDepartment = Department::factory()->create([
            'organization_id' => $otherOrganization->id,
            'branch_id' => $otherBranch->id
        ]);
        $otherUser = User::factory()->create();
        $otherUser->organizations()->attach($otherOrganization->id);

        // Create requisition in other organization
        $otherRequisition = StoreRequisition::create([
            'organization_id' => $otherOrganization->id,
            'branch_id' => $otherBranch->id,
            'department_id' => $otherDepartment->id,
            'requester_user_id' => $otherUser->id,
            'purpose' => 'Other org requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        // Create requisition in current organization (employee requisition for pending approval)
        $currentOrgRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Current org requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        // Create store keeper's own requisition in current organization
        $myRequisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->storeKeeper->id,
            'purpose' => 'My requisition',
            'status' => StoreRequisition::STATUS_DRAFT,
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        // Should only see data from current organization
        $response->assertInertia(fn (Assert $page) => $page
            ->where('stats.pending_approvals', 1)
            ->where('stats.my_requisitions', 1)
            ->has('pendingApprovals', 1)
            ->where('pendingApprovals.0.id', $currentOrgRequisition->id)
        );
    }

    public function test_non_store_keeper_cannot_access_dashboard()
    {
        $response = $this->actingAs($this->employee)
            ->get('/store-keeper/dashboard');

        $response->assertStatus(403);
    }

    public function test_dashboard_shows_recent_activity()
    {
        // Create some requisitions with history
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Test requisition for activity',
            'status' => StoreRequisition::STATUS_APPROVED,
            'requested_at' => now(),
            'approved_at' => now(),
            'approver_user_id' => $this->storeKeeper->id,
        ]);

        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->has('recentActivity')
        );
    }

    public function test_dashboard_handles_empty_data_gracefully()
    {
        // Delete the inventory item created in setup to test empty state
        $this->inventoryItem->delete();

        // Test dashboard with no requisitions or inventory alerts
        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->where('stats.pending_approvals', 0)
            ->where('stats.my_requisitions', 0)
            ->where('stats.inventory_items', 0)
            ->has('pendingApprovals', 0)
            ->has('inventoryAlerts', 0)
        );
    }

    public function test_dashboard_statistics_calculation_accuracy()
    {
        // Create requisitions in various states
        $states = [
            StoreRequisition::STATUS_DRAFT,
            StoreRequisition::STATUS_PENDING_APPROVAL,
            StoreRequisition::STATUS_APPROVED,
            StoreRequisition::STATUS_REJECTED,
            StoreRequisition::STATUS_ISSUED,
        ];

        // Create employee requisition for pending approval
        StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $this->employee->id,
            'purpose' => 'Employee pending requisition',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        // Create store keeper's own requisitions in various states
        foreach ($states as $index => $status) {
            StoreRequisition::create([
                'organization_id' => $this->organization->id,
                'branch_id' => $this->branch->id,
                'department_id' => $this->department->id,
                'requester_user_id' => $this->storeKeeper->id,
                'purpose' => "My requisition with status {$status}",
                'status' => $status,
                'requested_at' => $status !== StoreRequisition::STATUS_DRAFT ? now() : null,
                'approved_at' => in_array($status, [
                    StoreRequisition::STATUS_APPROVED,
                    StoreRequisition::STATUS_ISSUED
                ]) ? now() : null,
                'approver_user_id' => in_array($status, [
                    StoreRequisition::STATUS_APPROVED,
                    StoreRequisition::STATUS_ISSUED
                ]) ? $this->employee->id : null, // Employee approves store keeper's requisitions
            ]);
        }

        $response = $this->actingAs($this->storeKeeper)
            ->get('/store-keeper/dashboard');

        $response->assertInertia(fn (Assert $page) => $page
            ->where('stats.my_requisitions', 5)
            ->where('stats.pending_approvals', 1)
            ->where('stats.awaiting_fulfillment', 1) // Only approved, not issued
        );
    }
}
