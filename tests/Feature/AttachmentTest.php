<?php
/*
namespace Tests\Feature;

use App\Models\Attachment;
use App\Models\Requisition;
use App\Models\User;
use App\Models\Organization;
use App\Models\Department;
use App\Models\Branch;
use Src\Attachment\Application\Services\AttachmentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AttachmentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('private');
    }

    public function test_can_upload_file_to_requisition()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);
        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Test requisition'
        ]);

        // Authenticate user
        $this->actingAs($user);

        // Create a fake file
        $file = UploadedFile::fake()->create('test-document.pdf', 1024, 'application/pdf');

        // Upload file
        $response = $this->post("/attachments/requisitions/{$requisition->id}/upload", [
            'files' => [$file],
            'descriptions' => ['Test document'],
            'uploaded_at_step' => 'draft'
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'message',
            'attachments' => [
                '*' => [
                    'id',
                    'original_name',
                    'file_size',
                    'mime_type',
                    'description',
                    'is_evidence',
                    'uploaded_at_step'
                ]
            ]
        ]);

        // Verify attachment was created in database
        $this->assertDatabaseHas('attachments', [
            'attachable_type' => Requisition::class,
            'attachable_id' => $requisition->id,
            'original_name' => 'test-document.pdf',
            'uploaded_by' => $user->id,
            'description' => 'Test document',
            'is_evidence' => true,
            'uploaded_at_step' => 'draft'
        ]);

        // Verify file was stored
        $attachment = Attachment::where('attachable_id', $requisition->id)->first();
        Storage::disk('private')->assertExists($attachment->file_path);
    }

    public function test_can_download_attachment()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);
        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Test requisition'
        ]);

        // Create attachment with actual file
        $fileContent = 'Test file content';
        $filePath = 'attachments/requisition/2025/01/1/test.pdf';
        Storage::disk('private')->put($filePath, $fileContent);

        $attachment = Attachment::factory()->create([
            'attachable_type' => Requisition::class,
            'attachable_id' => $requisition->id,
            'uploaded_by' => $user->id,
            'original_name' => 'test.pdf',
            'file_path' => $filePath,
            'file_size' => strlen($fileContent),
            'mime_type' => 'application/pdf'
        ]);

        // Authenticate user
        $this->actingAs($user);

        // Download file
        $response = $this->get("/attachments/{$attachment->id}/download");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
        $response->assertHeader('Content-Disposition', 'attachment; filename="test.pdf"');
        $this->assertEquals($fileContent, $response->getContent());
    }

    public function test_attachment_service_can_copy_attachments()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);

        $sourceRequisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Source requisition'
        ]);

        $targetRequisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Target requisition'
        ]);

        // Create attachment with actual file
        $fileContent = 'Test file content';
        $filePath = 'attachments/requisition/2025/01/1/test.pdf';
        Storage::disk('private')->put($filePath, $fileContent);

        $sourceAttachment = Attachment::factory()->create([
            'attachable_type' => Requisition::class,
            'attachable_id' => $sourceRequisition->id,
            'uploaded_by' => $user->id,
            'file_path' => $filePath,
        ]);

        // Copy attachments
        $attachmentService = app(AttachmentService::class);
        $copiedAttachments = $attachmentService->copyAttachments(
            $sourceRequisition,
            $targetRequisition,
            'copied_test'
        );

        // Verify copy was created
        $this->assertCount(1, $copiedAttachments);
        $copiedAttachment = $copiedAttachments[0];

        $this->assertEquals($targetRequisition->id, $copiedAttachment->attachable_id);
        $this->assertEquals(Requisition::class, $copiedAttachment->attachable_type);
        $this->assertEquals($sourceAttachment->original_name, $copiedAttachment->original_name);
        $this->assertEquals('copied_test', $copiedAttachment->uploaded_at_step);

        // Verify file was copied
        Storage::disk('private')->assertExists($copiedAttachment->file_path);
        $this->assertEquals($fileContent, Storage::disk('private')->get($copiedAttachment->file_path));
    }

    public function test_unauthorized_user_cannot_upload_to_requisition()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $otherOrganization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $requester = User::factory()->create();
        $requester->organizations()->attach($organization->id);
        $unauthorizedUser = User::factory()->create();
        $unauthorizedUser->organizations()->attach($otherOrganization->id);

        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $requester->id,
            'purpose' => 'Test requisition'
        ]);

        // Authenticate unauthorized user
        $this->actingAs($unauthorizedUser);

        // Try to upload file
        $file = UploadedFile::fake()->create('test-document.pdf', 1024, 'application/pdf');
        $response = $this->post("/attachments/requisitions/{$requisition->id}/upload", [
            'files' => [$file],
        ]);

        $response->assertStatus(403);
    }
}
*/
