<?php

namespace Tests\Feature;

use App\Models\Attachment;
use App\Models\Requisition;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Organization;
use App\Models\Department;
use App\Models\Branch;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AttachmentInertiaTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('private');
    }

    public function test_inertia_upload_returns_redirect_response()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);

        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Test requisition for Inertia upload'
        ]);

        // Authenticate user
        $this->actingAs($user);

        // Create a fake file
        $file = UploadedFile::fake()->create('test-inertia.pdf', 1024, 'application/pdf');

        // Make request with Inertia header
        $response = $this->withHeaders([
            'X-Inertia' => 'true',
            'X-Inertia-Version' => '1.0',
        ])->post("/attachments/requisitions/{$requisition->id}/upload", [
            'files' => [$file],
            'descriptions' => ['Test file for Inertia'],
            'uploaded_at_step' => 'testing'
        ]);

        // Should return a redirect response for Inertia requests
        $response->assertStatus(302);
        $response->assertSessionHas('success', 'Files uploaded successfully');

        // Verify attachment was created
        $this->assertDatabaseHas('attachments', [
            'attachable_type' => Requisition::class,
            'attachable_id' => $requisition->id,
            'original_name' => 'test-inertia.pdf',
            'uploaded_by' => $user->id,
        ]);
    }

    public function test_api_upload_returns_json_response()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);

        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Test requisition for API upload'
        ]);

        // Authenticate user
        $this->actingAs($user);

        // Create a fake file
        $file = UploadedFile::fake()->create('test-api.pdf', 1024, 'application/pdf');

        // Make request without Inertia header (API request)
        $response = $this->post("/attachments/requisitions/{$requisition->id}/upload", [
            'files' => [$file],
            'descriptions' => ['Test file for API'],
            'uploaded_at_step' => 'testing'
        ]);

        // Should return a JSON response for API requests
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'message',
            'attachments' => [
                '*' => [
                    'id',
                    'original_name',
                    'file_size',
                    'mime_type',
                    'description',
                    'is_evidence',
                    'uploaded_at_step'
                ]
            ]
        ]);

        // Verify attachment was created
        $this->assertDatabaseHas('attachments', [
            'attachable_type' => Requisition::class,
            'attachable_id' => $requisition->id,
            'original_name' => 'test-api.pdf',
            'uploaded_by' => $user->id,
        ]);
    }

    public function test_inertia_delete_returns_redirect_response()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);

        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'purpose' => 'Test requisition for Inertia delete'
        ]);

        // Create an attachment
        $attachment = Attachment::factory()->create([
            'attachable_type' => Requisition::class,
            'attachable_id' => $requisition->id,
            'uploaded_by' => $user->id,
            'original_name' => 'test-delete.pdf',
            'file_name' => 'test-delete.pdf',
            'file_path' => 'test/path/test-delete.pdf',
            'file_size' => 1024,
            'mime_type' => 'application/pdf',
        ]);

        // Authenticate user
        $this->actingAs($user);

        // Make delete request with Inertia header
        $response = $this->withHeaders([
            'X-Inertia' => 'true',
            'X-Inertia-Version' => '1.0',
        ])->delete("/attachments/{$attachment->id}");

        // Should return a redirect response for Inertia requests (303 is correct for DELETE redirects)
        $response->assertRedirect();
        $response->assertSessionHas('success', 'Attachment deleted successfully');

        // Verify attachment was deleted
        $this->assertDatabaseMissing('attachments', [
            'id' => $attachment->id,
        ]);
    }
}
