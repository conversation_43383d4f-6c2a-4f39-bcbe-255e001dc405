<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class StoreRequisitionSecurityTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $storeKeeper;
    private User $financeManager;
    private Organization $organization;
    private Branch $branch;
    private Department $department;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        $this->department = Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ]);

        // Create users with different roles
        $this->employee = User::factory()->create();
        $this->employee->organizations()->attach($this->organization->id);

        $this->storeKeeper = User::factory()->create();
        $this->storeKeeper->organizations()->attach($this->organization->id);

        $this->financeManager = User::factory()->create();
        $this->financeManager->organizations()->attach($this->organization->id);

        // Create inventory item
        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item for testing',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 100,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-store-requisitions']);
        Permission::firstOrCreate(['name' => 'create-store-requisition']);
        Permission::firstOrCreate(['name' => 'edit-store-requisition']);
        Permission::firstOrCreate(['name' => 'approve-store-requisition']);
        Permission::firstOrCreate(['name' => 'store-keep']);

        // Create roles
        $financeManagerRole = \Spatie\Permission\Models\Role::firstOrCreate([
            'name' => 'Finance Manager',
            'organization_id' => $this->organization->id
        ]);

        // Assign permissions
        $this->employee->givePermissionTo([
            'view-store-requisitions',
            'create-store-requisition',
            'edit-store-requisition'
        ]);
        
        $this->storeKeeper->givePermissionTo(['store-keep']);
        
        $this->financeManager->givePermissionTo([
            'view-store-requisitions', 
            'create-store-requisition', 
            'edit-store-requisition', 
            'approve-store-requisition'
        ]);

        // Assign roles
        $this->financeManager->assignRole($financeManagerRole);
    }

    private function createPendingRequisition(User $requester): StoreRequisition
    {
        $requisition = StoreRequisition::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'department_id' => $this->department->id,
            'requester_user_id' => $requester->id,
            'purpose' => 'Test requisition for security testing',
            'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
            'requested_at' => now(),
        ]);

        StoreRequisitionItem::create([
            'store_requisition_id' => $requisition->id,
            'inventory_item_id' => $this->inventoryItem->id,
            'quantity_requested' => 5,
        ]);

        return $requisition;
    }

    public function test_approval_endpoint_has_rate_limiting()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Clear any existing rate limit cache
        Cache::flush();

        // Make 10 requests (should be allowed)
        for ($i = 0; $i < 10; $i++) {
            $response = $this->actingAs($this->storeKeeper)
                ->post("/store-requisitions/{$requisition->id}/approve", [
                    'comments' => "Approval attempt {$i}"
                ]);
            
            // First request should succeed, subsequent ones should fail due to status change
            if ($i === 0) {
                $response->assertRedirect();
            } else {
                $response->assertStatus(403); // Cannot approve own requisition
            }
        }

        // Create a new pending requisition for the 11th request
        $newRequisition = $this->createPendingRequisition($this->employee);

        // 11th request should be rate limited
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$newRequisition->id}/approve", [
                'comments' => 'Rate limited request'
            ]);

        $response->assertStatus(429); // Too Many Requests
    }

    public function test_rejection_endpoint_has_rate_limiting()
    {
        // Clear any existing rate limit cache
        Cache::flush();

        // Make 10 requests (should be allowed)
        for ($i = 0; $i < 10; $i++) {
            $requisition = $this->createPendingRequisition($this->employee);
            
            $response = $this->actingAs($this->storeKeeper)
                ->post("/store-requisitions/{$requisition->id}/reject", [
                    'rejection_reason' => "Rejection attempt {$i}"
                ]);

            $response->assertRedirect();
        }

        // Create a new pending requisition for the 11th request
        $newRequisition = $this->createPendingRequisition($this->employee);

        // 11th request should be rate limited
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$newRequisition->id}/reject", [
                'rejection_reason' => 'Rate limited rejection'
            ]);

        $response->assertStatus(429); // Too Many Requests
    }

    public function test_return_for_revision_endpoint_has_rate_limiting()
    {
        // Clear any existing rate limit cache
        Cache::flush();

        // Make 10 requests (should be allowed)
        for ($i = 0; $i < 10; $i++) {
            $requisition = $this->createPendingRequisition($this->employee);
            
            $response = $this->actingAs($this->storeKeeper)
                ->postJson("/store-requisitions/{$requisition->id}/return-for-revision", [
                    'comments' => "Return attempt {$i}"
                ]);

            $response->assertStatus(200);
        }

        // Create a new pending requisition for the 11th request
        $newRequisition = $this->createPendingRequisition($this->employee);

        // 11th request should be rate limited
        $response = $this->actingAs($this->storeKeeper)
            ->postJson("/store-requisitions/{$newRequisition->id}/return-for-revision", [
                'comments' => 'Rate limited return'
            ]);

        $response->assertStatus(429); // Too Many Requests
    }

    public function test_rate_limiting_blocks_excessive_requests()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Clear any existing rate limit cache
        Cache::flush();

        // Make exactly 10 requests to hit the limit
        for ($i = 0; $i < 10; $i++) {
            $newRequisition = $this->createPendingRequisition($this->employee);
            $this->actingAs($this->storeKeeper)
                ->post("/store-requisitions/{$newRequisition->id}/approve", [
                    'comments' => "Request {$i}"
                ]);
        }

        // Next request should be blocked
        $blockedRequisition = $this->createPendingRequisition($this->employee);
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$blockedRequisition->id}/approve", [
                'comments' => 'This should be blocked'
            ]);

        $response->assertStatus(429);
    }

    public function test_rate_limiting_allows_normal_usage()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Clear any existing rate limit cache
        Cache::flush();

        // Normal usage should work fine
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Normal approval'
            ]);

        $response->assertRedirect();
    }

    public function test_validation_constants_are_enforced()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test comment length validation (1000 character limit)
        $longComment = str_repeat('a', 1001);
        
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => $longComment
            ]);

        $response->assertSessionHasErrors(['comments']);
    }

    public function test_comment_length_validation_prevents_overflow()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test exactly at the limit (should pass)
        $maxComment = str_repeat('a', 1000);
        
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => $maxComment
            ]);

        $response->assertRedirect();

        // Test over the limit (should fail)
        $newRequisition = $this->createPendingRequisition($this->employee);
        $overLimitComment = str_repeat('a', 1001);
        
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$newRequisition->id}/approve", [
                'comments' => $overLimitComment
            ]);

        $response->assertSessionHasErrors(['comments']);
    }

    public function test_rejection_reason_length_validation()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test rejection reason length validation (1000 character limit)
        $longReason = str_repeat('a', 1001);
        
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/reject", [
                'rejection_reason' => $longReason
            ]);

        $response->assertSessionHasErrors(['rejection_reason']);
    }

    public function test_authorization_errors_return_proper_json()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        $response = $this->actingAs($this->employee)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Unauthorized approval'
            ]);

        $response->assertStatus(403);
    }

    public function test_invalid_requisition_id_returns_404()
    {
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/99999/approve", [
                'comments' => 'Approval for non-existent requisition'
            ]);

        $response->assertStatus(404);
    }

    public function test_malformed_request_data_is_rejected()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test with invalid JSON structure
        $response = $this->actingAs($this->storeKeeper)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => ['invalid' => 'structure'] // Should be string, not array
            ]);

        $response->assertSessionHasErrors(['comments']);
    }

    public function test_unauthorized_access_returns_403()
    {
        $requisition = $this->createPendingRequisition($this->employee);

        // Test with user who has no permissions
        $unauthorizedUser = User::factory()->create();
        $unauthorizedUser->organizations()->attach($this->organization->id);

        $response = $this->actingAs($unauthorizedUser)
            ->post("/store-requisitions/{$requisition->id}/approve", [
                'comments' => 'Unauthorized approval'
            ]);

        $response->assertStatus(403);
    }
}
