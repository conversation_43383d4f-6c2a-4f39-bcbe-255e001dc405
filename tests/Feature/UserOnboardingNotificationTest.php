<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Organization;
use App\Models\Role;
use App\Notifications\UserOnboardingNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class UserOnboardingNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions
        Permission::create(['name' => 'create-users']);
        Permission::create(['name' => 'manage-users']);
    }

    public function test_user_receives_onboarding_notification_when_created_by_organization_admin()
    {
        Notification::fake();

        // Create organization and organization admin
        $organization = Organization::factory()->create();
        
        $organizationAdminRole = Role::create([
            'name' => 'Organization Admin',
            'organization_id' => $organization->id,
            'description' => 'Administrator for the organization',
            'guard_name' => 'web'
        ]);
        
        $organizationAdminRole->givePermissionTo(['create-users', 'manage-users']);

        $organizationAdmin = User::factory()->create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>'
        ]);
        
        $organizationAdmin->assignRole($organizationAdminRole);
        $organizationAdmin->organizations()->attach($organization->id);

        // Create employee role
        $employeeRole = Role::create([
            'name' => 'Employee',
            'organization_id' => $organization->id,
            'description' => 'Regular employee',
            'guard_name' => 'web'
        ]);

        // Act as organization admin and create a new user
        $response = $this->actingAs($organizationAdmin)
            ->post(route('users.store'), [
                'username' => 'newuser',
                'first_name' => 'New',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'password' => 'TempPassword123!',
                'status' => 'active',
                'role_ids' => [$employeeRole->id],
            ]);

        // Get the created user
        $newUser = User::where('email', '<EMAIL>')->first();

        // Assert user was created
        $this->assertNotNull($newUser);

        // Assert notification was sent to the new user
        Notification::assertSentTo(
            $newUser,
            UserOnboardingNotification::class
        );

        // Assert notification was not sent to the organization admin
        Notification::assertNotSentTo(
            $organizationAdmin,
            UserOnboardingNotification::class
        );
    }

    public function test_onboarding_notification_contains_correct_data()
    {
        // Create test data
        $organization = Organization::factory()->create();
        
        $organizationAdmin = User::factory()->create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>'
        ]);

        $newUser = User::factory()->create([
            'username' => 'testuser',
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>'
        ]);

        $plainPassword = 'TempPassword123!';

        // Create notification
        $notification = new UserOnboardingNotification($newUser, $plainPassword, $organizationAdmin);
        $notificationData = $notification->toDatabase($newUser);

        // Assert notification content
        $this->assertEquals('Welcome to ' . config('app.name'), $notificationData['title']);
        $this->assertStringContainsString('Admin User', $notificationData['message']);
        $this->assertStringContainsString('check your email', $notificationData['message']);
        $this->assertEquals('user_onboarding', $notificationData['type']);
        $this->assertEquals($newUser->id, $notificationData['user_id']);
        $this->assertEquals('testuser', $notificationData['metadata']['username']);
        $this->assertEquals('<EMAIL>', $notificationData['metadata']['email']);
        $this->assertEquals('Admin User', $notificationData['metadata']['created_by']);
        $this->assertEquals($organizationAdmin->id, $notificationData['metadata']['created_by_id']);
    }

    public function test_onboarding_email_contains_login_credentials()
    {
        // Create test data
        $organizationAdmin = User::factory()->create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>'
        ]);

        $newUser = User::factory()->create([
            'username' => 'testuser',
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>'
        ]);

        $plainPassword = 'TempPassword123!';

        // Create notification
        $notification = new UserOnboardingNotification($newUser, $plainPassword, $organizationAdmin);
        $mailMessage = $notification->toMail($newUser);

        // Assert email content
        $this->assertEquals('Welcome to ' . config('app.name') . ' - Your Account Details', $mailMessage->subject);
        
        // Check that the email uses the correct markdown template
        $this->assertEquals('emails.users.onboarding-notification', $mailMessage->markdown);
        
        // Check that the correct data is passed to the template
        $this->assertEquals($newUser, $mailMessage->viewData['newUser']);
        $this->assertEquals($plainPassword, $mailMessage->viewData['plainPassword']);
        $this->assertEquals($organizationAdmin, $mailMessage->viewData['organizationAdmin']);
        $this->assertEquals(route('login'), $mailMessage->viewData['url']);
    }

    public function test_notification_action_url_points_to_login_page()
    {
        // Create test data
        $organizationAdmin = User::factory()->create();
        $newUser = User::factory()->create();
        $plainPassword = 'TempPassword123!';

        // Create notification
        $notification = new UserOnboardingNotification($newUser, $plainPassword, $organizationAdmin);
        $notificationData = $notification->toDatabase($newUser);

        // Assert action_url points to login page (relative URL)
        $expectedUrl = '/login';
        $this->assertEquals($expectedUrl, $notificationData['action_url']);
    }
}