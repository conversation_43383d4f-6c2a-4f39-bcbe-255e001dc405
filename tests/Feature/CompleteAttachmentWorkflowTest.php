<?php
/*
namespace Tests\Feature;

use App\Models\Attachment;
use App\Models\Requisition;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Organization;
use App\Models\Department;
use App\Models\Branch;
use App\Models\ChartOfAccount;
use Src\Attachment\Application\Services\AttachmentService;
use Src\Disbursement\Application\Services\DisbursementService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CompleteAttachmentWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('private');
    }

    public function test_complete_requisition_to_transaction_attachment_workflow()
    {
        // Setup test data
        $organization = Organization::factory()->create();
        $branch = Branch::factory()->create(['organization_id' => $organization->id]);
        $department = Department::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id
        ]);
        $user = User::factory()->create();
        $user->organizations()->attach($organization->id);
        $chartOfAccount = ChartOfAccount::factory()->create([
            'organization_id' => $organization->id,
        ]);

        $this->actingAs($user);

        // Step 1: Create a requisition
        $requisition = Requisition::factory()->create([
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'department_id' => $department->id,
            'requester_user_id' => $user->id,
            'status' => 'draft',
            'purpose' => 'Office supplies procurement'
        ]);

        // Step 2: Upload files to requisition during draft stage
        $attachmentService = app(AttachmentService::class);

        $draftFile = UploadedFile::fake()->create('budget-proposal.pdf', 1024, 'application/pdf');
        $draftAttachment = $attachmentService->uploadFile(
            $draftFile,
            $requisition,
            'Initial budget proposal',
            true,
            'draft'
        );

        $this->assertCount(1, $requisition->fresh()->attachments);
        $this->assertEquals('draft', $draftAttachment->uploaded_at_step);

        // Step 3: Update requisition status and upload approval documents
        $requisition->update(['status' => 'pending_approval']);

        $approvalFile = UploadedFile::fake()->create('manager-approval.jpg', 512, 'image/jpeg');
        $approvalAttachment = $attachmentService->uploadFile(
            $approvalFile,
            $requisition,
            'Manager approval signature',
            true,
            'pending_approval'
        );

        $this->assertCount(2, $requisition->fresh()->attachments);

        // Step 4: Approve requisition and upload final documents
        $requisition->update(['status' => 'approved']);

        $finalFile = UploadedFile::fake()->create('final-approval.pdf', 2048, 'application/pdf');
        $finalAttachment = $attachmentService->uploadFile(
            $finalFile,
            $requisition,
            'Final approval document',
            true,
            'approved'
        );

        $this->assertCount(3, $requisition->fresh()->attachments);

        // Step 5: Move requisition to transaction (disbursement)
        $disbursementService = app(DisbursementService::class);
        $itemsArray = [
            [
                'chart_of_account_id' => $chartOfAccount->id,
                'description' => 'Office supplies',
                'quantity' => 1,
                'unit_price' => 500.00,
                'total_price' => 500.00,
                'reference_number' => 'OFF-001'
            ]
        ];

        $transaction = $disbursementService->openTransaction($requisition, $itemsArray);

        // Step 6: Verify all attachments were copied to transaction
        $transactionAttachments = $transaction->fresh()->attachments;
        $this->assertCount(3, $transactionAttachments);

        // Verify each attachment was copied correctly
        $copiedDraft = $transactionAttachments->where('original_name', 'budget-proposal.pdf')->first();
        $copiedApproval = $transactionAttachments->where('original_name', 'manager-approval.jpg')->first();
        $copiedFinal = $transactionAttachments->where('original_name', 'final-approval.pdf')->first();

        $this->assertNotNull($copiedDraft);
        $this->assertNotNull($copiedApproval);
        $this->assertNotNull($copiedFinal);

        // All copied attachments should have the new step
        $this->assertEquals('moved_to_transaction', $copiedDraft->uploaded_at_step);
        $this->assertEquals('moved_to_transaction', $copiedApproval->uploaded_at_step);
        $this->assertEquals('moved_to_transaction', $copiedFinal->uploaded_at_step);

        // Step 7: Upload additional files to transaction
        $transactionFile = UploadedFile::fake()->create('bank-details.pdf', 1024, 'application/pdf');
        $transactionAttachment = $attachmentService->uploadFile(
            $transactionFile,
            $transaction,
            'Bank account details for payment',
            true,
            'payment_processing'
        );

        // Step 8: Verify transaction now has 4 attachments (3 copied + 1 new)
        $this->assertCount(4, $transaction->fresh()->attachments);

        // Step 9: Verify original requisition still has its 3 attachments
        $this->assertCount(3, $requisition->fresh()->attachments);

        // Step 10: Verify all files exist in storage
        $allAttachments = Attachment::all();
        $this->assertCount(7, $allAttachments); // 3 original + 3 copied + 1 new

        foreach ($allAttachments as $attachment) {
            Storage::disk('private')->assertExists($attachment->file_path);
        }

        // Step 11: Test file download functionality
        $response = $this->get("/attachments/{$transactionAttachment->id}/download");
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
        $response->assertHeader('Content-Disposition', 'attachment; filename="bank-details.pdf"');

        // Step 12: Test attachment deletion (only affects transaction, not requisition)
        $response = $this->delete("/attachments/{$copiedDraft->id}");
        $response->assertStatus(200);

        // Verify transaction now has 3 attachments, requisition still has 3
        $this->assertCount(3, $transaction->fresh()->attachments);
        $this->assertCount(3, $requisition->fresh()->attachments);

        // Step 13: Verify workflow tracking
        $remainingTransactionAttachments = $transaction->fresh()->attachments;
        $uploadSteps = $remainingTransactionAttachments->pluck('uploaded_at_step')->unique()->toArray();

        $this->assertContains('moved_to_transaction', $uploadSteps);
        $this->assertContains('payment_processing', $uploadSteps);

        // Final verification: Complete workflow integrity
        $this->assertTrue($transaction->exists());
        $this->assertTrue($requisition->exists());
        $this->assertEquals($requisition->id, $transaction->requisition_id);
        $this->assertEquals('opened', $transaction->status);
    }
}
*/
