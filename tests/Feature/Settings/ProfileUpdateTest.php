<?php

use App\Models\User;

// uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

// test('profile page is displayed', function () {
//     $user = User::factory()->create();

//     $response = $this
//         ->actingAs($user)
//         ->get('/settings/profile');

//     $response->assertOk();
// });

// test('profile information can be updated', function () {
//     $user = User::factory()->create();

//     $response = $this
//         ->actingAs($user)
//         ->patch('/settings/profile', [
//             'username' => 'Test User',
//             'first_name' => 'Test', 
//             'last_name' => 'User',
//             'email' => '<EMAIL>',
//         ]);

//     $response
//         ->assertSessionHasNoErrors()
//         ->assertRedirect('/settings/profile');

//     $user->refresh();

//     expect($user->username)->toBe('Test User');
//     expect($user->email)->toBe('<EMAIL>');
//     expect($user->email_verified_at)->toBeNull();
// });

// test('email verification status is unchanged when the email address is unchanged', function () {
//     $user = User::factory()->create();

//     $response = $this
//         ->actingAs($user)
//         ->patch('/settings/profile', [
//             // 'name' => 'Test User',
//             'username' => 'Test User',
//             'first_name' => 'Test', 
//             'last_name' => 'User',
//             'email' => $user->email,
//         ]);

//     $response
//         ->assertSessionHasNoErrors()
//         ->assertRedirect('/settings/profile');

//     expect($user->refresh()->email_verified_at)->not->toBeNull();
// });

// test('user can delete their account', function () {
//     $user = User::factory()->create();

//     $response = $this
//         ->actingAs($user)
//         ->delete('/settings/profile', [
//             'password' => 'password',
//         ]);

//     $response
//         ->assertSessionHasNoErrors()
//         ->assertRedirect('/');

//     $this->assertGuest();
//     expect($user->fresh())->toBeNull();
// });

// test('correct password must be provided to delete account', function () {
//     $user = User::factory()->create();

//     $response = $this
//         ->actingAs($user)
//         ->from('/settings/profile')
//         ->delete('/settings/profile', [
//             'password' => 'wrong-password',
//         ]);

//     $response
//         ->assertSessionHasErrors('password')
//         ->assertRedirect('/settings/profile');

//     expect($user->fresh())->not->toBeNull();
// });