<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class InventoryControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Branch $branch;
    private InventoryItem $inventoryItem;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
        
        $this->user = User::factory()->create();
        $this->user->organizations()->attach($this->organization->id);

        $this->inventoryItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'TEST-ITEM-001',
            'name' => 'Test Item',
            'description' => 'Test inventory item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 50,
            'reorder_level' => 10,
        ]);

        // Create permissions
        Permission::firstOrCreate(['name' => 'view-inventory']);
        Permission::firstOrCreate(['name' => 'manage-inventory']);
        Permission::firstOrCreate(['name' => 'store-keep']);
    }

    public function test_index_returns_inventory_items_with_view_permission()
    {
        $this->user->givePermissionTo('view-inventory');

        $response = $this->actingAs($this->user)
            ->getJson('/inventory/api');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'sku',
                        'name',
                        'description',
                        'unit_of_measure',
                        'quantity_on_hand',
                        'reorder_level'
                    ]
                ]
            ]);
    }

    public function test_index_requires_permission()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/inventory/api');

        $response->assertStatus(403);
    }

    public function test_store_creates_inventory_item_with_manage_permission()
    {
        $this->user->givePermissionTo('manage-inventory');

        $itemData = [
            'branch_id' => $this->branch->id,
            'sku' => 'NEW-ITEM-001',
            'name' => 'New Test Item',
            'description' => 'A new test item',
            'unit_of_measure' => 'kg',
            'quantity_on_hand' => 25,
            'reorder_level' => 5,
        ];

        $response = $this->actingAs($this->user)
            ->post('/inventory', $itemData);

        $response->assertStatus(302); // Redirect after successful creation

        $this->assertDatabaseHas('inventory_items', [
            'sku' => 'NEW-ITEM-001',
            'organization_id' => $this->organization->id
        ]);

        // Check transaction was created for initial stock
        $this->assertDatabaseHas('inventory_transactions', [
            'transaction_type' => 'receipt',
            'quantity_change' => 25,
            'notes' => 'Initial stock entry'
        ]);
    }

    public function test_store_validates_required_fields()
    {
        $this->user->givePermissionTo('manage-inventory');

        $response = $this->actingAs($this->user)
            ->postJson('/inventory', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sku', 'name', 'unit_of_measure']);
    }

    public function test_store_prevents_duplicate_sku()
    {
        $this->user->givePermissionTo('manage-inventory');

        $itemData = [
            'sku' => 'TEST-ITEM-001', // Same as existing item
            'name' => 'Duplicate SKU Item',
            'unit_of_measure' => 'piece',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/inventory', $itemData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sku']);
    }

    public function test_show_returns_inventory_item_with_permission()
    {
        $this->user->givePermissionTo('view-inventory');

        $response = $this->actingAs($this->user)
            ->get("/inventory/{$this->inventoryItem->id}");

        $response->assertStatus(200); // Returns Inertia page, not JSON
        $response->assertInertia(fn ($page) => $page
            ->component('Inventory/ShowInventory')
            ->has('inventory_item')
            ->where('inventory_item.id', $this->inventoryItem->id)
            ->where('inventory_item.sku', 'TEST-ITEM-001')
            ->where('inventory_item.name', 'Test Item')
        );
    }

    public function test_show_prevents_cross_organization_access()
    {
        $this->user->givePermissionTo('view-inventory');
        
        $otherOrg = Organization::factory()->create();
        $otherItem = InventoryItem::create([
            'organization_id' => $otherOrg->id,
            'sku' => 'OTHER-ITEM-001',
            'name' => 'Other Org Item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 10,
            'reorder_level' => 2,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/inventory/{$otherItem->id}");

        $response->assertStatus(403);
    }

    public function test_update_modifies_inventory_item()
    {
        $this->user->givePermissionTo('manage-inventory');

        $updateData = [
            'sku' => 'UPDATED-ITEM-001',
            'name' => 'Updated Test Item',
            'description' => 'Updated description',
            'unit_of_measure' => 'kg',
            'reorder_level' => 15,
        ];

        $response = $this->actingAs($this->user)
            ->put("/inventory/{$this->inventoryItem->id}", $updateData);

        $response->assertStatus(302); // Redirect after successful update

        $this->assertDatabaseHas('inventory_items', [
            'id' => $this->inventoryItem->id,
            'sku' => 'UPDATED-ITEM-001',
            'name' => 'Updated Test Item'
        ]);
    }

    public function test_destroy_deletes_inventory_item_without_requisitions()
    {
        $this->user->givePermissionTo('manage-inventory');

        $response = $this->actingAs($this->user)
            ->deleteJson("/inventory/{$this->inventoryItem->id}");

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => 'Inventory item deleted successfully']);

        $this->assertDatabaseMissing('inventory_items', [
            'id' => $this->inventoryItem->id
        ]);
    }

    public function test_adjust_stock_requires_permission()
    {
        $adjustmentData = [
            'quantity_change' => 10,
            'notes' => 'Stock adjustment test'
        ];

        $response = $this->actingAs($this->user)
            ->postJson("/inventory/{$this->inventoryItem->id}/adjust-stock", $adjustmentData);

        $response->assertStatus(403);
    }

    public function test_adjust_stock_validates_required_fields()
    {
        $this->user->givePermissionTo('manage-inventory');

        $response = $this->actingAs($this->user)
            ->postJson("/inventory/{$this->inventoryItem->id}/adjust-stock", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['quantity_change']);
    }

    public function test_low_stock_returns_items_below_reorder_level()
    {
        $this->user->givePermissionTo('view-inventory');

        // Create an item with low stock
        $lowStockItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'LOW-STOCK-001',
            'name' => 'Low Stock Item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 5,
            'reorder_level' => 10,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/inventory/low-stock');

        $response->assertStatus(200)
            ->assertJsonFragment([
                'sku' => 'LOW-STOCK-001',
                'name' => 'Low Stock Item'
            ]);
    }

    public function test_transaction_history_returns_item_transactions()
    {
        $this->user->givePermissionTo('view-inventory');

        // Create some transactions
        InventoryTransaction::create([
            'inventory_item_id' => $this->inventoryItem->id,
            'user_id' => $this->user->id,
            'transaction_type' => 'receipt',
            'quantity_change' => 20,
            'transaction_date' => now(),
            'notes' => 'Test receipt'
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/inventory/{$this->inventoryItem->id}/transactions");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'transaction_type',
                        'quantity_change',
                        'transaction_date',
                        'notes'
                    ]
                ]
            ]);
    }

    public function test_receive_goods_creates_receipt_transaction()
    {
        $this->user->givePermissionTo('manage-inventory');

        $receiptData = [
            'quantity_received' => 25,
            'reference_document' => 'PO-001',
            'supplier' => 'Test Supplier',
            'unit_cost' => 15.50,
            'condition' => 'good',
            'notes' => 'Good quality items'
        ];

        $response = $this->actingAs($this->user)
            ->postJson("/inventory/{$this->inventoryItem->id}/receive-goods", $receiptData);

        $response->assertStatus(200)
            ->assertJsonFragment([
                'message' => 'Goods received successfully',
                'new_quantity' => '75.00', // Original 50 + 25
                'condition' => 'good'
            ]);

        $this->assertDatabaseHas('inventory_transactions', [
            'transaction_type' => 'receipt',
            'quantity_change' => 25
        ]);
    }

    public function test_replenishment_suggestions_returns_low_stock_items()
    {
        $this->user->givePermissionTo('view-inventory');

        // Create items with different stock levels
        $criticalItem = InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'CRITICAL-001',
            'name' => 'Critical Item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 0,
            'reorder_level' => 5,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/inventory/replenishment-suggestions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'suggestions' => [
                    '*' => [
                        'item_id',
                        'sku',
                        'name',
                        'current_stock',
                        'reorder_level',
                        'suggested_order_quantity',
                        'priority'
                    ]
                ],
                'total_items',
                'critical_items'
            ]);
    }

    public function test_dashboard_summary_returns_inventory_statistics()
    {
        $this->user->givePermissionTo('view-inventory');

        // Create additional test data
        InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'OUT-OF-STOCK-001',
            'name' => 'Out of Stock Item',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 0,
            'reorder_level' => 5,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/inventory/dashboard-summary');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'inventory_summary' => [
                    'total_items',
                    'low_stock_items',
                    'out_of_stock_items',
                    'stock_health'
                ],
                'requisition_summary' => [
                    'pending_approval',
                    'awaiting_fulfillment'
                ],
                'recent_activity'
            ]);
    }

    public function test_store_keep_permission_allows_inventory_operations()
    {
        $this->user->givePermissionTo('store-keep');

        // Test that store-keep permission works for viewing
        $response = $this->actingAs($this->user)
            ->getJson('/inventory/api');

        $response->assertStatus(200);

        // Test that store-keep permission works for creating
        $itemData = [
            'branch_id' => $this->branch->id,
            'sku' => 'STORE-KEEP-001',
            'name' => 'Store Keep Item',
            'unit_of_measure' => 'piece',
        ];

        $response = $this->actingAs($this->user)
            ->post('/inventory', $itemData);

        $response->assertStatus(302); // Redirect after successful creation
    }
}
