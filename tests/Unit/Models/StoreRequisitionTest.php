<?php

namespace Tests\Unit\Models;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    $this->organization = Organization::factory()->create();
    $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
    $this->department = Department::factory()->create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id
    ]);
    
    $this->user = User::factory()->create();
    $this->user->organizations()->attach($this->organization->id);
    
    $this->inventoryItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-001',
        'name' => 'Test Item',
        'description' => 'Test inventory item',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
});

it('can be created with required fields', function () {
    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Test requisition purpose',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    
    expect($requisition)->toBeInstanceOf(StoreRequisition::class);
    expect($requisition->purpose)->toBe('Test requisition purpose');
    expect($requisition->status)->toBe(StoreRequisition::STATUS_DRAFT);
    expect($requisition->organization_id)->toBe($this->organization->id);
    expect($requisition->requester_user_id)->toBe($this->user->id);
});

it('has correct relationships', function () {
    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Test requisition purpose',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    
    expect($requisition->organization)->toBeInstanceOf(Organization::class);
    expect($requisition->branch)->toBeInstanceOf(Branch::class);
    expect($requisition->department)->toBeInstanceOf(Department::class);
    expect($requisition->requester)->toBeInstanceOf(User::class);
    expect($requisition->items)->toBeInstanceOf(\Illuminate\Database\Eloquent\Collection::class);
});

it('correctly identifies if it can be edited', function () {
    // Draft requisitions can be edited
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    expect($draftRequisition->canBeEdited())->toBeTrue();

    // Rejected requisitions can be edited
    $rejectedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Rejected requisition',
        'status' => StoreRequisition::STATUS_REJECTED,
    ]);
    expect($rejectedRequisition->canBeEdited())->toBeTrue();

    // Pending approval requisitions cannot be edited
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Pending requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
    ]);
    expect($pendingRequisition->canBeEdited())->toBeFalse();

    // Approved requisitions cannot be edited
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
    ]);
    expect($approvedRequisition->canBeEdited())->toBeFalse();
});

it('correctly identifies if it can be approved', function () {
    // Pending approval requisitions can be approved
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Pending requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
    ]);
    expect($pendingRequisition->canBeApproved())->toBeTrue();

    // Draft requisitions cannot be approved
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    expect($draftRequisition->canBeApproved())->toBeFalse();

    // Already approved requisitions cannot be approved again
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
    ]);
    expect($approvedRequisition->canBeApproved())->toBeFalse();
});

it('correctly identifies if it can be issued', function () {
    // Approved requisitions can be issued
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
    ]);
    expect($approvedRequisition->canBeIssued())->toBeTrue();

    // Partially issued requisitions can be issued
    $partiallyIssuedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Partially issued requisition',
        'status' => StoreRequisition::STATUS_PARTIALLY_ISSUED,
    ]);
    expect($partiallyIssuedRequisition->canBeIssued())->toBeTrue();

    // Draft requisitions cannot be issued
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    expect($draftRequisition->canBeIssued())->toBeFalse();

    // Pending requisitions cannot be issued
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Pending requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
    ]);
    expect($pendingRequisition->canBeIssued())->toBeFalse();
});

it('correctly identifies if it can be returned for revision', function () {
    // Pending approval requisitions can be returned for revision
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Pending requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
    ]);
    expect($pendingRequisition->canBeReturnedForRevision())->toBeTrue();

    // Draft requisitions cannot be returned for revision
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    expect($draftRequisition->canBeReturnedForRevision())->toBeFalse();

    // Approved requisitions cannot be returned for revision
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
    ]);
    expect($approvedRequisition->canBeReturnedForRevision())->toBeFalse();
});

it('calculates total items correctly', function () {
    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Test requisition for items',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    
    // Add items to the requisition
    StoreRequisitionItem::create([
        'store_requisition_id' => $requisition->id,
        'inventory_item_id' => $this->inventoryItem->id,
        'quantity_requested' => 5,
    ]);
    
    $secondInventoryItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-002',
        'name' => 'Test Item 2',
        'description' => 'Second test inventory item',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 50,
        'reorder_level' => 5,
    ]);
    
    StoreRequisitionItem::create([
        'store_requisition_id' => $requisition->id,
        'inventory_item_id' => $secondInventoryItem->id,
        'quantity_requested' => 3,
    ]);
    
    $requisition->refresh();
    expect($requisition->items->count())->toBe(2);
    expect($requisition->items->sum('quantity_requested'))->toBe(8.0);
});

it('tracks approval information correctly', function () {
    $approver = User::factory()->create();

    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
        'approver_user_id' => $approver->id,
        'approved_at' => now(),
    ]);
    
    expect($requisition->approver)->toBeInstanceOf(User::class);
    expect($requisition->approver->id)->toBe($approver->id);
    expect($requisition->approved_at)->not->toBeNull();
});

it('tracks issuer information correctly', function () {
    $issuer = User::factory()->create();

    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Issued requisition',
        'status' => StoreRequisition::STATUS_ISSUED,
        'issuer_user_id' => $issuer->id,
        'issued_at' => now(),
    ]);
    
    expect($requisition->issuer)->toBeInstanceOf(User::class);
    expect($requisition->issuer->id)->toBe($issuer->id);
    expect($requisition->issued_at)->not->toBeNull();
});

it('has correct status constants', function () {
    expect(StoreRequisition::STATUS_DRAFT)->toBe('draft');
    expect(StoreRequisition::STATUS_PENDING_APPROVAL)->toBe('pending_approval');
    expect(StoreRequisition::STATUS_APPROVED)->toBe('approved');
    expect(StoreRequisition::STATUS_REJECTED)->toBe('rejected');
    expect(StoreRequisition::STATUS_PARTIALLY_ISSUED)->toBe('partially_issued');
    expect(StoreRequisition::STATUS_ISSUED)->toBe('issued');
});

it('correctly identifies draft status', function () {
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
        'requested_at' => null,
    ]);
    
    expect($draftRequisition->status)->toBe(StoreRequisition::STATUS_DRAFT);
    expect($draftRequisition->requested_at)->toBeNull();
});

it('sets requested_at when status changes from draft', function () {
    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Draft to pending requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
        'requested_at' => null,
    ]);
    
    $requisition->update([
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);
    
    expect($requisition->status)->toBe(StoreRequisition::STATUS_PENDING_APPROVAL);
    expect($requisition->requested_at)->not->toBeNull();
});
