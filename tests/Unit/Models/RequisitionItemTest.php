<?php

namespace Tests\Unit\Models;

use App\Models\ChartOfAccount;
use App\Models\Requisition;
use App\Models\RequisitionItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

it('can be created and associated with a requisition and chart of account', function () {
    // Arrange
    $requisition = Requisition::factory()->create(['purpose' => 'Test Purpose']); // Add purpose
    $chartOfAccount = ChartOfAccount::factory()->create();

    // Act
    $requisitionItem = RequisitionItem::create([
        'requisition_id' => $requisition->id,
        'chart_of_account_id' => $chartOfAccount->id,
        'description' => 'Laptop Charger',
        'quantity' => 2,
        'unit_price' => 65.00,
    ]);

    // Assert
    expect($requisitionItem)->toBeInstanceOf(RequisitionItem::class);
    expect($requisitionItem->description)->toBe('Laptop Charger');
    expect($requisitionItem->quantity)->toBe(2);
    expect((string) $requisitionItem->unit_price)->toBe('65.00');
    expect((string) $requisitionItem->total_price)->toBe('130.00');
    expect($requisitionItem->requisition_id)->toBe($requisition->id);
    expect($requisitionItem->chart_of_account_id)->toBe($chartOfAccount->id);

    // Assert Relationships
    expect($requisitionItem->requisition)->toBeInstanceOf(Requisition::class);
    expect($requisitionItem->chartOfAccount)->toBeInstanceOf(ChartOfAccount::class);

    // Assert Database
    $this->assertDatabaseHas('requisition_items', [
        'id' => $requisitionItem->id,
        'requisition_id' => $requisition->id,
        'chart_of_account_id' => $chartOfAccount->id,
        'description' => 'Laptop Charger',
        'quantity' => 2,
        'unit_price' => 65.00,
        'total_price' => 130.00,
    ]);
});

it('automatically calculates total price on saving', function () {
    // Arrange
    $requisition = Requisition::factory()->create(['purpose' => 'Test Purpose']); // Add purpose
    $chartOfAccount = ChartOfAccount::factory()->create();

    $requisitionItem = new RequisitionItem([
        'requisition_id' => $requisition->id,
        'chart_of_account_id' => $chartOfAccount->id,
        'description' => 'Mouse',
        'quantity' => 5,
        'unit_price' => 25.50,
    ]);

    // Act
    $requisitionItem->save();

    // Assert
    expect((string) number_format($requisitionItem->total_price, 2, '.', ''))->toBe((string) number_format((5 * 25.50), 2, '.', ''));
    $this->assertDatabaseHas('requisition_items', [
        'id' => $requisitionItem->id,
        'total_price' => 5 * 25.50,
    ]);

    // Act: Update quantity
    $requisitionItem->quantity = 3;
    $requisitionItem->save();

    // Assert
    expect((string) number_format($requisitionItem->total_price, 2, '.', ''))->toBe((string) number_format((3 * 25.50), 2, '.', ''));
    $this->assertDatabaseHas('requisition_items', [
        'id' => $requisitionItem->id,
        'total_price' => 3 * 25.50,
    ]);

    // Act: Update unit price
    $requisitionItem->unit_price = 30.00;
    $requisitionItem->save();

    // Assert
    expect((string) number_format($requisitionItem->total_price, 2, '.', ''))->toBe((string) number_format((3 * 30.00), 2, '.', ''));
    $this->assertDatabaseHas('requisition_items', [
        'id' => $requisitionItem->id,
        'total_price' => 3 * 30.00,
    ]);
});
