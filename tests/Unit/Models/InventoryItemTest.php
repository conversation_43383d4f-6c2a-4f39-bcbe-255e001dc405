<?php

namespace Tests\Unit\Models;

use App\Models\Branch;
use App\Models\InventoryItem;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    $this->organization = Organization::factory()->create();
    $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
});

it('can be created with required fields', function () {
    $item = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-001',
        'name' => 'Test Item',
        'description' => 'Test inventory item',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
    
    expect($item)->toBeInstanceOf(InventoryItem::class);
    expect($item->sku)->toBe('TEST-ITEM-001');
    expect($item->name)->toBe('Test Item');
    expect($item->quantity_on_hand)->toBe('100.00'); // Decimal cast returns string
    expect($item->reorder_level)->toBe('10.00'); // Decimal cast returns string
});

it('has correct relationships', function () {
    $item = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-001',
        'name' => 'Test Item',
        'description' => 'Test inventory item',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
    
    expect($item->organization)->toBeInstanceOf(Organization::class);
    expect($item->branch)->toBeInstanceOf(Branch::class);
    expect($item->organization->id)->toBe($this->organization->id);
    expect($item->branch->id)->toBe($this->branch->id);
});

it('correctly identifies low stock items', function () {
    // Item with stock below reorder level
    $lowStockItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'LOW-STOCK-001',
        'name' => 'Low Stock Item',
        'description' => 'Item with low stock',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 5,
        'reorder_level' => 10,
    ]);
    
    $isLowStock = $lowStockItem->quantity_on_hand <= $lowStockItem->reorder_level;
    expect($isLowStock)->toBeTrue();
});

it('correctly identifies normal stock items', function () {
    // Item with stock above reorder level
    $normalStockItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'NORMAL-STOCK-001',
        'name' => 'Normal Stock Item',
        'description' => 'Item with normal stock',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 50,
        'reorder_level' => 10,
    ]);
    
    $isLowStock = $normalStockItem->quantity_on_hand <= $normalStockItem->reorder_level;
    expect($isLowStock)->toBeFalse();
});

it('correctly identifies out of stock items', function () {
    $outOfStockItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'OUT-OF-STOCK-001',
        'name' => 'Out of Stock Item',
        'description' => 'Item with no stock',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 0,
        'reorder_level' => 5,
    ]);
    
    $isOutOfStock = $outOfStockItem->quantity_on_hand == 0;
    expect($isOutOfStock)->toBeTrue();
});

it('calculates available stock correctly', function () {
    $item = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'AVAILABLE-STOCK-001',
        'name' => 'Available Stock Item',
        'description' => 'Item for stock calculation',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
    
    // Available stock is the current quantity on hand
    expect($item->quantity_on_hand)->toBe('100.00');

    // After issuing some items, available stock should decrease
    $item->update(['quantity_on_hand' => 85]);
    expect($item->quantity_on_hand)->toBe('85.00');
});

it('handles decimal quantities correctly', function () {
    $item = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'DECIMAL-QTY-001',
        'name' => 'Decimal Quantity Item',
        'description' => 'Item with decimal quantities',
        'unit_of_measure' => 'liter',
        'quantity_on_hand' => 25.75,
        'reorder_level' => 5.5,
    ]);
    
    expect($item->quantity_on_hand)->toBe('25.75');
    expect($item->reorder_level)->toBe('5.50');

    // Check low stock with decimal values (convert to float for comparison)
    $isLowStock = (float)$item->quantity_on_hand <= (float)$item->reorder_level;
    expect($isLowStock)->toBeFalse();

    // Update to low stock
    $item->update(['quantity_on_hand' => 3.25]);
    $isLowStock = (float)$item->quantity_on_hand <= (float)$item->reorder_level;
    expect($isLowStock)->toBeTrue();
});

it('enforces unique SKU within organization', function () {
    InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'UNIQUE-SKU-001',
        'name' => 'First Item',
        'description' => 'First item with this SKU',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 10,
        'reorder_level' => 2,
    ]);
    
    // Attempting to create another item with the same SKU should fail
    expect(function () {
        InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'UNIQUE-SKU-001',
            'name' => 'Second Item',
            'description' => 'Second item with same SKU',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 20,
            'reorder_level' => 5,
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

it('enforces global SKU uniqueness', function () {
    $otherOrganization = Organization::factory()->create();
    $otherBranch = Branch::factory()->create(['organization_id' => $otherOrganization->id]);

    // Create item in first organization
    $item1 = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'GLOBAL-UNIQUE-SKU-001',
        'name' => 'Item in Org 1',
        'description' => 'Item in first organization',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 10,
        'reorder_level' => 2,
    ]);

    // Attempting to create item with same SKU in different organization should fail
    expect(function () use ($otherOrganization, $otherBranch) {
        InventoryItem::create([
            'organization_id' => $otherOrganization->id,
            'branch_id' => $otherBranch->id,
            'sku' => 'GLOBAL-UNIQUE-SKU-001', // Same SKU
            'name' => 'Item in Org 2',
            'description' => 'Item in second organization',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 20,
            'reorder_level' => 5,
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

it('validates required fields', function () {
    // Missing SKU should fail
    expect(function () {
        InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'name' => 'Item without SKU',
            'description' => 'Item missing SKU',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 10,
            'reorder_level' => 2,
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
    
    // Missing name should fail
    expect(function () {
        InventoryItem::create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id,
            'sku' => 'NO-NAME-001',
            'description' => 'Item missing name',
            'unit_of_measure' => 'piece',
            'quantity_on_hand' => 10,
            'reorder_level' => 2,
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

it('can be updated with new stock levels', function () {
    $item = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'UPDATE-STOCK-001',
        'name' => 'Updatable Item',
        'description' => 'Item for stock updates',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
    
    // Update stock levels
    $item->update([
        'quantity_on_hand' => 75,
        'reorder_level' => 15,
    ]);
    
    expect($item->quantity_on_hand)->toBe('75.00');
    expect($item->reorder_level)->toBe('15.00');

    // Verify in database
    $this->assertDatabaseHas('inventory_items', [
        'id' => $item->id,
        'quantity_on_hand' => '75.00',
        'reorder_level' => '15.00',
    ]);
});

it('stores timestamps correctly', function () {
    $item = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TIMESTAMP-001',
        'name' => 'Timestamp Item',
        'description' => 'Item for timestamp testing',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 10,
        'reorder_level' => 2,
    ]);
    
    expect($item->created_at)->not->toBeNull();
    expect($item->updated_at)->not->toBeNull();
    expect($item->created_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
    expect($item->updated_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
});

it('can be filtered by organization', function () {
    $otherOrganization = Organization::factory()->create();
    $otherBranch = Branch::factory()->create(['organization_id' => $otherOrganization->id]);
    
    // Create items in different organizations
    $item1 = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'ORG-FILTER-001',
        'name' => 'Item in Org 1',
        'description' => 'Item in first organization',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 10,
        'reorder_level' => 2,
    ]);
    
    $item2 = InventoryItem::create([
        'organization_id' => $otherOrganization->id,
        'branch_id' => $otherBranch->id,
        'sku' => 'ORG-FILTER-002',
        'name' => 'Item in Org 2',
        'description' => 'Item in second organization',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 20,
        'reorder_level' => 5,
    ]);
    
    // Filter by organization
    $org1Items = InventoryItem::where('organization_id', $this->organization->id)->get();
    $org2Items = InventoryItem::where('organization_id', $otherOrganization->id)->get();
    
    expect($org1Items->count())->toBe(1);
    expect($org2Items->count())->toBe(1);
    expect($org1Items->first()->id)->toBe($item1->id);
    expect($org2Items->first()->id)->toBe($item2->id);
});
