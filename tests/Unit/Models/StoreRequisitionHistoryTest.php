<?php

namespace Tests\Unit\Models;

use App\Models\Branch;
use App\Models\Department;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionHistory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    $this->organization = Organization::factory()->create();
    $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
    $this->department = Department::factory()->create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id
    ]);
    
    $this->user = User::factory()->create();
    $this->user->organizations()->attach($this->organization->id);
    
    $this->storeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Test requisition for history testing',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
});

it('can create history record with valid data', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'created_as_draft',
        'Store requisition created as draft',
        ['status' => 'draft']
    );

    expect($history)->toBeInstanceOf(StoreRequisitionHistory::class);
    expect($history->store_requisition_id)->toBe($this->storeRequisition->id);
    expect($history->user_id)->toBe($this->user->id);
    expect($history->action)->toBe('created_as_draft');
    expect($history->comments)->toBe('Store requisition created as draft');
    expect($history->changes)->toBe(['status' => 'draft']);
});

it('validates action types against allowed list', function () {
    $allowedActions = [
        'created_as_draft', 'created_and_submitted', 'edited',
        'edited_and_submitted', 'edited_and_resubmitted',
        'submitted_for_approval', 'approved', 'rejected',
        'returned_for_revision', 'issued'
    ];

    foreach ($allowedActions as $action) {
        $history = StoreRequisitionHistory::createRecord(
            $this->storeRequisition->id,
            $this->user->id,
            $action,
            "Test action: {$action}"
        );

        expect($history->action)->toBe($action);
    }
});

it('throws exception for invalid action types', function () {
    expect(function () {
        StoreRequisitionHistory::createRecord(
            $this->storeRequisition->id,
            $this->user->id,
            'invalid_action',
            'This should fail'
        );
    })->toThrow(\InvalidArgumentException::class, 'Invalid action: invalid_action');
});

it('limits comment length to 1000 characters', function () {
    $longComment = str_repeat('a', 1500); // 1500 characters
    
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'approved',
        $longComment
    );

    expect(strlen($history->comments))->toBe(1000);
    expect($history->comments)->toBe(str_repeat('a', 1000));
});

it('truncates comments longer than 1000 characters', function () {
    $longComment = str_repeat('x', 1200) . 'END'; // 1203 characters
    
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'rejected',
        $longComment
    );

    expect(strlen($history->comments))->toBe(1000);
    expect($history->comments)->not->toContain('END');
    expect($history->comments)->toBe(str_repeat('x', 1000));
});

it('stores changes as JSON array', function () {
    $changes = [
        'status' => ['from' => 'draft', 'to' => 'pending_approval'],
        'requested_at' => now()->toISOString()
    ];
    
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'submitted_for_approval',
        'Submitted for approval',
        $changes
    );

    expect($history->changes)->toBe($changes);
    expect($history->changes)->toBeArray();
});

it('has correct relationship to store requisition', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'created_as_draft',
        'Test relationship'
    );

    expect($history->storeRequisition)->toBeInstanceOf(StoreRequisition::class);
    expect($history->storeRequisition->id)->toBe($this->storeRequisition->id);
});

it('has correct relationship to user', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'created_as_draft',
        'Test relationship'
    );

    expect($history->user)->toBeInstanceOf(User::class);
    expect($history->user->id)->toBe($this->user->id);
});

it('stores action names correctly', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'created_as_draft',
        'Test action storage'
    );

    expect($history->action)->toBe('created_as_draft');
});

it('orders by created_at descending by default', function () {
    // Create multiple history records with different timestamps
    $history1 = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'created_as_draft',
        'First record'
    );

    sleep(1); // Ensure different timestamps

    $history2 = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'submitted_for_approval',
        'Second record'
    );

    $histories = $this->storeRequisition->histories;
    
    expect($histories->first()->id)->toBe($history2->id);
    expect($histories->last()->id)->toBe($history1->id);
});

it('creates record with createRecord static method', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'approved',
        'Test static method'
    );

    expect($history)->toBeInstanceOf(StoreRequisitionHistory::class);
    expect($history->exists)->toBeTrue();
    
    $this->assertDatabaseHas('store_requisition_histories', [
        'store_requisition_id' => $this->storeRequisition->id,
        'user_id' => $this->user->id,
        'action' => 'approved',
        'comments' => 'Test static method'
    ]);
});

it('handles null comments gracefully', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'approved',
        null
    );

    expect($history->comments)->toBeNull();
});

it('handles null changes gracefully', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'approved',
        'Test comment',
        null
    );

    expect($history->changes)->toBeNull();
});

it('validates required fields on creation', function () {
    expect(function () {
        StoreRequisitionHistory::create([
            // Missing required fields
            'action' => 'approved'
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

it('casts created_at and updated_at to datetime', function () {
    $history = StoreRequisitionHistory::createRecord(
        $this->storeRequisition->id,
        $this->user->id,
        'approved',
        'Test datetime casting'
    );

    expect($history->created_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
    expect($history->updated_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
});

it('stores all approval workflow actions correctly', function () {
    $workflowActions = [
        'created_as_draft' => 'Draft created',
        'submitted_for_approval' => 'Submitted for approval',
        'approved' => 'Approved by manager',
        'rejected' => 'Rejected due to insufficient justification',
        'returned_for_revision' => 'Returned for more details',
        'issued' => 'Items issued to requester'
    ];

    foreach ($workflowActions as $action => $comment) {
        $history = StoreRequisitionHistory::createRecord(
            $this->storeRequisition->id,
            $this->user->id,
            $action,
            $comment
        );

        expect($history->action)->toBe($action);
        expect($history->comments)->toBe($comment);
        
        $this->assertDatabaseHas('store_requisition_histories', [
            'store_requisition_id' => $this->storeRequisition->id,
            'action' => $action,
            'comments' => $comment
        ]);
    }
});
