<?php

namespace Tests\Unit\Models;

use App\Models\Branch; // Assuming Branch model exists
use App\Models\ChartOfAccount;
use App\Models\Organization; // Assuming Organization model exists
use Illuminate\Foundation\Testing\RefreshDatabase; // Resets DB for each test
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class); // Use Traits via uses() in Pest

it('can be created with required attributes linked to an organization', function () {
    // Arrange: Create prerequisite data
    $organization = Organization::factory()->create();

    // Act: Create the ChartOfAccount
    $chartOfAccount = ChartOfAccount::create([
        'organization_id' => $organization->id,
        'name' => 'Office Supplies',
        'account_type' => 'expense',
    ]);

    $chartOfAccount->refresh(); // Reload the model instance from the database

    // Assert: Check if it was created and attributes are set
    expect($chartOfAccount)->toBeInstanceOf(ChartOfAccount::class);
    expect($chartOfAccount->name)->toBe('Office Supplies');
    expect($chartOfAccount->account_type)->toBe('expense');
    expect($chartOfAccount->is_active)->toBeTrue();
    expect($chartOfAccount->organization_id)->toBe($organization->id);

    // Assert Relationship (optional but good)
    expect($chartOfAccount->organization)->toBeInstanceOf(Organization::class);
    expect($chartOfAccount->organization->id)->toBe($organization->id);

    // Assert Database (Check the actual DB value which might be 1 or true)
    $this->assertDatabaseHas('chart_of_accounts', [
        'id' => $chartOfAccount->id,
        'name' => 'Office Supplies',
        'organization_id' => $organization->id,
        'account_type' => 'expense',
        'is_active' => true, // Or 1, depending on your DB driver's boolean handling
    ]);
});

it('can have optional attributes like branch and parent', function () {
     // Arrange
    $organization = Organization::factory()->create();
    $branch = Branch::factory()->create(['organization_id' => $organization->id]); // Assumes Branch factory
    $parentAccount = ChartOfAccount::factory()->create(['organization_id' => $organization->id]); // Assumes CoA factory

    // Act
    $chartOfAccount = ChartOfAccount::create([
        'organization_id' => $organization->id,
        'branch_id' => $branch->id,
        'parent_id' => $parentAccount->id,
        'code' => 'EXP-001',
        'name' => 'Specific Office Supplies',
        'description' => 'Supplies for Kisumu branch',
        'spending_limit' => 50000.00,
        'limit_period' => 'monthly',
        'account_type' => 'expense',
    ]);

    // Assert
    $this->assertDatabaseHas('chart_of_accounts', [
        'id' => $chartOfAccount->id,
        'branch_id' => $branch->id,
        'parent_id' => $parentAccount->id,
        'code' => 'EXP-001',
        'spending_limit' => 50000.00,
        'limit_period' => 'monthly',
    ]);

    // Assert Relationships
    expect($chartOfAccount->branch)->toBeInstanceOf(Branch::class);
    expect($chartOfAccount->parent)->toBeInstanceOf(ChartOfAccount::class);
});
