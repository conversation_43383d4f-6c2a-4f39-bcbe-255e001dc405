<?php

namespace Tests\Unit\Models;

use App\Models\Branch;
use App\Models\InventoryItem;
use App\Models\InventoryTransaction;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    $this->organization = Organization::factory()->create();
    $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
    
    $this->user = User::factory()->create();
    $this->user->organizations()->attach($this->organization->id);
    
    $this->inventoryItem = InventoryItem::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'sku' => 'TEST-ITEM-001',
        'name' => 'Test Item',
        'description' => 'Test inventory item for transactions',
        'unit_of_measure' => 'piece',
        'quantity_on_hand' => 100,
        'reorder_level' => 10,
    ]);
});

it('can create transaction with valid data', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -10,
        'related_document_id' => 1,
        'related_document_type' => StoreRequisition::class,
        'notes' => 'Test issuance transaction',
        'transaction_date' => now(),
    ]);

    expect($transaction)->toBeInstanceOf(InventoryTransaction::class);
    expect($transaction->inventory_item_id)->toBe($this->inventoryItem->id);
    expect($transaction->user_id)->toBe($this->user->id);
    expect($transaction->transaction_type)->toBe('issuance');
    expect($transaction->quantity_change)->toBe('-10.00');
    expect($transaction->related_document_type)->toBe(StoreRequisition::class);
    expect($transaction->notes)->toBe('Test issuance transaction');
});

it('validates transaction types', function () {
    $validTypes = ['issuance', 'receipt', 'adjustment'];

    foreach ($validTypes as $type) {
        $transaction = InventoryTransaction::create([
            'inventory_item_id' => $this->inventoryItem->id,
            'user_id' => $this->user->id,
            'transaction_type' => $type,
            'quantity_change' => $type === 'issuance' ? -5 : 5,
            'transaction_date' => now(),
        ]);

        expect($transaction->transaction_type)->toBe($type);
    }
});

it('calculates balance correctly', function () {
    $initialBalance = $this->inventoryItem->quantity_on_hand;

    // Create issuance transaction
    $issuanceTransaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -15,
        'transaction_date' => now(),
    ]);

    // Update inventory item balance
    $this->inventoryItem->update([
        'quantity_on_hand' => $this->inventoryItem->quantity_on_hand + $issuanceTransaction->quantity_change
    ]);

    expect($this->inventoryItem->fresh()->quantity_on_hand)->toBe('85.00');

    // Create receipt transaction
    $receiptTransaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'receipt',
        'quantity_change' => 20,
        'transaction_date' => now(),
    ]);

    // Update inventory item balance
    $this->inventoryItem->update([
        'quantity_on_hand' => $this->inventoryItem->quantity_on_hand + $receiptTransaction->quantity_change
    ]);

    expect($this->inventoryItem->fresh()->quantity_on_hand)->toBe('105.00');
});

it('has correct relationships', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'adjustment',
        'quantity_change' => 5,
        'transaction_date' => now(),
    ]);

    // Test inventory item relationship
    expect($transaction->inventoryItem)->toBeInstanceOf(InventoryItem::class);
    expect($transaction->inventoryItem->id)->toBe($this->inventoryItem->id);

    // Test user relationship
    expect($transaction->user)->toBeInstanceOf(User::class);
    expect($transaction->user->id)->toBe($this->user->id);
});

it('prevents negative quantities for stock_out', function () {
    // This test ensures business logic prevents issuing more than available
    $availableStock = $this->inventoryItem->quantity_on_hand;
    
    // Try to issue more than available (this should be prevented at the business logic level)
    $excessiveIssuance = $availableStock + 10;
    
    // The transaction can be created, but business logic should prevent it
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -$excessiveIssuance,
        'transaction_date' => now(),
    ]);

    expect($transaction)->toBeInstanceOf(InventoryTransaction::class);
    expect($transaction->quantity_change)->toBe('-110.00');
    
    // Business logic should prevent updating inventory to negative
    // This would be handled in the service layer, not the model
});

it('updates inventory item quantity', function () {
    $initialQuantity = $this->inventoryItem->quantity_on_hand;
    
    // Create transaction
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -8,
        'transaction_date' => now(),
    ]);

    // Simulate the business logic that would update the inventory
    $this->inventoryItem->update([
        'quantity_on_hand' => $this->inventoryItem->quantity_on_hand + $transaction->quantity_change
    ]);

    expect($this->inventoryItem->fresh()->quantity_on_hand)->toBe('92.00');
});

it('handles decimal quantities correctly', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'adjustment',
        'quantity_change' => 5.75,
        'transaction_date' => now(),
    ]);

    expect($transaction->quantity_change)->toBe('5.75');
    expect($transaction->quantity_change)->toBeString();
});

it('stores related document information', function () {
    $storeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => \App\Models\Department::factory()->create([
            'organization_id' => $this->organization->id,
            'branch_id' => $this->branch->id
        ])->id,
        'requester_user_id' => $this->user->id,
        'purpose' => 'Test requisition',
        'status' => 'approved',
    ]);

    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -3,
        'related_document_id' => $storeRequisition->id,
        'related_document_type' => StoreRequisition::class,
        'notes' => 'Issued for store requisition',
        'transaction_date' => now(),
    ]);

    expect($transaction->related_document_id)->toBe($storeRequisition->id);
    expect($transaction->related_document_type)->toBe(StoreRequisition::class);
    expect($transaction->notes)->toBe('Issued for store requisition');
});

it('casts transaction_date to datetime', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'receipt',
        'quantity_change' => 10,
        'transaction_date' => '2024-01-15 10:30:00',
    ]);

    expect($transaction->transaction_date)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
    expect($transaction->transaction_date->format('Y-m-d H:i:s'))->toBe('2024-01-15 10:30:00');
});

it('casts quantity_change to decimal', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'adjustment',
        'quantity_change' => '15.50',
        'transaction_date' => now(),
    ]);

    expect($transaction->quantity_change)->toBe('15.50');
    expect($transaction->quantity_change)->toBeString();
});

it('handles null optional fields gracefully', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'adjustment',
        'quantity_change' => 2,
        'transaction_date' => now(),
        // related_document_id, related_document_type, and notes are null
    ]);

    expect($transaction->related_document_id)->toBeNull();
    expect($transaction->related_document_type)->toBeNull();
    expect($transaction->notes)->toBeNull();
});

it('validates required fields', function () {
    expect(function () {
        InventoryTransaction::create([
            // Missing required fields
            'transaction_type' => 'issuance',
        ]);
    })->toThrow(\Illuminate\Database\QueryException::class);
});

it('creates audit trail for inventory changes', function () {
    $transaction = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -7,
        'notes' => 'Issued for testing purposes',
        'transaction_date' => now(),
    ]);

    // Verify transaction was created and can serve as audit trail
    $this->assertDatabaseHas('inventory_transactions', [
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -7,
        'notes' => 'Issued for testing purposes',
    ]);

    // Verify we can retrieve transaction history
    $transactions = InventoryTransaction::where('inventory_item_id', $this->inventoryItem->id)->get();
    expect($transactions)->toHaveCount(1);
    expect($transactions->first()->id)->toBe($transaction->id);
});

it('supports different transaction types with appropriate quantity changes', function () {
    // Issuance (negative quantity change)
    $issuance = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'issuance',
        'quantity_change' => -12,
        'transaction_date' => now(),
    ]);

    // Receipt (positive quantity change)
    $receipt = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'receipt',
        'quantity_change' => 25,
        'transaction_date' => now(),
    ]);

    // Adjustment (can be positive or negative)
    $adjustment = InventoryTransaction::create([
        'inventory_item_id' => $this->inventoryItem->id,
        'user_id' => $this->user->id,
        'transaction_type' => 'adjustment',
        'quantity_change' => -3,
        'notes' => 'Stock count adjustment',
        'transaction_date' => now(),
    ]);

    expect($issuance->quantity_change)->toBeLessThan(0);
    expect($receipt->quantity_change)->toBeGreaterThan(0);
    expect($adjustment->quantity_change)->toBeLessThan(0);
    expect($adjustment->notes)->toBe('Stock count adjustment');
});
