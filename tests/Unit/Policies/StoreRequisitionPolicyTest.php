<?php

namespace Tests\Unit\Policies;

use App\Models\Branch;
use App\Models\Department;
use App\Models\InventoryItem;
use App\Models\Organization;
use App\Models\StoreRequisition;
use App\Models\StoreRequisitionItem;
use App\Models\User;
use App\Policies\StoreRequisitionPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

uses(TestCase::class, RefreshDatabase::class);

beforeEach(function () {
    // Create test organization and related entities
    $this->organization = Organization::factory()->create();
    $this->otherOrganization = Organization::factory()->create();
    
    $this->branch = Branch::factory()->create(['organization_id' => $this->organization->id]);
    $this->department = Department::factory()->create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id
    ]);
    
    // Create users
    $this->employee = User::factory()->create();
    $this->employee->organizations()->attach($this->organization->id);
    
    $this->storeKeeper = User::factory()->create();
    $this->storeKeeper->organizations()->attach($this->organization->id);
    
    $this->overseer = User::factory()->create();
    $this->overseer->organizations()->attach($this->organization->id);

    $this->financeManager = User::factory()->create();
    $this->financeManager->organizations()->attach($this->organization->id);

    $this->organizationAdmin = User::factory()->create();
    $this->organizationAdmin->organizations()->attach($this->organization->id);

    $this->otherOrgUser = User::factory()->create();
    $this->otherOrgUser->organizations()->attach($this->otherOrganization->id);
    
    // Create permissions
    Permission::firstOrCreate(['name' => 'view-store-requisitions']);
    Permission::firstOrCreate(['name' => 'create-store-requisition']);
    Permission::firstOrCreate(['name' => 'edit-store-requisition']);
    Permission::firstOrCreate(['name' => 'store-keep']);
    Permission::firstOrCreate(['name' => 'approve-store-requisition']);
    
    // Create roles
    $financeManagerRole = \Spatie\Permission\Models\Role::firstOrCreate([
        'name' => 'Finance Manager',
        'organization_id' => $this->organization->id
    ]);

    $organizationAdminRole = \Spatie\Permission\Models\Role::firstOrCreate([
        'name' => 'Organization Admin',
        'organization_id' => $this->organization->id
    ]);

    // Assign permissions
    $this->employee->givePermissionTo(['view-store-requisitions', 'create-store-requisition', 'edit-store-requisition']);
    $this->storeKeeper->givePermissionTo(['store-keep']);
    // HOD can create, edit own, and view own requisitions
    $this->overseer->givePermissionTo(['view-store-requisitions', 'create-store-requisition', 'edit-store-requisition']);
    // Finance Manager and Organization Admin can create, edit own, view all, and approve Store Keeper requisitions
    $this->financeManager->givePermissionTo(['view-store-requisitions', 'create-store-requisition', 'edit-store-requisition', 'approve-store-requisition']);
    $this->organizationAdmin->givePermissionTo(['view-store-requisitions', 'create-store-requisition', 'edit-store-requisition', 'approve-store-requisition']);

    // Assign roles
    $this->financeManager->assignRole($financeManagerRole);
    $this->organizationAdmin->assignRole($organizationAdminRole);
    
    $this->policy = new StoreRequisitionPolicy();
});

it('allows employee to view their own draft requisition', function () {
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Draft requisition for testing',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);
    
    expect($this->policy->view($this->employee, $draftRequisition))->toBeTrue();
});

it('prevents employee from viewing other users draft requisitions', function () {
    $otherEmployee = User::factory()->create();
    $otherEmployee->organizations()->attach($this->organization->id);
    $otherEmployee->givePermissionTo(['view-store-requisitions', 'create-store-requisition']);

    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $otherEmployee->id,
        'purpose' => 'Test draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
        'requested_at' => null,
    ]);

    expect($this->policy->view($this->employee, $draftRequisition))->toBeFalse();
});

it('allows employees to view their own non-draft requisitions', function () {
    $submittedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test submitted requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->employee, $submittedRequisition))->toBeTrue();
});

it('prevents employees from viewing other users non-draft requisitions', function () {
    $submittedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test submitted requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->employee, $submittedRequisition))->toBeFalse();
});

it('prevents users from viewing requisitions from other organizations', function () {
    $otherBranch = Branch::factory()->create(['organization_id' => $this->otherOrganization->id]);
    $otherDepartment = Department::factory()->create([
        'organization_id' => $this->otherOrganization->id,
        'branch_id' => $otherBranch->id
    ]);

    $otherOrgRequisition = StoreRequisition::create([
        'organization_id' => $this->otherOrganization->id,
        'branch_id' => $otherBranch->id,
        'department_id' => $otherDepartment->id,
        'requester_user_id' => $this->otherOrgUser->id,
        'purpose' => 'Test other org requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->employee, $otherOrgRequisition))->toBeFalse();
});

it('allows employee to edit their own draft requisition', function () {
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
        'requested_at' => null,
    ]);

    expect($this->policy->update($this->employee, $draftRequisition))->toBeTrue();
});

it('prevents employee from editing non-draft requisitions', function () {
    $submittedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test submitted requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->update($this->employee, $submittedRequisition))->toBeFalse();
});

it('prevents employee from editing other users requisitions', function () {
    $otherUserRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test other user requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
        'requested_at' => null,
    ]);

    expect($this->policy->update($this->employee, $otherUserRequisition))->toBeFalse();
});

it('allows store keeper to approve employee requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $employeeRequisition))->toBeTrue();
});

it('prevents store keeper from approving their own requisitions', function () {
    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $storeKeeperRequisition))->toBeFalse();
});

it('prevents overseer from approving store keeper requisitions', function () {
    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->overseer, $storeKeeperRequisition))->toBeFalse();
});

it('prevents overseer from approving employee requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->overseer, $employeeRequisition))->toBeFalse();
});

it('prevents approval of requisitions from other organizations', function () {
    $otherBranch = Branch::factory()->create(['organization_id' => $this->otherOrganization->id]);
    $otherDepartment = Department::factory()->create([
        'organization_id' => $this->otherOrganization->id,
        'branch_id' => $otherBranch->id
    ]);

    $otherOrgRequisition = StoreRequisition::create([
        'organization_id' => $this->otherOrganization->id,
        'branch_id' => $otherBranch->id,
        'department_id' => $otherDepartment->id,
        'requester_user_id' => $this->otherOrgUser->id,
        'purpose' => 'Test other org requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $otherOrgRequisition))->toBeFalse();
    expect($this->policy->approve($this->overseer, $otherOrgRequisition))->toBeFalse();
});

it('allows employee to edit rejected requisitions they created', function () {
    $rejectedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test rejected requisition',
        'status' => StoreRequisition::STATUS_REJECTED,
        'requested_at' => now()->subHours(2),
        'approved_at' => now(),
        'approval_notes' => 'Insufficient justification',
    ]);

    expect($this->policy->update($this->employee, $rejectedRequisition))->toBeTrue();
});

it('prevents editing rejected requisitions by other users', function () {
    $rejectedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test rejected requisition',
        'status' => StoreRequisition::STATUS_REJECTED,
        'requested_at' => now()->subHours(2),
        'approved_at' => now(),
        'approval_notes' => 'Insufficient justification',
    ]);

    expect($this->policy->update($this->employee, $rejectedRequisition))->toBeFalse();
});

it('allows finance manager to approve store keeper requisitions', function () {
    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->financeManager, $storeKeeperRequisition))->toBeTrue();
});

it('prevents finance manager from approving employee requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->financeManager, $employeeRequisition))->toBeFalse();
});

it('allows organization admin to approve store keeper requisitions', function () {
    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Test store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->organizationAdmin, $storeKeeperRequisition))->toBeTrue();
});

it('prevents organization admin from approving employee requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->organizationAdmin, $employeeRequisition))->toBeFalse();
});

it('allows finance manager to view all non-draft requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->financeManager, $employeeRequisition))->toBeTrue();
});

it('allows organization admin to view all non-draft requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->organizationAdmin, $employeeRequisition))->toBeTrue();
});

it('prevents hod from viewing other users non-draft requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->overseer, $employeeRequisition))->toBeFalse();
});

it('allows hod to view their own non-draft requisitions', function () {
    $hodRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->overseer->id,
        'purpose' => 'Test HOD requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->overseer, $hodRequisition))->toBeTrue();
});

it('prevents finance manager from approving hod requisitions', function () {
    $hodRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->overseer->id,
        'purpose' => 'Test HOD requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->financeManager, $hodRequisition))->toBeFalse();
});

it('prevents organization admin from approving hod requisitions', function () {
    $hodRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->overseer->id,
        'purpose' => 'Test HOD requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->organizationAdmin, $hodRequisition))->toBeFalse();
});

// Enhanced Approval Authorization Tests
it('allows store keeper to approve hod requisitions', function () {
    $hodRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->overseer->id,
        'purpose' => 'HOD requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $hodRequisition))->toBeTrue();
});

it('prevents store keeper from approving other store keeper requisitions', function () {
    $otherStoreKeeper = User::factory()->create();
    $otherStoreKeeper->organizations()->attach($this->organization->id);
    $otherStoreKeeper->givePermissionTo(['store-keep']);

    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $otherStoreKeeper->id,
        'purpose' => 'Other store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $storeKeeperRequisition))->toBeFalse();
});

it('prevents hod from approving store keeper requisitions', function () {
    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->overseer, $storeKeeperRequisition))->toBeFalse();
});

it('prevents employee from approving any requisitions', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->overseer->id,
        'purpose' => 'Test requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->employee, $employeeRequisition))->toBeFalse();
});

it('prevents users from approving own requisitions', function () {
    $ownRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Own requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $ownRequisition))->toBeFalse();
});

it('prevents approval of non-pending requisitions', function () {
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Already approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
        'approved_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $approvedRequisition))->toBeFalse();
});

it('prevents approval of draft requisitions', function () {
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);

    expect($this->policy->approve($this->storeKeeper, $draftRequisition))->toBeFalse();
});

it('prevents approval across different organizations', function () {
    $otherOrganization = Organization::factory()->create();
    $otherBranch = Branch::factory()->create(['organization_id' => $otherOrganization->id]);
    $otherDepartment = Department::factory()->create([
        'organization_id' => $otherOrganization->id,
        'branch_id' => $otherBranch->id
    ]);

    $crossOrgRequisition = StoreRequisition::create([
        'organization_id' => $otherOrganization->id,
        'branch_id' => $otherBranch->id,
        'department_id' => $otherDepartment->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Cross organization requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $crossOrgRequisition))->toBeFalse();
});

// Rejection Authorization Tests
it('allows same users who can approve to also reject', function () {
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Employee requisition for rejection',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    // Store keeper can reject employee requisition
    expect($this->policy->approve($this->storeKeeper, $employeeRequisition))->toBeTrue();

    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Store keeper requisition for rejection',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    // Finance manager can reject store keeper requisition
    expect($this->policy->approve($this->financeManager, $storeKeeperRequisition))->toBeTrue();
});

it('prevents rejection of non-pending requisitions', function () {
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Already approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
        'approved_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $approvedRequisition))->toBeFalse();
});

it('allows return for revision with same authorization as approval', function () {
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Requisition for return',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    // Same authorization as approval
    expect($this->policy->approve($this->storeKeeper, $pendingRequisition))->toBeTrue();
});

// Edit Authorization Tests
it('allows editing of rejected requisitions by requester', function () {
    $rejectedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Rejected requisition',
        'status' => StoreRequisition::STATUS_REJECTED,
        'rejection_reason' => 'Insufficient justification',
    ]);

    expect($this->policy->update($this->employee, $rejectedRequisition))->toBeTrue();
});

it('allows editing of returned requisitions by requester', function () {
    $returnedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Returned requisition',
        'status' => StoreRequisition::STATUS_RETURNED_FOR_REVISION,
        'rejection_reason' => 'Needs more details',
    ]);

    expect($this->policy->update($this->employee, $returnedRequisition))->toBeTrue();
});

it('allows organization admin to edit requisitions in their org', function () {
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);

    expect($this->policy->update($this->organizationAdmin, $draftRequisition))->toBeTrue();
});

it('prevents editing of approved requisitions', function () {
    $approvedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Approved requisition',
        'status' => StoreRequisition::STATUS_APPROVED,
        'approved_at' => now(),
    ]);

    expect($this->policy->update($this->employee, $approvedRequisition))->toBeFalse();
});

it('prevents editing of issued requisitions', function () {
    $issuedRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Issued requisition',
        'status' => StoreRequisition::STATUS_ISSUED,
        'issued_at' => now(),
    ]);

    expect($this->policy->update($this->employee, $issuedRequisition))->toBeFalse();
});

it('prevents editing of pending requisitions by non-requesters', function () {
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Pending requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->update($this->storeKeeper, $pendingRequisition))->toBeFalse();
});

// View Authorization Tests
it('allows draft requisition viewing only by requester', function () {
    $draftRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Draft requisition',
        'status' => StoreRequisition::STATUS_DRAFT,
    ]);

    // Requester can view their own draft
    expect($this->policy->view($this->employee, $draftRequisition))->toBeTrue();

    // Others cannot view draft requisitions
    expect($this->policy->view($this->storeKeeper, $draftRequisition))->toBeFalse();
    expect($this->policy->view($this->overseer, $draftRequisition))->toBeFalse();
});

it('allows non-draft requisition viewing by authorized users', function () {
    $pendingRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Pending requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    // Authorized users can view non-draft requisitions
    expect($this->policy->view($this->employee, $pendingRequisition))->toBeTrue();
    expect($this->policy->view($this->storeKeeper, $pendingRequisition))->toBeTrue();
    expect($this->policy->view($this->financeManager, $pendingRequisition))->toBeTrue();
    expect($this->policy->view($this->organizationAdmin, $pendingRequisition))->toBeTrue();
});

it('prevents viewing across different organizations', function () {
    $otherOrganization = Organization::factory()->create();
    $otherBranch = Branch::factory()->create(['organization_id' => $otherOrganization->id]);
    $otherDepartment = Department::factory()->create([
        'organization_id' => $otherOrganization->id,
        'branch_id' => $otherBranch->id
    ]);

    $crossOrgRequisition = StoreRequisition::create([
        'organization_id' => $otherOrganization->id,
        'branch_id' => $otherBranch->id,
        'department_id' => $otherDepartment->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Cross organization requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->view($this->storeKeeper, $crossOrgRequisition))->toBeFalse();
    expect($this->policy->view($this->financeManager, $crossOrgRequisition))->toBeFalse();
});

// Complex Role-Based Scenarios
it('handles complex approval workflow for different requester types', function () {
    // Employee -> Store Keeper approval
    $employeeRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Employee requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $employeeRequisition))->toBeTrue();
    expect($this->policy->approve($this->financeManager, $employeeRequisition))->toBeFalse();

    // Store Keeper -> Finance Manager/Org Admin approval
    $storeKeeperRequisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->storeKeeper->id,
        'purpose' => 'Store keeper requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    expect($this->policy->approve($this->storeKeeper, $storeKeeperRequisition))->toBeFalse();
    expect($this->policy->approve($this->financeManager, $storeKeeperRequisition))->toBeTrue();
    expect($this->policy->approve($this->organizationAdmin, $storeKeeperRequisition))->toBeTrue();
});

it('validates organization membership for all operations', function () {
    $otherOrganization = Organization::factory()->create();
    $userFromOtherOrg = User::factory()->create();
    $userFromOtherOrg->organizations()->attach($otherOrganization->id);
    $userFromOtherOrg->givePermissionTo(['store-keep']);

    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test requisition',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    // User from different organization cannot perform any operations
    expect($this->policy->view($userFromOtherOrg, $requisition))->toBeFalse();
    expect($this->policy->update($userFromOtherOrg, $requisition))->toBeFalse();
    expect($this->policy->approve($userFromOtherOrg, $requisition))->toBeFalse();
});

it('enforces platform admin override permissions', function () {
    // Create a platform admin user using the factory method
    $platformAdmin = User::factory()->platformAdmin()->create();
    $platformAdmin->organizations()->attach($this->organization->id);

    $requisition = StoreRequisition::create([
        'organization_id' => $this->organization->id,
        'branch_id' => $this->branch->id,
        'department_id' => $this->department->id,
        'requester_user_id' => $this->employee->id,
        'purpose' => 'Test requisition for platform admin',
        'status' => StoreRequisition::STATUS_PENDING_APPROVAL,
        'requested_at' => now(),
    ]);

    // Platform admin should have override permissions
    expect($this->policy->view($platformAdmin, $requisition))->toBeTrue();
    expect($this->policy->approve($platformAdmin, $requisition))->toBeTrue();
});
