# Multi-stage build for optimized production image
# Stage 1: Frontend build stage
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# Copy package files for better caching
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./
COPY tailwind.config.js ./
COPY components.json ./
COPY eslint.config.js ./

# Install npm dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy frontend source files
COPY resources/ ./resources/
COPY public/ ./public/

# Build frontend assets
RUN npm run build

# Stage 2: PHP dependencies stage
FROM composer:2.6 AS composer-builder

WORKDIR /app

# Copy composer files
COPY composer.json composer.lock ./

# Install PHP dependencies (production only)
RUN composer install \
    --no-dev \
    --no-scripts \
    --no-interaction \
    --optimize-autoloader \
    --prefer-dist

# Stage 3: Production runtime stage
FROM php:8.2-apache AS production

ENV COMPOSER_ALLOW_SUPERUSER=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies and PHP extensions in one layer
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libpq-dev \
    zip \
    unzip \
    git \
    curl \
    # We will use the `release_command` in fly.toml for migrations,
    # so postgresql-client is not strictly needed in the web server image
    # but can be useful for debugging.
    postgresql-client \
    && docker-php-ext-install \
    pdo \
    pdo_pgsql \
    mbstring \
    bcmath \
    gd \
    && a2enmod rewrite \
    && a2enmod headers \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Set working directory
WORKDIR /var/www/html

# Copy PHP dependencies from composer stage
COPY --from=composer-builder /app/vendor ./vendor

# Copy built frontend assets from frontend stage
COPY --from=frontend-builder /app/public/build ./public/build

# Copy application files
COPY . .

# Create docker directory and configs
RUN mkdir -p docker

# Create a simplified Apache config for Fly.io
# Fly.io handles TLS termination, so we only need to listen on a single HTTP port.
RUN echo "Listen 8080" >> /etc/apache2/ports.conf && \
    cat > /etc/apache2/sites-available/000-default.conf <<'EOF'
<VirtualHost *:8080>
    DocumentRoot /var/www/html/public
    <Directory /var/www/html/public>
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog /var/log/apache2/laravel-error.log
    CustomLog /var/log/apache2/laravel-access.log combined
</VirtualHost>
EOF

# Create a simplified startup script
RUN cat > docker/start.sh <<'EOF'
#!/bin/bash
set -e

# Start Apache in foreground
exec apache2-foreground
EOF

# Make startup script executable
RUN chmod +x docker/start.sh

# Prepare Laravel environment
RUN if [ -f .env.docker ]; then \
        cp .env.docker .env; \
    elif [ ! -f .env ]; then \
        cp .env.example .env; \
    fi \
    && php artisan key:generate --force

# Set up directories and permissions
RUN mkdir -p \
    storage/framework/views \
    storage/framework/sessions \
    storage/framework/cache \
    bootstrap/cache \
    && chmod -R 775 storage bootstrap/cache \
    && chown -R www-data:www-data storage bootstrap/cache

# Run Laravel optimizations
RUN php artisan package:discover --ansi \
    && php artisan config:clear \
    && php artisan route:clear \
    && php artisan view:clear \
    && php artisan view:cache

# Final ownership and cleanup
RUN chown -R www-data:www-data /var/www/html \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && find /var/www/html -name "*.log" -delete

# Expose the port Apache is listening on
EXPOSE 8080

# Start the application
CMD ["./docker/start.sh"]